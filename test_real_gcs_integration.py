#!/usr/bin/env python3
"""
Real GCS Integration Test - Test actual Google Cloud Storage operations
"""

import os
import json
import gzip
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealGCSIntegrationTest:
    """Test real GCS integration with authentication and document operations"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize real GCS client
        self.gcs_client = GCSConnector()
        
        # Initialize other clients for full integration test
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Test data
        self.test_batch_id = f"gcs_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    async def test_gcs_authentication(self) -> bool:
        """Test GCS authentication and bucket access"""
        
        print("🔐 GCS AUTHENTICATION TEST")
        print("=" * 60)
        
        try:
            # Test bucket access
            bucket_name = self.gcs_client.bucket_name
            print(f"   Bucket: {bucket_name}")
            
            # Test bucket exists and is accessible
            bucket_exists = self.gcs_client.bucket.exists()
            print(f"   Bucket exists: {bucket_exists}")
            
            if not bucket_exists:
                print(f"   ❌ Bucket {bucket_name} does not exist or is not accessible")
                return False
            
            # Test bucket permissions by listing (limited)
            blobs = list(self.gcs_client.bucket.list_blobs(max_results=1))
            print(f"   ✅ Bucket accessible (found {len(blobs)} sample objects)")
            
            # Test service account info
            service_account_path = self.gcs_client.service_account_path
            print(f"   Service account: {service_account_path}")
            
            if os.path.exists(service_account_path):
                print(f"   ✅ Service account file exists")
            else:
                print(f"   ❌ Service account file not found")
                return False
            
            print(f"   ✅ GCS authentication successful")
            return True
            
        except Exception as e:
            print(f"   ❌ GCS authentication failed: {e}")
            return False
    
    async def test_document_upload(self) -> bool:
        """Test uploading full documents to GCS"""
        
        print("\n📤 GCS DOCUMENT UPLOAD TEST")
        print("=" * 60)
        
        try:
            # Get test cases from CAP data
            test_cases = self.get_test_cases(3)
            
            if not test_cases:
                print("   ❌ No test cases found")
                return False
            
            print(f"   Testing with {len(test_cases)} cases")
            
            uploaded_paths = []
            
            for i, case in enumerate(test_cases, 1):
                case_id = case.get('id', f'test_case_{i}')
                case_text = case.get('text', '')
                
                if not case_text:
                    print(f"   ⚠️ Case {case_id} has no text")
                    continue
                
                print(f"\n   📄 Case {i}: {case_id}")
                print(f"      Text length: {len(case_text):,} characters")
                
                # Create GCS path
                gcs_path = f"test_documents/tx/cases/{self.test_batch_id}/{case_id}.txt"
                print(f"      GCS path: {gcs_path}")
                
                # Upload document
                try:
                    stored_path = self.gcs_client.store_text(case_text, gcs_path)
                    uploaded_paths.append(stored_path)
                    print(f"      ✅ Uploaded successfully")
                    
                except Exception as e:
                    print(f"      ❌ Upload failed: {e}")
                    return False
            
            print(f"\n   📊 UPLOAD SUMMARY:")
            print(f"      Successfully uploaded: {len(uploaded_paths)} documents")
            print(f"      Total size: {sum(len(case.get('text', '')) for case in test_cases):,} characters")
            
            # Store paths for retrieval test
            self.uploaded_paths = uploaded_paths
            self.test_cases = test_cases
            
            return len(uploaded_paths) > 0
            
        except Exception as e:
            print(f"   ❌ Document upload test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_document_retrieval(self) -> bool:
        """Test retrieving documents from GCS"""
        
        print("\n📥 GCS DOCUMENT RETRIEVAL TEST")
        print("=" * 60)
        
        if not hasattr(self, 'uploaded_paths') or not self.uploaded_paths:
            print("   ❌ No uploaded documents to retrieve")
            return False
        
        try:
            retrieved_count = 0
            
            for i, gcs_path in enumerate(self.uploaded_paths, 1):
                print(f"\n   📄 Document {i}: {gcs_path}")

                # Extract path within bucket (remove gs://bucket-name/ prefix)
                if gcs_path.startswith('gs://'):
                    bucket_path = gcs_path.split('/', 3)[-1]  # Get path after gs://bucket-name/
                else:
                    bucket_path = gcs_path

                print(f"      Bucket path: {bucket_path}")

                # Retrieve document
                try:
                    retrieved_text = self.gcs_client.get_text(bucket_path)
                    
                    if retrieved_text:
                        print(f"      ✅ Retrieved successfully")
                        print(f"      Length: {len(retrieved_text):,} characters")
                        
                        # Verify content matches original
                        original_text = self.test_cases[i-1].get('text', '')
                        if retrieved_text == original_text:
                            print(f"      ✅ Content matches original")
                            retrieved_count += 1
                        else:
                            print(f"      ❌ Content mismatch")
                            print(f"         Original: {len(original_text)} chars")
                            print(f"         Retrieved: {len(retrieved_text)} chars")
                    else:
                        print(f"      ❌ Retrieved empty content")
                        
                except Exception as e:
                    print(f"      ❌ Retrieval failed: {e}")
            
            print(f"\n   📊 RETRIEVAL SUMMARY:")
            print(f"      Successfully retrieved: {retrieved_count}/{len(self.uploaded_paths)}")
            
            return retrieved_count == len(self.uploaded_paths)
            
        except Exception as e:
            print(f"   ❌ Document retrieval test failed: {e}")
            return False
    
    async def test_gcs_supabase_consistency(self) -> bool:
        """Test GCS path storage and consistency with Supabase"""
        
        print("\n🔗 GCS ↔ SUPABASE CONSISTENCY TEST")
        print("=" * 60)
        
        try:
            # Create test cases in Supabase with GCS paths
            print("   📊 Creating test cases in Supabase...")
            
            supabase_cases = []
            
            for i, (gcs_path, case) in enumerate(zip(self.uploaded_paths, self.test_cases), 1):
                case_id = case.get('id', f'test_case_{i}')
                
                # Create Supabase record
                case_record = {
                    'id': case_id,
                    'case_name': f'Test Case {i}',
                    'jurisdiction': 'TX',
                    'batch_id': self.test_batch_id,
                    'gcs_path': gcs_path,
                    'source': 'gcs_integration_test',
                    'created_at': datetime.now().isoformat()
                }
                
                # Insert into Supabase
                result = self.supabase.table('cases').insert(case_record).execute()
                
                if result.data:
                    supabase_cases.append(result.data[0])
                    print(f"      ✅ Case {case_id}: Supabase record created")
                else:
                    print(f"      ❌ Case {case_id}: Supabase insert failed")
                    return False
            
            print(f"   📊 Created {len(supabase_cases)} Supabase records")
            
            # Verify consistency
            print("\n   🔍 Verifying GCS ↔ Supabase consistency...")
            
            consistent_count = 0
            
            for case in supabase_cases:
                case_id = case['id']
                stored_gcs_path = case['gcs_path']
                
                print(f"\n      📄 Case: {case_id}")
                print(f"         Supabase GCS path: {stored_gcs_path}")
                
                # Check if GCS file exists at stored path
                try:
                    # Extract path within bucket (remove gs://bucket-name/ prefix)
                    if stored_gcs_path.startswith('gs://'):
                        bucket_path = stored_gcs_path.split('/', 3)[-1]
                    else:
                        bucket_path = stored_gcs_path

                    blob = self.gcs_client.bucket.blob(bucket_path)
                    exists = blob.exists()
                    
                    if exists:
                        print(f"         ✅ GCS file exists at stored path")
                        consistent_count += 1
                    else:
                        print(f"         ❌ GCS file not found at stored path")
                        
                except Exception as e:
                    print(f"         ❌ Error checking GCS file: {e}")
            
            print(f"\n   📊 CONSISTENCY SUMMARY:")
            print(f"      Consistent records: {consistent_count}/{len(supabase_cases)}")
            
            consistency_rate = consistent_count / len(supabase_cases) * 100
            print(f"      Consistency rate: {consistency_rate:.1f}%")
            
            return consistency_rate == 100.0
            
        except Exception as e:
            print(f"   ❌ GCS ↔ Supabase consistency test failed: {e}")
            return False
    
    async def test_full_pipeline_with_real_gcs(self) -> bool:
        """Test complete pipeline with real GCS integration"""
        
        print("\n🔄 FULL PIPELINE WITH REAL GCS TEST")
        print("=" * 60)
        
        try:
            # Create processor with real GCS
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            # Get fresh test cases
            raw_cases = self.get_test_cases(2)
            
            if not raw_cases:
                print("   ❌ No test cases for pipeline test")
                return False
            
            print(f"   📊 Processing {len(raw_cases)} cases through full pipeline")
            
            # Process with real GCS
            batch_id = f"{self.test_batch_id}_pipeline"
            
            result = await processor.process_coherent_batch(
                raw_cases=raw_cases,
                source_type='caselaw_access_project',
                batch_id=batch_id
            )
            
            if not result['success']:
                print(f"   ❌ Pipeline processing failed: {result}")
                return False
            
            print(f"   ✅ Pipeline processing successful")
            
            # Verify all systems have data
            print("\n   🔍 Verifying 4-system integration...")
            
            # Check Supabase
            cases = self.supabase.table('cases').select('*').eq('batch_id', batch_id).execute()
            supabase_count = len(cases.data)
            print(f"      Supabase: {supabase_count} cases")
            
            # Check GCS paths in Supabase
            gcs_paths = [case.get('gcs_path') for case in cases.data if case.get('gcs_path')]
            print(f"      GCS paths in Supabase: {len(gcs_paths)}")
            
            # Verify GCS files exist
            gcs_files_exist = 0
            for gcs_path in gcs_paths:
                if gcs_path:
                    try:
                        # Extract path within bucket (remove gs://bucket-name/ prefix)
                        if gcs_path.startswith('gs://'):
                            bucket_path = gcs_path.split('/', 3)[-1]
                        else:
                            bucket_path = gcs_path

                        blob = self.gcs_client.bucket.blob(bucket_path)
                        if blob.exists():
                            gcs_files_exist += 1
                    except:
                        pass
            
            print(f"      GCS files verified: {gcs_files_exist}")
            
            # Check Neo4j
            with self.neo4j_client.driver.session() as session:
                result = session.run('MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as count', batch_id=batch_id)
                neo4j_count = result.single()['count']
                print(f"      Neo4j: {neo4j_count} cases")
            
            print(f"\n   📊 4-SYSTEM INTEGRATION SUMMARY:")
            print(f"      Supabase: {supabase_count} cases")
            print(f"      GCS: {gcs_files_exist} files")
            print(f"      Neo4j: {neo4j_count} nodes")
            print(f"      Consistency: {'✅ Perfect' if supabase_count == gcs_files_exist == neo4j_count else '❌ Inconsistent'}")
            
            return supabase_count == gcs_files_exist == neo4j_count > 0
            
        except Exception as e:
            print(f"   ❌ Full pipeline test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_test_cases(self, num_cases: int) -> list:
        """Get test cases from CAP data"""
        
        cap_files = list(Path("data/caselaw_access_project").glob("*.jsonl.gz"))
        raw_cases = []
        
        for cap_file in cap_files:
            if len(raw_cases) >= num_cases:
                break
                
            try:
                with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                    for line in f:
                        if len(raw_cases) >= num_cases:
                            break
                            
                        try:
                            raw_case = json.loads(line.strip())
                            text = raw_case.get('text', '')
                            
                            # Filter for substantial cases
                            if len(text) > 1000 and 'texas' in text.lower():
                                raw_cases.append(raw_case)
                                
                        except json.JSONDecodeError:
                            continue
                            
            except Exception as e:
                continue
        
        return raw_cases
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print("\n🧹 CLEANING UP TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().like('batch_id', f'{self.test_batch_id}%').execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean GCS (test documents)
            blobs = self.gcs_client.bucket.list_blobs(prefix=f"test_documents/tx/cases/{self.test_batch_id}")
            for blob in blobs:
                blob.delete()
            print("   ✅ Cleaned GCS test documents")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case) WHERE c.batch_id CONTAINS $batch_id DELETE c', batch_id=self.test_batch_id)
            print("   ✅ Cleaned Neo4j test nodes")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run comprehensive real GCS integration test"""
    
    print("🚨 REAL GCS INTEGRATION TEST")
    print("=" * 80)
    print("🎯 Testing actual Google Cloud Storage operations with authentication")
    
    test = RealGCSIntegrationTest()
    
    try:
        # Run all tests
        tests = [
            ("GCS Authentication", test.test_gcs_authentication),
            ("Document Upload", test.test_document_upload),
            ("Document Retrieval", test.test_document_retrieval),
            ("GCS ↔ Supabase Consistency", test.test_gcs_supabase_consistency),
            ("Full Pipeline with Real GCS", test.test_full_pipeline_with_real_gcs)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n{'='*80}")
            print(f"🧪 RUNNING: {test_name}")
            print(f"{'='*80}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"\n🎯 {test_name}: {status}")
                
            except Exception as e:
                results[test_name] = False
                print(f"\n💥 {test_name}: ❌ EXCEPTION - {e}")
        
        # Final summary
        print(f"\n{'='*80}")
        print(f"📊 REAL GCS INTEGRATION TEST SUMMARY")
        print(f"{'='*80}")
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status}: {test_name}")
        
        print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
        
        if passed == total:
            print(f"🎉 REAL GCS INTEGRATION: SUCCESS!")
            print(f"✅ All GCS operations working with real authentication")
            return True
        else:
            print(f"❌ REAL GCS INTEGRATION: FAILED!")
            print(f"❌ {total - passed} tests failed")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
