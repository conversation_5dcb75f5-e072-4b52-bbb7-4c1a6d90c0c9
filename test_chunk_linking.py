#!/usr/bin/env python3
"""
Test that chunks belonging to the same case can be linked together via metadata
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from pinecone import Pinecone
from supabase import create_client

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChunkLinkingVerifier:
    """Verify chunks can be properly linked through metadata"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.pc = Pinecone(api_key=pinecone_api_key)
        index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
        self.index = self.pc.Index(index_name)
        
        logger.info("✅ Chunk linking verifier initialized")
    
    async def verify_chunk_linking(self):
        """Verify chunks can be linked together through metadata"""
        
        print("🔗 VERIFYING CHUNK LINKING THROUGH METADATA")
        print("=" * 80)
        
        # Get recent test cases
        cases = self.supabase.table('cases').select('id, case_name').like('batch_id', 'improved_chunking_test_%').execute()
        
        if not cases.data:
            print("❌ No test cases found")
            return False
        
        print(f"📊 Testing chunk linking for {len(cases.data)} cases")
        
        all_passed = True
        
        for case in cases.data:
            case_id = case['id']
            case_name = case.get('case_name', 'Unknown')
            
            print(f"\n{'='*60}")
            print(f"📄 CASE: {case_id}")
            print(f"📋 Name: {case_name}")
            print(f"{'='*60}")
            
            case_passed = await self._verify_case_linking(case_id)
            if not case_passed:
                all_passed = False
        
        print(f"\n{'='*80}")
        if all_passed:
            print("🎉 CHUNK LINKING VERIFICATION: SUCCESS!")
            print("✅ All chunks can be properly linked through metadata")
            print("✅ Case reconstruction is possible")
        else:
            print("❌ CHUNK LINKING VERIFICATION: FAILED")
            print("❌ Some chunks cannot be properly linked")
        
        return all_passed
    
    async def _verify_case_linking(self, case_id: str) -> bool:
        """Verify linking for a single case"""
        
        try:
            # 1. Get all vectors for this case
            stats = self.index.describe_index_stats()
            query_response = self.index.query(
                vector=[0.0] * stats.dimension,
                filter={"case_id": case_id},
                top_k=100,
                include_metadata=True,
                namespace="tx"
            )
            
            vectors = query_response.matches
            print(f"🔍 Found {len(vectors)} vectors for case")
            
            if len(vectors) == 0:
                print("❌ No vectors found")
                return False
            
            # 2. Verify all vectors have required linking metadata
            print(f"\n📋 VERIFYING LINKING METADATA:")

            # First, check what metadata fields are actually available
            sample_metadata = vectors[0].metadata if vectors else {}
            available_fields = list(sample_metadata.keys())
            print(f"   Available metadata fields: {available_fields}")

            # Core required fields for linking
            core_required_fields = ['case_id', 'chunk_index']
            optional_fields = ['case_name', 'batch_id', 'court_id', 'jurisdiction']

            missing_core_fields = set()
            missing_optional_fields = set()

            for vector in vectors:
                metadata = vector.metadata

                # Check core fields
                for field in core_required_fields:
                    if field not in metadata or metadata[field] is None:
                        missing_core_fields.add(field)

                # Check optional fields
                for field in optional_fields:
                    if field not in metadata or metadata[field] is None:
                        missing_optional_fields.add(field)

            if missing_core_fields:
                print(f"❌ Missing CORE metadata fields: {missing_core_fields}")
                return False

            print(f"✅ All vectors have core linking metadata")

            if missing_optional_fields:
                print(f"⚠️ Missing optional metadata fields: {missing_optional_fields}")
            else:
                print(f"✅ All vectors have complete metadata")
            
            # 3. Verify chunk ordering and continuity
            print(f"\n🔢 VERIFYING CHUNK ORDERING:")
            
            chunk_indices = []
            for vector in vectors:
                chunk_idx = vector.metadata.get('chunk_index')
                if chunk_idx is not None:
                    chunk_indices.append(int(float(chunk_idx)))
            
            chunk_indices.sort()
            expected_indices = list(range(len(vectors)))
            
            print(f"   Expected indices: {expected_indices}")
            print(f"   Actual indices: {chunk_indices}")
            
            if chunk_indices != expected_indices:
                print(f"❌ Chunk ordering broken")
                return False
            
            print(f"✅ Chunk ordering is continuous")
            
            # 4. Test case reconstruction by chunk order
            print(f"\n🔄 TESTING CASE RECONSTRUCTION:")
            
            # Sort vectors by chunk_index
            sorted_vectors = sorted(vectors, key=lambda v: int(float(v.metadata.get('chunk_index', 0))))
            
            reconstruction_data = []
            for vector in sorted_vectors:
                metadata = vector.metadata
                reconstruction_data.append({
                    'vector_id': vector.id,
                    'chunk_index': int(float(metadata.get('chunk_index', 0))),
                    'case_id': metadata.get('case_id'),
                    'case_name': metadata.get('case_name', ''),
                    'court_id': metadata.get('court_id', ''),
                    'batch_id': metadata.get('batch_id', ''),
                    'text_length': metadata.get('text_length', 0)
                })
            
            print(f"   Reconstructed {len(reconstruction_data)} chunks in order")
            print(f"   Sample reconstruction data:")
            for i, chunk_data in enumerate(reconstruction_data[:3]):
                print(f"      Chunk {i}: {chunk_data['vector_id']} (length: {chunk_data['text_length']})")
            
            # 5. Verify metadata consistency across chunks
            print(f"\n🔍 VERIFYING METADATA CONSISTENCY:")

            first_chunk = reconstruction_data[0]
            # Only check fields that exist
            consistent_fields = ['case_id', 'batch_id']
            if 'case_name' in first_chunk and first_chunk['case_name']:
                consistent_fields.append('case_name')

            for chunk_data in reconstruction_data[1:]:
                for field in consistent_fields:
                    if chunk_data.get(field) != first_chunk.get(field):
                        print(f"❌ Inconsistent {field}: {chunk_data.get(field)} vs {first_chunk.get(field)}")
                        return False

            print(f"✅ Metadata consistent across all chunks")
            
            # 6. Test retrieval by case_id (linking functionality)
            print(f"\n🔎 TESTING RETRIEVAL BY CASE_ID:")
            
            # Query all chunks for this case
            retrieval_response = self.index.query(
                vector=[0.0] * stats.dimension,
                filter={"case_id": case_id},
                top_k=100,
                include_metadata=True,
                namespace="tx"
            )
            
            retrieved_chunks = len(retrieval_response.matches)
            print(f"   Retrieved {retrieved_chunks} chunks by case_id filter")
            
            if retrieved_chunks != len(vectors):
                print(f"❌ Retrieval count mismatch: {retrieved_chunks} vs {len(vectors)}")
                return False
            
            print(f"✅ All chunks retrievable by case_id")
            
            # 7. Test partial case reconstruction (simulate search scenario)
            print(f"\n🎯 TESTING PARTIAL RECONSTRUCTION:")
            
            # Get first 3 chunks
            partial_response = self.index.query(
                vector=[0.0] * stats.dimension,
                filter={"case_id": case_id},
                top_k=3,
                include_metadata=True,
                namespace="tx"
            )
            
            partial_chunks = sorted(partial_response.matches, 
                                  key=lambda v: int(float(v.metadata.get('chunk_index', 0))))
            
            print(f"   Retrieved first 3 chunks:")
            for chunk in partial_chunks:
                chunk_idx = int(float(chunk.metadata.get('chunk_index', 0)))
                print(f"      Chunk {chunk_idx}: {chunk.id}")
            
            # Verify we can get the "next" chunks
            if len(vectors) > 3:
                next_chunk_idx = 3
                next_response = self.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id, "chunk_index": next_chunk_idx},
                    top_k=1,
                    include_metadata=True,
                    namespace="tx"
                )
                
                if len(next_response.matches) > 0:
                    print(f"   ✅ Successfully retrieved next chunk (index {next_chunk_idx})")
                else:
                    print(f"   ❌ Could not retrieve next chunk (index {next_chunk_idx})")
                    return False
            
            # 8. Final summary for this case
            print(f"\n📊 CASE LINKING SUMMARY:")
            print(f"   ✅ {len(vectors)} chunks with complete metadata")
            print(f"   ✅ Continuous chunk ordering (0-{len(vectors)-1})")
            print(f"   ✅ Consistent metadata across chunks")
            print(f"   ✅ Full case retrievable by case_id")
            print(f"   ✅ Partial reconstruction possible")
            print(f"   ✅ Sequential chunk access working")
            
            return True
            
        except Exception as e:
            print(f"❌ Error verifying case linking: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Run chunk linking verification"""
    
    verifier = ChunkLinkingVerifier()
    
    print("🎯 CHUNK LINKING VERIFICATION")
    print("This verifies chunks can be properly linked and reconstructed")
    print()
    
    success = await verifier.verify_chunk_linking()
    
    if success:
        print("\n🎉 CHUNK LINKING VERIFICATION PASSED!")
        print("✅ Chunks can be properly linked through metadata")
        print("✅ Case reconstruction is fully supported")
        return 0
    else:
        print("\n❌ CHUNK LINKING VERIFICATION FAILED!")
        print("❌ Metadata linking needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
