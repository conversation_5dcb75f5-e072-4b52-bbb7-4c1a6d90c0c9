"""
Graph Data Science API Router

This module provides API endpoints for accessing Neo4j GDS community detection
results and graph analytics for the legal case database.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from src.processing.graph.gds_community_detector import (
    GDSCommunityDetector,
    CommunityResult,
    detect_communities,
    search_case_communities,
    get_case_community_info
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/graph", tags=["graph-analytics"])

# Request/Response models
class CommunitySearchRequest(BaseModel):
    query: str
    jurisdiction: Optional[str] = None
    practice_area: Optional[str] = None
    algorithm: str = "louvain"
    limit: int = 20

class CommunitySearchResponse(BaseModel):
    communities: List[Dict[str, Any]]
    total_count: int
    query_info: Dict[str, Any]

class CommunityDetectionRequest(BaseModel):
    force_refresh: bool = False
    algorithms: List[str] = ["louvain", "leiden"]

class CommunityDetectionResponse(BaseModel):
    results: Dict[str, Any]
    execution_time: float
    status: str
    timestamp: datetime

@router.get("/communities/search")
async def search_communities(
    query: str = Query(..., description="Search query for communities"),
    jurisdiction: Optional[str] = Query(None, description="Filter by jurisdiction"),
    practice_area: Optional[str] = Query(None, description="Filter by practice area"),
    algorithm: str = Query("louvain", description="Algorithm to use (louvain, leiden)"),
    limit: int = Query(20, description="Maximum number of communities to return")
) -> CommunitySearchResponse:
    """
    Search for communities based on query criteria.
    
    This endpoint searches through detected communities to find clusters
    of related legal cases that match the search criteria.
    
    Example:
        GET /graph/communities/search?query=non-compete&jurisdiction=texas&practice_area=employment_law
    """
    try:
        start_time = datetime.now()
        
        # Validate algorithm
        if algorithm not in ["louvain", "leiden"]:
            raise HTTPException(status_code=400, detail="Algorithm must be 'louvain' or 'leiden'")
        
        # Search communities
        communities = search_case_communities(
            query=query,
            jurisdiction=jurisdiction,
            practice_area=practice_area
        )
        
        # Limit results
        communities = communities[:limit]
        
        # Add query performance info
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return CommunitySearchResponse(
            communities=communities,
            total_count=len(communities),
            query_info={
                "query": query,
                "jurisdiction": jurisdiction,
                "practice_area": practice_area,
                "algorithm": algorithm,
                "execution_time": execution_time
            }
        )
        
    except Exception as e:
        logger.error(f"Error searching communities: {e}")
        raise HTTPException(status_code=500, detail=f"Community search failed: {str(e)}")

@router.get("/communities/{case_id}")
async def get_case_communities(
    case_id: str,
    algorithms: List[str] = Query(["louvain", "leiden"], description="Algorithms to check")
) -> Dict[str, Any]:
    """
    Get community information for a specific case.
    
    This endpoint returns the communities that a specific case belongs to
    according to different community detection algorithms.
    
    Example:
        GET /graph/communities/case_12345?algorithms=louvain,leiden
    """
    try:
        # Validate algorithms
        valid_algorithms = ["louvain", "leiden"]
        invalid_algorithms = [alg for alg in algorithms if alg not in valid_algorithms]
        if invalid_algorithms:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid algorithms: {invalid_algorithms}. Must be one of: {valid_algorithms}"
            )
        
        # Get community info
        community_info = get_case_community_info(case_id)
        
        if not community_info:
            raise HTTPException(status_code=404, detail=f"Case {case_id} not found or not in any community")
        
        # Filter by requested algorithms
        filtered_info = {alg: info for alg, info in community_info.items() if alg in algorithms}
        
        return {
            "case_id": case_id,
            "communities": filtered_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting case communities: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get case communities: {str(e)}")

@router.post("/communities/detect")
async def run_community_detection(
    request: CommunityDetectionRequest,
    background_tasks: BackgroundTasks
) -> CommunityDetectionResponse:
    """
    Run community detection algorithms on the legal case graph.
    
    This endpoint triggers community detection algorithms to identify
    clusters of related legal cases. Can be run in background for large datasets.
    
    Example:
        POST /graph/communities/detect
        {
            "force_refresh": true,
            "algorithms": ["louvain", "leiden"]
        }
    """
    try:
        start_time = datetime.now()
        
        # Validate algorithms
        valid_algorithms = ["louvain", "leiden"]
        invalid_algorithms = [alg for alg in request.algorithms if alg not in valid_algorithms]
        if invalid_algorithms:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid algorithms: {invalid_algorithms}. Must be one of: {valid_algorithms}"
            )
        
        # Run community detection
        if request.force_refresh or len(request.algorithms) > 1:
            # Run in background for heavy operations
            background_tasks.add_task(
                _run_community_detection_background,
                request.force_refresh,
                request.algorithms
            )
            
            return CommunityDetectionResponse(
                results={"message": "Community detection started in background"},
                execution_time=0,
                status="running",
                timestamp=datetime.now()
            )
        else:
            # Run synchronously for single algorithm
            results = detect_communities(request.force_refresh)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CommunityDetectionResponse(
                results=_serialize_community_results(results),
                execution_time=execution_time,
                status="completed",
                timestamp=datetime.now()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running community detection: {e}")
        raise HTTPException(status_code=500, detail=f"Community detection failed: {str(e)}")

@router.get("/communities/stats")
async def get_community_stats() -> Dict[str, Any]:
    """
    Get statistics about detected communities.
    
    This endpoint provides overview statistics about the communities
    detected by different algorithms.
    
    Example:
        GET /graph/communities/stats
    """
    try:
        detector = GDSCommunityDetector()
        
        try:
            with detector.driver.session() as session:
                # Get community statistics for each algorithm
                stats = {}
                
                for algorithm in ["louvain", "leiden"]:
                    community_property = f"{algorithm}_community"
                    
                    # Get basic stats
                    stats_query = f"""
                    MATCH (c:Case)
                    WHERE c.{community_property} IS NOT NULL
                    WITH c.{community_property} AS community_id, COUNT(c) AS size
                    RETURN 
                        COUNT(community_id) AS total_communities,
                        AVG(size) AS avg_community_size,
                        MIN(size) AS min_community_size,
                        MAX(size) AS max_community_size,
                        SUM(size) AS total_cases_in_communities
                    """
                    
                    result = session.run(stats_query)
                    record = result.single()
                    
                    if record:
                        stats[algorithm] = {
                            "total_communities": record["total_communities"],
                            "avg_community_size": round(record["avg_community_size"], 2),
                            "min_community_size": record["min_community_size"],
                            "max_community_size": record["max_community_size"],
                            "total_cases_in_communities": record["total_cases_in_communities"]
                        }
                
                # Get overall graph stats
                graph_stats_query = """
                MATCH (c:Case)
                OPTIONAL MATCH (c)-[:CITES]->()
                RETURN 
                    COUNT(c) AS total_cases,
                    COUNT(DISTINCT c.jurisdiction) AS total_jurisdictions,
                    COUNT(DISTINCT c.practice_area) AS total_practice_areas
                """
                
                result = session.run(graph_stats_query)
                record = result.single()
                
                graph_stats = {
                    "total_cases": record["total_cases"],
                    "total_jurisdictions": record["total_jurisdictions"],
                    "total_practice_areas": record["total_practice_areas"]
                }
                
                return {
                    "community_stats": stats,
                    "graph_stats": graph_stats,
                    "timestamp": datetime.now().isoformat()
                }
                
        finally:
            detector.close()
            
    except Exception as e:
        logger.error(f"Error getting community stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get community stats: {str(e)}")

@router.get("/communities/practice-area/{practice_area}")
async def get_practice_area_communities(
    practice_area: str,
    algorithm: str = Query("louvain", description="Algorithm to use"),
    limit: int = Query(10, description="Maximum number of communities to return")
) -> Dict[str, Any]:
    """
    Get communities for a specific practice area.
    
    This endpoint returns communities that are primarily focused on
    a specific practice area of law.
    
    Example:
        GET /graph/communities/practice-area/personal_injury?algorithm=louvain&limit=10
    """
    try:
        # Validate algorithm
        if algorithm not in ["louvain", "leiden"]:
            raise HTTPException(status_code=400, detail="Algorithm must be 'louvain' or 'leiden'")
        
        detector = GDSCommunityDetector()
        
        try:
            with detector.driver.session() as session:
                community_property = f"{algorithm}_community"
                
                # Get communities with high concentration of the practice area
                query = f"""
                MATCH (c:Case {{practice_area: $practice_area}})
                WHERE c.{community_property} IS NOT NULL
                WITH c.{community_property} AS community_id, COUNT(c) AS practice_area_count
                MATCH (all_c:Case {{`{community_property}`: community_id}})
                WITH community_id, practice_area_count, COUNT(all_c) AS total_count,
                     COLLECT(all_c) AS all_cases
                WHERE practice_area_count > 0
                WITH community_id, practice_area_count, total_count, all_cases,
                     (practice_area_count * 1.0 / total_count) AS concentration
                WHERE concentration >= 0.3  // At least 30% of cases in this practice area
                RETURN community_id, practice_area_count, total_count, concentration, all_cases
                ORDER BY concentration DESC, practice_area_count DESC
                LIMIT $limit
                """
                
                result = session.run(query, practice_area=practice_area, limit=limit)
                
                communities = []
                for record in result:
                    community_id = record["community_id"]
                    practice_area_count = record["practice_area_count"]
                    total_count = record["total_count"]
                    concentration = record["concentration"]
                    all_cases = record["all_cases"]
                    
                    # Get case details
                    case_details = []
                    for case in all_cases[:10]:  # Top 10 cases
                        case_details.append({
                            "id": case.get("id"),
                            "name": case.get("case_name"),
                            "jurisdiction": case.get("jurisdiction"),
                            "practice_area": case.get("practice_area"),
                            "date_filed": case.get("date_filed"),
                            "authority_score": case.get("authority_score", 0)
                        })
                    
                    communities.append({
                        "community_id": community_id,
                        "practice_area_count": practice_area_count,
                        "total_count": total_count,
                        "concentration": round(concentration, 3),
                        "cases": case_details
                    })
                
                return {
                    "practice_area": practice_area,
                    "algorithm": algorithm,
                    "communities": communities,
                    "total_communities": len(communities),
                    "timestamp": datetime.now().isoformat()
                }
                
        finally:
            detector.close()
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting practice area communities: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get practice area communities: {str(e)}")

# Background task functions
async def _run_community_detection_background(force_refresh: bool, algorithms: List[str]):
    """Run community detection in the background"""
    try:
        logger.info(f"Starting background community detection with algorithms: {algorithms}")
        results = detect_communities(force_refresh)
        logger.info(f"Background community detection completed: {len(results)} algorithms processed")
    except Exception as e:
        logger.error(f"Background community detection failed: {e}")

def _serialize_community_results(results: Dict[str, CommunityResult]) -> Dict[str, Any]:
    """Serialize community results for JSON response"""
    serialized = {}
    
    for algorithm, result in results.items():
        serialized[algorithm] = {
            "algorithm": result.algorithm,
            "execution_time": result.execution_time,
            "community_count": result.community_count,
            "modularity": result.modularity,
            "communities": result.communities[:10],  # Top 10 communities
            "metadata": result.metadata,
            "created_at": result.created_at.isoformat()
        }
    
    return serialized