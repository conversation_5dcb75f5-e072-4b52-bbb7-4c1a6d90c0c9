"""
GDS Integration for GraphRAG Enhancement

This module provides integration functions to enhance the existing GraphRAG
system with Neo4j GDS community detection capabilities.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import httpx

from src.processing.graph.gds_community_detector import (
    GDSCommunityDetector,
    search_case_communities,
    get_case_community_info
)

logger = logging.getLogger(__name__)

class GraphRAGEnhancer:
    """
    Enhances the existing GraphRAG system with GDS community detection.
    
    This class provides methods to integrate community detection results
    into the research agent's graph expansion and refinement processes.
    """
    
    def __init__(self, laws_api_base_url: str = "http://localhost:8000"):
        """
        Initialize the GraphRAG enhancer.
        
        Args:
            laws_api_base_url: Base URL for the Laws API
        """
        self.laws_api_base_url = laws_api_base_url
        self.detector = GDSCommunityDetector()
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def enhance_graph_expansion(self, 
                                    initial_cases: List[Dict[str, Any]],
                                    query: str,
                                    jurisdiction: Optional[str] = None,
                                    practice_area: Optional[str] = None,
                                    max_communities: int = 5) -> Dict[str, Any]:
        """
        Enhance graph expansion with community-based case discovery.
        
        This method takes initial cases found through vector search and
        expands them using community detection to find related cases.
        
        Args:
            initial_cases: Cases found through initial vector search
            query: Original search query
            jurisdiction: Target jurisdiction (optional)
            practice_area: Target practice area (optional)
            max_communities: Maximum number of communities to explore
            
        Returns:
            Enhanced results with community-based expansions
        """
        try:
            enhancement_results = {
                "original_cases": initial_cases,
                "community_expansions": [],
                "related_communities": [],
                "expansion_summary": {}
            }
            
            # Step 1: Find communities for initial cases
            case_communities = {}
            for case in initial_cases:
                case_id = case.get("id")
                if case_id:
                    communities = await self._get_case_communities_async(case_id)
                    if communities:
                        case_communities[case_id] = communities
            
            # Step 2: Search for relevant communities based on query
            relevant_communities = await self._search_communities_async(
                query, jurisdiction, practice_area, max_communities
            )
            
            # Step 3: Expand each relevant community
            for community in relevant_communities[:max_communities]:
                expansion = await self._expand_community(
                    community, query, jurisdiction, practice_area
                )
                if expansion:
                    enhancement_results["community_expansions"].append(expansion)
            
            # Step 4: Generate expansion summary
            enhancement_results["expansion_summary"] = self._generate_expansion_summary(
                len(initial_cases), 
                len(enhancement_results["community_expansions"]),
                case_communities
            )
            
            enhancement_results["related_communities"] = relevant_communities
            
            return enhancement_results
            
        except Exception as e:
            logger.error(f"Error enhancing graph expansion: {e}")
            return {
                "original_cases": initial_cases,
                "community_expansions": [],
                "related_communities": [],
                "expansion_summary": {"error": str(e)}
            }
    
    async def get_community_context(self, 
                                  case_id: str,
                                  context_size: int = 10) -> Dict[str, Any]:
        """
        Get community context for a specific case.
        
        This method provides contextual information about a case based on
        its community membership, including related cases and patterns.
        
        Args:
            case_id: ID of the case to get context for
            context_size: Number of related cases to include
            
        Returns:
            Community context information
        """
        try:
            # Get community information for the case
            communities = await self._get_case_communities_async(case_id)
            
            if not communities:
                return {
                    "case_id": case_id,
                    "communities": {},
                    "related_cases": [],
                    "context_summary": "No community information available"
                }
            
            # Get related cases from communities
            related_cases = []
            context_summary_parts = []
            
            for algorithm, community_info in communities.items():
                if "related_cases" in community_info:
                    related_cases.extend(community_info["related_cases"][:context_size])
                    
                    community_id = community_info.get("community_id")
                    if community_id:
                        context_summary_parts.append(
                            f"{algorithm} community {community_id}"
                        )
            
            # Remove duplicates and limit size
            seen_ids = set()
            unique_related_cases = []
            for case in related_cases:
                case_id_key = case.get("id")
                if case_id_key and case_id_key not in seen_ids:
                    seen_ids.add(case_id_key)
                    unique_related_cases.append(case)
                    if len(unique_related_cases) >= context_size:
                        break
            
            context_summary = f"Case belongs to {', '.join(context_summary_parts)} with {len(unique_related_cases)} related cases"
            
            return {
                "case_id": case_id,
                "communities": communities,
                "related_cases": unique_related_cases,
                "context_summary": context_summary
            }
            
        except Exception as e:
            logger.error(f"Error getting community context: {e}")
            return {
                "case_id": case_id,
                "communities": {},
                "related_cases": [],
                "context_summary": f"Error getting context: {str(e)}"
            }
    
    async def get_community_trends(self, 
                                 practice_area: str,
                                 jurisdiction: Optional[str] = None,
                                 time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """
        Get trends and patterns within communities for a practice area.
        
        This method analyzes communities to identify trends, patterns,
        and evolution in legal concepts over time.
        
        Args:
            practice_area: Practice area to analyze
            jurisdiction: Jurisdiction to focus on (optional)
            time_range: Time range to analyze (start_date, end_date)
            
        Returns:
            Community trends and patterns
        """
        try:
            # Get communities for the practice area
            communities_response = await self._get_practice_area_communities_async(
                practice_area, jurisdiction
            )
            
            if not communities_response or not communities_response.get("communities"):
                return {
                    "practice_area": practice_area,
                    "jurisdiction": jurisdiction,
                    "trends": [],
                    "patterns": [],
                    "summary": "No communities found for this practice area"
                }
            
            communities = communities_response["communities"]
            
            # Analyze trends within communities
            trends = []
            patterns = []
            
            for community in communities:
                community_id = community.get("community_id")
                cases = community.get("cases", [])
                
                # Time-based analysis
                if time_range:
                    time_trend = self._analyze_time_trend(cases, time_range)
                    if time_trend:
                        trends.append({
                            "community_id": community_id,
                            "type": "temporal",
                            "trend": time_trend
                        })
                
                # Jurisdiction pattern analysis
                jurisdiction_pattern = self._analyze_jurisdiction_pattern(cases)
                if jurisdiction_pattern:
                    patterns.append({
                        "community_id": community_id,
                        "type": "jurisdictional",
                        "pattern": jurisdiction_pattern
                    })
                
                # Authority score pattern analysis
                authority_pattern = self._analyze_authority_pattern(cases)
                if authority_pattern:
                    patterns.append({
                        "community_id": community_id,
                        "type": "authority",
                        "pattern": authority_pattern
                    })
            
            # Generate summary
            summary = self._generate_trends_summary(
                practice_area, jurisdiction, len(communities), trends, patterns
            )
            
            return {
                "practice_area": practice_area,
                "jurisdiction": jurisdiction,
                "time_range": time_range,
                "communities_analyzed": len(communities),
                "trends": trends,
                "patterns": patterns,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Error getting community trends: {e}")
            return {
                "practice_area": practice_area,
                "jurisdiction": jurisdiction,
                "trends": [],
                "patterns": [],
                "summary": f"Error analyzing trends: {str(e)}"
            }
    
    # Private helper methods
    async def _get_case_communities_async(self, case_id: str) -> Dict[str, Any]:
        """Async wrapper for getting case communities"""
        try:
            url = f"{self.laws_api_base_url}/graph/communities/{case_id}"
            response = await self.client.get(url)
            response.raise_for_status()
            return response.json().get("communities", {})
        except Exception as e:
            logger.error(f"Error getting case communities via API: {e}")
            # Fallback to direct database access
            return get_case_community_info(case_id)
    
    async def _search_communities_async(self, 
                                      query: str,
                                      jurisdiction: Optional[str],
                                      practice_area: Optional[str],
                                      limit: int) -> List[Dict[str, Any]]:
        """Async wrapper for searching communities"""
        try:
            url = f"{self.laws_api_base_url}/graph/communities/search"
            params = {"query": query, "limit": limit}
            
            if jurisdiction:
                params["jurisdiction"] = jurisdiction
            if practice_area:
                params["practice_area"] = practice_area
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            return response.json().get("communities", [])
        except Exception as e:
            logger.error(f"Error searching communities via API: {e}")
            # Fallback to direct database access
            return search_case_communities(query, jurisdiction, practice_area)
    
    async def _get_practice_area_communities_async(self, 
                                                 practice_area: str,
                                                 jurisdiction: Optional[str]) -> Dict[str, Any]:
        """Async wrapper for getting practice area communities"""
        try:
            url = f"{self.laws_api_base_url}/graph/communities/practice-area/{practice_area}"
            params = {}
            
            if jurisdiction:
                params["jurisdiction"] = jurisdiction
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting practice area communities via API: {e}")
            return {"communities": []}
    
    async def _expand_community(self, 
                              community: Dict[str, Any],
                              query: str,
                              jurisdiction: Optional[str],
                              practice_area: Optional[str]) -> Dict[str, Any]:
        """Expand a community with additional context"""
        try:
            community_id = community.get("id")
            cases = community.get("cases", [])
            
            # Score cases based on relevance to query
            scored_cases = []
            for case in cases:
                relevance_score = self._calculate_relevance_score(
                    case, query, jurisdiction, practice_area
                )
                case_with_score = case.copy()
                case_with_score["relevance_score"] = relevance_score
                scored_cases.append(case_with_score)
            
            # Sort by relevance
            scored_cases.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
            
            return {
                "community_id": community_id,
                "size": community.get("size", 0),
                "practice_areas": community.get("practice_areas", []),
                "jurisdictions": community.get("jurisdictions", []),
                "top_cases": scored_cases[:10],  # Top 10 most relevant cases
                "expansion_type": "community_based",
                "query_relevance": community.get("query_relevance", 0)
            }
            
        except Exception as e:
            logger.error(f"Error expanding community: {e}")
            return None
    
    def _calculate_relevance_score(self, 
                                 case: Dict[str, Any],
                                 query: str,
                                 jurisdiction: Optional[str],
                                 practice_area: Optional[str]) -> float:
        """Calculate relevance score for a case"""
        score = 0.0
        
        # Authority score component
        authority_score = case.get("authority_score", 0)
        score += authority_score * 0.3
        
        # Jurisdiction match
        if jurisdiction and case.get("jurisdiction") == jurisdiction:
            score += 0.3
        
        # Practice area match
        if practice_area and case.get("practice_area") == practice_area:
            score += 0.3
        
        # Text relevance (simple keyword matching)
        case_text = (case.get("name", "") + " " + case.get("text", "")).lower()
        query_words = query.lower().split()
        
        matching_words = sum(1 for word in query_words if word in case_text)
        text_relevance = matching_words / len(query_words) if query_words else 0
        score += text_relevance * 0.1
        
        return score
    
    def _analyze_time_trend(self, cases: List[Dict[str, Any]], 
                          time_range: Tuple[str, str]) -> Dict[str, Any]:
        """Analyze temporal trends in cases"""
        try:
            start_date, end_date = time_range
            
            # Filter cases within time range
            filtered_cases = []
            for case in cases:
                date_filed = case.get("date_filed")
                if date_filed and start_date <= date_filed <= end_date:
                    filtered_cases.append(case)
            
            if not filtered_cases:
                return None
            
            # Analyze trends
            years = {}
            for case in filtered_cases:
                year = case.get("date_filed", "")[:4]
                if year:
                    years[year] = years.get(year, 0) + 1
            
            return {
                "time_range": time_range,
                "total_cases": len(filtered_cases),
                "yearly_distribution": years,
                "trend_direction": "increasing" if len(years) > 1 and list(years.values())[-1] > list(years.values())[0] else "stable"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing time trend: {e}")
            return None
    
    def _analyze_jurisdiction_pattern(self, cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze jurisdictional patterns in cases"""
        try:
            jurisdictions = {}
            for case in cases:
                jurisdiction = case.get("jurisdiction")
                if jurisdiction:
                    jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1
            
            if not jurisdictions:
                return None
            
            total_cases = len(cases)
            dominant_jurisdiction = max(jurisdictions.items(), key=lambda x: x[1])
            
            return {
                "jurisdiction_distribution": jurisdictions,
                "dominant_jurisdiction": dominant_jurisdiction[0],
                "dominance_percentage": (dominant_jurisdiction[1] / total_cases) * 100,
                "cross_jurisdictional": len(jurisdictions) > 1
            }
            
        except Exception as e:
            logger.error(f"Error analyzing jurisdiction pattern: {e}")
            return None
    
    def _analyze_authority_pattern(self, cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze authority score patterns in cases"""
        try:
            authority_scores = [case.get("authority_score", 0) for case in cases if case.get("authority_score") is not None]
            
            if not authority_scores:
                return None
            
            avg_authority = sum(authority_scores) / len(authority_scores)
            max_authority = max(authority_scores)
            min_authority = min(authority_scores)
            
            return {
                "average_authority": round(avg_authority, 3),
                "max_authority": max_authority,
                "min_authority": min_authority,
                "authority_range": max_authority - min_authority,
                "high_authority_cases": len([s for s in authority_scores if s > avg_authority])
            }
            
        except Exception as e:
            logger.error(f"Error analyzing authority pattern: {e}")
            return None
    
    def _generate_expansion_summary(self, 
                                  original_count: int,
                                  expansion_count: int,
                                  case_communities: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of expansion results"""
        total_related_cases = sum(
            len(communities.get("louvain", {}).get("related_cases", [])) +
            len(communities.get("leiden", {}).get("related_cases", []))
            for communities in case_communities.values()
        )
        
        return {
            "original_cases": original_count,
            "community_expansions": expansion_count,
            "total_related_cases": total_related_cases,
            "expansion_ratio": total_related_cases / original_count if original_count > 0 else 0,
            "communities_involved": len(case_communities)
        }
    
    def _generate_trends_summary(self, 
                               practice_area: str,
                               jurisdiction: Optional[str],
                               community_count: int,
                               trends: List[Dict[str, Any]],
                               patterns: List[Dict[str, Any]]) -> str:
        """Generate human-readable summary of trends"""
        summary_parts = []
        
        summary_parts.append(f"Analyzed {community_count} communities for {practice_area}")
        
        if jurisdiction:
            summary_parts.append(f"in {jurisdiction}")
        
        if trends:
            summary_parts.append(f"Found {len(trends)} temporal trends")
        
        if patterns:
            pattern_types = set(p.get("type") for p in patterns)
            summary_parts.append(f"Identified {len(pattern_types)} types of patterns")
        
        return " ".join(summary_parts) + "."
    
    async def close(self):
        """Close the HTTP client and detector"""
        await self.client.aclose()
        self.detector.close()

# Convenience functions for easy integration
async def enhance_research_with_communities(
    initial_cases: List[Dict[str, Any]],
    query: str,
    jurisdiction: Optional[str] = None,
    practice_area: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to enhance research with community detection.
    
    Args:
        initial_cases: Cases found through initial search
        query: Original search query
        jurisdiction: Target jurisdiction
        practice_area: Target practice area
        
    Returns:
        Enhanced research results
    """
    enhancer = GraphRAGEnhancer()
    try:
        return await enhancer.enhance_graph_expansion(
            initial_cases, query, jurisdiction, practice_area
        )
    finally:
        await enhancer.close()

async def get_case_community_context(case_id: str) -> Dict[str, Any]:
    """
    Convenience function to get community context for a case.
    
    Args:
        case_id: ID of the case
        
    Returns:
        Community context information
    """
    enhancer = GraphRAGEnhancer()
    try:
        return await enhancer.get_community_context(case_id)
    finally:
        await enhancer.close()