"""
Main FastAPI application for Week 4 implementation.
Combines search, recommendation, and graph APIs with authentication and rate limiting.
"""

import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from src.api.search.search_router import router as search_router
from src.api.recommend.recommend_router import router as recommend_router
from src.api.graph.graph_router import router as graph_router
from src.api.graph.gds_router import router as gds_router
from src.api.jobs.jobs_router import router as jobs_router
from src.api.auth.jwt_middleware import get_current_user
from src.api.auth.rate_limiter import check_rate_limit, get_rate_limit_headers
from src.api.auth.rbac import log_access_attempt
from src.api.auth.security_middleware import security_middleware
from src.cache.cache import cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Enhanced rate limiting is now handled by the rate_limiter module


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Legal Database API v0.1.0")
    logger.info(f"Cache backend: {cache.backend.__class__.__name__}")
    
    # Test database connections (non-blocking)
    try:
        # Test cache (quick test)
        cache.set("startup_test", "ok", 60)
        if cache.get("startup_test") == "ok":
            logger.info("Cache connection: OK")
        else:
            logger.warning("Cache connection: Failed")

        logger.info("API startup completed successfully")
    except Exception as e:
        logger.warning(f"Startup warning (non-critical): {e}")
        # Don't fail startup on database connection issues
    
    yield
    
    # Shutdown
    logger.info("Shutting down Legal Database API")


# Create FastAPI app
app = FastAPI(
    title="Legal Database API",
    description="React-Flow ready backend for legal document search, recommendations, and network visualization",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

# Add security middleware (must be before other middlewares)
@app.middleware("http")
async def security_middleware_handler(request: Request, call_next):
    """Security middleware for additional protection."""
    return await security_middleware(request, call_next)


@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """Enhanced rate limiting middleware with Redis support."""
    # Skip rate limiting for health checks and docs
    if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
        response = await call_next(request)
        return response

    # Skip rate limiting for non-API endpoints
    if not request.url.path.startswith(("/v0/", "/v1/")):
        response = await call_next(request)
        return response

    try:
        # Extract user info from request state (set by JWT middleware)
        user_id = getattr(request.state, 'user_id', None)
        tenant_id = getattr(request.state, 'tenant_id', None)
        role = getattr(request.state, 'role', 'anonymous')

        # If no user info, use anonymous (will be caught by auth middleware later)
        if not user_id:
            user_id = "anonymous"

        # Check rate limits
        rate_limit_result = await check_rate_limit(user_id, tenant_id)

        # Get client IP for logging
        client_ip = request.client.host if request.client else 'unknown'

        if not rate_limit_result["allowed"]:
            # Log rate limit exceeded
            log_access_attempt(
                user_id=user_id,
                role=role,
                tenant_id=tenant_id,
                endpoint=request.url.path,
                status_code=429,
                client_ip=client_ip
            )

            # Return rate limit exceeded response
            headers = get_rate_limit_headers(rate_limit_result)
            return JSONResponse(
                status_code=429,
                content={
                    "error": "rate_limit_exceeded",
                    "message": "Rate limit exceeded. Try again later.",
                    "remaining_requests": rate_limit_result["remaining"],
                    "reset_time": rate_limit_result["reset_time"]
                },
                headers=headers
            )

        # Process request
        response = await call_next(request)

        # Add rate limit headers to successful responses
        rate_limit_headers = get_rate_limit_headers(rate_limit_result)
        for header, value in rate_limit_headers.items():
            response.headers[header] = value

        # Log successful access
        log_access_attempt(
            user_id=user_id,
            role=role,
            tenant_id=tenant_id,
            endpoint=request.url.path,
            status_code=response.status_code,
            client_ip=client_ip
        )

        return response

    except Exception as e:
        logger.error(f"Rate limiting error: {e}")
        # Continue without rate limiting on error
        response = await call_next(request)
        return response


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Request logging middleware."""
    start_time = time.time()
    
    # Process request
    response = await call_next(request)
    
    # Log request
    process_time = time.time() - start_time
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    # Add timing header
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# Include routers
app.include_router(search_router)
app.include_router(recommend_router)
app.include_router(graph_router)
app.include_router(gds_router)
app.include_router(jobs_router)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Legal Database API",
        "version": "0.1.0",
        "description": "React-Flow ready backend for legal document search and network visualization",
        "endpoints": {
            "search": "/v0/search",
            "recommendations": "/v0/recommend/{document_id}",
            "graph": "/v0/graph",
            "jobs": "/jobs/authority/calculate",
            "health": "/health",
            "docs": "/docs"
        },
        "features": [
            "Hybrid search (semantic + keyword)",
            "Citation-based recommendations",
            "React-Flow compatible graph data",
            "Authority score calculation (PageRank + recency)",
            "JWT authentication",
            "Rate limiting",
            "Response caching"
        ]
    }


@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint."""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "version": "0.1.0",
            "services": {
                "api": "healthy",
                "cache": "unknown",
                "database": "unknown"
            },
            "metrics": {
                "uptime_seconds": time.time(),  # Would be actual uptime
                "cache_backend": cache.backend.__class__.__name__,
                "rate_limit_backend": "enhanced"
            }
        }
        
        # Test cache
        try:
            cache.set("health_check", "ok", 60)
            if cache.get("health_check") == "ok":
                health_status["services"]["cache"] = "healthy"
            else:
                health_status["services"]["cache"] = "unhealthy"
        except Exception as e:
            health_status["services"]["cache"] = "unhealthy"
            logger.error(f"Cache health check failed: {e}")
        
        # Overall status
        unhealthy_services = [
            service for service, status in health_status["services"].items()
            if status == "unhealthy"
        ]
        
        if unhealthy_services:
            health_status["status"] = "degraded"
            health_status["unhealthy_services"] = unhealthy_services
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }


@app.get("/metrics")
async def get_metrics():
    """Basic metrics endpoint for monitoring."""
    return {
        "api_version": "0.1.0",
        "cache_backend": cache.backend.__class__.__name__,
        "rate_limit_backend": "enhanced",
        "timestamp": time.time()
    }


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "http_error",
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": request.url.path,
            "timestamp": time.time()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_server_error",
            "message": "An internal error occurred",
            "path": request.url.path,
            "timestamp": time.time()
        }
    )


if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
