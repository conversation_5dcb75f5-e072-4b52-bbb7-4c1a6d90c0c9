"""
NetworkX-based Community Detection for Legal Cases

This module provides community detection using NetworkX as a hybrid solution
for Neo4j Aura instances without full GDS support.
"""

import logging
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from collections import defaultdict
import json

import networkx as nx
import numpy as np
from neo4j import GraphDatabase
from neo4j.exceptions import ClientError

# Community detection algorithms
try:
    import community as community_louvain  # python-louvain
    HAS_LOUVAIN = True
except ImportError:
    HAS_LOUVAIN = False

try:
    import leidenalg
    import igraph as ig
    HAS_LEIDEN = True
except ImportError:
    HAS_LEIDEN = False

logger = logging.getLogger(__name__)

@dataclass
class NetworkXCommunityResult:
    """Result of NetworkX community detection algorithm"""
    algorithm: str
    execution_time: float
    community_count: int
    modularity: Optional[float] = None
    communities: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

class NetworkXCommunityDetector:
    """
    NetworkX-based community detection for legal cases.
    
    This class provides a hybrid solution using NetworkX for community detection
    algorithms while maintaining Neo4j for storage and queries.
    """
    
    def __init__(self, uri: str = None, username: str = None, password: str = None):
        """Initialize the NetworkX community detector."""
        self.uri = uri or os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = username or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD", "password")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            logger.info(f"Connected to Neo4j at {self.uri}")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
        
        # Check algorithm availability
        self._check_algorithm_availability()
    
    def _check_algorithm_availability(self):
        """Check which community detection algorithms are available."""
        logger.info("Checking available community detection algorithms...")
        
        if HAS_LOUVAIN:
            logger.info("✅ Louvain algorithm available")
        else:
            logger.warning("❌ Louvain algorithm not available (install: pip install python-louvain)")
        
        if HAS_LEIDEN:
            logger.info("✅ Leiden algorithm available")
        else:
            logger.warning("❌ Leiden algorithm not available (install: pip install leidenalg)")
    
    def load_graph_from_neo4j(self, 
                             node_query: str = None,
                             relationship_query: str = None,
                             limit: int = None) -> nx.Graph:
        """
        Load graph from Neo4j into NetworkX.
        
        Args:
            node_query: Custom query for nodes
            relationship_query: Custom query for relationships
            limit: Limit number of nodes/relationships for testing
            
        Returns:
            NetworkX graph object
        """
        G = nx.Graph()
        
        try:
            with self.driver.session() as session:
                # Default node query
                if node_query is None:
                    node_query = """
                    MATCH (c:Case)
                    RETURN c.id AS id, c.name AS name, c.jurisdiction AS jurisdiction,
                           c.year AS year, c.date_filed AS date_filed, c.court_id AS court_id
                    """
                    if limit:
                        node_query += f" LIMIT {limit}"
                
                # Load nodes
                logger.info("Loading nodes from Neo4j...")
                result = session.run(node_query)
                node_count = 0
                
                for record in result:
                    node_id = record["id"]
                    
                    # Skip nodes with null IDs
                    if node_id is None:
                        continue
                    
                    attributes = {
                        "name": record.get("name"),
                        "jurisdiction": record.get("jurisdiction"),
                        "year": record.get("year"),
                        "date_filed": record.get("date_filed"),
                        "court_id": record.get("court_id")
                    }
                    G.add_node(node_id, **attributes)
                    node_count += 1
                
                logger.info(f"Loaded {node_count} nodes")
                
                # Default relationship query
                if relationship_query is None:
                    relationship_query = """
                    MATCH (c1:Case)-[:CITES]->(c2:Case)
                    RETURN c1.id AS source, c2.id AS target, 1 AS weight
                    """
                    if limit:
                        relationship_query += f" LIMIT {limit * 10}"  # More relationships than nodes
                
                # Load relationships
                logger.info("Loading relationships from Neo4j...")
                result = session.run(relationship_query)
                edge_count = 0
                
                for record in result:
                    source = record["source"]
                    target = record["target"]
                    weight = record.get("weight", 1)
                    
                    # Skip edges with null nodes
                    if source is None or target is None:
                        continue
                    
                    # Only add edge if both nodes exist
                    if source in G and target in G:
                        G.add_edge(source, target, weight=weight)
                        edge_count += 1
                
                logger.info(f"Loaded {edge_count} edges")
                
        except Exception as e:
            logger.error(f"Error loading graph from Neo4j: {e}")
            raise
        
        return G
    
    def run_louvain_community_detection(self, 
                                      graph: nx.Graph = None,
                                      resolution: float = 1.0,
                                      random_state: int = None) -> NetworkXCommunityResult:
        """
        Run Louvain community detection algorithm.
        
        Args:
            graph: NetworkX graph (loaded from Neo4j if None)
            resolution: Resolution parameter for community detection
            random_state: Random state for reproducibility
            
        Returns:
            NetworkXCommunityResult with detection results
        """
        if not HAS_LOUVAIN:
            raise ImportError("Louvain algorithm not available. Install: pip install python-louvain")
        
        start_time = time.time()
        
        try:
            # Load graph if not provided
            if graph is None:
                graph = self.load_graph_from_neo4j()
            
            logger.info(f"Running Louvain on graph with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges")
            
            # Run Louvain algorithm
            partition = community_louvain.best_partition(
                graph,
                resolution=resolution,
                random_state=random_state
            )
            
            # Calculate modularity
            modularity = community_louvain.modularity(partition, graph)
            
            # Process results
            communities = self._process_louvain_results(partition, graph)
            
            execution_time = time.time() - start_time
            
            return NetworkXCommunityResult(
                algorithm="louvain",
                execution_time=execution_time,
                community_count=len(communities),
                modularity=modularity,
                communities=communities,
                metadata={
                    "resolution": resolution,
                    "random_state": random_state,
                    "nodes": graph.number_of_nodes(),
                    "edges": graph.number_of_edges()
                }
            )
            
        except Exception as e:
            logger.error(f"Error running Louvain: {e}")
            return NetworkXCommunityResult(
                algorithm="louvain",
                execution_time=time.time() - start_time,
                community_count=0,
                communities=[]
            )
    
    def run_leiden_community_detection(self,
                                     graph: nx.Graph = None,
                                     resolution: float = 1.0,
                                     random_state: int = None) -> NetworkXCommunityResult:
        """
        Run Leiden community detection algorithm.
        
        Args:
            graph: NetworkX graph (loaded from Neo4j if None)
            resolution: Resolution parameter for community detection
            random_state: Random state for reproducibility
            
        Returns:
            NetworkXCommunityResult with detection results
        """
        if not HAS_LEIDEN:
            raise ImportError("Leiden algorithm not available. Install: pip install leidenalg")
        
        start_time = time.time()
        
        try:
            # Load graph if not provided
            if graph is None:
                graph = self.load_graph_from_neo4j()
            
            logger.info(f"Running Leiden on graph with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges")
            
            # Convert NetworkX graph to igraph
            ig_graph = self._networkx_to_igraph(graph)
            
            # Run Leiden algorithm
            partition = leidenalg.find_partition(
                ig_graph,
                leidenalg.ModularityVertexPartition,
                resolution_parameter=resolution,
                seed=random_state
            )
            
            # Calculate modularity
            modularity = partition.modularity
            
            # Process results
            communities = self._process_leiden_results(partition, graph)
            
            execution_time = time.time() - start_time
            
            return NetworkXCommunityResult(
                algorithm="leiden",
                execution_time=execution_time,
                community_count=len(communities),
                modularity=modularity,
                communities=communities,
                metadata={
                    "resolution": resolution,
                    "random_state": random_state,
                    "nodes": graph.number_of_nodes(),
                    "edges": graph.number_of_edges()
                }
            )
            
        except Exception as e:
            logger.error(f"Error running Leiden: {e}")
            return NetworkXCommunityResult(
                algorithm="leiden",
                execution_time=time.time() - start_time,
                community_count=0,
                communities=[]
            )
    
    def _networkx_to_igraph(self, G: nx.Graph):
        """Convert NetworkX graph to igraph."""
        if not HAS_LEIDEN:
            raise ImportError("igraph and leidenalg are required for Leiden algorithm")

        # Create mapping from node IDs to integers
        node_mapping = {node: i for i, node in enumerate(G.nodes())}

        # Create edge list with integer indices
        edges = [(node_mapping[u], node_mapping[v]) for u, v in G.edges()]

        # Create igraph
        ig_graph = ig.Graph(edges)
        
        # Add node attributes
        for i, node in enumerate(G.nodes()):
            ig_graph.vs[i]["original_id"] = node
            for attr, value in G.nodes[node].items():
                ig_graph.vs[i][attr] = value
        
        # Add edge weights if present
        if G.edges() and 'weight' in G.edges[list(G.edges())[0]]:
            weights = [G.edges[u, v].get('weight', 1) for u, v in G.edges()]
            ig_graph.es['weight'] = weights
        
        return ig_graph
    
    def _process_louvain_results(self, partition: Dict, graph: nx.Graph) -> List[Dict[str, Any]]:
        """Process Louvain partition results into community information."""
        # Group nodes by community
        communities_dict = defaultdict(list)
        for node, community_id in partition.items():
            communities_dict[community_id].append(node)
        
        communities = []
        for community_id, nodes in communities_dict.items():
            if len(nodes) >= 3:  # Only include communities with 3+ nodes
                community_info = self._create_community_info(community_id, nodes, graph)
                communities.append(community_info)
        
        # Sort by size (descending)
        communities.sort(key=lambda x: x['size'], reverse=True)
        
        return communities
    
    def _process_leiden_results(self, partition, graph: nx.Graph) -> List[Dict[str, Any]]:
        """Process Leiden partition results into community information."""
        communities = []
        
        for i, community in enumerate(partition):
            # Get original node IDs
            nodes = [partition.graph.vs[node_idx]["original_id"] for node_idx in community]
            
            if len(nodes) >= 3:  # Only include communities with 3+ nodes
                community_info = self._create_community_info(i, nodes, graph)
                communities.append(community_info)
        
        # Sort by size (descending)
        communities.sort(key=lambda x: x['size'], reverse=True)
        
        return communities
    
    def _create_community_info(self, community_id: int, nodes: List[str], graph: nx.Graph) -> Dict[str, Any]:
        """Create community information dictionary."""
        # Extract node attributes
        jurisdictions = set()
        years = set()
        courts = set()
        case_details = []
        
        for node in nodes:
            node_data = graph.nodes[node]
            case_info = {
                "id": node,
                "name": node_data.get("name"),
                "jurisdiction": node_data.get("jurisdiction"),
                "year": node_data.get("year"),
                "date_filed": node_data.get("date_filed"),
                "court_id": node_data.get("court_id")
            }
            case_details.append(case_info)
            
            if case_info["jurisdiction"]:
                jurisdictions.add(case_info["jurisdiction"])
            if case_info["year"]:
                years.add(case_info["year"])
            if case_info["court_id"]:
                courts.add(case_info["court_id"])
        
        # Calculate internal vs external edges
        internal_edges = 0
        external_edges = 0
        
        for node in nodes:
            for neighbor in graph.neighbors(node):
                if neighbor in nodes:
                    internal_edges += 1
                else:
                    external_edges += 1
        
        internal_edges = internal_edges // 2  # Each edge counted twice
        
        # Create time span
        years_list = [y for y in years if isinstance(y, (int, float))]
        time_span = f"{min(years_list)}-{max(years_list)}" if len(years_list) > 1 else str(years_list[0]) if years_list else "N/A"
        
        return {
            "id": community_id,
            "size": len(nodes),
            "cases": case_details[:10],  # Top 10 cases
            "jurisdictions": list(jurisdictions),
            "years": sorted(years_list),
            "courts": list(courts),
            "time_span": time_span,
            "internal_edges": internal_edges,
            "external_edges": external_edges,
            "density": internal_edges / (len(nodes) * (len(nodes) - 1) / 2) if len(nodes) > 1 else 0,
            "summary": self._generate_community_summary(case_details, jurisdictions, years_list, courts)
        }
    
    def _generate_community_summary(self, cases: List[Dict], jurisdictions: set, years: List, courts: set) -> str:
        """Generate a human-readable summary of a community."""
        summary_parts = []
        
        summary_parts.append(f"Community of {len(cases)} cases")
        
        if jurisdictions:
            if len(jurisdictions) == 1:
                summary_parts.append(f"in {list(jurisdictions)[0]}")
            else:
                summary_parts.append(f"across {len(jurisdictions)} jurisdictions")
        
        if years:
            if len(years) == 1:
                summary_parts.append(f"from {years[0]}")
            elif len(years) > 1:
                summary_parts.append(f"spanning {min(years)} to {max(years)}")
        
        if courts:
            summary_parts.append(f"in {len(courts)} courts")
        
        return " ".join(summary_parts)
    
    def save_communities_to_neo4j(self, results: Dict[str, NetworkXCommunityResult]) -> bool:
        """
        Save community detection results back to Neo4j.
        
        Args:
            results: Dictionary of algorithm results
            
        Returns:
            Success flag
        """
        try:
            with self.driver.session() as session:
                for algorithm, result in results.items():
                    logger.info(f"Saving {algorithm} results to Neo4j...")
                    
                    # Create community property name
                    community_property = f"{algorithm}_community"
                    
                    # Clear existing community assignments
                    session.run(f"MATCH (c:Case) REMOVE c.{community_property}")
                    
                    # Assign community IDs to cases
                    for community in result.communities:
                        community_id = community["id"]
                        case_ids = [case["id"] for case in community["cases"]]
                        
                        session.run(
                            f"MATCH (c:Case) WHERE c.id IN $case_ids SET c.{community_property} = $community_id",
                            case_ids=case_ids,
                            community_id=community_id
                        )
                    
                    # Store algorithm metadata
                    session.run("""
                        MERGE (meta:CommunityDetectionResult {algorithm: $algorithm})
                        SET meta.execution_time = $execution_time,
                            meta.community_count = $community_count,
                            meta.modularity = $modularity,
                            meta.created_at = $created_at,
                            meta.updated_at = datetime(),
                            meta.metadata = $metadata
                    """,
                    algorithm=algorithm,
                    execution_time=result.execution_time,
                    community_count=result.community_count,
                    modularity=result.modularity,
                    created_at=result.created_at.isoformat(),
                    metadata=json.dumps(result.metadata))
                    
                    logger.info(f"Saved {result.community_count} communities for {algorithm}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error saving communities to Neo4j: {e}")
            return False
    
    def run_all_community_detection(self, 
                                  graph: nx.Graph = None,
                                  save_to_neo4j: bool = True) -> Dict[str, NetworkXCommunityResult]:
        """
        Run all available community detection algorithms.
        
        Args:
            graph: NetworkX graph (loaded from Neo4j if None)
            save_to_neo4j: Whether to save results back to Neo4j
            
        Returns:
            Dictionary of algorithm results
        """
        logger.info("Running all NetworkX community detection algorithms...")
        
        # Load graph once if not provided
        if graph is None:
            graph = self.load_graph_from_neo4j()
        
        results = {}
        
        # Run Louvain if available
        if HAS_LOUVAIN:
            logger.info("Running Louvain community detection...")
            results["louvain"] = self.run_louvain_community_detection(graph)
        
        # Run Leiden if available
        if HAS_LEIDEN:
            logger.info("Running Leiden community detection...")
            results["leiden"] = self.run_leiden_community_detection(graph)
        
        # Save results to Neo4j if requested
        if save_to_neo4j and results:
            self.save_communities_to_neo4j(results)
        
        logger.info("NetworkX community detection completed!")
        return results
    
    def close(self):
        """Close the Neo4j driver connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j driver closed")

# Convenience functions
def detect_communities_networkx(save_to_neo4j: bool = True) -> Dict[str, NetworkXCommunityResult]:
    """
    Convenience function to run NetworkX community detection.
    
    Args:
        save_to_neo4j: Whether to save results back to Neo4j
        
    Returns:
        Dictionary of algorithm results
    """
    detector = NetworkXCommunityDetector()
    try:
        return detector.run_all_community_detection(save_to_neo4j=save_to_neo4j)
    finally:
        detector.close()