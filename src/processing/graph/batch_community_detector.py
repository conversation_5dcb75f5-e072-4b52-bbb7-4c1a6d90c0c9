"""
Batch Community Detection for Large-Scale Legal Case Processing

This module provides batch processing capabilities for running community detection
on large datasets (millions of cases) with memory optimization and progress tracking.
"""

import logging
import os
import time
import json
from typing import Dict, List, Optional, Any, Iterator
from datetime import datetime
from dataclasses import dataclass, field
import math

import networkx as nx
from neo4j import GraphDatabase
from neo4j.exceptions import ClientError

from .networkx_community_detector import NetworkXCommunityDetector, NetworkXCommunityResult

logger = logging.getLogger(__name__)

@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing."""
    batch_size: int = 50000  # Cases per batch
    max_memory_mb: int = 4096  # Maximum memory usage in MB
    overlap_size: int = 5000  # Overlap between batches for continuity
    checkpoint_interval: int = 10  # Save checkpoint every N batches
    algorithms: List[str] = field(default_factory=lambda: ["louvain", "leiden"])
    save_to_neo4j: bool = True
    progress_callback: Optional[callable] = None

@dataclass
class BatchResult:
    """Result of batch processing."""
    batch_id: int
    total_batches: int
    cases_processed: int
    communities_found: int
    execution_time: float
    memory_usage_mb: float
    algorithm_results: Dict[str, NetworkXCommunityResult] = field(default_factory=dict)

class BatchCommunityDetector:
    """
    Batch processing system for large-scale community detection.
    
    This class handles memory-efficient processing of large datasets by:
    1. Splitting data into manageable batches
    2. Processing each batch independently
    3. Merging results with overlap handling
    4. Providing progress tracking and checkpointing
    """
    
    def __init__(self, config: BatchProcessingConfig = None):
        """Initialize the batch community detector."""
        self.config = config or BatchProcessingConfig()
        self.detector = NetworkXCommunityDetector()
        self.current_batch = 0
        self.total_cases_processed = 0
        self.all_communities = {}
        self.checkpoint_data = {}
        
        logger.info(f"Batch processing configured:")
        logger.info(f"  Batch size: {self.config.batch_size:,} cases")
        logger.info(f"  Memory limit: {self.config.max_memory_mb:,} MB")
        logger.info(f"  Algorithms: {self.config.algorithms}")
    
    def estimate_batch_count(self, total_cases: int) -> int:
        """Estimate number of batches needed."""
        return math.ceil(total_cases / self.config.batch_size)
    
    def get_batch_queries(self, batch_id: int, batch_size: int) -> Dict[str, str]:
        """
        Generate queries for a specific batch.
        
        Args:
            batch_id: Batch identifier (0-based)
            batch_size: Number of cases in this batch
            
        Returns:
            Dictionary with node and relationship queries
        """
        offset = batch_id * batch_size
        
        # Include overlap from previous batch for continuity
        if batch_id > 0:
            overlap_offset = max(0, offset - self.config.overlap_size)
            actual_offset = overlap_offset
            actual_limit = batch_size + self.config.overlap_size
        else:
            actual_offset = offset
            actual_limit = batch_size
        
        node_query = f"""
        MATCH (c:Case)
        WHERE c.id IS NOT NULL
        RETURN c.id AS id, c.name AS name, c.jurisdiction AS jurisdiction,
               c.year AS year, c.date_filed AS date_filed, c.court_id AS court_id
        ORDER BY c.id
        SKIP {actual_offset}
        LIMIT {actual_limit}
        """
        
        # Get relationships for the cases in this batch
        relationship_query = f"""
        MATCH (c1:Case)-[:CITES]->(c2:Case)
        WHERE c1.id IS NOT NULL AND c2.id IS NOT NULL
        WITH c1, c2
        ORDER BY c1.id
        SKIP {actual_offset}
        LIMIT {actual_limit * 10}
        RETURN c1.id AS source, c2.id AS target, 1 AS weight
        """
        
        return {
            "node_query": node_query,
            "relationship_query": relationship_query
        }
    
    def process_batch(self, batch_id: int, total_batches: int) -> BatchResult:
        """
        Process a single batch of cases.
        
        Args:
            batch_id: Batch identifier
            total_batches: Total number of batches
            
        Returns:
            BatchResult with processing information
        """
        start_time = time.time()
        logger.info(f"Processing batch {batch_id + 1}/{total_batches}...")
        
        try:
            # Get batch queries
            queries = self.get_batch_queries(batch_id, self.config.batch_size)
            
            # Load graph for this batch
            graph = self.detector.load_graph_from_neo4j(
                node_query=queries["node_query"],
                relationship_query=queries["relationship_query"]
            )
            
            logger.info(f"Batch {batch_id + 1}: {graph.number_of_nodes()} nodes, {graph.number_of_edges()} edges")
            
            # Skip if graph is too small
            if graph.number_of_nodes() < 10:
                logger.warning(f"Batch {batch_id + 1}: Skipping due to small graph size")
                return BatchResult(
                    batch_id=batch_id,
                    total_batches=total_batches,
                    cases_processed=0,
                    communities_found=0,
                    execution_time=time.time() - start_time,
                    memory_usage_mb=0
                )
            
            # Run community detection algorithms
            algorithm_results = {}
            
            for algorithm in self.config.algorithms:
                if algorithm == "louvain":
                    try:
                        result = self.detector.run_louvain_community_detection(graph)
                        algorithm_results[algorithm] = result
                        logger.info(f"Batch {batch_id + 1}: {algorithm} found {result.community_count} communities")
                    except Exception as e:
                        logger.error(f"Batch {batch_id + 1}: {algorithm} failed: {e}")
                
                elif algorithm == "leiden":
                    try:
                        result = self.detector.run_leiden_community_detection(graph)
                        algorithm_results[algorithm] = result
                        logger.info(f"Batch {batch_id + 1}: {algorithm} found {result.community_count} communities")
                    except Exception as e:
                        logger.error(f"Batch {batch_id + 1}: {algorithm} failed: {e}")
            
            # Calculate metrics
            total_communities = sum(result.community_count for result in algorithm_results.values())
            memory_usage = self._estimate_memory_usage(graph)
            
            # Save to Neo4j if configured
            if self.config.save_to_neo4j:
                self._save_batch_results(batch_id, algorithm_results)
            
            # Update progress
            self.total_cases_processed += graph.number_of_nodes()
            
            # Call progress callback if provided
            if self.config.progress_callback:
                self.config.progress_callback(batch_id + 1, total_batches, self.total_cases_processed)
            
            execution_time = time.time() - start_time
            
            return BatchResult(
                batch_id=batch_id,
                total_batches=total_batches,
                cases_processed=graph.number_of_nodes(),
                communities_found=total_communities,
                execution_time=execution_time,
                memory_usage_mb=memory_usage,
                algorithm_results=algorithm_results
            )
            
        except Exception as e:
            logger.error(f"Batch {batch_id + 1} failed: {e}")
            return BatchResult(
                batch_id=batch_id,
                total_batches=total_batches,
                cases_processed=0,
                communities_found=0,
                execution_time=time.time() - start_time,
                memory_usage_mb=0
            )
    
    def process_all_batches(self, total_cases: int = None) -> List[BatchResult]:
        """
        Process all batches for the complete dataset.
        
        Args:
            total_cases: Total number of cases (estimated if None)
            
        Returns:
            List of BatchResult objects
        """
        logger.info("Starting batch processing of all cases...")
        
        # Estimate total cases if not provided
        if total_cases is None:
            total_cases = self._estimate_total_cases()
        
        total_batches = self.estimate_batch_count(total_cases)
        logger.info(f"Processing {total_cases:,} cases in {total_batches} batches")
        
        batch_results = []
        overall_start_time = time.time()
        
        for batch_id in range(total_batches):
            # Process batch
            result = self.process_batch(batch_id, total_batches)
            batch_results.append(result)
            
            # Save checkpoint
            if (batch_id + 1) % self.config.checkpoint_interval == 0:
                self._save_checkpoint(batch_id, batch_results)
            
            # Memory check
            if result.memory_usage_mb > self.config.max_memory_mb:
                logger.warning(f"Memory usage ({result.memory_usage_mb:.1f}MB) exceeds limit ({self.config.max_memory_mb}MB)")
                logger.warning("Consider reducing batch size or increasing memory limit")
        
        # Final summary
        total_time = time.time() - overall_start_time
        total_communities = sum(result.communities_found for result in batch_results)
        total_processed = sum(result.cases_processed for result in batch_results)
        
        logger.info(f"\\n{'='*60}")
        logger.info(f"BATCH PROCESSING COMPLETED")
        logger.info(f"{'='*60}")
        logger.info(f"Total cases processed: {total_processed:,}")
        logger.info(f"Total communities found: {total_communities:,}")
        logger.info(f"Total execution time: {total_time:.1f} seconds")
        logger.info(f"Average time per batch: {total_time/total_batches:.1f} seconds")
        logger.info(f"Processing rate: {total_processed/total_time:.1f} cases/second")
        
        return batch_results
    
    def _estimate_total_cases(self) -> int:
        """Estimate total number of cases in the database."""
        try:
            with self.detector.driver.session() as session:
                result = session.run("MATCH (c:Case) RETURN count(c) as total")
                return result.single()["total"]
        except Exception as e:
            logger.error(f"Error estimating total cases: {e}")
            return 100000  # Default estimate
    
    def _estimate_memory_usage(self, graph: nx.Graph) -> float:
        """Estimate memory usage in MB for a graph."""
        # Rough estimate: 
        # - Each node: ~500 bytes (attributes + overhead)
        # - Each edge: ~200 bytes (source, target, weight)
        node_memory = graph.number_of_nodes() * 500
        edge_memory = graph.number_of_edges() * 200
        return (node_memory + edge_memory) / (1024 * 1024)
    
    def _save_batch_results(self, batch_id: int, results: Dict[str, NetworkXCommunityResult]):
        """Save batch results to Neo4j with batch information."""
        try:
            with self.detector.driver.session() as session:
                for algorithm, result in results.items():
                    # Add batch information to metadata
                    result.metadata["batch_id"] = batch_id
                    result.metadata["batch_timestamp"] = datetime.now().isoformat()
                    
                    # Use existing save method
                    self.detector.save_communities_to_neo4j({algorithm: result})
                    
                    logger.info(f"Saved batch {batch_id + 1} {algorithm} results to Neo4j")
        except Exception as e:
            logger.error(f"Error saving batch {batch_id + 1} results: {e}")
    
    def _save_checkpoint(self, batch_id: int, batch_results: List[BatchResult]):
        """Save processing checkpoint."""
        checkpoint_file = f"batch_checkpoint_{batch_id + 1}.json"
        
        try:
            checkpoint_data = {
                "last_batch_id": batch_id,
                "total_cases_processed": self.total_cases_processed,
                "timestamp": datetime.now().isoformat(),
                "config": {
                    "batch_size": self.config.batch_size,
                    "algorithms": self.config.algorithms
                },
                "summary": {
                    "batches_completed": len(batch_results),
                    "total_communities": sum(r.communities_found for r in batch_results),
                    "total_time": sum(r.execution_time for r in batch_results)
                }
            }
            
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2)
            
            logger.info(f"Checkpoint saved: {checkpoint_file}")
            
        except Exception as e:
            logger.error(f"Error saving checkpoint: {e}")
    
    def load_checkpoint(self, checkpoint_file: str) -> Optional[Dict]:
        """Load processing checkpoint."""
        try:
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
            
            self.total_cases_processed = checkpoint_data["total_cases_processed"]
            logger.info(f"Checkpoint loaded: {checkpoint_file}")
            return checkpoint_data
            
        except Exception as e:
            logger.error(f"Error loading checkpoint: {e}")
            return None
    
    def close(self):
        """Close the detector."""
        self.detector.close()

# Convenience functions
def run_batch_community_detection(config: BatchProcessingConfig = None) -> List[BatchResult]:
    """
    Convenience function to run batch community detection.
    
    Args:
        config: Batch processing configuration
        
    Returns:
        List of batch results
    """
    detector = BatchCommunityDetector(config)
    try:
        return detector.process_all_batches()
    finally:
        detector.close()

def create_progress_callback():
    """Create a simple progress callback function."""
    def progress_callback(current_batch: int, total_batches: int, cases_processed: int):
        percentage = (current_batch / total_batches) * 100
        logger.info(f"Progress: {percentage:.1f}% ({current_batch}/{total_batches} batches, {cases_processed:,} cases)")
    
    return progress_callback