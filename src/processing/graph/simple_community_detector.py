"""
Simple Community Detection for Legal Cases

This module provides a basic community detection implementation that works
without the Neo4j GDS library, using citation relationships and basic graph algorithms.
"""

import logging
import os
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from neo4j import GraphDatabase
from neo4j.exceptions import ClientError
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class SimpleCommunityResult:
    """Result of simple community detection"""
    algorithm: str
    execution_time: float
    community_count: int
    communities: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

class SimpleCommunityDetector:
    """
    Simple community detection for legal cases using basic graph algorithms.
    
    This implementation works without Neo4j GDS by using citation relationships
    and basic clustering algorithms.
    """
    
    def __init__(self, uri: str = None, username: str = None, password: str = None):
        """Initialize the simple community detector."""
        self.uri = uri or os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = username or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD", "password")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            logger.info(f"Connected to Neo4j at {self.uri}")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    def detect_communities_by_citations(self, min_community_size: int = 3) -> SimpleCommunityResult:
        """
        Detect communities based on citation patterns.
        
        Args:
            min_community_size: Minimum number of cases in a community
            
        Returns:
            SimpleCommunityResult with detected communities
        """
        start_time = datetime.now()
        
        try:
            with self.driver.session() as session:
                # Get all cases and their citation relationships
                graph_data = self._load_graph_data(session)
                
                # Build adjacency list for undirected graph
                adjacency = self._build_adjacency_list(graph_data)
                
                # Find connected components
                communities = self._find_connected_components(adjacency, min_community_size)
                
                # Enhance communities with case details
                enhanced_communities = self._enhance_communities(session, communities)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return SimpleCommunityResult(
                    algorithm="citation_clustering",
                    execution_time=execution_time,
                    community_count=len(enhanced_communities),
                    communities=enhanced_communities,
                    metadata={"min_community_size": min_community_size}
                )
                
        except Exception as e:
            logger.error(f"Error in citation-based community detection: {e}")
            return SimpleCommunityResult(
                algorithm="citation_clustering",
                execution_time=(datetime.now() - start_time).total_seconds(),
                community_count=0,
                communities=[]
            )
    
    def detect_communities_by_jurisdiction(self, min_community_size: int = 2) -> SimpleCommunityResult:
        """
        Detect communities based on jurisdiction clustering.
        
        Args:
            min_community_size: Minimum number of cases in a community
            
        Returns:
            SimpleCommunityResult with detected communities
        """
        start_time = datetime.now()
        
        try:
            with self.driver.session() as session:
                # Group cases by jurisdiction
                jurisdiction_query = """
                MATCH (c:Case)
                WHERE c.jurisdiction IS NOT NULL
                RETURN c.jurisdiction AS jurisdiction, COLLECT(c) AS cases
                """
                
                result = session.run(jurisdiction_query)
                
                communities = []
                for record in result:
                    jurisdiction = record["jurisdiction"]
                    cases = record["cases"]
                    
                    if len(cases) >= min_community_size:
                        community = self._create_jurisdiction_community(jurisdiction, cases)
                        communities.append(community)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return SimpleCommunityResult(
                    algorithm="jurisdiction_clustering",
                    execution_time=execution_time,
                    community_count=len(communities),
                    communities=communities,
                    metadata={"min_community_size": min_community_size}
                )
                
        except Exception as e:
            logger.error(f"Error in jurisdiction-based community detection: {e}")
            return SimpleCommunityResult(
                algorithm="jurisdiction_clustering",
                execution_time=(datetime.now() - start_time).total_seconds(),
                community_count=0,
                communities=[]
            )
    
    def detect_communities_by_year(self, min_community_size: int = 2) -> SimpleCommunityResult:
        """
        Detect communities based on temporal clustering.
        
        Args:
            min_community_size: Minimum number of cases in a community
            
        Returns:
            SimpleCommunityResult with detected communities
        """
        start_time = datetime.now()
        
        try:
            with self.driver.session() as session:
                # Group cases by year
                year_query = """
                MATCH (c:Case)
                WHERE c.year IS NOT NULL
                RETURN c.year AS year, COLLECT(c) AS cases
                ORDER BY year DESC
                """
                
                result = session.run(year_query)
                
                communities = []
                for record in result:
                    year = record["year"]
                    cases = record["cases"]
                    
                    if len(cases) >= min_community_size:
                        community = self._create_temporal_community(year, cases)
                        communities.append(community)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return SimpleCommunityResult(
                    algorithm="temporal_clustering",
                    execution_time=execution_time,
                    community_count=len(communities),
                    communities=communities,
                    metadata={"min_community_size": min_community_size}
                )
                
        except Exception as e:
            logger.error(f"Error in temporal community detection: {e}")
            return SimpleCommunityResult(
                algorithm="temporal_clustering",
                execution_time=(datetime.now() - start_time).total_seconds(),
                community_count=0,
                communities=[]
            )
    
    def _load_graph_data(self, session) -> Dict[str, Dict[str, Any]]:
        """Load graph data from Neo4j."""
        graph_data = {}
        
        # Load cases
        case_query = """
        MATCH (c:Case)
        RETURN c.id AS case_id, c.name AS case_name, c.jurisdiction AS jurisdiction,
               c.year AS year, c.date_filed AS date_filed, c.court_id AS court_id
        """
        
        result = session.run(case_query)
        for record in result:
            case_id = record["case_id"]
            graph_data[case_id] = {
                "id": case_id,
                "name": record["case_name"],
                "jurisdiction": record["jurisdiction"],
                "year": record["year"],
                "date_filed": record["date_filed"],
                "court_id": record["court_id"],
                "citations": []
            }
        
        # Load citations
        citation_query = """
        MATCH (c1:Case)-[:CITES]->(c2:Case)
        RETURN c1.id AS source_id, c2.id AS target_id
        """
        
        result = session.run(citation_query)
        for record in result:
            source_id = record["source_id"]
            target_id = record["target_id"]
            
            if source_id in graph_data and target_id in graph_data:
                graph_data[source_id]["citations"].append(target_id)
        
        return graph_data
    
    def _build_adjacency_list(self, graph_data: Dict[str, Dict[str, Any]]) -> Dict[str, Set[str]]:
        """Build undirected adjacency list from citation data."""
        adjacency = defaultdict(set)
        
        for case_id, case_data in graph_data.items():
            adjacency[case_id] = set()
            
            # Add bidirectional edges for citations
            for cited_case_id in case_data["citations"]:
                if cited_case_id in graph_data:
                    adjacency[case_id].add(cited_case_id)
                    adjacency[cited_case_id].add(case_id)
        
        return adjacency
    
    def _find_connected_components(self, adjacency: Dict[str, Set[str]], 
                                 min_size: int) -> List[Set[str]]:
        """Find connected components using BFS."""
        visited = set()
        components = []
        
        for node in adjacency:
            if node not in visited:
                component = set()
                queue = deque([node])
                
                while queue:
                    current = queue.popleft()
                    if current not in visited:
                        visited.add(current)
                        component.add(current)
                        
                        for neighbor in adjacency[current]:
                            if neighbor not in visited:
                                queue.append(neighbor)
                
                if len(component) >= min_size:
                    components.append(component)
        
        return components
    
    def _enhance_communities(self, session, communities: List[Set[str]]) -> List[Dict[str, Any]]:
        """Enhance communities with case details."""
        enhanced_communities = []
        
        for i, community in enumerate(communities):
            case_ids = list(community)
            
            # Get case details
            case_query = """
            MATCH (c:Case)
            WHERE c.id IN $case_ids
            RETURN c.id AS case_id, c.name AS case_name, c.jurisdiction AS jurisdiction,
                   c.year AS year, c.date_filed AS date_filed, c.court_id AS court_id
            """
            
            result = session.run(case_query, case_ids=case_ids)
            cases = []
            jurisdictions = set()
            years = set()
            
            for record in result:
                case_info = {
                    "id": record["case_id"],
                    "name": record["case_name"],
                    "jurisdiction": record["jurisdiction"],
                    "year": record["year"],
                    "date_filed": record["date_filed"],
                    "court_id": record["court_id"]
                }
                cases.append(case_info)
                
                if case_info["jurisdiction"]:
                    jurisdictions.add(case_info["jurisdiction"])
                if case_info["year"]:
                    years.add(case_info["year"])
            
            # Calculate community metrics
            community_info = {
                "id": i + 1,
                "size": len(cases),
                "cases": cases,
                "jurisdictions": list(jurisdictions),
                "years": sorted(list(years)),
                "time_span": f"{min(years)}-{max(years)}" if years and all(isinstance(y, (int, float)) for y in years) else "N/A",
                "summary": self._generate_community_summary(cases, jurisdictions, years)
            }
            
            enhanced_communities.append(community_info)
        
        return enhanced_communities
    
    def _create_jurisdiction_community(self, jurisdiction: str, cases: List[Any]) -> Dict[str, Any]:
        """Create a jurisdiction-based community."""
        case_details = []
        years = set()
        
        for case in cases:
            case_info = {
                "id": case.get("id"),
                "name": case.get("name"),
                "jurisdiction": case.get("jurisdiction"),
                "year": case.get("year"),
                "date_filed": case.get("date_filed"),
                "court_id": case.get("court_id")
            }
            case_details.append(case_info)
            
            if case_info["year"]:
                years.add(case_info["year"])
        
        return {
            "id": f"jurisdiction_{jurisdiction}",
            "size": len(case_details),
            "cases": case_details,
            "jurisdictions": [jurisdiction],
            "years": sorted(list(years)),
            "time_span": f"{min(years)}-{max(years)}" if years and all(isinstance(y, (int, float)) for y in years) else "N/A",
            "summary": f"Jurisdiction-based community for {jurisdiction} with {len(case_details)} cases"
        }
    
    def _create_temporal_community(self, year: int, cases: List[Any]) -> Dict[str, Any]:
        """Create a temporal community."""
        case_details = []
        jurisdictions = set()
        
        for case in cases:
            case_info = {
                "id": case.get("id"),
                "name": case.get("name"),
                "jurisdiction": case.get("jurisdiction"),
                "year": case.get("year"),
                "date_filed": case.get("date_filed"),
                "court_id": case.get("court_id")
            }
            case_details.append(case_info)
            
            if case_info["jurisdiction"]:
                jurisdictions.add(case_info["jurisdiction"])
        
        return {
            "id": f"year_{year}",
            "size": len(case_details),
            "cases": case_details,
            "jurisdictions": list(jurisdictions),
            "years": [year],
            "time_span": str(year),
            "summary": f"Temporal community for {year} with {len(case_details)} cases across {len(jurisdictions)} jurisdictions"
        }
    
    def _generate_community_summary(self, cases: List[Dict], jurisdictions: Set, years: Set) -> str:
        """Generate a human-readable summary of a community."""
        summary_parts = []
        
        summary_parts.append(f"Community of {len(cases)} cases")
        
        if jurisdictions:
            if len(jurisdictions) == 1:
                summary_parts.append(f"in {list(jurisdictions)[0]}")
            else:
                summary_parts.append(f"across {len(jurisdictions)} jurisdictions")
        
        if years:
            years_list = [y for y in years if isinstance(y, (int, float))]
            if len(years_list) == 1:
                summary_parts.append(f"from {years_list[0]}")
            elif len(years_list) > 1:
                summary_parts.append(f"spanning {min(years_list)} to {max(years_list)}")
        
        return " ".join(summary_parts)
    
    def run_all_community_detection(self) -> Dict[str, SimpleCommunityResult]:
        """Run all community detection algorithms."""
        logger.info("Running all community detection algorithms...")
        
        results = {}
        
        # Citation-based communities
        logger.info("Running citation-based community detection...")
        results["citation"] = self.detect_communities_by_citations()
        
        # Jurisdiction-based communities
        logger.info("Running jurisdiction-based community detection...")
        results["jurisdiction"] = self.detect_communities_by_jurisdiction()
        
        # Temporal communities
        logger.info("Running temporal community detection...")
        results["temporal"] = self.detect_communities_by_year()
        
        logger.info("Community detection completed!")
        return results
    
    def close(self):
        """Close the Neo4j driver connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j driver closed")

# Convenience functions
def detect_simple_communities() -> Dict[str, SimpleCommunityResult]:
    """Convenience function to run simple community detection."""
    detector = SimpleCommunityDetector()
    try:
        return detector.run_all_community_detection()
    finally:
        detector.close()