"""
Neo4j Graph Data Science (GDS) Community Detection System

This module implements community detection algorithms using Neo4j GDS library
to identify clusters of related legal cases and enhance GraphRAG capabilities.
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from neo4j import GraphDatabase, Driver, Result
from neo4j.exceptions import ClientError

logger = logging.getLogger(__name__)

@dataclass
class CommunityResult:
    """Result of community detection algorithm"""
    algorithm: str
    execution_time: float
    community_count: int
    modularity: Optional[float] = None
    communities: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class CommunityInfo:
    """Information about a detected community"""
    id: int
    size: int
    cases: List[Dict[str, Any]]
    practice_areas: List[str]
    jurisdictions: List[str]
    time_range: Tuple[Optional[str], Optional[str]]
    centrality_scores: Dict[str, float]
    summary: Optional[str] = None

class GDSCommunityDetector:
    """
    Neo4j Graph Data Science community detection system for legal cases.
    
    Implements various community detection algorithms to identify clusters
    of related legal cases for enhanced GraphRAG capabilities.
    """
    
    def __init__(self, uri: str = None, username: str = None, password: str = None):
        """
        Initialize the GDS community detector.
        
        Args:
            uri: Neo4j connection URI
            username: Neo4j username
            password: Neo4j password
        """
        # Use existing Neo4j connection or create new one
        self.uri = uri or os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = username or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD", "password")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            logger.info(f"Connected to Neo4j at {self.uri}")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
        
        # Graph projection names
        self.graph_name = "legalCaseGraph"
        self.citation_graph_name = "citationGraph"
        
        # Initialize GDS setup
        self._setup_gds()
    
    def _setup_gds(self):
        """Setup GDS environment and verify functionality"""
        try:
            with self.driver.session() as session:
                # Set up Aura API credentials if available
                client_id = os.getenv("NEO4J_CLIENT_ID")
                client_secret = os.getenv("NEO4J_CLIENT_SECRET")
                
                if client_id and client_secret:
                    logger.info("Setting up Aura API credentials...")
                    try:
                        session.run("WITH gds.aura.api.credentials($client_id, $client_secret) AS credentials RETURN credentials",
                                   client_id=client_id, client_secret=client_secret)
                        logger.info("Aura API credentials set successfully")
                    except Exception as e:
                        logger.warning(f"Failed to set Aura API credentials: {e}")
                
                # Check GDS availability
                result = session.run("CALL gds.version() YIELD version")
                version = result.single()
                if version:
                    logger.info(f"GDS version: {version['version']}")
                else:
                    logger.warning("GDS not available - some features may be limited")
                
                # Check existing graphs
                result = session.run("CALL gds.graph.list() YIELD graphName")
                existing_graphs = [record["graphName"] for record in result]
                logger.info(f"Existing GDS graphs: {existing_graphs}")
                
        except ClientError as e:
            logger.warning(f"GDS setup check failed: {e}")
    
    def create_graph_projection(self, projection_name: str = None, 
                               node_filter: Dict[str, Any] = None,
                               relationship_filter: Dict[str, Any] = None) -> bool:
        """
        Create a graph projection for community detection.
        
        Args:
            projection_name: Name for the graph projection
            node_filter: Filters for node selection
            relationship_filter: Filters for relationship selection
            
        Returns:
            Success flag
        """
        projection_name = projection_name or self.graph_name
        
        try:
            with self.driver.session() as session:
                # Drop existing projection if it exists
                try:
                    session.run(f"CALL gds.graph.drop('{projection_name}')")
                    logger.info(f"Dropped existing projection: {projection_name}")
                except ClientError:
                    pass  # Projection doesn't exist
                
                # Create new projection with legal case focus
                projection_query = f"""
                CALL gds.graph.project(
                    '{projection_name}',
                    ['Case', 'Document'],
                    {{
                        CITES: {{
                            type: 'CITES',
                            orientation: 'NATURAL',
                            properties: ['weight']
                        }},
                        SIMILAR_TO: {{
                            type: 'SIMILAR_TO',
                            orientation: 'UNDIRECTED',
                            properties: ['similarity_score']
                        }}
                    }},
                    {{
                        nodeProperties: ['jurisdiction', 'practice_area', 'date_filed', 'authority_score', 'citation_count'],
                        relationshipProperties: ['weight', 'similarity_score']
                    }}
                )
                """
                
                result = session.run(projection_query)
                record = result.single()
                
                if record:
                    node_count = record.get('nodeCount', 0)
                    relationship_count = record.get('relationshipCount', 0)
                    logger.info(f"Created projection '{projection_name}' with {node_count} nodes and {relationship_count} relationships")
                    return True
                else:
                    logger.error(f"Failed to create projection: {projection_name}")
                    return False
                    
        except ClientError as e:
            logger.error(f"Error creating graph projection: {e}")
            return False
    
    def run_louvain_community_detection(self, projection_name: str = None,
                                       write_property: str = "louvain_community",
                                       **kwargs) -> CommunityResult:
        """
        Run Louvain community detection algorithm.
        
        Args:
            projection_name: Name of the graph projection
            write_property: Property name to write community IDs
            **kwargs: Additional algorithm parameters
            
        Returns:
            CommunityResult with detection results
        """
        projection_name = projection_name or self.graph_name
        
        try:
            with self.driver.session() as session:
                start_time = datetime.now()
                
                # Run Louvain algorithm
                louvain_query = f"""
                CALL gds.louvain.write('{projection_name}', {{
                    writeProperty: '{write_property}',
                    includeIntermediateCommunities: true,
                    maxLevels: 10,
                    maxIterations: 10,
                    tolerance: 0.0001
                }})
                YIELD communityCount, modularity, modularities
                """
                
                result = session.run(louvain_query)
                record = result.single()
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if record:
                    community_count = record.get('communityCount', 0)
                    modularity = record.get('modularity', 0.0)
                    
                    logger.info(f"Louvain completed: {community_count} communities, modularity: {modularity:.4f}")
                    
                    # Get community details
                    communities = self._get_community_details(session, write_property)
                    
                    return CommunityResult(
                        algorithm="louvain",
                        execution_time=execution_time,
                        community_count=community_count,
                        modularity=modularity,
                        communities=communities,
                        metadata={"projection": projection_name, "write_property": write_property}
                    )
                else:
                    logger.error("Louvain algorithm failed")
                    return CommunityResult(
                        algorithm="louvain",
                        execution_time=execution_time,
                        community_count=0,
                        communities=[]
                    )
                    
        except ClientError as e:
            logger.error(f"Error running Louvain: {e}")
            return CommunityResult(
                algorithm="louvain",
                execution_time=0,
                community_count=0,
                communities=[]
            )
    
    def run_leiden_community_detection(self, projection_name: str = None,
                                      write_property: str = "leiden_community",
                                      **kwargs) -> CommunityResult:
        """
        Run Leiden community detection algorithm (more accurate than Louvain).
        
        Args:
            projection_name: Name of the graph projection
            write_property: Property name to write community IDs
            **kwargs: Additional algorithm parameters
            
        Returns:
            CommunityResult with detection results
        """
        projection_name = projection_name or self.graph_name
        
        try:
            with self.driver.session() as session:
                start_time = datetime.now()
                
                # Run Leiden algorithm
                leiden_query = f"""
                CALL gds.leiden.write('{projection_name}', {{
                    writeProperty: '{write_property}',
                    includeIntermediateCommunities: true,
                    maxLevels: 10,
                    gamma: 1.0,
                    theta: 0.01
                }})
                YIELD communityCount, modularity, modularities
                """
                
                result = session.run(leiden_query)
                record = result.single()
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if record:
                    community_count = record.get('communityCount', 0)
                    modularity = record.get('modularity', 0.0)
                    
                    logger.info(f"Leiden completed: {community_count} communities, modularity: {modularity:.4f}")
                    
                    # Get community details
                    communities = self._get_community_details(session, write_property)
                    
                    return CommunityResult(
                        algorithm="leiden",
                        execution_time=execution_time,
                        community_count=community_count,
                        modularity=modularity,
                        communities=communities,
                        metadata={"projection": projection_name, "write_property": write_property}
                    )
                else:
                    logger.error("Leiden algorithm failed")
                    return CommunityResult(
                        algorithm="leiden",
                        execution_time=execution_time,
                        community_count=0,
                        communities=[]
                    )
                    
        except ClientError as e:
            logger.error(f"Error running Leiden: {e}")
            return CommunityResult(
                algorithm="leiden",
                execution_time=0,
                community_count=0,
                communities=[]
            )
    
    def _get_community_details(self, session, community_property: str) -> List[Dict[str, Any]]:
        """
        Get detailed information about detected communities.
        
        Args:
            session: Neo4j session
            community_property: Property containing community IDs
            
        Returns:
            List of community details
        """
        communities = []
        
        try:
            # Get community statistics
            stats_query = f"""
            MATCH (n:Case)
            WHERE n.{community_property} IS NOT NULL
            WITH n.{community_property} AS communityId, 
                 COLLECT(n) AS cases,
                 COUNT(n) AS size
            WHERE size >= 3  // Only include communities with 3+ cases
            RETURN communityId, size, cases
            ORDER BY size DESC
            LIMIT 100
            """
            
            result = session.run(stats_query)
            
            for record in result:
                community_id = record["communityId"]
                size = record["size"]
                cases = record["cases"]
                
                # Extract case details
                case_details = []
                practice_areas = set()
                jurisdictions = set()
                dates = []
                
                for case in cases:
                    case_info = {
                        "id": case.get("id"),
                        "name": case.get("case_name", case.get("name")),
                        "jurisdiction": case.get("jurisdiction"),
                        "practice_area": case.get("practice_area"),
                        "date_filed": case.get("date_filed"),
                        "authority_score": case.get("authority_score", 0),
                        "citation_count": case.get("citation_count", 0)
                    }
                    case_details.append(case_info)
                    
                    if case_info["practice_area"]:
                        practice_areas.add(case_info["practice_area"])
                    if case_info["jurisdiction"]:
                        jurisdictions.add(case_info["jurisdiction"])
                    if case_info["date_filed"]:
                        dates.append(case_info["date_filed"])
                
                # Calculate time range
                time_range = (None, None)
                if dates:
                    dates.sort()
                    time_range = (dates[0], dates[-1])
                
                # Calculate centrality scores for top cases
                centrality_scores = self._calculate_community_centrality(
                    session, community_id, community_property
                )
                
                community_info = {
                    "id": community_id,
                    "size": size,
                    "cases": case_details[:10],  # Top 10 cases
                    "practice_areas": list(practice_areas),
                    "jurisdictions": list(jurisdictions),
                    "time_range": time_range,
                    "centrality_scores": centrality_scores,
                    "summary": self._generate_community_summary(case_details, practice_areas, jurisdictions)
                }
                
                communities.append(community_info)
                
        except ClientError as e:
            logger.error(f"Error getting community details: {e}")
        
        return communities
    
    def _calculate_community_centrality(self, session, community_id: int, 
                                      community_property: str) -> Dict[str, float]:
        """
        Calculate centrality scores for cases within a community.
        
        Args:
            session: Neo4j session
            community_id: ID of the community
            community_property: Property containing community IDs
            
        Returns:
            Dictionary of centrality scores
        """
        try:
            centrality_query = f"""
            MATCH (n:Case {{`{community_property}`: $community_id}})
            OPTIONAL MATCH (n)-[r:CITES]->()
            OPTIONAL MATCH ()-[r2:CITES]->(n)
            RETURN n.id AS case_id,
                   n.case_name AS case_name,
                   COUNT(r) AS out_degree,
                   COUNT(r2) AS in_degree,
                   n.authority_score AS authority_score
            ORDER BY (COUNT(r) + COUNT(r2)) DESC
            LIMIT 5
            """
            
            result = session.run(centrality_query, community_id=community_id)
            
            centrality_scores = {}
            for record in result:
                case_id = record["case_id"]
                centrality_scores[case_id] = {
                    "case_name": record["case_name"],
                    "out_degree": record["out_degree"],
                    "in_degree": record["in_degree"],
                    "authority_score": record["authority_score"] or 0
                }
            
            return centrality_scores
            
        except ClientError as e:
            logger.error(f"Error calculating centrality: {e}")
            return {}
    
    def _generate_community_summary(self, cases: List[Dict], practice_areas: set, 
                                  jurisdictions: set) -> str:
        """
        Generate a human-readable summary of a community.
        
        Args:
            cases: List of cases in the community
            practice_areas: Set of practice areas
            jurisdictions: Set of jurisdictions
            
        Returns:
            Community summary string
        """
        summary_parts = []
        
        # Practice area summary
        if practice_areas:
            if len(practice_areas) == 1:
                summary_parts.append(f"focuses on {list(practice_areas)[0]}")
            else:
                summary_parts.append(f"spans {len(practice_areas)} practice areas: {', '.join(practice_areas)}")
        
        # Jurisdiction summary
        if jurisdictions:
            if len(jurisdictions) == 1:
                summary_parts.append(f"within {list(jurisdictions)[0]} jurisdiction")
            else:
                summary_parts.append(f"across {len(jurisdictions)} jurisdictions")
        
        # Time range summary
        if cases:
            dates = [case["date_filed"] for case in cases if case["date_filed"]]
            if dates:
                dates.sort()
                if len(dates) > 1:
                    summary_parts.append(f"spanning {dates[0][:4]} to {dates[-1][:4]}")
                else:
                    summary_parts.append(f"from {dates[0][:4]}")
        
        if summary_parts:
            return f"Community of {len(cases)} cases " + " ".join(summary_parts)
        else:
            return f"Community of {len(cases)} related cases"
    
    def get_case_communities(self, case_id: str, algorithms: List[str] = None) -> Dict[str, Any]:
        """
        Get community information for a specific case.
        
        Args:
            case_id: ID of the case
            algorithms: List of algorithms to check (default: ['louvain', 'leiden'])
            
        Returns:
            Dictionary with community information
        """
        algorithms = algorithms or ['louvain', 'leiden']
        
        try:
            with self.driver.session() as session:
                case_communities = {}
                
                for algorithm in algorithms:
                    community_property = f"{algorithm}_community"
                    
                    query = f"""
                    MATCH (c:Case {{id: $case_id}})
                    WHERE c.{community_property} IS NOT NULL
                    OPTIONAL MATCH (related:Case {{`{community_property}`: c.{community_property}}})
                    WHERE related.id <> $case_id
                    RETURN c.{community_property} AS community_id,
                           COLLECT({{
                               id: related.id,
                               name: related.case_name,
                               jurisdiction: related.jurisdiction,
                               practice_area: related.practice_area,
                               authority_score: related.authority_score
                           }}) AS related_cases
                    """
                    
                    result = session.run(query, case_id=case_id)
                    record = result.single()
                    
                    if record:
                        case_communities[algorithm] = {
                            "community_id": record["community_id"],
                            "related_cases": record["related_cases"][:10]  # Top 10 related cases
                        }
                
                return case_communities
                
        except ClientError as e:
            logger.error(f"Error getting case communities: {e}")
            return {}
    
    def search_communities(self, query: str, jurisdiction: str = None, 
                          practice_area: str = None, algorithm: str = "louvain") -> List[Dict[str, Any]]:
        """
        Search for communities based on query criteria.
        
        Args:
            query: Search query
            jurisdiction: Filter by jurisdiction
            practice_area: Filter by practice area
            algorithm: Algorithm to use for communities
            
        Returns:
            List of matching communities
        """
        try:
            with self.driver.session() as session:
                community_property = f"{algorithm}_community"
                
                # Build search query
                where_conditions = []
                params = {"query": query.lower()}
                
                if jurisdiction:
                    where_conditions.append("c.jurisdiction = $jurisdiction")
                    params["jurisdiction"] = jurisdiction
                
                if practice_area:
                    where_conditions.append("c.practice_area = $practice_area")
                    params["practice_area"] = practice_area
                
                where_clause = ""
                if where_conditions:
                    where_clause = "AND " + " AND ".join(where_conditions)
                
                search_query = f"""
                MATCH (c:Case)
                WHERE (toLower(c.case_name) CONTAINS $query OR toLower(c.text) CONTAINS $query)
                  AND c.{community_property} IS NOT NULL
                  {where_clause}
                WITH c.{community_property} AS community_id, 
                     COUNT(c) AS matching_cases,
                     COLLECT(c) AS cases
                WHERE matching_cases >= 1
                RETURN community_id, matching_cases, cases
                ORDER BY matching_cases DESC
                LIMIT 20
                """
                
                result = session.run(search_query, **params)
                
                communities = []
                for record in result:
                    community_id = record["community_id"]
                    matching_cases = record["matching_cases"]
                    cases = record["cases"]
                    
                    # Get full community details
                    community_details = self._get_community_details(session, community_property)
                    community_info = next((c for c in community_details if c["id"] == community_id), None)
                    
                    if community_info:
                        community_info["matching_cases"] = matching_cases
                        community_info["query_relevance"] = matching_cases / community_info["size"]
                        communities.append(community_info)
                
                return communities
                
        except ClientError as e:
            logger.error(f"Error searching communities: {e}")
            return []
    
    def update_community_detection(self, force_refresh: bool = False) -> Dict[str, CommunityResult]:
        """
        Update community detection by running all algorithms.
        
        Args:
            force_refresh: Force recreation of graph projection
            
        Returns:
            Dictionary of algorithm results
        """
        logger.info("Starting community detection update...")
        
        results = {}
        
        try:
            # Create or update graph projection
            if force_refresh or not self._graph_projection_exists():
                if not self.create_graph_projection():
                    logger.error("Failed to create graph projection")
                    return results
            
            # Run Louvain algorithm
            logger.info("Running Louvain community detection...")
            results["louvain"] = self.run_louvain_community_detection()
            
            # Run Leiden algorithm
            logger.info("Running Leiden community detection...")
            results["leiden"] = self.run_leiden_community_detection()
            
            # Store results metadata
            self._store_community_results(results)
            
            logger.info("Community detection update completed successfully")
            
        except Exception as e:
            logger.error(f"Error during community detection update: {e}")
        
        return results
    
    def _graph_projection_exists(self) -> bool:
        """Check if the graph projection exists."""
        try:
            with self.driver.session() as session:
                result = session.run(
                    "CALL gds.graph.exists($graph_name) YIELD exists",
                    graph_name=self.graph_name
                )
                record = result.single()
                return record["exists"] if record else False
        except ClientError:
            return False
    
    def _store_community_results(self, results: Dict[str, CommunityResult]):
        """Store community detection results metadata."""
        try:
            with self.driver.session() as session:
                for algorithm, result in results.items():
                    # Store in a metadata node or separate table
                    metadata_query = """
                    MERGE (meta:CommunityDetectionResult {algorithm: $algorithm})
                    SET meta.execution_time = $execution_time,
                        meta.community_count = $community_count,
                        meta.modularity = $modularity,
                        meta.created_at = $created_at,
                        meta.updated_at = datetime()
                    """
                    
                    session.run(metadata_query,
                               algorithm=algorithm,
                               execution_time=result.execution_time,
                               community_count=result.community_count,
                               modularity=result.modularity,
                               created_at=result.created_at.isoformat())
        except ClientError as e:
            logger.error(f"Error storing community results: {e}")
    
    def close(self):
        """Close the Neo4j driver connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j driver closed")

# Convenience functions for easy integration
def detect_communities(force_refresh: bool = False) -> Dict[str, CommunityResult]:
    """
    Convenience function to run community detection.
    
    Args:
        force_refresh: Force recreation of graph projection
        
    Returns:
        Dictionary of algorithm results
    """
    detector = GDSCommunityDetector()
    try:
        return detector.update_community_detection(force_refresh)
    finally:
        detector.close()

def search_case_communities(query: str, jurisdiction: str = None, 
                           practice_area: str = None) -> List[Dict[str, Any]]:
    """
    Convenience function to search communities.
    
    Args:
        query: Search query
        jurisdiction: Filter by jurisdiction
        practice_area: Filter by practice area
        
    Returns:
        List of matching communities
    """
    detector = GDSCommunityDetector()
    try:
        return detector.search_communities(query, jurisdiction, practice_area)
    finally:
        detector.close()

def get_case_community_info(case_id: str) -> Dict[str, Any]:
    """
    Convenience function to get community info for a case.
    
    Args:
        case_id: ID of the case
        
    Returns:
        Dictionary with community information
    """
    detector = GDSCommunityDetector()
    try:
        return detector.get_case_communities(case_id)
    finally:
        detector.close()