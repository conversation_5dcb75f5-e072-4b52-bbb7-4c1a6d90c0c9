"""
Retry Manager with Dead Letter Queue
Handles retries with exponential backoff + jitter and DLQ for failed items
"""

import json
import logging
import asyncio
import random
from pathlib import Path
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
import httpx

logger = logging.getLogger(__name__)


@dataclass
class RetryAttempt:
    """Represents a retry attempt"""
    attempt: int
    timestamp: str
    error: str
    delay: float


@dataclass
class DLQItem:
    """Dead Letter Queue item"""
    item_id: str
    original_data: Any
    error_message: str
    retry_attempts: List[RetryAttempt]
    timestamp: str
    context: Dict[str, Any]


class HTTPError429(Exception):
    """Rate limit exceeded (429 Too Many Requests)"""
    pass


class HTTPError5xx(Exception):
    """Server error (5xx status codes)"""
    pass


class MaxRetriesExceeded(Exception):
    """Maximum retry attempts exceeded"""
    pass


class RetryManager:
    """
    Manages retries with exponential backoff + jitter and dead letter queue
    """
    
    def __init__(
        self,
        max_retries: int = 5,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_multiplier: float = 2.0,
        jitter_range: float = 1.0,
        dlq_file: str = "state/dlq.jsonl"
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_multiplier = backoff_multiplier
        self.jitter_range = jitter_range
        
        # Setup DLQ file
        self.dlq_file = Path(dlq_file)
        self.dlq_file.parent.mkdir(exist_ok=True)
        
        # Retry statistics
        self.retry_stats = {
            'total_attempts': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'dlq_items': 0
        }
    
    async def retry_with_backoff(
        self,
        operation: Callable,
        *args,
        item_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Any:
        """
        Retry operation with exponential backoff + jitter
        
        Args:
            operation: Async function to retry
            *args: Arguments for the operation
            item_id: Unique identifier for the item being processed
            context: Additional context for error tracking
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Result of the operation
            
        Raises:
            MaxRetriesExceeded: If all retry attempts fail
        """
        retry_attempts = []
        last_exception = None
        
        for attempt in range(self.max_retries):
            self.retry_stats['total_attempts'] += 1
            
            try:
                result = await operation(*args, **kwargs)
                
                # Success - log retry recovery if this wasn't the first attempt
                if attempt > 0:
                    self.retry_stats['successful_retries'] += 1
                    logger.info(f"Operation succeeded after {attempt + 1} attempts")
                
                return result
                
            except (httpx.HTTPStatusError, HTTPError429, HTTPError5xx) as e:
                last_exception = e
                
                # Determine if this is a retryable error
                if isinstance(e, httpx.HTTPStatusError):
                    if e.response.status_code == 429:
                        error_type = "rate_limit"
                    elif 500 <= e.response.status_code < 600:
                        error_type = "server_error"
                    else:
                        # Non-retryable HTTP error
                        await self._send_to_dlq(
                            item_id or "unknown",
                            args[0] if args else kwargs,
                            f"Non-retryable HTTP error: {e}",
                            retry_attempts,
                            context
                        )
                        raise e
                else:
                    error_type = "network_error"
                
                # Record this attempt
                delay = self._calculate_delay(attempt)
                retry_attempts.append(RetryAttempt(
                    attempt=attempt + 1,
                    timestamp=datetime.now().isoformat(),
                    error=str(e),
                    delay=delay
                ))
                
                # If this is the last attempt, send to DLQ
                if attempt == self.max_retries - 1:
                    self.retry_stats['failed_retries'] += 1
                    await self._send_to_dlq(
                        item_id or "unknown",
                        args[0] if args else kwargs,
                        f"Max retries exceeded: {e}",
                        retry_attempts,
                        context
                    )
                    raise MaxRetriesExceeded(f"Failed after {self.max_retries} attempts: {e}")
                
                # Wait before retrying
                logger.warning(
                    f"Retry {attempt + 1}/{self.max_retries} after {delay:.2f}s "
                    f"({error_type}): {e}"
                )
                await asyncio.sleep(delay)
                
            except Exception as e:
                # For non-HTTP exceptions, treat as retryable for testing
                last_exception = e

                # Record this attempt
                delay = self._calculate_delay(attempt)
                retry_attempts.append(RetryAttempt(
                    attempt=attempt + 1,
                    timestamp=datetime.now().isoformat(),
                    error=str(e),
                    delay=delay
                ))

                # If this is the last attempt, send to DLQ
                if attempt == self.max_retries - 1:
                    self.retry_stats['failed_retries'] += 1
                    await self._send_to_dlq(
                        item_id or "unknown",
                        args[0] if args else kwargs,
                        f"Max retries exceeded: {e}",
                        retry_attempts,
                        context
                    )
                    raise MaxRetriesExceeded(f"Failed after {self.max_retries} attempts: {e}")

                # Wait before retrying
                logger.warning(
                    f"Retry {attempt + 1}/{self.max_retries} after {delay:.2f}s "
                    f"(generic error): {e}"
                )
                await asyncio.sleep(delay)
        
        # This should never be reached, but just in case
        raise MaxRetriesExceeded(f"Unexpected retry loop exit: {last_exception}")
    
    def _calculate_delay(self, attempt: int) -> float:
        """
        Calculate delay with exponential backoff + jitter
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        # Exponential backoff
        delay = self.base_delay * (self.backoff_multiplier ** attempt)
        
        # Apply maximum delay limit
        delay = min(delay, self.max_delay)
        
        # Add jitter to avoid thundering herd
        jitter = random.uniform(0, self.jitter_range)
        delay += jitter
        
        return delay
    
    async def _send_to_dlq(
        self,
        item_id: str,
        original_data: Any,
        error_message: str,
        retry_attempts: List[RetryAttempt],
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Send failed item to dead letter queue
        
        Args:
            item_id: Unique identifier for the item
            original_data: Original data that failed processing
            error_message: Final error message
            retry_attempts: List of retry attempts made
            context: Additional context information
        """
        dlq_item = DLQItem(
            item_id=item_id,
            original_data=original_data,
            error_message=error_message,
            retry_attempts=retry_attempts,
            timestamp=datetime.now().isoformat(),
            context=context or {}
        )
        
        try:
            # Append to DLQ file (JSONL format)
            with open(self.dlq_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(asdict(dlq_item), ensure_ascii=False) + '\n')
            
            self.retry_stats['dlq_items'] += 1
            logger.error(f"Item {item_id} sent to DLQ after {len(retry_attempts)} attempts")
            
        except Exception as e:
            logger.critical(f"Failed to write to DLQ file {self.dlq_file}: {e}")
            # This is a critical error - we're losing data
            raise RuntimeError(f"DLQ write failure: {e}")
    
    def get_retry_stats(self) -> Dict[str, Any]:
        """Get retry statistics"""
        return {
            **self.retry_stats,
            'success_rate': (
                self.retry_stats['successful_retries'] / 
                max(self.retry_stats['total_attempts'], 1)
            ),
            'dlq_rate': (
                self.retry_stats['dlq_items'] / 
                max(self.retry_stats['total_attempts'], 1)
            )
        }
    
    def load_dlq_items(self, limit: Optional[int] = None) -> List[DLQItem]:
        """
        Load items from dead letter queue
        
        Args:
            limit: Maximum number of items to load
            
        Returns:
            List of DLQ items
        """
        if not self.dlq_file.exists():
            return []
        
        items = []
        try:
            with open(self.dlq_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if limit and len(items) >= limit:
                        break
                    
                    try:
                        data = json.loads(line.strip())
                        items.append(DLQItem(**data))
                    except Exception as e:
                        logger.warning(f"Failed to parse DLQ line {line_num}: {e}")
        
        except Exception as e:
            logger.error(f"Failed to read DLQ file: {e}")
        
        return items
    
    def clear_dlq(self) -> int:
        """
        Clear the dead letter queue
        
        Returns:
            Number of items that were in the queue
        """
        if not self.dlq_file.exists():
            return 0
        
        # Count items before clearing
        items = self.load_dlq_items()
        item_count = len(items)
        
        try:
            self.dlq_file.unlink()
            logger.info(f"Cleared DLQ with {item_count} items")
            return item_count
        except Exception as e:
            logger.error(f"Failed to clear DLQ: {e}")
            return 0
    
    async def reprocess_dlq_items(
        self,
        operation: Callable,
        limit: Optional[int] = None,
        clear_on_success: bool = True
    ) -> Dict[str, int]:
        """
        Reprocess items from dead letter queue
        
        Args:
            operation: Operation to retry on DLQ items
            limit: Maximum number of items to reprocess
            clear_on_success: Whether to clear DLQ after successful reprocessing
            
        Returns:
            Dict with reprocessing statistics
        """
        dlq_items = self.load_dlq_items(limit)
        
        if not dlq_items:
            logger.info("No items in DLQ to reprocess")
            return {"processed": 0, "succeeded": 0, "failed": 0}
        
        logger.info(f"Reprocessing {len(dlq_items)} items from DLQ")
        
        succeeded = 0
        failed = 0
        
        for item in dlq_items:
            try:
                await operation(item.original_data)
                succeeded += 1
                logger.debug(f"Successfully reprocessed DLQ item: {item.item_id}")
            except Exception as e:
                failed += 1
                logger.warning(f"Failed to reprocess DLQ item {item.item_id}: {e}")
        
        stats = {
            "processed": len(dlq_items),
            "succeeded": succeeded,
            "failed": failed
        }
        
        if clear_on_success and succeeded > 0:
            cleared_count = self.clear_dlq()
            stats["cleared"] = cleared_count
        
        logger.info(f"DLQ reprocessing complete: {stats}")
        return stats


# Convenience functions
def create_retry_manager(**kwargs) -> RetryManager:
    """Create a retry manager with custom settings"""
    return RetryManager(**kwargs)


# Decorator for automatic retry
def with_retry(retry_manager: RetryManager):
    """Decorator to add retry capability to functions"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            return await retry_manager.retry_with_backoff(func, *args, **kwargs)
        return wrapper
    return decorator


if __name__ == "__main__":
    # Test the retry manager
    import asyncio
    
    async def test_retry_manager():
        retry_manager = RetryManager(max_retries=3, base_delay=0.1)
        
        # Test successful operation
        async def success_operation():
            return "success"
        
        result = await retry_manager.retry_with_backoff(success_operation)
        print(f"Success result: {result}")
        
        # Test failing operation
        async def failing_operation():
            raise HTTPError429("Rate limit exceeded")
        
        try:
            await retry_manager.retry_with_backoff(
                failing_operation,
                item_id="test_item",
                context={"test": True}
            )
        except MaxRetriesExceeded as e:
            print(f"Expected failure: {e}")
        
        # Check stats
        stats = retry_manager.get_retry_stats()
        print(f"Retry stats: {stats}")
        
        # Check DLQ
        dlq_items = retry_manager.load_dlq_items()
        print(f"DLQ items: {len(dlq_items)}")
    
    asyncio.run(test_retry_manager())
