#!/usr/bin/env python3
"""
Cross-Reference Validator
Validates judge identities against external databases and sources
"""

import requests
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ValidationSource(Enum):
    """External validation sources"""
    FEDERAL_JUDICIAL_CENTER = "fjc"
    WIKIPEDIA = "wikipedia"
    BALLOTPEDIA = "ballotpedia"
    COURT_WEBSITES = "court_websites"

@dataclass
class ValidationResult:
    """Result of cross-reference validation"""
    judge_name: str
    source: ValidationSource
    confidence: float
    validated: bool
    external_data: Dict[str, Any]
    validation_notes: str

class CrossReferenceValidator:
    """Validates judge identities against external sources"""
    
    def __init__(self):
        # Federal Judicial Center API (if available)
        self.fjc_base_url = "https://www.fjc.gov/api"  # Hypothetical API
        
        # Wikipedia API
        self.wikipedia_api = "https://en.wikipedia.org/api/rest_v1"
        
        # Court websites patterns
        self.court_websites = {
            'ca5': 'https://www.ca5.uscourts.gov',
            'ca2': 'https://www.ca2.uscourts.gov',
            'scotus': 'https://www.supremecourt.gov',
        }
        
        # Validation cache
        self.validation_cache = {}
    
    def validate_judge(self, 
                      judge_name: str, 
                      court: str, 
                      era: str = None) -> List[ValidationResult]:
        """Validate a judge against external sources"""
        
        # Check cache first
        cache_key = f"{judge_name}_{court}_{era}"
        if cache_key in self.validation_cache:
            return self.validation_cache[cache_key]
        
        results = []
        
        # Try different validation sources
        try:
            # 1. Wikipedia validation
            wiki_result = self._validate_wikipedia(judge_name, court)
            if wiki_result:
                results.append(wiki_result)
            
            # 2. Federal Judicial Center validation (framework)
            fjc_result = self._validate_fjc(judge_name, court)
            if fjc_result:
                results.append(fjc_result)
            
            # 3. Court website validation (framework)
            court_result = self._validate_court_website(judge_name, court)
            if court_result:
                results.append(court_result)
        
        except Exception as e:
            logger.warning(f"Cross-reference validation failed for {judge_name}: {e}")
        
        # Cache results
        self.validation_cache[cache_key] = results
        
        return results
    
    def _validate_wikipedia(self, judge_name: str, court: str) -> Optional[ValidationResult]:
        """Validate judge against Wikipedia"""
        
        try:
            # Search Wikipedia for the judge
            search_url = f"{self.wikipedia_api}/page/summary/{judge_name.replace(' ', '_')}"
            
            response = requests.get(search_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if it's about a judge
                extract = data.get('extract', '').lower()
                title = data.get('title', '').lower()
                
                judge_indicators = ['judge', 'justice', 'court', 'judicial']
                
                if any(indicator in extract or indicator in title for indicator in judge_indicators):
                    # Calculate confidence based on content match
                    confidence = self._calculate_wikipedia_confidence(data, court)
                    
                    return ValidationResult(
                        judge_name=judge_name,
                        source=ValidationSource.WIKIPEDIA,
                        confidence=confidence,
                        validated=confidence > 0.7,
                        external_data=data,
                        validation_notes=f"Wikipedia article found with {confidence:.1%} confidence"
                    )
            
        except Exception as e:
            logger.debug(f"Wikipedia validation failed for {judge_name}: {e}")
        
        return None
    
    def _calculate_wikipedia_confidence(self, data: Dict, court: str) -> float:
        """Calculate confidence score for Wikipedia validation"""
        
        score = 0.0
        extract = data.get('extract', '').lower()
        
        # Check for court mentions
        if court.lower() in extract:
            score += 0.4
        
        # Check for judicial terms
        judicial_terms = ['judge', 'justice', 'court', 'appointed', 'nominated']
        term_matches = sum(1 for term in judicial_terms if term in extract)
        score += min(0.3, term_matches * 0.1)
        
        # Check for federal court indicators
        federal_indicators = ['federal', 'circuit', 'district', 'supreme']
        federal_matches = sum(1 for indicator in federal_indicators if indicator in extract)
        score += min(0.3, federal_matches * 0.1)
        
        return min(1.0, score)
    
    def _validate_fjc(self, judge_name: str, court: str) -> Optional[ValidationResult]:
        """Validate judge against Federal Judicial Center (framework)"""
        
        # This is a framework for future FJC API integration
        # For now, return a placeholder result for demonstration
        
        try:
            # Placeholder: In real implementation, this would query FJC database
            # fjc_url = f"{self.fjc_base_url}/judges/search?name={judge_name}&court={court}"
            # response = requests.get(fjc_url, timeout=10)
            
            # For now, return a framework result
            return ValidationResult(
                judge_name=judge_name,
                source=ValidationSource.FEDERAL_JUDICIAL_CENTER,
                confidence=0.0,  # No actual validation yet
                validated=False,
                external_data={"status": "framework_only"},
                validation_notes="FJC validation framework ready - API integration needed"
            )
            
        except Exception as e:
            logger.debug(f"FJC validation failed for {judge_name}: {e}")
        
        return None
    
    def _validate_court_website(self, judge_name: str, court: str) -> Optional[ValidationResult]:
        """Validate judge against court websites (framework)"""
        
        # This is a framework for future court website scraping
        # For now, return a placeholder result for demonstration
        
        try:
            court_url = self.court_websites.get(court)
            
            if court_url:
                # Placeholder: In real implementation, this would scrape court websites
                # for judge directories or biographical information
                
                return ValidationResult(
                    judge_name=judge_name,
                    source=ValidationSource.COURT_WEBSITES,
                    confidence=0.0,  # No actual validation yet
                    validated=False,
                    external_data={"court_url": court_url, "status": "framework_only"},
                    validation_notes="Court website validation framework ready - scraping logic needed"
                )
            
        except Exception as e:
            logger.debug(f"Court website validation failed for {judge_name}: {e}")
        
        return None
    
    def get_validation_summary(self, judge_name: str, court: str) -> Dict[str, Any]:
        """Get a summary of all validation results for a judge"""
        
        results = self.validate_judge(judge_name, court)
        
        if not results:
            return {
                'judge_name': judge_name,
                'court': court,
                'validated': False,
                'confidence': 0.0,
                'sources_checked': 0,
                'validation_notes': 'No external validation sources found'
            }
        
        # Calculate overall confidence
        total_confidence = sum(r.confidence for r in results)
        avg_confidence = total_confidence / len(results)
        
        # Check if any source validated
        any_validated = any(r.validated for r in results)
        
        return {
            'judge_name': judge_name,
            'court': court,
            'validated': any_validated,
            'confidence': avg_confidence,
            'sources_checked': len(results),
            'validation_sources': [r.source.value for r in results],
            'validation_notes': f"Checked {len(results)} sources, avg confidence: {avg_confidence:.1%}"
        }


def main():
    """Test cross-reference validation"""
    
    validator = CrossReferenceValidator()
    
    # Test with a known judge
    test_judges = [
        ("John Roberts", "scotus"),
        ("Miranda M. Du", "ca5"),
        ("Ruth Bader Ginsburg", "scotus"),
    ]
    
    for judge_name, court in test_judges:
        print(f"\n🔍 Validating: {judge_name} ({court})")
        
        summary = validator.get_validation_summary(judge_name, court)
        
        print(f"   Validated: {'✅' if summary['validated'] else '❌'}")
        print(f"   Confidence: {summary['confidence']:.1%}")
        print(f"   Sources: {summary['sources_checked']}")
        print(f"   Notes: {summary['validation_notes']}")


if __name__ == "__main__":
    main()
