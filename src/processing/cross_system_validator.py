"""
Cross-System Validator
Validates consistency across Supabase, GCS, Pinecone, and Neo4j storage systems
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class SystemCounts:
    """Storage system counts"""
    supabase_cases: int
    gcs_objects: int
    pinecone_vectors: int
    neo4j_nodes: int
    
    def to_dict(self) -> Dict[str, int]:
        return {
            'supabase': self.supabase_cases,
            'gcs': self.gcs_objects,
            'pinecone': self.pinecone_vectors,
            'neo4j': self.neo4j_nodes
        }


@dataclass
class ConsistencyReport:
    """Cross-system consistency report"""
    timestamp: str
    batch_id: str
    expected_cases: int
    actual_counts: SystemCounts
    consistency_scores: Dict[str, float]
    vector_stats: Dict[str, Any]
    issues: List[str]
    overall_score: float
    passed: bool


class CrossSystemValidator:
    """
    Validates consistency across all storage systems
    """
    
    def __init__(
        self,
        supabase_client,
        gcs_client,
        pinecone_client,
        neo4j_client,
        tolerance: float = 0.02  # 2% tolerance
    ):
        self.supabase = supabase_client
        self.gcs = gcs_client
        self.pinecone = pinecone_client
        self.neo4j = neo4j_client
        self.tolerance = tolerance
    
    async def validate_batch_consistency(
        self,
        batch_id: str,
        expected_cases: int,
        time_range: Optional[Dict[str, str]] = None
    ) -> ConsistencyReport:
        """
        Validate consistency for a specific batch
        
        Args:
            batch_id: Batch identifier
            expected_cases: Expected number of cases
            time_range: Optional time range filter
            
        Returns:
            ConsistencyReport with validation results
        """
        logger.info(f"🔍 Validating consistency for batch {batch_id}")
        
        # Get actual counts from each system
        actual_counts = await self._get_system_counts(batch_id, time_range)
        
        # Calculate consistency scores
        consistency_scores = self._calculate_consistency_scores(expected_cases, actual_counts)
        
        # Get vector statistics
        vector_stats = await self._get_vector_statistics(batch_id)
        
        # Identify issues
        issues = self._identify_issues(expected_cases, actual_counts, consistency_scores)
        
        # Calculate overall score
        overall_score = sum(consistency_scores.values()) / len(consistency_scores)
        
        # Determine if validation passed
        passed = all(score > (1.0 - self.tolerance) for score in consistency_scores.values())
        
        report = ConsistencyReport(
            timestamp=datetime.now().isoformat(),
            batch_id=batch_id,
            expected_cases=expected_cases,
            actual_counts=actual_counts,
            consistency_scores=consistency_scores,
            vector_stats=vector_stats,
            issues=issues,
            overall_score=overall_score,
            passed=passed
        )
        
        self._log_report(report)
        return report
    
    async def _get_system_counts(
        self,
        batch_id: str,
        time_range: Optional[Dict[str, str]] = None
    ) -> SystemCounts:
        """Get counts from all storage systems"""
        
        # Supabase count
        supabase_count = await self._count_supabase_cases(batch_id, time_range)
        
        # GCS count
        gcs_count = await self._count_gcs_objects(batch_id, time_range)
        
        # Pinecone count
        pinecone_count = await self._count_pinecone_vectors(batch_id, time_range)
        
        # Neo4j count
        neo4j_count = await self._count_neo4j_nodes(batch_id, time_range)
        
        return SystemCounts(
            supabase_cases=supabase_count,
            gcs_objects=gcs_count,
            pinecone_vectors=pinecone_count,
            neo4j_nodes=neo4j_count
        )
    
    async def _count_supabase_cases(self, batch_id: str, time_range: Optional[Dict[str, str]]) -> int:
        """Count cases in Supabase"""
        try:
            query = self.supabase.table('cases').select('id', count='exact')
            
            if batch_id:
                query = query.eq('batch_id', batch_id)
            
            if time_range:
                query = query.gte('date_filed', time_range['start']).lte('date_filed', time_range['end'])
            
            result = query.execute()
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Error counting Supabase cases: {e}")
            return 0
    
    async def _count_gcs_objects(self, batch_id: str, time_range: Optional[Dict[str, str]]) -> int:
        """Count objects in GCS"""
        try:
            if hasattr(self.gcs, 'bucket') and self.gcs.name != "mock_gcs":
                # Real GCS implementation
                bucket = self.gcs.bucket('legal-cases')
                blobs = bucket.list_blobs(prefix='cases/tx/')
                return len(list(blobs))
            else:
                # Mock implementation - estimate based on Supabase
                supabase_count = await self._count_supabase_cases(batch_id, time_range)
                return supabase_count  # Assume 1:1 for mock
                
        except Exception as e:
            logger.error(f"Error counting GCS objects: {e}")
            return 0
    
    async def _count_pinecone_vectors(self, batch_id: str, time_range: Optional[Dict[str, str]]) -> int:
        """Count vectors in Pinecone"""
        try:
            if hasattr(self.pinecone, 'describe_index_stats') and not hasattr(self.pinecone, 'name'):
                # Real Pinecone implementation
                stats = self.pinecone.describe_index_stats()
                return stats.get('total_vector_count', 0)
            else:
                # Mock implementation - estimate based on Supabase (assume 3 vectors per case)
                supabase_count = await self._count_supabase_cases(batch_id, time_range)
                return supabase_count * 3  # Assume 3 vectors per case for mock
                
        except Exception as e:
            logger.error(f"Error counting Pinecone vectors: {e}")
            return 0
    
    async def _count_neo4j_nodes(self, batch_id: str, time_range: Optional[Dict[str, str]]) -> int:
        """Count nodes in Neo4j"""
        try:
            if hasattr(self.neo4j, 'session') and not hasattr(self.neo4j, 'name'):
                # Real Neo4j implementation
                with self.neo4j.session() as session:
                    query = "MATCH (c:Case) "
                    if batch_id:
                        query += "WHERE c.batch_id = $batch_id "
                    query += "RETURN count(c) as count"
                    
                    result = session.run(query, batch_id=batch_id)
                    return result.single()['count']
            else:
                # Mock implementation - estimate based on Supabase
                supabase_count = await self._count_supabase_cases(batch_id, time_range)
                return supabase_count  # Assume 1:1 for mock
                
        except Exception as e:
            logger.error(f"Error counting Neo4j nodes: {e}")
            return 0
    
    def _calculate_consistency_scores(self, expected: int, actual: SystemCounts) -> Dict[str, float]:
        """Calculate consistency scores for each system"""
        scores = {}
        
        for system, count in actual.to_dict().items():
            if expected == 0:
                scores[system] = 1.0 if count == 0 else 0.0
            else:
                # For Pinecone, we expect multiple vectors per case
                if system == 'pinecone':
                    # Assume 2-5 vectors per case is normal
                    expected_vectors = expected * 3  # Average expectation
                    scores[system] = min(1.0, count / expected_vectors) if expected_vectors > 0 else 0.0
                else:
                    # For other systems, expect 1:1 with cases
                    scores[system] = min(1.0, count / expected)
        
        return scores
    
    async def _get_vector_statistics(self, batch_id: str) -> Dict[str, Any]:
        """Get detailed vector statistics"""
        try:
            # Query Supabase for vector counts per case
            result = self.supabase.table('cases').select('id, word_count').eq('batch_id', batch_id).execute()
            
            vector_counts = [row.get('word_count', 0) for row in result.data if row.get('word_count')]
            
            if vector_counts:
                return {
                    'total_vectors': sum(vector_counts),
                    'avg_vectors_per_case': sum(vector_counts) / len(vector_counts),
                    'min_vectors_per_case': min(vector_counts),
                    'max_vectors_per_case': max(vector_counts),
                    'cases_with_vectors': len(vector_counts)
                }
            else:
                return {
                    'total_vectors': 0,
                    'avg_vectors_per_case': 0,
                    'min_vectors_per_case': 0,
                    'max_vectors_per_case': 0,
                    'cases_with_vectors': 0
                }
                
        except Exception as e:
            logger.error(f"Error getting vector statistics: {e}")
            return {}
    
    def _identify_issues(
        self,
        expected: int,
        actual: SystemCounts,
        scores: Dict[str, float]
    ) -> List[str]:
        """Identify consistency issues"""
        issues = []
        
        for system, score in scores.items():
            if score < (1.0 - self.tolerance):
                count = actual.to_dict()[system]
                issues.append(f"{system.title()}: Expected ~{expected}, got {count} (score: {score:.1%})")
        
        return issues
    
    def _log_report(self, report: ConsistencyReport):
        """Log the consistency report"""
        status = "✅ PASSED" if report.passed else "❌ FAILED"
        
        logger.info(f"📊 Consistency Report for {report.batch_id}: {status}")
        logger.info(f"   Overall Score: {report.overall_score:.1%}")
        logger.info(f"   Expected Cases: {report.expected_cases}")
        
        for system, count in report.actual_counts.to_dict().items():
            score = report.consistency_scores[system]
            logger.info(f"   {system.title()}: {count} ({score:.1%})")
        
        if report.vector_stats:
            avg_vectors = report.vector_stats.get('avg_vectors_per_case', 0)
            logger.info(f"   Avg Vectors/Case: {avg_vectors:.1f}")
        
        if report.issues:
            logger.warning("   Issues found:")
            for issue in report.issues:
                logger.warning(f"     • {issue}")


# Convenience function
def create_cross_system_validator(
    supabase_client,
    gcs_client,
    pinecone_client,
    neo4j_client,
    **kwargs
) -> CrossSystemValidator:
    """Create a cross-system validator"""
    return CrossSystemValidator(
        supabase_client,
        gcs_client,
        pinecone_client,
        neo4j_client,
        **kwargs
    )
