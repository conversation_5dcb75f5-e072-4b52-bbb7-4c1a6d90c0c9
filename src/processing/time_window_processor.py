"""
Time Window Processor
Implements month-based time slicing with deep pagination fallback strategies
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any, AsyncGenerator
from dataclasses import dataclass
import calendar
import httpx
from urllib.parse import urlencode, parse_qs, urlparse

logger = logging.getLogger(__name__)


@dataclass
class TimeWindow:
    """Represents a time processing window"""
    start: str  # YYYY-MM-DD format
    end: str    # YYYY-MM-DD format
    year: int
    month: int
    
    def __str__(self):
        return f"{self.year}-{self.month:02d} ({self.start} to {self.end})"


@dataclass
class PaginationState:
    """Tracks pagination state for a time window"""
    cursor: Optional[str] = None
    page: Optional[int] = None
    last_id: Optional[int] = None
    next_url: Optional[str] = None
    total_fetched: int = 0
    exhausted: bool = False


class DeepPaginationError(Exception):
    """Raised when deep pagination limits are hit"""
    pass


class TimeWindowProcessor:
    """
    Processes data in time windows with fallback strategies for deep pagination
    """
    
    def __init__(
        self,
        session: httpx.AsyncClient,
        base_url: str,
        max_page_v3: int = 100,  # v3 API page limit
        window_size: str = "month"  # "month" or "day"
    ):
        self.session = session
        self.base_url = base_url
        self.max_page_v3 = max_page_v3
        self.window_size = window_size
    
    def generate_time_windows(
        self,
        start_year: int = 1994,
        end_year: int = 2025,
        start_month: int = 1,
        end_month: int = 12
    ) -> List[TimeWindow]:
        """
        Generate time windows for processing
        
        Args:
            start_year: Starting year
            end_year: Ending year (inclusive)
            start_month: Starting month (1-12)
            end_month: Ending month (1-12)
            
        Returns:
            List of TimeWindow objects
        """
        windows = []
        
        for year in range(start_year, end_year + 1):
            # Determine month range for this year
            first_month = start_month if year == start_year else 1
            last_month = end_month if year == end_year else 12
            
            for month in range(first_month, last_month + 1):
                # Calculate first and last day of month
                first_day = date(year, month, 1)
                last_day = date(year, month, calendar.monthrange(year, month)[1])
                
                window = TimeWindow(
                    start=first_day.strftime("%Y-%m-%d"),
                    end=last_day.strftime("%Y-%m-%d"),
                    year=year,
                    month=month
                )
                windows.append(window)
        
        logger.info(f"Generated {len(windows)} time windows from {start_year}-{start_month:02d} to {end_year}-{end_month:02d}")
        return windows
    
    async def process_window(
        self,
        window: TimeWindow,
        court_ids: List[str],
        base_params: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a single time window with fallback strategies
        
        Args:
            window: Time window to process
            court_ids: List of court IDs to filter by
            base_params: Base parameters for API requests
            
        Yields:
            API response data dictionaries
        """
        logger.info(f"Processing time window: {window}")
        
        # Build parameters for this window
        params = base_params.copy()
        params.update({
            'court': court_ids,
            'date_filed__gte': window.start,
            'date_filed__lte': window.end,
            'ordering': 'id'  # Consistent ordering for id__gt fallback
        })
        
        try:
            # Try cursor-based pagination first (v4 API)
            async for data in self._process_with_cursor(params):
                yield data
                
        except DeepPaginationError as e:
            logger.warning(f"Deep pagination limit hit for {window}: {e}")
            
            # Fallback to id__gt bisection
            async for data in self._process_with_id_gt(params, window):
                yield data
    
    async def _process_with_cursor(
        self,
        params: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process using cursor-based pagination (v4 API)
        
        Args:
            params: API request parameters
            
        Yields:
            API response data dictionaries
        """
        url = f"{self.base_url}/opinions/"
        pagination_state = PaginationState()
        request_count = 0
        
        while not pagination_state.exhausted:
            # Build request parameters
            request_params = params.copy()
            request_params['page_size'] = 100
            
            if pagination_state.cursor:
                request_params['cursor'] = pagination_state.cursor
            
            # Make API request
            logger.debug(f"Cursor request {request_count + 1}: {url}")
            response = await self.session.get(url, params=request_params)
            response.raise_for_status()
            
            data = response.json()
            results = data.get('results', [])
            
            if not results:
                logger.debug("No more results - cursor pagination complete")
                break
            
            pagination_state.total_fetched += len(results)
            request_count += 1
            
            # Yield the data
            yield data
            
            # Update pagination state
            next_url = data.get('next')
            if next_url:
                # Ensure page_size=100 is preserved
                if 'page_size=' not in next_url:
                    separator = '&' if '?' in next_url else '?'
                    next_url = f"{next_url}{separator}page_size=100"
                
                # Extract cursor from next URL
                parsed_url = urlparse(next_url)
                query_params = parse_qs(parsed_url.query)
                pagination_state.cursor = query_params.get('cursor', [None])[0]
                pagination_state.next_url = next_url
                
                # Check for potential v3 page limit (heuristic)
                page_param = query_params.get('page', [None])[0]
                if page_param and int(page_param) >= self.max_page_v3:
                    raise DeepPaginationError(f"Approaching v3 page limit: page={page_param}")
            else:
                pagination_state.exhausted = True
        
        logger.info(f"Cursor pagination complete: {pagination_state.total_fetched} items in {request_count} requests")
    
    async def _process_with_id_gt(
        self,
        params: Dict[str, Any],
        window: TimeWindow
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process using id__gt fallback for deep pagination
        
        Args:
            params: API request parameters
            window: Time window being processed
            
        Yields:
            API response data dictionaries
        """
        logger.info(f"Using id__gt fallback for window {window}")
        
        url = f"{self.base_url}/opinions/"
        last_id = 0
        total_fetched = 0
        request_count = 0
        
        while True:
            # Build request parameters with id__gt
            request_params = params.copy()
            request_params.update({
                'page_size': 100,
                'id__gt': last_id,
                'ordering': 'id'  # Ensure consistent ordering
            })
            
            # Remove cursor if present (not compatible with id__gt)
            request_params.pop('cursor', None)
            
            logger.debug(f"id__gt request {request_count + 1}: id__gt={last_id}")
            response = await self.session.get(url, params=request_params)
            response.raise_for_status()
            
            data = response.json()
            results = data.get('results', [])
            
            if not results:
                logger.debug("No more results - id__gt pagination complete")
                break
            
            total_fetched += len(results)
            request_count += 1
            
            # Update last_id for next iteration
            last_id = max(result.get('id', 0) for result in results)
            
            # Yield the data
            yield data
            
            # Safety check - if we get less than page_size, we're probably done
            if len(results) < 100:
                logger.debug(f"Received {len(results)} < 100 results, assuming end of data")
                break
        
        logger.info(f"id__gt pagination complete: {total_fetched} items in {request_count} requests")
    
    def split_window_by_date(self, window: TimeWindow, split_count: int = 2) -> List[TimeWindow]:
        """
        Split a time window into smaller windows for bisection
        
        Args:
            window: Window to split
            split_count: Number of sub-windows to create
            
        Returns:
            List of smaller TimeWindow objects
        """
        start_date = datetime.strptime(window.start, "%Y-%m-%d").date()
        end_date = datetime.strptime(window.end, "%Y-%m-%d").date()
        
        total_days = (end_date - start_date).days + 1
        days_per_split = max(1, total_days // split_count)
        
        sub_windows = []
        current_start = start_date
        
        for i in range(split_count):
            if i == split_count - 1:
                # Last split gets remaining days
                current_end = end_date
            else:
                current_end = current_start + timedelta(days=days_per_split - 1)
                current_end = min(current_end, end_date)
            
            sub_window = TimeWindow(
                start=current_start.strftime("%Y-%m-%d"),
                end=current_end.strftime("%Y-%m-%d"),
                year=current_start.year,
                month=current_start.month
            )
            sub_windows.append(sub_window)
            
            current_start = current_end + timedelta(days=1)
            if current_start > end_date:
                break
        
        logger.debug(f"Split window {window} into {len(sub_windows)} sub-windows")
        return sub_windows
    
    async def estimate_window_size(
        self,
        window: TimeWindow,
        court_ids: List[str],
        base_params: Dict[str, Any]
    ) -> int:
        """
        Estimate the number of items in a time window
        
        Args:
            window: Time window to estimate
            court_ids: List of court IDs
            base_params: Base API parameters
            
        Returns:
            Estimated number of items
        """
        params = base_params.copy()
        params.update({
            'court': court_ids,
            'date_filed__gte': window.start,
            'date_filed__lte': window.end,
            'page_size': 1  # Just get count info
        })
        
        try:
            url = f"{self.base_url}/opinions/"
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            count = data.get('count', 0)
            
            logger.debug(f"Window {window} estimated size: {count}")
            return count
            
        except Exception as e:
            logger.warning(f"Failed to estimate window size for {window}: {e}")
            return 0
    
    def get_window_stats(self, windows: List[TimeWindow]) -> Dict[str, Any]:
        """
        Get statistics about time windows
        
        Args:
            windows: List of time windows
            
        Returns:
            Statistics dictionary
        """
        if not windows:
            return {}
        
        years = [w.year for w in windows]
        months = [w.month for w in windows]
        
        return {
            'total_windows': len(windows),
            'year_range': f"{min(years)}-{max(years)}",
            'months_covered': len(set(months)),
            'first_window': str(windows[0]),
            'last_window': str(windows[-1])
        }


# Convenience functions
def create_monthly_windows(start_year: int = 1994, end_year: int = 2025) -> List[TimeWindow]:
    """Create monthly time windows for the given year range"""
    processor = TimeWindowProcessor(None, "")  # Dummy processor for window generation
    return processor.generate_time_windows(start_year, end_year)


if __name__ == "__main__":
    # Test time window generation
    processor = TimeWindowProcessor(None, "")
    
    # Generate test windows
    windows = processor.generate_time_windows(2024, 2024, 1, 3)  # Jan-Mar 2024
    
    print(f"Generated {len(windows)} windows:")
    for window in windows:
        print(f"  {window}")
    
    # Test window splitting
    if windows:
        sub_windows = processor.split_window_by_date(windows[0], 4)
        print(f"\nSplit {windows[0]} into:")
        for sub_window in sub_windows:
            print(f"  {sub_window}")
    
    # Get stats
    stats = processor.get_window_stats(windows)
    print(f"\nWindow stats: {stats}")
