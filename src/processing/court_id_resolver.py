"""
CourtListener Court ID Resolver
Dynamically fetches real Texas court IDs from the /courts/ API
"""

import logging
import asyncio
from typing import List, Dict, Optional
import httpx
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class CourtIDResolver:
    """Resolves court IDs dynamically from CourtListener API"""
    
    def __init__(self, api_key: str, base_url: str = "https://www.courtlistener.com/api/rest/v4"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = httpx.AsyncClient(
            headers={"Authorization": f"Token {api_key}"},
            timeout=30.0
        )
        self._cache = {}
        self._cache_expiry = None
        self._cache_duration = timedelta(hours=24)  # Cache for 24 hours
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.aclose()
    
    async def fetch_texas_court_ids(self, include_federal: bool = True) -> List[str]:
        """
        Fetch real TX court IDs from /courts/ API with state_abbrev=="TX"
        
        Args:
            include_federal: Whether to include ca5 federal circuit court
            
        Returns:
            List of Texas court IDs
        """
        # Check cache first
        if self._is_cache_valid():
            logger.info("Using cached Texas court IDs")
            return self._cache.get('texas_courts', [])
        
        logger.info("Fetching Texas court IDs from CourtListener API...")
        court_ids = []
        url = f"{self.base_url}/courts/?page_size=100&format=json"
        page_count = 0
        
        try:
            while url and page_count < 50:  # Safety limit
                logger.debug(f"Fetching courts page {page_count + 1}: {url}")
                
                response = await self.session.get(url)
                response.raise_for_status()
                data = response.json()
                
                # Filter for Texas courts (state_abbrev == "TX")
                page_tx_courts = [
                    court['id'] for court in data.get('results', [])
                    if court.get('state_abbrev') == 'TX'
                ]
                
                court_ids.extend(page_tx_courts)
                logger.debug(f"Found {len(page_tx_courts)} TX courts on page {page_count + 1}")
                
                # Get next URL and ensure page_size=100 is preserved
                url = data.get('next')
                if url and 'page_size=' not in url:
                    separator = '&' if '?' in url else '?'
                    url = f"{url}{separator}page_size=100"
                
                page_count += 1
            
            # Add federal circuit court if requested
            if include_federal:
                court_ids.append('ca5')
                logger.debug("Added ca5 federal circuit court")
            
            # Remove duplicates and sort
            court_ids = sorted(list(set(court_ids)))
            
            # Cache the results
            self._cache['texas_courts'] = court_ids
            self._cache_expiry = datetime.now() + self._cache_duration
            
            logger.info(f"Successfully fetched {len(court_ids)} Texas court IDs")
            logger.debug(f"Texas court IDs: {court_ids}")
            
            return court_ids
            
        except Exception as e:
            logger.error(f"Error fetching Texas court IDs: {e}")
            # Return fallback IDs if API fails
            fallback_ids = self._get_fallback_texas_courts(include_federal)
            logger.warning(f"Using fallback court IDs: {fallback_ids}")
            return fallback_ids
    
    def _is_cache_valid(self) -> bool:
        """Check if cached court IDs are still valid"""
        return (
            self._cache_expiry is not None and 
            datetime.now() < self._cache_expiry and
            'texas_courts' in self._cache
        )
    
    def _get_fallback_texas_courts(self, include_federal: bool = True) -> List[str]:
        """
        Fallback Texas court IDs based on known patterns
        Used when API is unavailable
        """
        fallback_courts = [
            "tex",           # Supreme Court of Texas
            "texcrimapp",    # Texas Court of Criminal Appeals
            *[f"texapp{i}" for i in range(1, 15)],  # texapp1-14 (Courts of Appeals)
            "txnd", "txsd", "txed", "txwd",  # Federal district courts
        ]
        
        if include_federal:
            fallback_courts.append("ca5")  # Fifth Circuit Court of Appeals
        
        return fallback_courts
    
    async def validate_court_ids(self, court_ids: List[str]) -> Dict[str, bool]:
        """
        Validate that court IDs exist in CourtListener
        
        Args:
            court_ids: List of court IDs to validate
            
        Returns:
            Dict mapping court_id -> exists (bool)
        """
        validation_results = {}
        
        for court_id in court_ids:
            try:
                url = f"{self.base_url}/courts/{court_id}/"
                response = await self.session.get(url)
                validation_results[court_id] = response.status_code == 200
                
                if response.status_code != 200:
                    logger.warning(f"Court ID '{court_id}' not found (status: {response.status_code})")
                
            except Exception as e:
                logger.error(f"Error validating court ID '{court_id}': {e}")
                validation_results[court_id] = False
        
        valid_count = sum(validation_results.values())
        logger.info(f"Validated {valid_count}/{len(court_ids)} court IDs")
        
        return validation_results
    
    async def get_court_details(self, court_ids: List[str]) -> Dict[str, Dict]:
        """
        Get detailed information about courts
        
        Args:
            court_ids: List of court IDs to get details for
            
        Returns:
            Dict mapping court_id -> court details
        """
        court_details = {}
        
        for court_id in court_ids:
            try:
                url = f"{self.base_url}/courts/{court_id}/"
                response = await self.session.get(url)
                
                if response.status_code == 200:
                    court_details[court_id] = response.json()
                else:
                    logger.warning(f"Could not fetch details for court '{court_id}'")
                    
            except Exception as e:
                logger.error(f"Error fetching details for court '{court_id}': {e}")
        
        return court_details


# Convenience functions
async def get_texas_court_ids(api_key: str) -> List[str]:
    """Convenience function to get Texas court IDs"""
    async with CourtIDResolver(api_key) as resolver:
        return await resolver.fetch_texas_court_ids()


async def validate_texas_courts(api_key: str, court_ids: List[str]) -> Dict[str, bool]:
    """Convenience function to validate Texas court IDs"""
    async with CourtIDResolver(api_key) as resolver:
        return await resolver.validate_court_ids(court_ids)


if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not api_key:
        print("Error: COURTLISTENER_API_KEY not found in environment")
        exit(1)
    
    async def main():
        async with CourtIDResolver(api_key) as resolver:
            # Fetch Texas court IDs
            court_ids = await resolver.fetch_texas_court_ids()
            print(f"Found {len(court_ids)} Texas courts:")
            for court_id in court_ids:
                print(f"  - {court_id}")
            
            # Validate the IDs
            validation = await resolver.validate_court_ids(court_ids)
            invalid_courts = [cid for cid, valid in validation.items() if not valid]
            if invalid_courts:
                print(f"Invalid court IDs: {invalid_courts}")
            else:
                print("All court IDs validated successfully!")
    
    asyncio.run(main())
