#!/usr/bin/env python3
"""
Judge Extraction Service - Centralized judge metadata extraction for both CourtListener and CAP data
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class JudgeInfo:
    """Judge information extracted from case data"""
    name: str
    full_name: Optional[str] = None
    confidence: float = 0.0
    source: str = "unknown"
    court: Optional[str] = None
    role: Optional[str] = None  # "majority", "dissent", "concur", "presiding"
    extraction_method: str = "pattern"  # "pattern", "api", "metadata"
    metadata: Optional[Dict[str, Any]] = None  # Additional metadata from API


class CourtDataService:
    """Service for fetching and managing court data from CourtListener Courts API"""

    def __init__(self):
        self.courts_cache = {}
        self.base_url = "https://www.courtlistener.com/api/rest/v4"

    async def fetch_courts_data(self, session) -> Dict[str, Dict[str, Any]]:
        """Fetch comprehensive court data from Courts API with pagination"""
        if self.courts_cache:
            return self.courts_cache

        try:
            courts_url = f"{self.base_url}/courts/"
            page = 1
            total_courts = 0

            while True:
                params = {'page': page, 'page_size': 100, 'format': 'json'}

                response = await session.get(courts_url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    courts = data.get('results', [])

                    if not courts:  # No more courts
                        break

                    # Create court_id -> court_data mapping
                    for court in courts:
                        court_id = court.get('id')
                        if court_id:
                            self.courts_cache[court_id] = {
                                'id': court_id,
                                'short_name': court.get('short_name', ''),
                                'full_name': court.get('full_name', ''),
                                'jurisdiction': court.get('jurisdiction', ''),
                                'citation_string': court.get('citation_string', ''),
                                'url': court.get('url', ''),
                                'start_date': court.get('start_date', ''),
                                'position': court.get('position', 0)
                            }

                    total_courts += len(courts)
                    logger.info(f"Loaded page {page}: {len(courts)} courts (total: {total_courts})")

                    # Check if there are more pages
                    if not data.get('next'):
                        break

                    page += 1
                else:
                    logger.warning(f"Failed to fetch courts data page {page}: HTTP {response.status_code}")
                    break

            logger.info(f"Loaded {len(self.courts_cache)} courts from Courts API")

        except Exception as e:
            logger.error(f"Error fetching courts data: {e}")

        return self.courts_cache

    def get_court_info(self, court_id: str) -> Optional[Dict[str, Any]]:
        """Get court information by court ID"""
        return self.courts_cache.get(court_id)


class JudgeExtractionService:
    """Centralized service for extracting judge information from legal cases"""

    def __init__(self):
        self.court_service = CourtDataService()
        # Court hierarchy with geographic metadata
        self.court_hierarchy = {
            'scotus': {
                'type': 'supreme', 'level': 4, 'region': 'federal',
                'geography': {'state': 'DC', 'region': 'Federal', 'district': 'Supreme Court', 'court_level': 'federal_supreme'}
            },
            'ca1': {
                'type': 'circuit', 'level': 3, 'region': '1st_circuit',
                'geography': {'state': 'MA', 'region': 'Northeast', 'district': '1st Circuit', 'court_level': 'federal_circuit'}
            },
            'ca2': {
                'type': 'circuit', 'level': 3, 'region': '2nd_circuit',
                'geography': {'state': 'NY', 'region': 'Northeast', 'district': '2nd Circuit', 'court_level': 'federal_circuit'}
            },
            'ca5': {
                'type': 'circuit', 'level': 3, 'region': '5th_circuit',
                'geography': {'state': 'TX', 'region': 'South', 'district': '5th Circuit', 'court_level': 'federal_circuit'}
            },
            'ca9': {
                'type': 'circuit', 'level': 3, 'region': '9th_circuit',
                'geography': {'state': 'CA', 'region': 'West', 'district': '9th Circuit', 'court_level': 'federal_circuit'}
            },
            'txnd': {
                'type': 'district', 'level': 2, 'region': 'texas_north',
                'geography': {'state': 'TX', 'region': 'South', 'district': 'Northern District of Texas', 'court_level': 'federal_district'}
            },
            'txsd': {
                'type': 'district', 'level': 2, 'region': 'texas_south',
                'geography': {'state': 'TX', 'region': 'South', 'district': 'Southern District of Texas', 'court_level': 'federal_district'}
            },
            'nysd': {
                'type': 'district', 'level': 2, 'region': 'new_york_south',
                'geography': {'state': 'NY', 'region': 'Northeast', 'district': 'Southern District of New York', 'court_level': 'federal_district'}
            },
        }
        
        # Enhanced judge extraction patterns optimized for CourtListener text content
        self.judge_patterns = [
            # High-priority patterns for modern CourtListener content
            r'(?:^|\n)\s*([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+),?\s+(?:U\.S\.?\s+)?(?:District|Circuit|Magistrate)\s+Judge',
            r'(?:^|\n)\s*(?:The\s+Honorable\s+)?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+),?\s+(?:Chief\s+)?(?:Judge|Justice)',
            r'Before:?\s+(?:(?:Chief\s+)?(?:Judge|Justice)\s+)?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)',
            r'(?:Presiding\s+)?Judge:?\s+([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)',

            # Opinion authorship patterns
            r'(?:Judge|Justice)\s+([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)\s+(?:delivered|wrote|authored|held|ruled|found)',
            r'([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+),?\s+(?:U\.S\.?\s+)?(?:District|Circuit)\s+Judge,?\s+(?:delivered|wrote|held)',

            # Panel and multi-judge patterns
            r'Panel:?\s+(?:Judges?\s+)?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)',
            r'(?:^|\n)\s*([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)\s+and\s+([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+),?\s+(?:Judges?|Justices?)',

            # HTML-specific patterns (for HTML content)
            r'<author[^>]*>([^<]+)</author>',
            r'<judge[^>]*>([^<]+)</judge>',

            # Signature/conclusion patterns
            r'(?:^|\n)\s*([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)\s*,?\s*(?:U\.S\.?\s+)?(?:District|Circuit)\s+Judge\s*$',

            # Enhanced modern patterns for edge cases
            r'(?:^|\n)\s*([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*),?\s+(?:U\.S\.?\s+)?(?:District|Circuit|Magistrate)\s+Judge',
            r'(?:^|\n)\s*(?:Judge|Justice)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s*(?:,|\n|$)',

            # ENHANCED CAP historical patterns (pre-1994) - IMPROVED FOR METADATA EXTRACTION
            # Priority 1: Enhanced metadata author patterns (handles mixed case and variations)
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][a-z]*),\s+Circuit\s+[Jj]udge\.?',  # "PECK, Circuit judge" or "PECK, Circuit Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][a-z]*),\s+Chief\s+[Jj]udge\.?',  # "SWYGERT, Chief Judge" or "SWYGERT, Chief judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][a-z]*),\s+District\s+[Jj]udge\.?',  # "NIXON, District Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][a-z]*),\s+(?:Associate\s+)?[Jj]ustice\.?',  # "BRENNAN, Justice"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][a-z]*),\s+Senior\s+[Jj]udge\.?',  # "SMITH, Senior Judge"

            # Priority 2: Full name patterns with mixed case (1970s-1980s)
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+Circuit\s+[Jj]udge\.?',  # "WILLIAM H. REHNQUIST, Circuit Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+Chief\s+[Jj]udge\.?',  # "PATRICIA M. WALD, Chief Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+District\s+[Jj]udge\.?',  # "JOHN T. NIXON, District Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+(?:Associate\s+)?[Jj]ustice\.?',  # "SANDRA DAY O'CONNOR, Associate Justice"

            # Priority 3: Historical surname-only patterns (1940s-1950s)
            r'([A-Z][A-Z]+),\s+C\.?\s*J\.?',  # "HICKMAN, C. J."
            r'([A-Z][A-Z]+),\s+J\.?',  # "BRENNAN, J."
            r'([A-Z][A-Z]+),\s+Justice\.?',  # "BLACKMUN, Justice"
            r'([A-Z][A-Z]+),\s+Judge\.?',  # "JOHNSON, Judge"
        ]
    
    def extract_judges_from_courtlistener(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Extract judges from CourtListener API response data with enhanced text extraction"""
        judges = []

        # Method 1: Extract from API fields (often empty, but try anyway)
        api_judges = self._extract_from_courtlistener_api(case_data)
        judges.extend(api_judges)

        # Method 2: Enhanced multi-source text extraction
        text_judges = self._extract_from_multiple_text_sources(case_data)
        judges.extend(text_judges)

        return self._deduplicate_judges(judges)

    def extract_court_from_courtlistener(self, case_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract structured court information from CourtListener case data"""

        # Method 1: Extract from docket court information (most reliable)
        docket = case_data.get('docket')
        if docket:
            # If docket is a URL, we'd need to fetch it, but often it's embedded
            if isinstance(docket, dict):
                court_id = docket.get('court')
                if court_id:
                    court_info = self.court_service.get_court_info(court_id)
                    if court_info:
                        return court_info

        # Method 2: Extract from cluster docket court (MAIN PATH)
        cluster_data = case_data.get('cluster_data', {})
        if cluster_data:
            cluster_docket = cluster_data.get('docket')

            # Check if we have docket data embedded
            docket_data = case_data.get('_docket_data', {})
            if docket_data:
                court_url = docket_data.get('court', '')
                if court_url and isinstance(court_url, str):
                    # Extract court ID from URL like "https://www.courtlistener.com/api/rest/v4/courts/scotus/"
                    court_id = self._extract_court_id_from_url(court_url)
                    if court_id:
                        court_info = self.court_service.get_court_info(court_id)
                        if court_info:
                            return court_info

            # If no embedded docket data, try to parse from cluster docket URL
            elif isinstance(cluster_docket, str) and cluster_docket.startswith('http'):
                # We can't fetch it synchronously, but we can try to infer from the URL pattern
                # URL pattern: https://www.courtlistener.com/api/rest/v4/dockets/{id}/
                # The court info would need to be fetched async, so this is a limitation
                pass

        # Method 3: Try to infer from court field if present
        court_field = case_data.get('court')
        if court_field:
            if isinstance(court_field, str):
                # It might be a court ID
                court_info = self.court_service.get_court_info(court_field)
                if court_info:
                    return court_info
            elif isinstance(court_field, dict):
                # It might be embedded court data
                court_id = court_field.get('id')
                if court_id:
                    court_info = self.court_service.get_court_info(court_id)
                    if court_info:
                        return court_info

        return None

    def _extract_court_id_from_url(self, court_url: str) -> Optional[str]:
        """Extract court ID from court URL"""
        # URL pattern: https://www.courtlistener.com/api/rest/v4/courts/scotus/?format=json
        import re
        match = re.search(r'/courts/([^/?]+)', court_url)
        if match:
            return match.group(1)
        return None

    def _extract_from_multiple_text_sources(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Extract judges from multiple text sources in CourtListener data"""
        judges = []

        # Priority order of text sources (best to worst)
        text_sources = [
            ('plain_text', 1.0),      # Full opinion text - highest priority
            ('html', 0.95),           # HTML content - very good
            ('headnotes', 0.9),       # Case summaries - good for judge names
            ('syllabus', 0.85),       # Case overviews - good context
            ('summary', 0.8),         # Brief summaries - moderate
        ]

        for field_name, confidence_multiplier in text_sources:
            text_content = case_data.get(field_name, '')
            if text_content and len(text_content) > 50:  # Ensure substantial content

                # Extract judges from this text source
                field_judges = self._extract_from_text(text_content, source="courtlistener")

                # Adjust confidence based on source quality
                for judge in field_judges:
                    judge.confidence *= confidence_multiplier
                    judge.extraction_method = f"text_{field_name}"

                judges.extend(field_judges)

                # If we found high-confidence judges from a good source, we can be less aggressive with other sources
                if field_judges and field_name in ['plain_text', 'html'] and any(j.confidence > 0.8 for j in field_judges):
                    break

        # If still no judges found, try cluster data as fallback
        if not judges:
            cluster_judges = self._extract_from_cluster_data(case_data)
            judges.extend(cluster_judges)

        return judges
    
    def extract_judges_from_cap(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Extract judges from CAP (Case Law Access Project) data with enhanced metadata processing"""
        judges = []

        # PRIORITY 1: Enhanced metadata author extraction (highest accuracy)
        metadata = case_data.get('metadata', {})
        if metadata and 'author' in metadata:
            author = metadata['author']
            if author and author != 'PER CURIAM:':
                # Enhanced metadata author processing
                metadata_judges = self._extract_from_cap_metadata_author(author)
                judges.extend(metadata_judges)

        # PRIORITY 2: Text extraction from multiple sources
        text_sources = [
            case_data.get('text', ''),  # Direct text field
            case_data.get('casebody', {}).get('data', {}).get('text', ''),  # Legacy structure
            case_data.get('content', ''),  # Alternative field
        ]

        text = ''
        for source_text in text_sources:
            if source_text and len(source_text) > len(text):
                text = source_text

        if text:
            text_judges = self._extract_from_text(text, source="caselaw_access_project")
            judges.extend(text_judges)

        # PRIORITY 3: Extract from case name if present (lowest priority)
        case_name = case_data.get('name', '') or case_data.get('name_abbreviation', '')
        if case_name:
            name_judges = self._extract_from_case_name(case_name)
            judges.extend(name_judges)

        return self._deduplicate_judges(judges)

    def _extract_from_cap_metadata_author(self, author: str) -> List[JudgeInfo]:
        """Enhanced extraction from CAP metadata author field with specialized patterns"""
        judges = []

        # Clean up author field
        author_clean = author.replace(':', '').strip()
        if not author_clean:
            return judges

        # Enhanced patterns specifically for CAP metadata author field
        cap_metadata_patterns = [
            # High-confidence patterns for metadata author field
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+Circuit\s+[Jj]udge\.?$',  # "PECK, Circuit judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+Chief\s+[Jj]udge\.?$',  # "SWYGERT, Chief Judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+District\s+[Jj]udge\.?$',  # "EDWARDS, District Judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+(?:Associate\s+)?[Jj]ustice\.?$',  # "BRENNAN, Justice"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+Senior\s+[Jj]udge\.?$',  # "SMITH, Senior Judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z]*[a-z]*),\s+[Jj]udge\.?$',  # "JOHNSON, Judge"

            # Full name patterns (1970s-1980s)
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s+)*[A-Z][A-Z]+),\s+Circuit\s+[Jj]udge\.?$',  # "WILLIAM H. REHNQUIST, Circuit Judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s+)*[A-Z][A-Z]+),\s+Chief\s+[Jj]udge\.?$',  # "PATRICIA M. WALD, Chief Judge"
            r'^([A-Z][A-Z]+(?:\s+[A-Z]\.?\s+)*[A-Z][A-Z]+),\s+District\s+[Jj]udge\.?$',  # "JOHN T. NIXON, District Judge"

            # Abbreviated patterns
            r'^([A-Z][A-Z]+),\s+C\.?\s*J\.?$',  # "HICKMAN, C. J."
            r'^([A-Z][A-Z]+),\s+J\.?$',  # "BRENNAN, J."
        ]

        for pattern in cap_metadata_patterns:
            matches = re.findall(pattern, author_clean, re.IGNORECASE)
            for match in matches:
                judge_name = match.strip()
                if judge_name and not self._is_false_positive(judge_name):
                    # Higher confidence for metadata author field
                    confidence = self._calculate_confidence(judge_name, pattern, "metadata_author")
                    confidence += 0.15  # Bonus for metadata source

                    judge = JudgeInfo(
                        name=judge_name,
                        full_name=judge_name if len(judge_name.split()) > 1 else None,
                        confidence=min(confidence, 0.95),  # Cap at 0.95
                        source="caselaw_access_project",
                        extraction_method="metadata_author",
                        metadata={'pattern': pattern, 'source_field': 'metadata.author'}
                    )
                    judges.append(judge)

        return judges
    
    def create_judge_metadata(self, judges: List[JudgeInfo], case_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create structured judge metadata for storage"""
        if not judges:
            return None
        
        metadata = {
            'judges': [],
            'primary_judge': None,
            'extraction_stats': {
                'total_extracted': len(judges),
                'avg_confidence': sum(j.confidence for j in judges) / len(judges),
                'sources': list(set(j.source for j in judges)),
                'methods': list(set(j.extraction_method for j in judges))
            }
        }
        
        # Process each judge
        for judge in judges:
            # Get geographic information from court hierarchy
            court_info = self.court_hierarchy.get(case_data.get('court_id', ''), {})
            geography = court_info.get('geography', {})

            judge_data = {
                'name': judge.name,
                'full_name': judge.full_name,
                'confidence': judge.confidence,
                'source': judge.source,
                'court': judge.court,
                'role': judge.role,
                'extraction_method': judge.extraction_method,
                'geography': geography if geography else None
            }
            metadata['judges'].append(judge_data)
        
        # Set primary judge (highest confidence)
        if judges:
            primary = max(judges, key=lambda j: j.confidence)
            metadata['primary_judge'] = primary.name
        
        return metadata
    
    def validate_judge_extraction(self, judges: List[JudgeInfo]) -> bool:
        """Validate extracted judge information"""
        if not judges:
            return False

        # Check if at least one judge has reasonable confidence
        # Use different thresholds for different sources
        has_confident_judge = any(
            (j.confidence >= 0.4 and j.source == 'caselaw_access_project') or
            (j.confidence >= 0.5 and j.source != 'caselaw_access_project')
            for j in judges
        )

        # Check if names are reasonable (allow single surnames for historical data)
        valid_names = all(
            1 <= len(j.name.split()) <= 5 and  # Allow single names
            len(j.name.strip()) >= 2 and      # At least 2 characters
            j.name.strip().replace(' ', '').replace('.', '').replace(',', '').isalpha() and  # Only letters/punctuation
            not self._is_false_positive(j.name)  # Filter out false positives
            for j in judges
        )

        return has_confident_judge and valid_names

    def _is_false_positive(self, name: str) -> bool:
        """Check if extracted name is likely a false positive"""
        name_lower = name.lower().strip()

        # Common false positives
        false_positives = {
            'was stayed', 'stayed', 'denied', 'granted', 'dismissed', 'affirmed', 'reversed',
            'court', 'case', 'opinion', 'decision', 'order', 'motion', 'appeal', 'judgment',
            'plaintiff', 'defendant', 'petitioner', 'respondent', 'appellant', 'appellee',
            'united states', 'state', 'federal', 'district', 'circuit', 'supreme',
            'per curiam', 'curiam', 'memorandum', 'memo', 'brief', 'filing',
            'before', 'after', 'during', 'within', 'without', 'against', 'between',
            # Common words that get misidentified
            'the', 'and', 'or', 'but', 'for', 'with', 'from', 'this', 'that', 'these', 'those',
            'said', 'held', 'found', 'ruled', 'ordered', 'stated', 'noted', 'concluded',
            'section', 'rule', 'law', 'act', 'code', 'title', 'chapter', 'part',
            'first', 'second', 'third', 'last', 'next', 'prior', 'following', 'above', 'below'
        }

        # Check exact matches
        if name_lower in false_positives:
            return True

        # Check patterns that indicate false positives
        false_positive_patterns = [
            r'^and\s+[A-Z]+$',  # "and ANDERSON", "and FEDERICO"
            r'^or\s+[A-Z]+$',   # "or SMITH"
            r'^but\s+[A-Z]+$',  # "but JONES"
            r'^with\s+[A-Z]+$', # "with BROWN"
            r'^\d+',            # Names starting with numbers
            r'^[a-z]',          # Names starting with lowercase (likely not names)
        ]

        import re
        for pattern in false_positive_patterns:
            if re.match(pattern, name, re.IGNORECASE):
                return True

        return False

    def _extract_courtlistener_fallback(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Fallback extraction for CourtListener cases with no judges found"""
        judges = []

        # Try extracting from case name if it contains judge information
        case_name = case_data.get('case_name', '') or case_data.get('caseName', '')
        if case_name:
            name_judges = self._extract_from_case_name(case_name)
            judges.extend(name_judges)

        # Try extracting from any text fields available
        text_fields = ['summary', 'syllabus', 'headnotes', 'per_curiam']
        for field in text_fields:
            if field in case_data and case_data[field]:
                field_judges = self._extract_from_text(case_data[field], source="courtlistener")
                judges.extend(field_judges)

        # If still no judges, try a more aggressive pattern search in HTML
        if not judges and 'html' in case_data:
            html_content = case_data['html']
            if html_content:
                # Remove HTML tags for cleaner text extraction
                import re
                clean_text = re.sub(r'<[^>]+>', ' ', html_content)
                clean_text = re.sub(r'\s+', ' ', clean_text)  # Normalize whitespace

                # Try more aggressive patterns
                aggressive_judges = self._extract_aggressive_patterns(clean_text)
                judges.extend(aggressive_judges)

        return judges

    def _extract_aggressive_patterns(self, text: str) -> List[JudgeInfo]:
        """More aggressive pattern matching for difficult cases"""
        judges = []
        seen_names = set()

        # Conservative aggressive patterns - only look for likely judge names
        aggressive_patterns = [
            r'(?:Judge|Justice)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)',  # "Judge John A. Smith"
            r'([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)(?:\s+(?:ruled|held|found|ordered))',  # "John A. Smith ruled"
            r'(?:^|\n)\s*([A-Z][a-z]+\s+[A-Z][a-z]+)\s*,\s*(?:Judge|Justice)',  # "John Smith, Judge"
        ]

        import re
        for i, pattern in enumerate(aggressive_patterns):
            matches = re.finditer(pattern, text[:3000])  # Search first 3000 chars
            for match in matches:
                judge_name = match.group(1)
                if (judge_name and
                    judge_name not in seen_names and
                    not self._is_false_positive(judge_name) and
                    len(judge_name.split()) >= 2):  # Require at least first + last name

                    # Conservative confidence for aggressive patterns
                    confidence = 0.5 - (i * 0.05)  # Slight decrease for later patterns

                    if confidence > 0.3:  # Higher minimum threshold
                        judge = JudgeInfo(
                            name=judge_name.strip(),
                            full_name=judge_name.strip(),
                            confidence=confidence,
                            source="courtlistener",
                            extraction_method="aggressive_pattern"
                        )
                        judges.append(judge)
                        seen_names.add(judge_name)

        return judges[:1]  # Limit to 1 judge for aggressive patterns

    def _extract_from_courtlistener_api(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Extract judges from CourtListener API fields with enhanced People API integration"""
        judges = []

        # Method 1: Extract from author URL (HIGH ACCURACY - structured data)
        author_url = case_data.get('author')
        if author_url and isinstance(author_url, str) and author_url.startswith('http'):
            author_judge = self._extract_from_author_url(author_url, case_data)
            if author_judge:
                judges.append(author_judge)

        # Method 2: Extract from direct opinion fields (often empty, but try anyway)
        author_str = case_data.get('author_str', '')
        if author_str and not judges:  # Only if we didn't get from author URL
            judge = JudgeInfo(
                name=author_str.strip(),
                full_name=author_str.strip(),
                confidence=0.9,  # High confidence for API data
                source="courtlistener",
                extraction_method="api_author_str"
            )
            judges.append(judge)

        # Method 3: Extract from cluster data (fallback)
        if not judges:
            cluster_judges = self._extract_from_cluster_data(case_data)
            judges.extend(cluster_judges)

        return judges

    def _extract_from_author_url(self, author_url: str, case_data: Dict[str, Any]) -> JudgeInfo:
        """Extract judge from People API using author URL - SYNCHRONOUS VERSION"""
        # Note: This is a synchronous version. For production, we'd want async.
        # For now, we'll store the URL and process it later, or use a cached approach.

        # Check if we have cached author data
        author_data = case_data.get('_author_data')  # Cached from async fetch
        if author_data:
            return self._create_judge_from_people_data(author_data)

        # If no cached data, we'll need to fetch it asynchronously
        # For now, return None and rely on other methods
        return None

    def _create_judge_from_people_data(self, person_data: Dict[str, Any]) -> JudgeInfo:
        """Create JudgeInfo from People API data"""
        # Construct full name from first + last (since name_full is empty)
        name_first = person_data.get('name_first', '').strip()
        name_last = person_data.get('name_last', '').strip()
        name_middle = person_data.get('name_middle', '').strip()

        if name_first and name_last:
            if name_middle:
                full_name = f"{name_first} {name_middle} {name_last}"
            else:
                full_name = f"{name_first} {name_last}"

            # Get position information for context
            positions = person_data.get('positions', [])
            court_info = ""
            if positions:
                # Get the first position for context
                pos = positions[0] if isinstance(positions[0], dict) else {}
                court = pos.get('court', {})
                if isinstance(court, dict):
                    court_info = court.get('short_name', '')

            judge = JudgeInfo(
                name=full_name,
                full_name=full_name,
                confidence=0.95,  # Very high confidence for People API data
                source="courtlistener",
                extraction_method="api_people",
                metadata={
                    'person_id': person_data.get('id'),
                    'court': court_info,
                    'positions': len(positions)
                }
            )
            return judge

        return None

    def _extract_from_cluster_data(self, case_data: Dict[str, Any]) -> List[JudgeInfo]:
        """Extract judges from cluster data - often contains structured judge information"""
        judges = []

        # Check if cluster data is already embedded in the case data
        cluster_data = case_data.get('cluster_data', {})
        if cluster_data:
            # Cluster data is embedded
            cluster_judges = cluster_data.get('judges', '')
            if cluster_judges and isinstance(cluster_judges, str):
                # Parse judge names from cluster judges field
                judge_names = self._parse_cluster_judges_field(cluster_judges)
                for name in judge_names:
                    judge = JudgeInfo(
                        name=name.strip(),
                        full_name=name.strip(),
                        confidence=0.95,  # Very high confidence for cluster API data
                        source="courtlistener",
                        extraction_method="api_cluster"
                    )
                    judges.append(judge)

        # Note: For async cluster fetching, we would need to modify the architecture
        # For now, we'll enhance the existing processors to fetch cluster data

        return judges

    def _parse_cluster_judges_field(self, judges_field: str) -> List[str]:
        """Parse judge names from cluster judges field"""
        if not judges_field or not isinstance(judges_field, str):
            return []

        # Handle different formats:
        # "Sarah Weyland Ellis" (single judge)
        # "Robert B. Gordon, Kenneth W. Salinger" (multiple judges)
        # "Panel: John Doe, Jane Smith" (panel format)

        # Clean up the field
        judges_clean = judges_field.strip()

        # Remove common prefixes
        prefixes_to_remove = ['Panel:', 'Judges:', 'Before:', 'The Honorable']
        for prefix in prefixes_to_remove:
            if judges_clean.startswith(prefix):
                judges_clean = judges_clean[len(prefix):].strip()

        # Split by common separators
        if ',' in judges_clean:
            judge_names = [name.strip() for name in judges_clean.split(',')]
        elif ';' in judges_clean:
            judge_names = [name.strip() for name in judges_clean.split(';')]
        elif ' and ' in judges_clean:
            judge_names = [name.strip() for name in judges_clean.split(' and ')]
        else:
            # Single judge
            judge_names = [judges_clean.strip()]

        # Filter out empty names and validate
        valid_names = []
        for name in judge_names:
            if name and len(name) > 2 and not self._is_false_positive(name):
                valid_names.append(name)

        return valid_names[:5]  # Limit to 5 judges
    
    def _extract_from_text(self, text: str, source: str = "unknown") -> List[JudgeInfo]:
        """Extract judge names from case text using patterns"""
        judges = []
        seen_names = set()
        
        # Search in first 3000 characters where judge names typically appear
        search_text = text[:3000]
        
        for i, pattern in enumerate(self.judge_patterns):
            try:
                matches = re.finditer(pattern, search_text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    # Handle multiple groups (for patterns with multiple judges)
                    for group_idx in range(1, len(match.groups()) + 1):
                        judge_name = match.group(group_idx)
                        if judge_name and judge_name not in seen_names:
                            # Calculate confidence based on pattern priority and characteristics
                            confidence = self._calculate_confidence(judge_name, i, pattern)
                            
                            if confidence >= 0.3:  # Minimum confidence threshold
                                judge = JudgeInfo(
                                    name=judge_name.strip(),
                                    full_name=judge_name.strip() if ' ' in judge_name else None,
                                    confidence=confidence,
                                    source=source,
                                    extraction_method="pattern"
                                )
                                judges.append(judge)
                                seen_names.add(judge_name)
            except re.error as e:
                logger.warning(f"Regex error in pattern {i}: {e}")
                continue
        
        return judges[:5]  # Limit to 5 judges per case
    
    def _extract_from_case_name(self, case_name: str) -> List[JudgeInfo]:
        """Extract judge names from case name if present"""
        judges = []
        
        # Pattern for "In re Judge [Name]" or similar
        patterns = [
            r'(?:In re|Matter of)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+v\.',
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, case_name, re.IGNORECASE)
            for match in matches:
                judge_name = match.group(1)
                if judge_name:
                    judge = JudgeInfo(
                        name=judge_name.strip(),
                        full_name=judge_name.strip(),
                        confidence=0.7,
                        source="case_name",
                        extraction_method="pattern"
                    )
                    judges.append(judge)
        
        return judges
    
    def _calculate_confidence(self, judge_name: str, pattern_index: int, pattern: str) -> float:
        """Calculate confidence score for extracted judge name"""
        confidence = 0.5  # Base confidence

        # Pattern priority (earlier patterns are more reliable)
        if pattern_index < 4:  # Top patterns
            confidence += 0.3
        elif pattern_index < 8:  # Good patterns
            confidence += 0.2
        else:  # Basic patterns
            confidence += 0.1

        # Full name bonus
        if ' ' in judge_name and len(judge_name.split()) >= 2:
            confidence += 0.2

        # Length penalty for very short or very long names
        word_count = len(judge_name.split())
        if word_count == 1:
            # For historical CAP data, single surnames are common and reliable
            if judge_name.isupper() and len(judge_name) > 3:  # Historical pattern like "BRENNAN"
                confidence += 0.1  # Bonus instead of penalty
            else:
                confidence -= 0.1
        elif word_count > 4:
            confidence -= 0.1

        # Historical pattern bonus (CAP data often uses all caps)
        if judge_name.isupper() and len(judge_name) > 3:
            # Check if it matches historical patterns
            if any(marker in pattern for marker in [', J.', ', Justice', ', Chief']):
                confidence += 0.2  # Historical judicial patterns are reliable
            else:
                confidence -= 0.05  # Small penalty for other all caps

        # False positive penalty
        if self._is_false_positive(judge_name):
            confidence -= 0.5  # Heavy penalty for false positives

        return min(1.0, max(0.3, confidence))  # Minimum confidence of 0.3 for valid patterns
    
    def _deduplicate_judges(self, judges: List[JudgeInfo]) -> List[JudgeInfo]:
        """Remove duplicate judges, keeping highest confidence"""
        if not judges:
            return []
        
        # Group by normalized name
        judge_groups = {}
        for judge in judges:
            normalized_name = judge.name.lower().strip()
            if normalized_name not in judge_groups:
                judge_groups[normalized_name] = []
            judge_groups[normalized_name].append(judge)
        
        # Keep highest confidence judge from each group
        deduplicated = []
        for group in judge_groups.values():
            best_judge = max(group, key=lambda j: j.confidence)
            deduplicated.append(best_judge)
        
        # Sort by confidence (highest first)
        return sorted(deduplicated, key=lambda j: j.confidence, reverse=True)

    def get_primary_judge_name(self, judges: List[JudgeInfo]) -> Optional[str]:
        """Get the primary judge name (highest confidence) for storage in judge_name field"""
        if not judges:
            return None

        primary_judge = max(judges, key=lambda j: j.confidence)
        # Use lower threshold for historical CAP data
        threshold = 0.4 if primary_judge.source == 'caselaw_access_project' else 0.5
        return primary_judge.name if primary_judge.confidence >= threshold else None

    def enhance_with_court_info(self, judges: List[JudgeInfo], court_id: str) -> List[JudgeInfo]:
        """Enhance judge information with court context"""
        court_info = self.court_hierarchy.get(court_id, {})

        for judge in judges:
            if not judge.court and court_info:
                judge.court = f"{court_info.get('type', 'unknown')} court"

        return judges

    def format_for_storage(self, judges: List[JudgeInfo], case_data: Dict[str, Any]) -> Tuple[Optional[str], Optional[Dict]]:
        """Format judge data for storage in database fields"""
        if not judges:
            return None, None

        # Get primary judge name for judge_name field
        primary_judge_name = self.get_primary_judge_name(judges)

        # Create judge metadata for judge_metadata field
        judge_metadata = self.create_judge_metadata(judges, case_data)

        return primary_judge_name, judge_metadata
