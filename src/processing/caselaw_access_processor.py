"""
Caselaw Access Project Data Processor

Processes the 6.7M case dataset from Caselaw Access Project, integrating it with
existing Court Listener data while maintaining consistency with Voyage embeddings.
"""

import os
import json
import gzip
import logging
import hashlib
import asyncio
from typing import Dict, List, Optional, Any, Generator, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from pathlib import Path
import re
import uuid
import time

from .storage.supabase_connector import SupabaseConnector
from .storage.vector_store import PineconeConnector
from .storage.neo4j_connector import Neo4jConnector
from .storage.gcs_connector import GCSConnector
from ..relationships.practice_area_classifier import PracticeAreaClassifier
from .graph.batch_community_detector import BatchCommunityDetector, BatchProcessingConfig
from ..graph.graph_sync import write_opinion

logger = logging.getLogger(__name__)

@dataclass
class CaselawDocument:
    """Represents a processed document from Caselaw Access Project"""
    id: str
    source: str
    added: datetime
    created: datetime
    author: Optional[str]
    license: str
    url: Optional[str]
    text: str
    
    # Derived fields
    case_name: Optional[str] = None
    jurisdiction: Optional[str] = None
    court: Optional[str] = None
    date_filed: Optional[datetime] = None
    docket_number: Optional[str] = None
    historical_era: Optional[str] = None
    content_hash: Optional[str] = None
    word_count: Optional[int] = None
    practice_area: Optional[str] = None
    temporal_authority_score: Optional[float] = None
    completeness_score: Optional[float] = None

    # Source-specific IDs
    court_listener_id: Optional[str] = None
    cap_id: Optional[str] = None

    # Additional metadata
    precedential_status: Optional[str] = None
    citation_count: Optional[int] = None
    judges: Optional[List[str]] = None
    parties: Optional[List[str]] = None
    citations: Optional[List] = None
    volume: Optional[str] = None
    reporter: Optional[str] = None
    page: Optional[str] = None

@dataclass
class ProcessingState:
    """Comprehensive processing state for checkpoints"""
    current_file_index: int = 0
    total_files: int = 0
    current_file_path: Optional[str] = None
    documents_processed_in_file: int = 0
    total_documents_in_file: int = 0
    current_batch_index: int = 0
    documents_in_current_batch: int = 0
    batch_size: int = 100
    total_documents_processed: int = 0
    successful_documents: int = 0
    failed_documents: int = 0
    duplicate_documents: int = 0
    estimated_total_documents: int = 6_700_000
    average_processing_time: float = 0.0
    documents_per_minute: float = 0.0
    estimated_completion_time: Optional[str] = None
    batch_id: Optional[str] = None
    last_successful_document_id: Optional[str] = None
    pinecone_vectors_created: int = 0
    neo4j_nodes_created: int = 0
    supabase_records_created: int = 0
    recent_errors: List[str] = None
    retry_count: int = 0
    failed_document_ids: set = None
    recent_document_ids: List[str] = None
    
    def __post_init__(self):
        if self.recent_errors is None:
            self.recent_errors = []
        if self.failed_document_ids is None:
            self.failed_document_ids = set()
        if self.recent_document_ids is None:
            self.recent_document_ids = []

class CaselawAccessProcessor:
    """
    Processes Caselaw Access Project data and integrates with existing pipeline.
    Maintains consistency with Voyage embeddings and existing database schema.
    """
    
    def __init__(self, data_dir: str = "data/caselaw_access_project"):
        """
        Initialize the processor with database connections.
        
        Args:
            data_dir: Directory containing Caselaw Access Project files
        """
        self.data_dir = Path(data_dir)
        self.batch_size = 100  # Process in batches for memory efficiency
        
        # Initialize database connections
        self.supabase = SupabaseConnector()
        self.pinecone = PineconeConnector()
        self.neo4j = Neo4jConnector()
        self.gcs = GCSConnector()
        
        # Initialize deduplication and analysis components
        self.deduplicator = AdvancedDeduplicator()
        self.temporal_analyzer = TemporalAnalyzer()
        self.practice_area_classifier = PracticeAreaClassifier()
        
        # Initialize checkpoint and recovery systems
        self.checkpoint_manager = RobustCheckpointManager()
        self.retry_manager = IntelligentRetryManager()
        self.processing_state = ProcessingState()
        
        # Performance tracking
        self.performance_monitor = PerformanceMonitor()
        
        # Initialize community detection system
        self.community_detector = None
        self.community_config = BatchProcessingConfig(
            batch_size=50000,  # 50k cases per batch
            max_memory_mb=8192,  # 8GB memory limit
            algorithms=["louvain", "leiden"],
            save_to_neo4j=True
        )
        
        logger.info(f"Initialized CaselawAccessProcessor with data directory: {self.data_dir}")
    
    def get_jsonl_files(self) -> List[Path]:
        """Get all JSONL files in the data directory."""
        files = list(self.data_dir.glob("cap_*.jsonl.gz"))
        files.sort()  # Process in order
        logger.info(f"Found {len(files)} JSONL files to process")
        return files
    
    def read_jsonl_file(self, file_path: Path) -> Generator[Dict, None, None]:
        """
        Read and parse a compressed JSONL file.
        
        Args:
            file_path: Path to the compressed JSONL file
            
        Yields:
            Parsed JSON documents
        """
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            yield json.loads(line)
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON decode error in {file_path}:{line_num}: {e}")
                            continue
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    def normalize_document(self, raw_doc: Dict) -> CaselawDocument:
        """
        Normalize a raw Caselaw Access Project document to our schema.
        
        Args:
            raw_doc: Raw document dictionary from JSONL
            
        Returns:
            Normalized CaselawDocument
        """
        # Extract metadata
        metadata = raw_doc.get('metadata', {})
        
        # Parse timestamps
        added = datetime.fromisoformat(raw_doc.get('added', '').replace('Z', '+00:00'))
        created = datetime.fromisoformat(raw_doc.get('created', '').replace('Z', '+00:00'))
        
        # Create base document
        doc = CaselawDocument(
            id=raw_doc.get('id', ''),
            source=raw_doc.get('source', 'Caselaw Access Project'),
            added=added,
            created=created,
            author=metadata.get('author'),
            license=metadata.get('license', 'Public Domain'),
            url=metadata.get('url'),
            text=raw_doc.get('text', '')
        )
        
        # Extract additional metadata from text
        self._extract_case_metadata(doc)
        
        # Calculate derived fields
        doc.content_hash = self._calculate_content_hash(doc.text)
        doc.word_count = len(doc.text.split())
        doc.historical_era = self._classify_historical_era(doc.date_filed)
        
        # Classify practice area
        doc.practice_area = self._classify_practice_area(doc)
        
        # Calculate scores
        doc.completeness_score = self._calculate_completeness_score(doc)
        doc.temporal_authority_score = self._calculate_temporal_authority_score(doc)
        
        return doc
    
    def _extract_case_metadata(self, doc: CaselawDocument) -> None:
        """
        Extract case metadata from the document text using pattern matching.
        
        Args:
            doc: Document to extract metadata from
        """
        text = doc.text
        
        # Extract case name (first line often contains case name)
        lines = text.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if ' v. ' in line or ' v ' in line:
                doc.case_name = line
                break
        
        # Extract court information
        court_patterns = [
            r'United States Court of Appeals, (.+)',
            r'United States District Court, (.+)',
            r'Supreme Court of (.+)',
            r'Court of Appeals of (.+)',
            r'Superior Court of (.+)',
            r'(\w+) Supreme Court'
        ]
        
        for pattern in court_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                doc.court = match.group(1).strip()
                break
        
        # Extract jurisdiction from court information
        if doc.court:
            doc.jurisdiction = self._extract_jurisdiction_from_court(doc.court)

        # Extract date filed
        date_patterns = [
            r'(\w+ \d{1,2}, \d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{4}-\d{2}-\d{2})'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text[:1000])  # Search first 1000 chars
            if matches:
                try:
                    # Try to parse the first match
                    date_str = matches[0]
                    doc.date_filed = self._parse_date(date_str)
                    break
                except:
                    continue
        
        # Extract docket number
        docket_patterns = [
            r'No\. ([A-Z0-9-]+)',
            r'Docket No\. ([A-Z0-9-]+)',
            r'Case No\. ([A-Z0-9-]+)'
        ]
        
        for pattern in docket_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                doc.docket_number = match.group(1)
                break
    
    def _extract_jurisdiction_from_court(self, court: str) -> str:
        """Extract jurisdiction code from court name."""
        court_lower = court.lower()

        # Federal courts
        if 'united states' in court_lower or 'federal' in court_lower:
            return 'fed'
        
        # State mappings
        state_mappings = {
            'texas': 'tx',
            'california': 'ca',
            'new york': 'ny',
            'florida': 'fl',
            'ohio': 'oh',
            'illinois': 'il',
            'pennsylvania': 'pa',
            'michigan': 'mi',
            'georgia': 'ga',
            'north carolina': 'nc',
            'new jersey': 'nj',
            'virginia': 'va',
            'washington': 'wa',
            'arizona': 'az',
            'massachusetts': 'ma',
            'tennessee': 'tn',
            'indiana': 'in',
            'missouri': 'mo',
            'maryland': 'md',
            'wisconsin': 'wi',
            'colorado': 'co',
            'minnesota': 'mn',
            'south carolina': 'sc',
            'alabama': 'al',
            'louisiana': 'la',
            'kentucky': 'ky',
            'oregon': 'or',
            'oklahoma': 'ok',
            'connecticut': 'ct',
            'utah': 'ut',
            'iowa': 'ia',
            'nevada': 'nv',
            'arkansas': 'ar',
            'mississippi': 'ms',
            'kansas': 'ks',
            'new mexico': 'nm',
            'nebraska': 'ne',
            'west virginia': 'wv',
            'idaho': 'id',
            'hawaii': 'hi',
            'new hampshire': 'nh',
            'maine': 'me',
            'rhode island': 'ri',
            'montana': 'mt',
            'delaware': 'de',
            'south dakota': 'sd',
            'north dakota': 'nd',
            'alaska': 'ak',
            'vermont': 'vt',
            'wyoming': 'wy'
        }
        
        for state, code in state_mappings.items():
            if state in court_lower:
                return code
        
        return 'unknown'
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse various date formats."""
        formats = [
            '%B %d, %Y',    # January 1, 2000
            '%b %d, %Y',    # Jan 1, 2000
            '%m/%d/%Y',     # 01/01/2000
            '%Y-%m-%d',     # 2000-01-01
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        raise ValueError(f"Could not parse date: {date_str}")
    
    def parse_caselaw_document(self, raw_data: Dict) -> CaselawDocument:
        """
        Parse raw Caselaw Access Project data into CaselawDocument format.
        
        Args:
            raw_data: Raw JSON data from Caselaw Access Project
            
        Returns:
            CaselawDocument instance
        """
        # Parse dates
        added_date = None
        created_date = None
        
        try:
            if raw_data.get('added'):
                added_date = datetime.fromisoformat(raw_data['added'].replace('Z', '+00:00'))
        except:
            added_date = datetime.now()
            
        try:
            if raw_data.get('created'):
                created_date = datetime.fromisoformat(raw_data['created'].replace('Z', '+00:00'))
        except:
            created_date = datetime.now()
        
        # Extract text
        text = raw_data.get('text', '')
        
        # Parse metadata
        metadata = raw_data.get('metadata', {})
        
        # Extract case information from text using regex patterns
        case_name = self._extract_case_name(text)
        court_info = self._extract_court_info(text)
        jurisdiction = self._extract_jurisdiction(text, metadata)
        date_filed = self._extract_date_filed(text)
        docket_number = self._extract_docket_number(text)
        
        # Create document
        doc = CaselawDocument(
            id=raw_data.get('id', str(uuid.uuid4())),
            source=raw_data.get('source', 'Caselaw Access Project'),
            added=added_date,
            created=created_date,
            author=court_info.get('name', ''),
            license='public',
            url=raw_data.get('url', ''),
            text=text,
            case_name=case_name,
            jurisdiction=jurisdiction,
            court=court_info.get('name', ''),
            date_filed=date_filed,
            docket_number=docket_number,
            historical_era=self._classify_historical_era(date_filed),
            content_hash=self._calculate_content_hash(text)
        )
        
        return doc
    
    def _extract_case_name(self, text: str) -> Optional[str]:
        """Extract case name from text using regex patterns."""
        # Common case name patterns
        patterns = [
            r'^(.+?)\s+v\.\s+(.+?)\s*\n',  # "Plaintiff v. Defendant" at start
            r'^(.+?)\s+vs\.\s+(.+?)\s*\n',  # "Plaintiff vs. Defendant" at start
            r'^\s*(.+?)\s+v\.\s+(.+?)\s*,',  # With comma after
            r'^\s*(.+?)\s+vs\.\s+(.+?)\s*,',  # With comma after
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if match:
                plaintiff = match.group(1).strip()
                defendant = match.group(2).strip()
                return f"{plaintiff} v. {defendant}"
        
        # Fallback: extract first line as potential case name
        lines = text.split('\n')
        if lines:
            first_line = lines[0].strip()
            if first_line and len(first_line) < 200:
                return first_line
        
        return None
    
    def _extract_court_info(self, text: str) -> Dict[str, str]:
        """Extract court information from text."""
        court_patterns = [
            r'(United States .+?Court.+?)',
            r'(Supreme Court of .+?)',
            r'(.+?Court of Appeals.+?)',
            r'(.+?District Court.+?)',
            r'(.+?Superior Court.+?)',
            r'(.+?Circuit Court.+?)',
        ]
        
        for pattern in court_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return {'name': match.group(1).strip()}
        
        return {'name': 'Unknown Court'}
    
    def _extract_jurisdiction(self, text: str, metadata: Dict) -> Optional[str]:
        """Extract jurisdiction from text and metadata."""
        # Check metadata first
        if metadata.get('jurisdiction'):
            return metadata['jurisdiction']
        
        # Look for jurisdiction indicators in text
        jurisdiction_patterns = [
            r'United States',
            r'Federal',
            r'California',
            r'Texas',
            r'New York',
            r'Florida',
            # Add more states as needed
        ]
        
        for pattern in jurisdiction_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                if pattern == 'United States' or pattern == 'Federal':
                    return 'us'
                elif pattern == 'California':
                    return 'ca'
                elif pattern == 'Texas':
                    return 'tx'
                elif pattern == 'New York':
                    return 'ny'
                elif pattern == 'Florida':
                    return 'fl'
        
        return None
    
    def _extract_date_filed(self, text: str) -> Optional[datetime]:
        """Extract filing date from text."""
        date_patterns = [
            r'Decided\s+(.+?)\.',
            r'Filed\s+(.+?)\.',
            r'Argued\s+(.+?)\.',
            r'(\w+\s+\d{1,2},\s+\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                date_str = match.group(1).strip()
                try:
                    return self._parse_date(date_str)
                except ValueError:
                    continue
        
        return None
    
    def _extract_docket_number(self, text: str) -> Optional[str]:
        """Extract docket number from text."""
        docket_patterns = [
            r'No\.\s+([A-Z0-9\-]+)',
            r'Case\s+No\.\s+([A-Z0-9\-]+)',
            r'Docket\s+No\.\s+([A-Z0-9\-]+)',
            r'([A-Z0-9]{2,4}-\d{2,4})',
        ]
        
        for pattern in docket_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _calculate_content_hash(self, text: str) -> str:
        """Calculate SHA-256 hash of document content."""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()
    
    def _classify_historical_era(self, date_filed: Optional[datetime]) -> str:
        """Classify document by historical era."""
        if not date_filed:
            return 'unknown'
        
        year = date_filed.year
        if year >= 2000:
            return 'modern'
        elif year >= 1950:
            return 'mid_century'
        elif year >= 1900:
            return 'early_modern'
        else:
            return 'historical'
    
    def _classify_practice_area(self, doc: CaselawDocument) -> str:
        """Classify document practice area using existing classifier."""
        try:
            # Use the existing practice area classifier with proper method name
            document_dict = {
                "text": doc.text,
                "content": doc.text,
                "title": getattr(doc, 'title', ''),
                "case_name": getattr(doc, 'case_name', '')
            }

            # Use the classify method that returns primary area with confidence
            primary_area, confidence, all_scores = self.practice_area_classifier.classify_with_confidence(document_dict)

            # Only use classification if confidence is reasonable
            if confidence > 0.2:  # Lowered threshold for better coverage
                logger.info(f"Classified document {doc.id} as '{primary_area}' with confidence {confidence:.3f}")
                return primary_area
            else:
                logger.info(f"Low confidence classification for document {doc.id}: '{primary_area}' ({confidence:.3f}), using 'general'")
                return 'general'

        except Exception as e:
            logger.warning(f"Error classifying practice area for document {doc.id}: {e}")
            return 'general'
    
    def _calculate_temporal_authority_score(self, doc: CaselawDocument) -> float:
        """Calculate temporal authority score based on document characteristics."""
        if not doc.date_filed:
            return 0.0
        
        # Base score starts at 1.0
        score = 1.0
        
        # Age factor (newer documents get slight boost)
        age_years = (datetime.now() - doc.date_filed).days / 365.25
        if age_years < 10:
            score += 0.1
        elif age_years > 50:
            score -= 0.1
        
        # Court level factor
        if doc.court:
            court_lower = doc.court.lower()
            if 'supreme' in court_lower:
                score += 0.3
            elif 'appeals' in court_lower:
                score += 0.2
            elif 'district' in court_lower:
                score += 0.1
        
        # Jurisdiction factor
        if doc.jurisdiction == 'fed':
            score += 0.2
        
        # Document quality factor
        if doc.completeness_score and doc.completeness_score > 0.8:
            score += 0.1
        
        return min(score, 2.0)  # Cap at 2.0
    
    def _determine_vector_namespace(self, doc: CaselawDocument) -> str:
        """Determine appropriate Pinecone namespace based on existing patterns."""
        jurisdiction = doc.jurisdiction or 'unknown'
        
        # Use practice area if available and confident
        if doc.practice_area and doc.practice_area != 'general':
            if doc.historical_era in ['modern', 'mid_century']:
                return f"{jurisdiction}-case-{doc.practice_area}-modern"
            else:
                return f"{jurisdiction}-case-{doc.practice_area}-historical"
        
        # Fallback to existing pattern: {jurisdiction}-case
        return f"{jurisdiction}-case"
    
    async def process_document(self, doc: CaselawDocument) -> bool:
        """
        Process a single document through the full pipeline with robust error handling.

        Args:
            doc: Document to process

        Returns:
            Success flag
        """
        try:
            # 1. Check for duplicates
            if await self.deduplicator.is_duplicate(doc):
                logger.info(f"Skipping duplicate document: {doc.id}")
                return False

            # 2. Generate embeddings using Voyage
            try:
                embeddings = await self._generate_embeddings(doc)
            except Exception as e:
                logger.error(f"Failed to generate embeddings for {doc.id}: {e}")
                embeddings = []  # Continue without embeddings

            # 3. Store in databases with individual error handling
            success_count = 0
            errors = []
            case_id = None

            # Supabase storage (most critical)
            try:
                case_id = await self._store_in_supabase(doc)
                success_count += 1
            except Exception as e:
                if "already exists" in str(e) or "23505" in str(e):
                    # This is a duplicate that slipped through - treat as success
                    logger.warning(f"Document {doc.id} already exists in database - treating as duplicate")
                    # Update our in-memory cache to prevent future attempts
                    case_id = self._generate_case_id(doc)
                    self.deduplicator.existing_ids.add(case_id)
                    if hasattr(doc, 'content_hash') and doc.content_hash:
                        self.deduplicator.existing_content_hashes.add(doc.content_hash)
                    return False  # Count as duplicate, not error
                else:
                    errors.append(f"Supabase: {e}")
                    logger.error(f"Supabase storage failed for {doc.id}: {e}")

            # If Supabase failed, generate case_id for other systems
            if not case_id:
                case_id = self._generate_case_id(doc)

            # Pinecone storage (important for search)
            try:
                await self._store_in_pinecone(doc, embeddings, case_id)
                success_count += 1
            except Exception as e:
                errors.append(f"Pinecone: {e}")
                logger.error(f"Pinecone storage failed for {doc.id}: {e}")

            # Neo4j storage (important for relationships)
            try:
                await self._store_in_neo4j(doc, case_id)
                success_count += 1
            except Exception as e:
                errors.append(f"Neo4j: {e}")
                logger.error(f"Neo4j storage failed for {doc.id}: {e}")

            # GCS storage (nice to have for full text)
            try:
                await self._store_in_gcs(doc, case_id)
                success_count += 1
            except Exception as e:
                errors.append(f"GCS: {e}")
                logger.error(f"GCS storage failed for {doc.id}: {e}")

            # Temporal analysis (optional)
            try:
                await self.temporal_analyzer.analyze_document(doc, case_id)
            except Exception as e:
                logger.warning(f"Temporal analysis failed for {doc.id}: {e}")

            # Determine overall success
            if success_count >= 2:  # At least 2 out of 4 systems succeeded
                if errors:
                    logger.warning(f"Partial success for {doc.id}: {success_count}/4 systems. Errors: {'; '.join(errors)}")
                else:
                    logger.info(f"Successfully processed document: {doc.id}")
                return True
            else:
                logger.error(f"Failed to process {doc.id}: only {success_count}/4 systems succeeded. Errors: {'; '.join(errors)}")
                return False

        except Exception as e:
            logger.error(f"Unexpected error processing document {doc.id}: {e}")
            return False
    
    async def _generate_embeddings(self, doc: CaselawDocument) -> List[List[float]]:
        """Generate Voyage embeddings for document chunks."""
        # Split text into chunks for embedding
        chunks = self._chunk_text(doc.text)
        embeddings = []
        
        for chunk in chunks:
            embedding = self.pinecone.generate_embedding(chunk)
            embeddings.append(embedding)
        
        return embeddings
    
    def _chunk_text(self, text: str, chunk_size: int = 1000) -> List[str]:
        """Split text into chunks for embedding."""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), chunk_size):
            chunk = ' '.join(words[i:i + chunk_size])
            chunks.append(chunk)
        
        return chunks
    
    async def _store_in_supabase(self, doc: CaselawDocument) -> str:
        """Store document in Supabase."""
        from datetime import datetime

        # Use the document's actual ID, don't force cap_ prefix
        case_id = doc.id.replace('/', '_')

        # Apply time window filter for CAP: 1950-12-31 ≤ date_filed ≤ 1993-12-31
        if doc.date_filed:
            try:
                if isinstance(doc.date_filed, str):
                    filed_date = datetime.fromisoformat(doc.date_filed.replace('Z', '+00:00'))
                else:
                    filed_date = doc.date_filed

                # Check if date falls within CAP historical window
                if filed_date.year < 1950 or filed_date.year > 1993:
                    logger.info(f"Skipping case {case_id}: date {filed_date.year} outside CAP window (1950-1993)")
                    return None

            except Exception as e:
                logger.warning(f"Could not parse date for case {case_id}: {e}")

        # Classify document type
        document_type = self._classify_document_type(doc)

        case_data = {
            'id': case_id,
            'case_name': doc.case_name or 'Unknown',
            'jurisdiction': doc.jurisdiction or 'unknown',
            'court': doc.court,
            'date_filed': doc.date_filed.isoformat() if doc.date_filed and hasattr(doc.date_filed, 'isoformat') else (doc.date_filed if doc.date_filed else None),
            'docket_number': doc.docket_number,
            'document_type': document_type,  # Add document type classification
            'source_window': 'historical',  # CAP handles historical era (1950-1993)
            'source': doc.source,
            'source_id': doc.id,
            'source_type': 'caselaw_access_project',
            'caselaw_access_id': doc.id,
            'license_info': doc.license,
            'source_url': doc.url,
            'historical_era': doc.historical_era,
            'word_count': doc.word_count,
            'completeness_score': self._calculate_completeness_score(doc),
            'document_quality': self._assess_document_quality(doc),
            'created_at': doc.created.isoformat() if doc.created and hasattr(doc.created, 'isoformat') else (doc.created if doc.created else datetime.now().isoformat()),
            'updated_at': datetime.now().isoformat()
        }
        
        result = self.supabase.client.table('cases').insert(case_data).execute()
        return case_id
    
    async def _store_in_pinecone(self, doc: CaselawDocument, embeddings: List[List[float]], case_id: str):
        """Store document embeddings in Pinecone with enhanced namespace strategy."""
        chunks = self._chunk_text(doc.text)
        
        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            vector_id = f"{case_id}_chunk_{i}"
            
            # Enhanced metadata with practice area (ensure no null values for Pinecone)
            metadata = {
                'case_id': case_id,
                'jurisdiction': doc.jurisdiction or 'unknown',
                'source': doc.source or 'unknown',
                'source_type': 'caselaw_access_project',
                'historical_era': doc.historical_era or 'unknown',
                'practice_area': doc.practice_area or 'general',
                'chunk_position': i,
                'text': chunk,
                'date_filed': doc.date_filed.isoformat() if doc.date_filed and hasattr(doc.date_filed, 'isoformat') else (doc.date_filed if doc.date_filed else 'unknown'),
                'court': doc.court or 'unknown',
                'doc_type': 'case',
                'temporal_authority_score': doc.temporal_authority_score or 0.0,
                'word_count': len(chunk.split())
            }
            
            success = self.pinecone.upsert_vector(vector_id, embedding, metadata)
            if not success:
                raise Exception(f"Failed to store vector {vector_id}")
            
            # Track vector creation
            self.processing_state.pinecone_vectors_created += 1
    
    async def _store_in_neo4j(self, doc: CaselawDocument, case_id: str):
        """Store document in Neo4j graph database using graph sync."""
        try:
            # Prepare opinion data for graph sync
            opinion_data = {
                'opinion_id': case_id,
                'court_id': self._map_court_to_standard_id(doc.court),
                'year_filed': doc.date_filed.year if doc.date_filed else None,
                'practice_areas': [doc.practice_area] if doc.practice_area else [],
                'citation': getattr(doc, 'citation', None),
                'docket_number': doc.docket_number,
                'gcs_uri': f"gs://texas-laws-cases/{case_id}.json",
                'case_name': doc.case_name,
                'date_filed': doc.date_filed.isoformat() if doc.date_filed and hasattr(doc.date_filed, 'isoformat') else str(doc.date_filed) if doc.date_filed else None,
                'source': doc.source,
                'source_window': 'historical'  # CAP is historical window
            }

            # Write to graph using new sync module
            write_opinion(opinion_data)

            # Track node creation
            self.processing_state.neo4j_nodes_created += 1

        except Exception as e:
            logger.error(f"Failed to sync opinion {case_id} to Neo4j: {e}")
            # Don't fail the entire processing for graph sync issues

    def _map_court_to_standard_id(self, court_info):
        """Map CAP court info to standardized court ID."""
        if isinstance(court_info, dict):
            court_name = court_info.get('name', '').lower()
            if 'supreme' in court_name and 'texas' in court_name:
                return 'TXSC'
            elif 'criminal appeals' in court_name and 'texas' in court_name:
                return 'TCCA'
        elif isinstance(court_info, str):
            court_name = court_info.lower()
            if 'supreme' in court_name and 'texas' in court_name:
                return 'TXSC'
            elif 'criminal appeals' in court_name and 'texas' in court_name:
                return 'TCCA'

        # Default to TXSC for CAP data
        return 'TXSC'
    
    async def _create_practice_area_relationships(self, case_id: str, practice_area: str):
        """Create relationships to practice area nodes."""
        if not practice_area or practice_area == 'general':
            return
        
        # Create or find practice area node and link document
        practice_area_query = """
        MERGE (pa:PracticeArea {name: $practice_area})
        SET pa.created_at = CASE WHEN pa.created_at IS NULL THEN datetime() ELSE pa.created_at END
        WITH pa
        MATCH (d:Document {id: $case_id})
        MERGE (d)-[:BELONGS_TO_PRACTICE_AREA]->(pa)
        """
        
        with self.neo4j.driver.session() as session:
            session.run(practice_area_query, {
                'practice_area': practice_area,
                'case_id': case_id
            })
    
    async def _store_in_gcs(self, doc: CaselawDocument, case_id: str):
        """Store full document text in Google Cloud Storage."""
        # Use appropriate path based on source
        if doc.source == 'court_listener':
            blob_name = f"court_listener/{doc.jurisdiction}/{case_id}.txt"
        else:
            # Default to caselaw_access path for CAP documents
            blob_name = f"caselaw_access/{doc.historical_era}/{case_id}.txt"
        self.gcs.store_text(doc.text, blob_name)
    
    def _calculate_completeness_score(self, doc: CaselawDocument) -> float:
        """Calculate completeness score based on available metadata."""
        score = 0.0
        total_fields = 7
        
        if doc.case_name: score += 1
        if doc.jurisdiction: score += 1
        if doc.court: score += 1
        if doc.date_filed: score += 1
        if doc.docket_number: score += 1
        if doc.author: score += 1
        if doc.text and len(doc.text) > 100: score += 1
        
        return score / total_fields
    
    def _assess_document_quality(self, doc: CaselawDocument) -> str:
        """Assess document quality based on content analysis."""
        if not doc.text or len(doc.text) < 100:
            return 'poor'
        
        # Check for OCR errors and completeness
        completeness = self._calculate_completeness_score(doc)
        
        if completeness >= 0.8:
            return 'excellent'
        elif completeness >= 0.6:
            return 'good'
        elif completeness >= 0.4:
            return 'fair'
        else:
            return 'poor'
    
    async def process_batch(self, documents: List[CaselawDocument]) -> Dict[str, int]:
        """Process a batch of documents."""
        results = {'success': 0, 'failed': 0, 'duplicates': 0}
        
        for doc in documents:
            try:
                success = await self.process_document(doc)
                if success:
                    results['success'] += 1
                else:
                    results['duplicates'] += 1
            except Exception as e:
                logger.error(f"Error processing document {doc.id}: {e}")
                results['failed'] += 1
        
        return results
    
    def process_single_case(self, case_data: Dict) -> bool:
        """
        Process a single case from Court Listener or other sources.
        
        Args:
            case_data: Case data dictionary
            
        Returns:
            True if processed successfully, False if duplicate or error
        """
        try:
            # Convert case data to CaselawDocument format
            doc = self._convert_case_to_document(case_data)
            
            # Process synchronously for Court Listener integration
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                success = loop.run_until_complete(self.process_document(doc))
                return success
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"Error processing single case: {e}")
            return False
    
    def _convert_case_to_document(self, case_data: Dict) -> CaselawDocument:
        """Convert case data to CaselawDocument format."""
        # Extract text from opinions
        text_parts = []
        if case_data.get('opinions'):
            for opinion in case_data['opinions']:
                if isinstance(opinion, dict) and opinion.get('text'):
                    text_parts.append(opinion['text'])
                elif isinstance(opinion, str):
                    text_parts.append(opinion)
        
        text = ' '.join(text_parts) if text_parts else case_data.get('text', '')
        
        # Parse decision date
        decision_date = None
        if case_data.get('decision_date'):
            try:
                decision_date = datetime.fromisoformat(case_data['decision_date'].replace('Z', '+00:00'))
            except:
                pass
        
        # Handle both raw Court Listener format and transformed format
        # Get court name (handle both dict and string formats)
        court_name = ''
        if case_data.get('court'):
            court_data = case_data['court']
            if isinstance(court_data, dict):
                court_name = court_data.get('name', '')
            elif isinstance(court_data, str):
                court_name = court_data
        
        # Get jurisdiction name (handle both dict and string formats)  
        jurisdiction_name = ''
        if case_data.get('jurisdiction'):
            jurisdiction_data = case_data['jurisdiction']
            if isinstance(jurisdiction_data, dict):
                jurisdiction_name = jurisdiction_data.get('name', '')
            elif isinstance(jurisdiction_data, str):
                jurisdiction_name = jurisdiction_data
        
        # Create document
        doc = CaselawDocument(
            id=str(case_data.get('id', uuid.uuid4())),
            source=case_data.get('source', 'court_listener'),
            added=datetime.now(),
            created=decision_date or datetime.now(),
            author=court_name,
            license='public',
            url=case_data.get('url', ''),
            text=text,
            case_name=case_data.get('name', ''),
            jurisdiction=jurisdiction_name,
            court=court_name,
            date_filed=decision_date,
            docket_number=case_data.get('docket_number', ''),
            content_hash=hashlib.sha256(text.encode()).hexdigest()
        )
        
        return doc
    
    async def process_file(self, file_path: Path) -> Dict[str, int]:
        """Process a single JSONL file."""
        logger.info(f"Processing file: {file_path}")
        
        batch = []
        results = {'success': 0, 'failed': 0, 'duplicates': 0}
        
        for raw_doc in self.read_jsonl_file(file_path):
            try:
                doc = self.normalize_document(raw_doc)
                batch.append(doc)
                
                if len(batch) >= self.batch_size:
                    batch_results = await self.process_batch(batch)
                    for key in results:
                        results[key] += batch_results[key]
                    batch = []
                    
            except Exception as e:
                logger.error(f"Error normalizing document: {e}")
                results['failed'] += 1
        
        # Process remaining documents
        if batch:
            batch_results = await self.process_batch(batch)
            for key in results:
                results[key] += batch_results[key]
        
        logger.info(f"Completed processing {file_path}: {results}")
        return results

    async def process_file_with_filter(self, file_path: Path, jurisdiction_filter: str = None) -> Dict[str, int]:
        """Process a single JSONL file with optional jurisdiction filtering."""
        logger.info(f"Processing file: {file_path} (filter: {jurisdiction_filter})")

        batch = []
        results = {'processed': 0, 'success': 0, 'failed': 0, 'duplicates': 0}

        for raw_doc in self.read_jsonl_file(file_path):
            try:
                # Apply jurisdiction filter if specified
                if jurisdiction_filter:
                    doc_jurisdiction = raw_doc.get('jurisdiction', '').lower()
                    if jurisdiction_filter.lower() not in doc_jurisdiction:
                        continue

                doc = self.normalize_document(raw_doc)
                batch.append(doc)
                results['processed'] += 1

                if len(batch) >= self.batch_size:
                    batch_results = await self.process_batch(batch)
                    results['success'] += batch_results['success']
                    results['failed'] += batch_results['failed']
                    results['duplicates'] += batch_results['duplicates']
                    batch = []

            except Exception as e:
                logger.error(f"Error normalizing document: {e}")
                results['failed'] += 1

        # Process remaining documents
        if batch:
            batch_results = await self.process_batch(batch)
            results['success'] += batch_results['success']
            results['failed'] += batch_results['failed']
            results['duplicates'] += batch_results['duplicates']

        logger.info(f"File processing complete: {results}")
        return results

    async def process_court_listener_case(self, case_data: Dict[str, Any]) -> bool:
        """
        Process a single case from Court Listener API.

        Args:
            case_data: Case data dictionary from Court Listener

        Returns:
            True if processed successfully, False if duplicate or error
        """
        try:
            # Convert case data to CaselawDocument format
            doc = self._convert_case_to_document(case_data)

            # Process the document
            return await self.process_document(doc)

        except Exception as e:
            logger.error(f"Error processing Court Listener case: {e}")
            return False

    def _convert_case_to_document(self, case_data: Dict[str, Any]) -> CaselawDocument:
        """Convert Court Listener case data to CaselawDocument format."""
        try:
            # Extract basic information
            case_name = case_data.get('case_name', '')
            date_filed_str = case_data.get('date_filed', '')
            court = case_data.get('court', {})
            court_name = court.get('full_name', '') if isinstance(court, dict) else str(court)

            # Parse date
            date_filed = None
            if date_filed_str:
                try:
                    date_filed = datetime.fromisoformat(date_filed_str.replace('Z', '+00:00'))
                except:
                    pass

            # Create document with correct field names
            doc = CaselawDocument(
                id=str(case_data.get('id', '')),
                source='court_listener',
                added=datetime.now(),
                created=date_filed or datetime.now(),
                author=None,
                license='public_domain',
                url=case_data.get('absolute_url', ''),
                text=case_data.get('plain_text', ''),
                case_name=case_name,
                jurisdiction=case_data.get('jurisdiction', ''),
                court=court_name,
                date_filed=date_filed,
                docket_number=case_data.get('docket_number', ''),
                content_hash=None  # Will be calculated during processing
            )

            return doc

        except Exception as e:
            logger.error(f"Error converting Court Listener case to document: {e}")
            raise

    async def process_all_files(self) -> Dict[str, int]:
        """Process all JSONL files in the data directory."""
        # Load existing data for fast duplicate detection
        await self.deduplicator.load_existing_data()

        files = self.get_jsonl_files()
        total_results = {'success': 0, 'failed': 0, 'duplicates': 0}

        for file_path in files:
            try:
                results = await self.process_file(file_path)
                for key in total_results:
                    total_results[key] += results[key]
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                total_results['failed'] += 1
        
        logger.info(f"Completed processing all files: {total_results}")
        return total_results
    
    def run_community_detection(self, run_after_processing: bool = True) -> Dict[str, Any]:
        """
        Run community detection on all processed cases.
        
        Args:
            run_after_processing: Whether to run immediately after processing
            
        Returns:
            Dictionary with community detection results
        """
        if not run_after_processing:
            logger.info("Community detection skipped (run_after_processing=False)")
            return {}
        
        logger.info("🔍 Starting community detection on all processed cases...")
        
        try:
            # Initialize community detector if not already done
            if self.community_detector is None:
                self.community_detector = BatchCommunityDetector(self.community_config)
            
            # Run batch community detection
            batch_results = self.community_detector.process_all_batches()
            
            # Compile results
            total_communities = sum(result.communities_found for result in batch_results)
            total_time = sum(result.execution_time for result in batch_results)
            total_cases = sum(result.cases_processed for result in batch_results)
            
            results = {
                "total_cases_processed": total_cases,
                "total_communities_found": total_communities,
                "total_execution_time": total_time,
                "batches_processed": len(batch_results),
                "average_time_per_batch": total_time / len(batch_results) if batch_results else 0,
                "processing_rate": total_cases / total_time if total_time > 0 else 0,
                "algorithms_used": self.community_config.algorithms
            }
            
            logger.info(f"✅ Community detection completed:")
            logger.info(f"  Cases processed: {total_cases:,}")
            logger.info(f"  Communities found: {total_communities:,}")
            logger.info(f"  Execution time: {total_time:.1f} seconds")
            logger.info(f"  Processing rate: {results['processing_rate']:.1f} cases/second")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Community detection failed: {e}")
            return {"error": str(e)}
    
    def run_full_pipeline_with_community_detection(self) -> Dict[str, Any]:
        """
        Run the complete pipeline: process files + community detection.
        
        Returns:
            Combined results from processing and community detection
        """
        logger.info("🚀 Starting full pipeline with community detection...")
        
        try:
            # Step 1: Process all files
            processing_results = asyncio.run(self.process_all_files())
            
            # Step 2: Run community detection
            community_results = self.run_community_detection(run_after_processing=True)
            
            # Combine results
            full_results = {
                "processing_results": processing_results,
                "community_detection_results": community_results,
                "pipeline_completed": True,
                "completion_time": datetime.now().isoformat()
            }
            
            logger.info("✅ Full pipeline completed successfully!")
            return full_results
            
        except Exception as e:
            logger.error(f"❌ Full pipeline failed: {e}")
            return {
                "processing_results": {},
                "community_detection_results": {"error": str(e)},
                "pipeline_completed": False,
                "completion_time": datetime.now().isoformat()
            }
    
    def close(self):
        """Close all connections and cleanup."""
        if self.community_detector:
            self.community_detector.close()
        # Close other connections as needed
        logger.info("CaselawAccessProcessor closed")

    def _generate_case_id(self, doc: CaselawDocument) -> str:
        """Generate the same case ID that would be used for storage."""
        if hasattr(doc, 'file_path') and doc.file_path:
            # For Caselaw Access Project documents
            return f"cap_{doc.file_path.replace('/', '_').replace('.html', '')}"
        elif hasattr(doc, 'court_listener_id') and doc.court_listener_id:
            # For Court Listener documents
            return f"cl_{doc.court_listener_id}"
        else:
            # Fallback to hash-based ID
            import hashlib
            content = f"{doc.case_name}_{doc.docket_number}_{doc.date_filed}"
            return f"hash_{hashlib.md5(content.encode()).hexdigest()[:12]}"

    def _classify_document_type(self, doc: CaselawDocument) -> str:
        """Classify document type using pattern matching."""

        # Create a mock case data structure for the classifier
        case_data = {
            'name': doc.case_name or '',
            'court': {'name': doc.court or ''},
            'casebody': {'data': {'text': doc.text or ''}}
        }

        # Use the CAP document type classifier
        try:
            from cap_document_type_classifier import CAPDocumentTypeClassifier
            classifier = CAPDocumentTypeClassifier()
            document_type, confidence, details = classifier.classify_document_type(case_data)

            # Only return 'opinion' if confidence is reasonable, otherwise filter out
            if document_type == 'opinion' and confidence >= 0.3:
                return 'opinion'
            elif document_type == 'order':
                return 'order'
            elif document_type == 'docket':
                return 'docket'
            else:
                return 'unknown'

        except Exception as e:
            # Fallback to simple pattern matching if classifier fails
            text = (doc.text or '').lower()

            # Simple opinion indicators
            if any(word in text[:1000] for word in ['opinion', 'judgment', 'decision', 'holding', 'affirm', 'reverse']):
                return 'opinion'
            elif any(word in text[:1000] for word in ['order', 'motion', 'petition', 'denied', 'granted']):
                return 'order'
            else:
                return 'opinion'  # Default to opinion for CAP data


class AdvancedDeduplicator:
    """Advanced deduplication system with optimized batch loading for fast duplicate detection."""

    def __init__(self):
        self.supabase = SupabaseConnector()
        self.pinecone = PineconeConnector()

        # Batch-loaded data for fast duplicate checking
        self.existing_content_hashes = set()
        self.existing_case_identifiers = set()
        self.existing_ids = set()

        # Note: Cross-source duplicate tracking removed - 1994 cutoff prevents CAP/CourtListener overlap

        self.batch_loaded = False

    async def load_existing_data(self):
        """Load all existing case data for fast duplicate checking."""
        if self.batch_loaded:
            return

        logger.info("🔄 Loading existing cases for fast duplicate detection...")

        # Load all existing cases in batches
        all_cases = []
        offset = 0
        batch_size = 1000

        while True:
            query = self.supabase.client.table('cases').select(
                'id, content_hash, case_name, docket_number, source'
            ).range(offset, offset + batch_size - 1)

            result = query.execute()
            if not result.data:
                break

            all_cases.extend(result.data)
            offset += batch_size

            if len(result.data) < batch_size:
                break

        # Build fast lookup sets
        for case in all_cases:
            if case.get('id'):
                self.existing_ids.add(case['id'])
            if case.get('content_hash'):
                self.existing_content_hashes.add(case['content_hash'])
            if case.get('case_name') and case.get('docket_number'):
                identifier = f"{case['case_name']}||{case['docket_number']}"
                self.existing_case_identifiers.add(identifier)

            # Note: Cross-source tracking removed - temporal separation prevents duplicates

            # Note: Cross-source mapping will be implemented when schema includes
            # court_listener_id and cap_id columns

        self.batch_loaded = True
        logger.info(f"✅ Loaded {len(all_cases):,} existing cases for duplicate detection")
        logger.info(f"   - {len(self.existing_content_hashes):,} content hashes")
        logger.info(f"   - {len(self.existing_case_identifiers):,} case identifiers")
        logger.info(f"   - {len(self.existing_by_source['court_listener']):,} Court Listener cases")
        logger.info(f"   - {len(self.existing_by_source['caselaw_access_project']):,} CAP cases")
        logger.info(f"   - {len(self.existing_by_source['cross_source_matches']):,} cross-source matches")

    async def is_duplicate(self, doc: CaselawDocument, source: str = None) -> bool:
        """Check if document is a duplicate using optimized batch-loaded data."""
        # Ensure data is loaded
        await self.load_existing_data()

        # Level 1: Content hash check (instant)
        if doc.content_hash and doc.content_hash in self.existing_content_hashes:
            return True

        # Level 2: Legal identifier check (instant)
        if doc.case_name and doc.docket_number:
            identifier = f"{doc.case_name}||{doc.docket_number}"
            if identifier in self.existing_case_identifiers:
                return True

        # Level 3: ID check (instant)
        case_id = self._generate_case_id(doc)
        if case_id in self.existing_ids:
            return True

        # Note: Cross-source duplicate check removed - 1994 cutoff prevents overlap

        # Level 5: Semantic similarity check (only if needed)
        if await self._check_semantic_similarity(doc):
            return True

        return False

    # Note: check_cross_source_duplicate method removed - 1994 cutoff prevents CAP/CourtListener overlap

    def _generate_case_id(self, doc: CaselawDocument) -> str:
        """Generate the same case ID that would be used for storage."""
        if hasattr(doc, 'file_path') and doc.file_path:
            # For Caselaw Access Project documents
            return f"cap_{doc.file_path.replace('/', '_').replace('.html', '')}"
        elif hasattr(doc, 'court_listener_id') and doc.court_listener_id:
            # For Court Listener documents
            return f"cl_{doc.court_listener_id}"
        else:
            # Fallback to hash-based ID
            import hashlib
            content = f"{doc.case_name}_{doc.docket_number}_{doc.date_filed}"
            return f"hash_{hashlib.md5(content.encode()).hexdigest()[:12]}"

    async def _check_content_hash(self, doc: CaselawDocument) -> bool:
        """Check for exact content matches (legacy method - now uses batch data)."""
        await self.load_existing_data()
        return doc.content_hash and doc.content_hash in self.existing_content_hashes
    
    async def _check_semantic_similarity(self, doc: CaselawDocument, threshold: float = 0.95) -> bool:
        """Check for semantic similarity using embeddings (only when needed)."""
        if not doc.text:
            return False

        # Generate embedding for the document
        embedding = self.pinecone.generate_embedding(doc.text[:1000])  # First 1000 chars

        # Search for similar documents
        results = self.pinecone.query(
            query_text=doc.text[:1000],
            top_k=1,
            filter={'source_type': 'court_listener'}  # Check against existing data
        )

        if results and results[0].get('score', 0) > threshold:
            return True

        return False

    async def _check_legal_identifiers(self, doc: CaselawDocument) -> bool:
        """Check for matching legal identifiers (legacy method - now uses batch data)."""
        await self.load_existing_data()
        if not doc.case_name or not doc.docket_number:
            return False

        identifier = f"{doc.case_name}||{doc.docket_number}"
        return identifier in self.existing_case_identifiers


class TemporalAnalyzer:
    """Analyzes temporal aspects of legal documents."""
    
    def __init__(self):
        self.supabase = SupabaseConnector()
    
    async def analyze_document(self, doc: CaselawDocument, case_id: str):
        """Perform temporal analysis on a document."""
        if not doc.date_filed:
            return
        
        # Calculate temporal authority score
        authority_score = self._calculate_temporal_authority(doc)
        
        # Analyze historical significance
        significance = self._analyze_historical_significance(doc)
        
        # Store temporal analysis
        analysis_data = {
            'id': str(uuid.uuid4()),
            'document_id': case_id,
            'historical_precedents': {},
            'authority_evolution': {'current_score': authority_score},
            'concept_stability': significance,
            'created_at': datetime.now().isoformat()
        }
        
        self.supabase.client.table('temporal_precedent_analysis').insert(analysis_data).execute()
    
    def _calculate_temporal_authority(self, doc: CaselawDocument) -> float:
        """Calculate temporal authority score based on document age and other factors."""
        if not doc.date_filed:
            return 0.0
        
        # Base score starts at 1.0
        score = 1.0
        
        # Age factor (newer documents get slight boost)
        age_years = (datetime.now() - doc.date_filed).days / 365.25
        if age_years < 10:
            score += 0.1
        elif age_years > 50:
            score -= 0.1
        
        # Court level factor
        if doc.court:
            court_lower = doc.court.lower()
            if 'supreme' in court_lower:
                score += 0.3
            elif 'appeals' in court_lower:
                score += 0.2
            elif 'district' in court_lower:
                score += 0.1
        
        # Jurisdiction factor
        if doc.jurisdiction == 'fed':
            score += 0.2
        
        return min(score, 2.0)  # Cap at 2.0
    
    def _analyze_historical_significance(self, doc: CaselawDocument) -> Dict[str, Any]:
        """Analyze historical significance of the document."""
        significance = {
            'era': doc.historical_era,
            'court_level': self._determine_court_level(doc.court),
            'jurisdiction_importance': self._assess_jurisdiction_importance(doc.jurisdiction),
            'estimated_precedent_value': self._estimate_precedent_value(doc)
        }
        
        return significance
    
    def _determine_court_level(self, court: Optional[str]) -> str:
        """Determine court level from court name."""
        if not court:
            return 'unknown'
        
        court_lower = court.lower()
        if 'supreme' in court_lower:
            return 'supreme'
        elif 'appeals' in court_lower:
            return 'appellate'
        elif 'district' in court_lower:
            return 'district'
        else:
            return 'other'
    
    def _assess_jurisdiction_importance(self, jurisdiction: Optional[str]) -> float:
        """Assess jurisdiction importance for legal precedent."""
        if not jurisdiction:
            return 0.5
        
        # Federal courts have highest importance
        if jurisdiction == 'fed':
            return 1.0
        
        # Large state jurisdictions
        major_states = ['tx', 'ca', 'ny', 'fl', 'il', 'pa', 'oh', 'ga', 'nc', 'mi']
        if jurisdiction in major_states:
            return 0.8
        
        # Other states
        return 0.6
    
    def _estimate_precedent_value(self, doc: CaselawDocument) -> float:
        """Estimate precedent value based on document characteristics."""
        value = 0.5  # Base value
        
        # Court level affects precedent value
        if doc.court:
            court_lower = doc.court.lower()
            if 'supreme' in court_lower:
                value += 0.4
            elif 'appeals' in court_lower:
                value += 0.3
            elif 'district' in court_lower:
                value += 0.1
        
        # Historical era affects value
        if doc.historical_era == 'modern':
            value += 0.1
        elif doc.historical_era == 'historical':
            value += 0.2  # Historical cases can be very important
        
        # Document quality affects value
        if doc.word_count and doc.word_count > 5000:
            value += 0.1  # Longer opinions often more significant
        
        return min(value, 1.0)


class RobustCheckpointManager:
    """Manages comprehensive checkpoints for processing interruption/resumption."""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.checkpoint_interval = 50  # Save every 50 documents
        
    def save_processing_state(self, state: ProcessingState):
        """Save comprehensive processing state."""
        checkpoint_data = {
            'timestamp': datetime.now().isoformat(),
            'file_progress': {
                'current_file_index': state.current_file_index,
                'total_files': state.total_files,
                'current_file_path': state.current_file_path,
                'documents_processed_in_file': state.documents_processed_in_file,
                'total_documents_in_file': state.total_documents_in_file
            },
            'batch_progress': {
                'current_batch_index': state.current_batch_index,
                'documents_in_current_batch': state.documents_in_current_batch,
                'batch_size': state.batch_size
            },
            'overall_progress': {
                'total_documents_processed': state.total_documents_processed,
                'successful_documents': state.successful_documents,
                'failed_documents': state.failed_documents,
                'duplicate_documents': state.duplicate_documents,
                'estimated_total_documents': state.estimated_total_documents
            },
            'processing_stats': {
                'average_processing_time': state.average_processing_time,
                'documents_per_minute': state.documents_per_minute,
                'estimated_completion_time': state.estimated_completion_time
            },
            'database_state': {
                'batch_id': state.batch_id,
                'last_successful_document_id': state.last_successful_document_id,
                'pinecone_vectors_created': state.pinecone_vectors_created,
                'neo4j_nodes_created': state.neo4j_nodes_created,
                'supabase_records_created': state.supabase_records_created
            },
            'error_recovery': {
                'recent_errors': state.recent_errors[-10:],  # Last 10 errors
                'retry_count': state.retry_count,
                'failed_document_ids': list(state.failed_document_ids)
            }
        }
        
        self._save_checkpoint_redundant(checkpoint_data)
    
    def _save_checkpoint_redundant(self, checkpoint_data: Dict):
        """Save checkpoint to multiple locations for redundancy."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Primary checkpoint
        primary_path = self.checkpoint_dir / f'processing_checkpoint_{timestamp}.json'
        
        # Backup checkpoint
        backup_path = self.checkpoint_dir / 'backup' / f'checkpoint_{timestamp}.json'
        
        # Latest checkpoint (always overwritten)
        latest_path = self.checkpoint_dir / 'latest_checkpoint.json'
        
        for path in [primary_path, backup_path, latest_path]:
            path.parent.mkdir(parents=True, exist_ok=True)
            try:
                with open(path, 'w') as f:
                    json.dump(checkpoint_data, f, indent=2)
            except Exception as e:
                logger.warning(f"Failed to save checkpoint to {path}: {e}")
    
    def load_latest_checkpoint(self) -> Optional[ProcessingState]:
        """Load the most recent valid checkpoint."""
        checkpoint_paths = [
            self.checkpoint_dir / 'latest_checkpoint.json',
            *sorted(self.checkpoint_dir.glob('processing_checkpoint_*.json'), reverse=True)
        ]
        
        for path in checkpoint_paths:
            if path.exists():
                try:
                    with open(path, 'r') as f:
                        checkpoint_data = json.load(f)
                    
                    # Validate checkpoint is recent (within 24 hours)
                    checkpoint_time = datetime.fromisoformat(checkpoint_data['timestamp'])
                    if datetime.now() - checkpoint_time > timedelta(hours=24):
                        continue
                    
                    return self._restore_processing_state(checkpoint_data)
                    
                except Exception as e:
                    logger.warning(f"Failed to load checkpoint {path}: {e}")
                    continue
        
        return None
    
    def _restore_processing_state(self, checkpoint_data: Dict) -> ProcessingState:
        """Restore processing state from checkpoint data."""
        state = ProcessingState()
        
        # File progress
        fp = checkpoint_data.get('file_progress', {})
        state.current_file_index = fp.get('current_file_index', 0)
        state.total_files = fp.get('total_files', 0)
        state.current_file_path = fp.get('current_file_path')
        state.documents_processed_in_file = fp.get('documents_processed_in_file', 0)
        state.total_documents_in_file = fp.get('total_documents_in_file', 0)
        
        # Batch progress
        bp = checkpoint_data.get('batch_progress', {})
        state.current_batch_index = bp.get('current_batch_index', 0)
        state.documents_in_current_batch = bp.get('documents_in_current_batch', 0)
        state.batch_size = bp.get('batch_size', 100)
        
        # Overall progress
        op = checkpoint_data.get('overall_progress', {})
        state.total_documents_processed = op.get('total_documents_processed', 0)
        state.successful_documents = op.get('successful_documents', 0)
        state.failed_documents = op.get('failed_documents', 0)
        state.duplicate_documents = op.get('duplicate_documents', 0)
        state.estimated_total_documents = op.get('estimated_total_documents', 6_700_000)
        
        # Processing stats
        ps = checkpoint_data.get('processing_stats', {})
        state.average_processing_time = ps.get('average_processing_time', 0.0)
        state.documents_per_minute = ps.get('documents_per_minute', 0.0)
        state.estimated_completion_time = ps.get('estimated_completion_time')
        
        # Database state
        ds = checkpoint_data.get('database_state', {})
        state.batch_id = ds.get('batch_id')
        state.last_successful_document_id = ds.get('last_successful_document_id')
        state.pinecone_vectors_created = ds.get('pinecone_vectors_created', 0)
        state.neo4j_nodes_created = ds.get('neo4j_nodes_created', 0)
        state.supabase_records_created = ds.get('supabase_records_created', 0)
        
        # Error recovery
        er = checkpoint_data.get('error_recovery', {})
        state.recent_errors = er.get('recent_errors', [])
        state.retry_count = er.get('retry_count', 0)
        state.failed_document_ids = set(er.get('failed_document_ids', []))
        
        return state


class IntelligentRetryManager:
    """Manages intelligent retry logic with backoff strategies."""
    
    def __init__(self):
        self.max_retries = 3
        self.retry_delays = [1, 5, 15]  # Progressive delays
        self.error_patterns = {
            'network': ['ConnectionError', 'TimeoutError', 'HTTPError'],
            'rate_limit': ['RateLimitError', '429', 'TooManyRequests'],
            'database': ['DatabaseError', 'ConnectionError', 'TransactionError'],
            'data': ['ValidationError', 'ParseError', 'FormatError']
        }
    
    async def retry_with_backoff(self, operation: Callable, *args, **kwargs) -> Any:
        """Retry operation with intelligent backoff."""
        for attempt in range(self.max_retries):
            try:
                return await operation(*args, **kwargs)
                
            except Exception as e:
                error_type = self._classify_error(e)
                
                if attempt == self.max_retries - 1:
                    raise  # Last attempt, re-raise
                
                delay = self._calculate_delay(error_type, attempt)
                logger.warning(f"Attempt {attempt + 1} failed ({error_type}), retrying in {delay}s: {e}")
                
                await asyncio.sleep(delay)
    
    def _classify_error(self, error: Exception) -> str:
        """Classify error type for appropriate retry strategy."""
        error_str = str(error)
        error_type = type(error).__name__
        
        for category, patterns in self.error_patterns.items():
            if any(pattern in error_str or pattern in error_type for pattern in patterns):
                return category
        
        return 'unknown'
    
    def _calculate_delay(self, error_type: str, attempt: int) -> int:
        """Calculate appropriate delay based on error type."""
        base_delay = self.retry_delays[attempt]
        
        if error_type == 'rate_limit':
            return base_delay * 2  # Longer delay for rate limits
        elif error_type == 'network':
            return base_delay * 1.5  # Moderate delay for network issues
        else:
            return base_delay


class PerformanceMonitor:
    """Monitors and tracks processing performance."""
    
    def __init__(self):
        self.metrics = {
            'documents_per_minute': 0,
            'embedding_generation_rate': 0,
            'database_write_rate': 0,
            'error_rate': 0,
            'memory_usage': 0
        }
        self.start_time = time.time()
        self.last_measurement = time.time()
        self.recommended_batch_size = 100
    
    def track_processing_rate(self, documents_processed: int, time_elapsed: float):
        """Track processing rate and adjust batch sizes."""
        if time_elapsed == 0:
            return
            
        rate = documents_processed / (time_elapsed / 60)  # documents per minute
        self.metrics['documents_per_minute'] = rate
        
        # Adjust batch size based on performance
        if rate > 50:  # Very fast processing
            self.recommended_batch_size = 200
        elif rate > 20:  # Good processing
            self.recommended_batch_size = 100
        else:  # Slower processing
            self.recommended_batch_size = 50
        
        logger.info(f"Processing rate: {rate:.1f} docs/min, recommended batch size: {self.recommended_batch_size}")
    
    def calculate_eta(self, documents_processed: int, total_documents: int) -> str:
        """Calculate estimated time to completion."""
        if documents_processed == 0:
            return "unknown"
        
        elapsed = time.time() - self.start_time
        rate = documents_processed / elapsed  # documents per second
        
        if rate == 0:
            return "unknown"
        
        remaining_documents = total_documents - documents_processed
        remaining_seconds = remaining_documents / rate
        
        # Convert to human readable format
        if remaining_seconds < 3600:
            return f"{remaining_seconds / 60:.1f} minutes"
        elif remaining_seconds < 86400:
            return f"{remaining_seconds / 3600:.1f} hours"
        else:
            return f"{remaining_seconds / 86400:.1f} days"

