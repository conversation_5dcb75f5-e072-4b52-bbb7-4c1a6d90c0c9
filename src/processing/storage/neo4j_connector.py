"""
Neo4j Graph Database Connector
Handles citation relationships between legal documents with multi-jurisdictional support.
Implements proper jurisdiction labeling and cross-jurisdictional citation tracking.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class Neo4jConnector:
    """
    Connector for Neo4j graph database operations.
    Manages citation relationships with jurisdiction-based organization.
    """
    
    def __init__(self, uri: Optional[str] = None, username: Optional[str] = None, password: Optional[str] = None):
        """
        Initialize the Neo4j connector.
        
        Args:
            uri: Optional Neo4j URI override
            username: Optional username override
            password: Optional password override
        """
        self.uri = uri or os.getenv("NEO4J_URI")
        self.username = username or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("Missing Neo4j password in environment variables")
        
        # Initialize Neo4j driver
        self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
        
        # Initialize schema
        self._initialize_schema()
        
        logger.info(f"Initialized Neo4j connector for {self.uri}")
    
    def close(self):
        """
        Close the Neo4j driver connection.
        """
        self.driver.close()
    
    def _initialize_schema(self):
        """
        Initialize the Neo4j schema with constraints and indexes.
        """
        with self.driver.session() as session:
            # Create constraints
            try:
                session.run("""
                    CREATE CONSTRAINT case_id_constraint IF NOT EXISTS
                    FOR (c:Case) REQUIRE c.id IS UNIQUE
                """)
                
                session.run("""
                    CREATE CONSTRAINT opinion_id_constraint IF NOT EXISTS
                    FOR (o:Opinion) REQUIRE o.id IS UNIQUE
                """)
                
                session.run("""
                    CREATE CONSTRAINT jurisdiction_code_constraint IF NOT EXISTS
                    FOR (j:Jurisdiction) REQUIRE j.code IS UNIQUE
                """)
                
                session.run("""
                    CREATE CONSTRAINT court_id_constraint IF NOT EXISTS
                    FOR (c:Court) REQUIRE c.id IS UNIQUE
                """)
                
                # Create indexes
                session.run("""
                    CREATE INDEX case_jurisdiction_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.jurisdiction)
                """)
                
                session.run("""
                    CREATE INDEX case_year_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.year)
                """)
                
                session.run("""
                    CREATE INDEX case_doc_type_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.doc_type)
                """)
                
                logger.info("Neo4j schema initialized with constraints and indexes")
            except Exception as e:
                logger.error(f"Error initializing Neo4j schema: {str(e)}")
    
    def create_jurisdiction(self, code: str, name: str, level: str, parent_code: Optional[str] = None) -> bool:
        """
        Create a jurisdiction node in Neo4j.
        
        Args:
            code: Jurisdiction code
            name: Jurisdiction name
            level: Jurisdiction level (federal, state, local)
            parent_code: Optional parent jurisdiction code
            
        Returns:
            Success flag
        """
        with self.driver.session() as session:
            try:
                # Create jurisdiction node
                result = session.run("""
                    MERGE (j:Jurisdiction {code: $code})
                    ON CREATE SET 
                        j.name = $name,
                        j.level = $level,
                        j.created_at = datetime()
                    ON MATCH SET 
                        j.name = $name,
                        j.level = $level,
                        j.updated_at = datetime()
                    RETURN j
                """, code=code, name=name, level=level)
                
                # Link to parent jurisdiction if provided
                if parent_code:
                    session.run("""
                        MATCH (j:Jurisdiction {code: $code})
                        MATCH (parent:Jurisdiction {code: $parent_code})
                        MERGE (j)-[:PART_OF]->(parent)
                    """, code=code, parent_code=parent_code)
                
                logger.info(f"Created/updated jurisdiction {code} in Neo4j")
                return True
            except Exception as e:
                logger.error(f"Error creating jurisdiction {code}: {str(e)}")
                return False
    
    def create_court(self, id: str, name: str, jurisdiction_code: str, level: int, parent_id: Optional[str] = None) -> bool:
        """
        Create a court node in Neo4j.
        
        Args:
            id: Court ID
            name: Court name
            jurisdiction_code: Jurisdiction code
            level: Court hierarchy level (1=highest)
            parent_id: Optional parent court ID
            
        Returns:
            Success flag
        """
        with self.driver.session() as session:
            try:
                # Create court node
                session.run("""
                    MERGE (c:Court {id: $id})
                    ON CREATE SET 
                        c.name = $name,
                        c.level = $level,
                        c.created_at = datetime()
                    ON MATCH SET 
                        c.name = $name,
                        c.level = $level,
                        c.updated_at = datetime()
                """, id=id, name=name, level=level)
                
                # Link to jurisdiction
                session.run("""
                    MATCH (c:Court {id: $id})
                    MATCH (j:Jurisdiction {code: $jurisdiction_code})
                    MERGE (c)-[:IN_JURISDICTION]->(j)
                """, id=id, jurisdiction_code=jurisdiction_code)
                
                # Link to parent court if provided
                if parent_id:
                    session.run("""
                        MATCH (c:Court {id: $id})
                        MATCH (parent:Court {id: $parent_id})
                        MERGE (c)-[:SUBORDINATE_TO]->(parent)
                    """, id=id, parent_id=parent_id)
                
                logger.info(f"Created/updated court {id} in Neo4j")
                return True
            except Exception as e:
                logger.error(f"Error creating court {id}: {str(e)}")
                return False
    
    def create_case(self, case_data: Dict[str, Any]) -> bool:
        """
        Create a case node in Neo4j.
        
        Args:
            case_data: Case data dictionary with required fields:
                - id: Unique case ID
                - name: Case name
                - jurisdiction: Jurisdiction code
                - court_id: Court ID
                - doc_type: Document type (default: case)
                - year: Case year
                - date_filed: Filing date
                
        Returns:
            Success flag
        """
        required_fields = ["id", "name", "jurisdiction"]
        for field in required_fields:
            if field not in case_data:
                logger.error(f"Missing required field {field} for case creation")
                return False
        
        # Set defaults for optional fields
        case_data.setdefault("doc_type", "case")
        
        # Extract year from date_filed if available and not provided
        if "year" not in case_data and "date_filed" in case_data:
            try:
                case_data["year"] = int(case_data["date_filed"].split("-")[0])
            except (IndexError, ValueError, AttributeError):
                # Default to current year if parsing fails
                case_data["year"] = datetime.now().year
        
        with self.driver.session() as session:
            try:
                # Create case node with all properties
                create_query = """
                    MERGE (c:Case {id: $id})
                    ON CREATE SET 
                        c.name = $name,
                        c.jurisdiction = $jurisdiction,
                        c.doc_type = $doc_type,
                        c.created_at = datetime()
                """
                
                # Add all other properties dynamically (only scalar values)
                for key, value in case_data.items():
                    if key not in ["id", "created_at", "updated_at"]:
                        # Only add scalar values to avoid Neo4j property type errors
                        if isinstance(value, (str, int, float, bool)) or value is None:
                            create_query += f", c.{key} = ${key}"
                        elif isinstance(value, dict):
                            # For complex objects like jurisdiction, extract key fields
                            if key == "jurisdiction" and isinstance(value, dict):
                                # Extract jurisdiction code if it's a complex object
                                jurisdiction_code = value.get("code") or value.get("id") or str(value)
                                case_data["jurisdiction_code"] = jurisdiction_code
                                create_query += f", c.jurisdiction_code = $jurisdiction_code"
                            elif key == "court" and isinstance(value, dict):
                                # Extract court ID if it's a complex object
                                court_id = value.get("id") or value.get("code") or str(value)
                                case_data["court_id"] = court_id
                                create_query += f", c.court_id = $court_id"
                        elif isinstance(value, list):
                            # Convert lists to comma-separated strings
                            if all(isinstance(item, (str, int, float)) for item in value):
                                case_data[f"{key}_list"] = ", ".join(str(item) for item in value)
                                create_query += f", c.{key}_list = ${key}_list"
                
                create_query += """
                    ON MATCH SET 
                        c.name = $name,
                        c.jurisdiction = $jurisdiction,
                        c.doc_type = $doc_type,
                        c.updated_at = datetime()
                """
                
                # Add all other properties dynamically for update
                for key, value in case_data.items():
                    if key not in ["id", "created_at", "updated_at"]:
                        create_query += f", c.{key} = ${key}"
                
                create_query += " RETURN c"
                
                # Execute the query
                session.run(create_query, **case_data)
                
                # Link to jurisdiction
                session.run("""
                    MATCH (c:Case {id: $id})
                    MATCH (j:Jurisdiction {code: $jurisdiction})
                    MERGE (c)-[:IN_JURISDICTION]->(j)
                """, id=case_data["id"], jurisdiction=case_data["jurisdiction"])
                
                # Link to court if court_id is provided
                if "court_id" in case_data:
                    session.run("""
                        MATCH (c:Case {id: $id})
                        MATCH (court:Court {id: $court_id})
                        MERGE (c)-[:DECIDED_BY]->(court)
                    """, id=case_data["id"], court_id=case_data["court_id"])
                
                logger.info(f"Created/updated case {case_data['id']} in Neo4j")
                return True
            except Exception as e:
                logger.error(f"Error creating case {case_data.get('id')}: {str(e)}")
                return False
    
    def create_opinion(self, opinion_data: Dict[str, Any]) -> bool:
        """
        Create an opinion node in Neo4j.
        
        Args:
            opinion_data: Opinion data dictionary with required fields:
                - id: Unique opinion ID
                - case_id: Parent case ID
                - type: Opinion type (majority, dissent, concurrence)
                - author: Author name
                
        Returns:
            Success flag
        """
        required_fields = ["id", "case_id"]
        for field in required_fields:
            if field not in opinion_data:
                logger.error(f"Missing required field {field} for opinion creation")
                return False
        
        with self.driver.session() as session:
            try:
                # Create opinion node with all properties
                create_query = """
                    MERGE (o:Opinion {id: $id})
                    ON CREATE SET 
                        o.created_at = datetime()
                """
                
                # Add all other properties dynamically
                for key, value in opinion_data.items():
                    if key not in ["id", "case_id", "created_at", "updated_at"]:
                        create_query += f", o.{key} = ${key}"
                
                create_query += """
                    ON MATCH SET 
                        o.updated_at = datetime()
                """
                
                # Add all other properties dynamically for update
                for key, value in opinion_data.items():
                    if key not in ["id", "case_id", "created_at", "updated_at"]:
                        create_query += f", o.{key} = ${key}"
                
                create_query += " RETURN o"
                
                # Execute the query
                session.run(create_query, **opinion_data)
                
                # Link to case
                session.run("""
                    MATCH (o:Opinion {id: $id})
                    MATCH (c:Case {id: $case_id})
                    MERGE (o)-[:PART_OF]->(c)
                """, id=opinion_data["id"], case_id=opinion_data["case_id"])
                
                logger.info(f"Created/updated opinion {opinion_data['id']} in Neo4j")
                return True
            except Exception as e:
                logger.error(f"Error creating opinion {opinion_data.get('id')}: {str(e)}")
                return False
    
    def create_citation(self, 
                       citing_id: str, 
                       cited_id: str, 
                       citation_text: Optional[str] = None,
                       citation_type: str = "cites",
                       metadata: Optional[Dict] = None) -> bool:
        """
        Create a citation relationship between cases in Neo4j.
        
        Args:
            citing_id: ID of the citing case/opinion
            cited_id: ID of the cited case
            citation_text: Optional citation text
            citation_type: Relationship type (cites, follows, distinguishes, etc.)
            metadata: Optional additional metadata
            
        Returns:
            Success flag
        """
        with self.driver.session() as session:
            try:
                # Determine if citing_id is a case or opinion
                is_opinion = session.run("""
                    MATCH (o:Opinion {id: $id})
                    RETURN COUNT(o) > 0 AS is_opinion
                """, id=citing_id).single()["is_opinion"]
                
                # Create citation relationship
                if is_opinion:
                    # Citation from opinion to case
                    query = """
                        MATCH (o:Opinion {id: $citing_id})
                        MATCH (c:Case {id: $cited_id})
                        MERGE (o)-[r:%s]->(c)
                        ON CREATE SET 
                            r.created_at = datetime()
                    """ % citation_type
                else:
                    # Citation from case to case
                    query = """
                        MATCH (c1:Case {id: $citing_id})
                        MATCH (c2:Case {id: $cited_id})
                        MERGE (c1)-[r:%s]->(c2)
                        ON CREATE SET 
                            r.created_at = datetime()
                    """ % citation_type
                
                # Add citation text if provided
                if citation_text:
                    query += ", r.citation_text = $citation_text"
                
                # Add metadata if provided
                if metadata:
                    for key, value in metadata.items():
                        query += f", r.{key} = ${key}"
                
                # Execute the query with all parameters
                params = {
                    "citing_id": citing_id,
                    "cited_id": cited_id,
                    "citation_text": citation_text
                }
                
                # Add metadata to params
                if metadata:
                    params.update(metadata)
                
                session.run(query, **params)
                
                logger.info(f"Created citation from {citing_id} to {cited_id} in Neo4j")
                return True
            except Exception as e:
                logger.error(f"Error creating citation from {citing_id} to {cited_id}: {str(e)}")
                return False
    
    def check_case_exists(self, case_id: str) -> bool:
        """
        Check if a Case node with the given ID exists.
        
        Args:
            case_id: The unique ID of the case to check.
            
        Returns:
            True if the case node exists, False otherwise.
        """
        with self.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (c:Case {id: $case_id})
                    RETURN c IS NOT NULL AS exists
                    LIMIT 1
                """, case_id=case_id).single()
                
                return result["exists"] if result else False
            except Exception as e:
                logger.error(f"Error checking existence for case {case_id}: {str(e)}")
                return False # Assume false on error

    def get_case_citations(self, case_id: str, direction: str = "outgoing") -> List[Dict]:
        """
        Get citations for a case.
        
        Args:
            case_id: Case ID
            direction: 'outgoing' for cases cited by this case, 
                      'incoming' for cases citing this case
            
        Returns:
            List of citation data
        """
        with self.driver.session() as session:
            try:
                if direction == "outgoing":
                    # Cases cited by this case
                    result = session.run("""
                        MATCH (c:Case {id: $case_id})-[r]->(cited:Case)
                        RETURN cited.id AS cited_id, cited.name AS cited_name, 
                               cited.jurisdiction AS cited_jurisdiction,
                               type(r) AS citation_type, r.citation_text AS citation_text,
                               cited.year AS cited_year
                        ORDER BY cited.year DESC
                    """, case_id=case_id)
                else:
                    # Cases citing this case
                    result = session.run("""
                        MATCH (citing:Case)-[r]->(c:Case {id: $case_id})
                        RETURN citing.id AS citing_id, citing.name AS citing_name, 
                               citing.jurisdiction AS citing_jurisdiction,
                               type(r) AS citation_type, r.citation_text AS citation_text,
                               citing.year AS citing_year
                        ORDER BY citing.year DESC
                    """, case_id=case_id)
                
                return [dict(record) for record in result]
            except Exception as e:
                logger.error(f"Error getting {direction} citations for case {case_id}: {str(e)}")
                return []
    
    def get_cross_jurisdictional_citations(self, jurisdiction: str) -> List[Dict]:
        """
        Get cross-jurisdictional citations for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            List of cross-jurisdictional citation data
        """
        with self.driver.session() as session:
            try:
                # Get outgoing citations to other jurisdictions
                outgoing_result = session.run("""
                    MATCH (c:Case {jurisdiction: $jurisdiction})-[r]->(cited:Case)
                    WHERE cited.jurisdiction <> $jurisdiction
                    RETURN c.id AS citing_id, c.name AS citing_name, 
                           cited.id AS cited_id, cited.name AS cited_name,
                           cited.jurisdiction AS cited_jurisdiction,
                           type(r) AS citation_type, r.citation_text AS citation_text
                    ORDER BY cited.jurisdiction, cited.year DESC
                """, jurisdiction=jurisdiction)
                
                outgoing = [dict(record) for record in outgoing_result]
                
                # Get incoming citations from other jurisdictions
                incoming_result = session.run("""
                    MATCH (citing:Case)-[r]->(c:Case {jurisdiction: $jurisdiction})
                    WHERE citing.jurisdiction <> $jurisdiction
                    RETURN citing.id AS citing_id, citing.name AS citing_name,
                           citing.jurisdiction AS citing_jurisdiction, 
                           c.id AS cited_id, c.name AS cited_name,
                           type(r) AS citation_type, r.citation_text AS citation_text
                    ORDER BY citing.jurisdiction, citing.year DESC
                """, jurisdiction=jurisdiction)
                
                incoming = [dict(record) for record in incoming_result]
                
                return {
                    "outgoing": outgoing,
                    "incoming": incoming
                }
            except Exception as e:
                logger.error(f"Error getting cross-jurisdictional citations for {jurisdiction}: {str(e)}")
                return {"outgoing": [], "incoming": []}
    
    def get_jurisdiction_statistics(self, jurisdiction: str) -> Dict:
        """
        Get citation statistics for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            Dictionary of citation statistics
        """
        with self.driver.session() as session:
            try:
                # Get case count
                case_count = session.run("""
                    MATCH (c:Case {jurisdiction: $jurisdiction})
                    RETURN COUNT(c) AS count
                """, jurisdiction=jurisdiction).single()["count"]
                
                # Get citation counts
                citation_stats = session.run("""
                    MATCH (c:Case {jurisdiction: $jurisdiction})
                    OPTIONAL MATCH (c)-[out:CITES]->()
                    OPTIONAL MATCH ()-[in:CITES]->(c)
                    RETURN 
                        COUNT(DISTINCT c) AS case_count,
                        COUNT(out) AS outgoing_citations,
                        COUNT(in) AS incoming_citations,
                        COUNT(DISTINCT c)-COUNT(DISTINCT c.id) AS isolated_cases
                """, jurisdiction=jurisdiction).single()
                
                # Get cross-jurisdictional citation counts
                cross_stats = session.run("""
                    MATCH (c:Case {jurisdiction: $jurisdiction})
                    OPTIONAL MATCH (c)-[out:CITES]->(cited:Case)
                    WHERE cited.jurisdiction <> $jurisdiction
                    OPTIONAL MATCH (citing:Case)-[in:CITES]->(c)
                    WHERE citing.jurisdiction <> $jurisdiction
                    RETURN 
                        COUNT(DISTINCT cited) AS outgoing_cross_jurisdictional,
                        COUNT(DISTINCT citing) AS incoming_cross_jurisdictional
                """, jurisdiction=jurisdiction).single()
                
                return {
                    "case_count": case_count,
                    "citation_stats": dict(citation_stats),
                    "cross_jurisdictional": dict(cross_stats)
                }
            except Exception as e:
                logger.error(f"Error getting jurisdiction statistics for {jurisdiction}: {str(e)}")
                return {}
    
    def migrate_jurisdiction_labels(self) -> bool:
        """
        Migrate existing case nodes to include proper jurisdiction labels.
        
        Returns:
            Success flag
        """
        with self.driver.session() as session:
            try:
                # Get all jurisdictions
                jurisdictions = session.run("""
                    MATCH (j:Jurisdiction)
                    RETURN j.code AS code, j.name AS name
                """)
                
                for jurisdiction in jurisdictions:
                    # Create relationships between cases and jurisdictions
                    session.run("""
                        MATCH (c:Case {jurisdiction: $code})
                        MATCH (j:Jurisdiction {code: $code})
                        MERGE (c)-[:IN_JURISDICTION]->(j)
                    """, code=jurisdiction["code"])
                    
                    logger.info(f"Migrated jurisdiction relationships for {jurisdiction['code']}")
                
                return True
            except Exception as e:
                logger.error(f"Error migrating jurisdiction labels: {str(e)}")
                return False
