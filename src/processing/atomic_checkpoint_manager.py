"""
Atomic Checkpoint Manager
Provides atomic checkpoint operations with .tmp → rename pattern and enhanced schema
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import tempfile
import os

logger = logging.getLogger(__name__)


@dataclass
class EnhancedCheckpoint:
    """Enhanced checkpoint with cursor tracking and atomic operations"""
    schema_version: int
    checkpoint_id: str
    timestamp: str
    phase: str  # "fetch" | "process" | "complete"
    
    # Time window information
    window: Optional[Dict[str, str]]  # {"start": "1996-03-01", "end": "1996-03-31"}
    
    # Pagination state
    last_id: Optional[int]
    page: Optional[int]
    page_size: int
    next_url: Optional[str]
    cursor: Optional[str]
    
    # Progress tracking
    fetched: int
    persisted: int
    retries: int
    dlq: int
    
    # Court configuration
    court_ids: List[str]
    
    # Additional metadata
    metadata: Dict[str, Any]


class AtomicCheckpointManager:
    """
    Atomic checkpoint manager with .tmp → rename pattern
    Ensures checkpoint files are never corrupted during writes
    """
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        
        # Ensure we have write permissions
        if not os.access(self.checkpoint_dir, os.W_OK):
            raise PermissionError(f"No write permission for checkpoint directory: {self.checkpoint_dir}")
    
    async def save_checkpoint(self, checkpoint_data: Dict[str, Any]) -> bool:
        """
        Atomically save checkpoint using .tmp → rename pattern
        
        Args:
            checkpoint_data: Checkpoint data to save
            
        Returns:
            True if saved successfully
        """
        checkpoint_id = checkpoint_data.get('checkpoint_id')
        if not checkpoint_id:
            raise ValueError("checkpoint_id is required in checkpoint_data")
        
        # Create enhanced checkpoint object
        enhanced_checkpoint = EnhancedCheckpoint(
            schema_version=1,
            checkpoint_id=checkpoint_id,
            timestamp=datetime.now().isoformat(),
            phase=checkpoint_data.get('phase', 'fetch'),
            window=checkpoint_data.get('window'),
            last_id=checkpoint_data.get('last_id'),
            page=checkpoint_data.get('page'),
            page_size=checkpoint_data.get('page_size', 100),
            next_url=checkpoint_data.get('next_url'),
            cursor=checkpoint_data.get('cursor'),
            fetched=checkpoint_data.get('fetched', 0),
            persisted=checkpoint_data.get('persisted', 0),
            retries=checkpoint_data.get('retries', 0),
            dlq=checkpoint_data.get('dlq', 0),
            court_ids=checkpoint_data.get('court_ids', []),
            metadata=checkpoint_data.get('metadata', {})
        )
        
        temp_file = None
        final_file = self.checkpoint_dir / f"{checkpoint_id}.json"
        
        try:
            # Create temporary file in same directory (for atomic rename)
            with tempfile.NamedTemporaryFile(
                mode='w',
                dir=self.checkpoint_dir,
                prefix=f"{checkpoint_id}_",
                suffix='.tmp',
                delete=False
            ) as f:
                temp_file = Path(f.name)
                
                # Write checkpoint data to temporary file
                json.dump(asdict(enhanced_checkpoint), f, indent=2, ensure_ascii=False)
                f.flush()
                os.fsync(f.fileno())  # Force write to disk
            
            # Atomic rename (this is the atomic operation)
            temp_file.rename(final_file)
            
            logger.debug(f"Checkpoint saved atomically: {checkpoint_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint {checkpoint_id}: {e}")
            
            # Clean up temporary file if it exists
            if temp_file and temp_file.exists():
                try:
                    temp_file.unlink()
                except Exception as cleanup_error:
                    logger.warning(f"Failed to clean up temp file {temp_file}: {cleanup_error}")
            
            return False
    
    def load_checkpoint(self, checkpoint_id: str) -> Optional[EnhancedCheckpoint]:
        """
        Load checkpoint by ID
        
        Args:
            checkpoint_id: ID of checkpoint to load
            
        Returns:
            EnhancedCheckpoint or None if not found
        """
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            return None
        
        try:
            with open(checkpoint_file, 'r') as f:
                data = json.load(f)
            
            # Handle legacy checkpoints without schema_version
            if 'schema_version' not in data:
                logger.warning(f"Loading legacy checkpoint: {checkpoint_id}")
                return self._migrate_legacy_checkpoint(data)
            
            return EnhancedCheckpoint(**data)
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint {checkpoint_id}: {e}")
            return None
    
    def list_checkpoints(self, phase: Optional[str] = None) -> List[EnhancedCheckpoint]:
        """
        List all checkpoints, optionally filtered by phase
        
        Args:
            phase: Optional phase filter ("fetch", "process", "complete")
            
        Returns:
            List of checkpoints sorted by timestamp (newest first)
        """
        checkpoints = []
        
        for checkpoint_file in self.checkpoint_dir.glob("*.json"):
            try:
                checkpoint = self.load_checkpoint(checkpoint_file.stem)
                if checkpoint and (not phase or checkpoint.phase == phase):
                    checkpoints.append(checkpoint)
            except Exception as e:
                logger.warning(f"Failed to load checkpoint from {checkpoint_file}: {e}")
        
        # Sort by timestamp (newest first)
        checkpoints.sort(key=lambda x: x.timestamp, reverse=True)
        return checkpoints
    
    def find_resumable_checkpoint(self, jurisdiction: str, phase: str = "fetch") -> Optional[EnhancedCheckpoint]:
        """
        Find the most recent resumable checkpoint for a jurisdiction
        
        Args:
            jurisdiction: Jurisdiction code (e.g., 'tx')
            phase: Phase to look for
            
        Returns:
            Most recent resumable checkpoint or None
        """
        checkpoints = self.list_checkpoints(phase)
        
        for checkpoint in checkpoints:
            # Check if this checkpoint is for the requested jurisdiction
            checkpoint_jurisdiction = checkpoint.metadata.get('jurisdiction')
            if checkpoint_jurisdiction == jurisdiction and checkpoint.phase == phase:
                logger.info(f"Found resumable checkpoint: {checkpoint.checkpoint_id}")
                return checkpoint
        
        return None
    
    def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """
        Delete a checkpoint file
        
        Args:
            checkpoint_id: ID of checkpoint to delete
            
        Returns:
            True if deleted successfully
        """
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"
        
        try:
            if checkpoint_file.exists():
                checkpoint_file.unlink()
                logger.info(f"Deleted checkpoint: {checkpoint_id}")
                return True
            else:
                logger.warning(f"Checkpoint file not found: {checkpoint_id}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete checkpoint {checkpoint_id}: {e}")
            return False
    
    def cleanup_old_checkpoints(self, keep_count: int = 10) -> int:
        """
        Clean up old checkpoint files, keeping only the most recent ones
        
        Args:
            keep_count: Number of recent checkpoints to keep
            
        Returns:
            Number of checkpoints deleted
        """
        checkpoints = self.list_checkpoints()
        
        if len(checkpoints) <= keep_count:
            return 0
        
        checkpoints_to_delete = checkpoints[keep_count:]
        deleted_count = 0
        
        for checkpoint in checkpoints_to_delete:
            if self.delete_checkpoint(checkpoint.checkpoint_id):
                deleted_count += 1
        
        logger.info(f"Cleaned up {deleted_count} old checkpoints")
        return deleted_count
    
    def _migrate_legacy_checkpoint(self, data: Dict[str, Any]) -> EnhancedCheckpoint:
        """
        Migrate legacy checkpoint format to enhanced format
        
        Args:
            data: Legacy checkpoint data
            
        Returns:
            EnhancedCheckpoint with migrated data
        """
        return EnhancedCheckpoint(
            schema_version=1,
            checkpoint_id=data.get('checkpoint_id', 'legacy'),
            timestamp=data.get('timestamp', datetime.now().isoformat()),
            phase=data.get('phase', 'fetch'),
            window=data.get('window'),
            last_id=data.get('last_id'),
            page=data.get('page'),
            page_size=data.get('page_size', 100),
            next_url=data.get('next_url'),
            cursor=data.get('cursor'),
            fetched=data.get('fetched', 0),
            persisted=data.get('persisted', 0),
            retries=data.get('retries', 0),
            dlq=data.get('dlq', 0),
            court_ids=data.get('court_ids', []),
            metadata=data.get('metadata', data)  # Put all legacy data in metadata
        )
    
    def get_progress_summary(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """
        Get progress summary for a checkpoint
        
        Args:
            checkpoint_id: ID of checkpoint
            
        Returns:
            Progress summary or None if checkpoint not found
        """
        checkpoint = self.load_checkpoint(checkpoint_id)
        if not checkpoint:
            return None
        
        return {
            "checkpoint_id": checkpoint.checkpoint_id,
            "phase": checkpoint.phase,
            "timestamp": checkpoint.timestamp,
            "fetched": checkpoint.fetched,
            "persisted": checkpoint.persisted,
            "success_rate": checkpoint.persisted / max(checkpoint.fetched, 1),
            "retries": checkpoint.retries,
            "dlq_items": checkpoint.dlq,
            "window": checkpoint.window,
            "court_count": len(checkpoint.court_ids)
        }


# Convenience functions
def create_checkpoint_manager(checkpoint_dir: str = "checkpoints") -> AtomicCheckpointManager:
    """Create an atomic checkpoint manager"""
    return AtomicCheckpointManager(checkpoint_dir)


if __name__ == "__main__":
    # Test the atomic checkpoint manager
    import asyncio
    
    async def test_atomic_checkpoint():
        manager = AtomicCheckpointManager("test_checkpoints")
        
        # Test saving a checkpoint
        test_data = {
            "checkpoint_id": "test_tx_20250725_120000",
            "phase": "fetch",
            "window": {"start": "2024-01-01", "end": "2024-01-31"},
            "cursor": "cD0xMTA4ODY2OQ==",
            "fetched": 1500,
            "persisted": 1450,
            "court_ids": ["tex", "texcrimapp", "texapp1"],
            "metadata": {"jurisdiction": "tx", "test": True}
        }
        
        success = await manager.save_checkpoint(test_data)
        print(f"Checkpoint saved: {success}")
        
        # Test loading the checkpoint
        loaded = manager.load_checkpoint("test_tx_20250725_120000")
        if loaded:
            print(f"Loaded checkpoint: {loaded.checkpoint_id}")
            print(f"Progress: {loaded.persisted}/{loaded.fetched}")
        
        # Test progress summary
        summary = manager.get_progress_summary("test_tx_20250725_120000")
        if summary:
            print(f"Progress summary: {summary}")
    
    asyncio.run(test_atomic_checkpoint())
