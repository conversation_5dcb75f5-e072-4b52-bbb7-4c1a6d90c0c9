{"jurisdictions": {"fed": {"code": "fed", "name": "Federal", "full_name": "United States Federal", "level": "federal", "parent_jurisdiction": null, "abbreviation": "US", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of the United States", "abbreviation": "SCOTUS", "court_id": "scotus"}, {"level": 2, "name": "United States Courts of Appeals", "abbreviation": "Circuit Courts", "court_id": "circuit"}, {"level": 3, "name": "United States District Courts", "abbreviation": "District Courts", "court_id": "district"}, {"level": 4, "name": "United States Bankruptcy Courts", "abbreviation": "Bankruptcy Courts", "court_id": "bankruptcy"}]}, "citation_formats": {"statute": ["US\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["US\\. Admin\\. Code § \\d+"], "case": {"scotus": ["\\d+ U\\.S\\. \\d+", "\\d+ S\\.Ct\\. \\d+"], "circuit": ["\\d+ F\\.\\d+d \\d+", "\\d+ F\\. App'x \\d+"], "district": ["\\d+ F\\. Supp\\. \\d+d \\d+"], "bankruptcy": ["\\d+ B\\.R\\. \\d+"]}, "constitution": ["US\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/fed", "pinecone_namespace": "fed", "neo4j_labels": ["Document", "Federal", "Federal"]}}, "al": {"code": "al", "name": "Alabama", "full_name": "State of Alabama", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "AL", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Alabama", "abbreviation": "AL Sup. Ct.", "court_id": "al_supreme"}, {"level": 2, "name": "Alabama Court of Appeals", "abbreviation": "AL App.", "court_id": "al_appeals"}, {"level": 3, "name": "Alabama District Court", "abbreviation": "AL Dist. Ct.", "court_id": "al_district"}]}, "citation_formats": {"statute": ["AL\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["AL\\. Admin\\. Code § \\d+"], "case": {"al_supreme": ["\\d+ AL\\. \\d+d \\d+"], "al_appeals": ["\\d+ AL\\. App\\. \\d+d \\d+"], "al_district": ["\\d+ AL\\. Dist\\. \\d+d \\d+"]}, "constitution": ["AL\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/al", "pinecone_namespace": "al", "neo4j_labels": ["Document", "Alabama", "State"]}}, "ak": {"code": "ak", "name": "Alaska", "full_name": "State of Alaska", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "AK", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Alaska", "abbreviation": "AK Sup. Ct.", "court_id": "ak_supreme"}, {"level": 2, "name": "Alaska Court of Appeals", "abbreviation": "AK App.", "court_id": "ak_appeals"}, {"level": 3, "name": "Alaska District Court", "abbreviation": "AK Dist. Ct.", "court_id": "ak_district"}]}, "citation_formats": {"statute": ["AK\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["AK\\. Admin\\. Code § \\d+"], "case": {"ak_supreme": ["\\d+ AK\\. \\d+d \\d+"], "ak_appeals": ["\\d+ AK\\. App\\. \\d+d \\d+"], "ak_district": ["\\d+ AK\\. Dist\\. \\d+d \\d+"]}, "constitution": ["AK\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ak", "pinecone_namespace": "ak", "neo4j_labels": ["Document", "Alaska", "State"]}}, "az": {"code": "az", "name": "Arizona", "full_name": "State of Arizona", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "AZ", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Arizona", "abbreviation": "AZ Sup. Ct.", "court_id": "az_supreme"}, {"level": 2, "name": "Arizona Court of Appeals", "abbreviation": "AZ App.", "court_id": "az_appeals"}, {"level": 3, "name": "Arizona District Court", "abbreviation": "AZ Dist. Ct.", "court_id": "az_district"}]}, "citation_formats": {"statute": ["AZ\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["AZ\\. Admin\\. Code § \\d+"], "case": {"az_supreme": ["\\d+ AZ\\. \\d+d \\d+"], "az_appeals": ["\\d+ AZ\\. App\\. \\d+d \\d+"], "az_district": ["\\d+ AZ\\. Dist\\. \\d+d \\d+"]}, "constitution": ["AZ\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/az", "pinecone_namespace": "az", "neo4j_labels": ["Document", "Arizona", "State"]}}, "ar": {"code": "ar", "name": "Arkansas", "full_name": "State of Arkansas", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "AR", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Arkansas", "abbreviation": "AR Sup. Ct.", "court_id": "ar_supreme"}, {"level": 2, "name": "Arkansas Court of Appeals", "abbreviation": "AR App.", "court_id": "ar_appeals"}, {"level": 3, "name": "Arkansas District Court", "abbreviation": "AR Dist. Ct.", "court_id": "ar_district"}]}, "citation_formats": {"statute": ["AR\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["AR\\. Admin\\. Code § \\d+"], "case": {"ar_supreme": ["\\d+ AR\\. \\d+d \\d+"], "ar_appeals": ["\\d+ AR\\. App\\. \\d+d \\d+"], "ar_district": ["\\d+ AR\\. Dist\\. \\d+d \\d+"]}, "constitution": ["AR\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ar", "pinecone_namespace": "ar", "neo4j_labels": ["Document", "Arkansas", "State"]}}, "ca": {"code": "ca", "name": "California", "full_name": "State of California", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "CA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of California", "abbreviation": "CA Sup. Ct.", "court_id": "ca_supreme"}, {"level": 2, "name": "California Court of Appeals", "abbreviation": "CA App.", "court_id": "ca_appeals"}, {"level": 3, "name": "California District Court", "abbreviation": "CA Dist. Ct.", "court_id": "ca_district"}]}, "citation_formats": {"statute": ["CA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["CA\\. Admin\\. Code § \\d+"], "case": {"ca_supreme": ["\\d+ CA\\. \\d+d \\d+"], "ca_appeals": ["\\d+ CA\\. App\\. \\d+d \\d+"], "ca_district": ["\\d+ CA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["CA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ca", "pinecone_namespace": "ca", "neo4j_labels": ["Document", "California", "State"]}}, "co": {"code": "co", "name": "Colorado", "full_name": "State of Colorado", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "CO", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Colorado", "abbreviation": "CO Sup. Ct.", "court_id": "co_supreme"}, {"level": 2, "name": "Colorado Court of Appeals", "abbreviation": "CO App.", "court_id": "co_appeals"}, {"level": 3, "name": "Colorado District Court", "abbreviation": "CO Dist. Ct.", "court_id": "co_district"}]}, "citation_formats": {"statute": ["CO\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["CO\\. Admin\\. Code § \\d+"], "case": {"co_supreme": ["\\d+ CO\\. \\d+d \\d+"], "co_appeals": ["\\d+ CO\\. App\\. \\d+d \\d+"], "co_district": ["\\d+ CO\\. Dist\\. \\d+d \\d+"]}, "constitution": ["CO\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/co", "pinecone_namespace": "co", "neo4j_labels": ["Document", "Colorado", "State"]}}, "ct": {"code": "ct", "name": "Connecticut", "full_name": "State of Connecticut", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "CT", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Connecticut", "abbreviation": "CT Sup. Ct.", "court_id": "ct_supreme"}, {"level": 2, "name": "Connecticut Court of Appeals", "abbreviation": "CT App.", "court_id": "ct_appeals"}, {"level": 3, "name": "Connecticut District Court", "abbreviation": "CT Dist. Ct.", "court_id": "ct_district"}]}, "citation_formats": {"statute": ["CT\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["CT\\. Admin\\. Code § \\d+"], "case": {"ct_supreme": ["\\d+ CT\\. \\d+d \\d+"], "ct_appeals": ["\\d+ CT\\. App\\. \\d+d \\d+"], "ct_district": ["\\d+ CT\\. Dist\\. \\d+d \\d+"]}, "constitution": ["CT\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ct", "pinecone_namespace": "ct", "neo4j_labels": ["Document", "Connecticut", "State"]}}, "de": {"code": "de", "name": "Delaware", "full_name": "State of Delaware", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "DE", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Delaware", "abbreviation": "DE Sup. Ct.", "court_id": "de_supreme"}, {"level": 2, "name": "Delaware Court of Appeals", "abbreviation": "DE App.", "court_id": "de_appeals"}, {"level": 3, "name": "Delaware District Court", "abbreviation": "DE Dist. Ct.", "court_id": "de_district"}]}, "citation_formats": {"statute": ["DE\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["DE\\. Admin\\. Code § \\d+"], "case": {"de_supreme": ["\\d+ DE\\. \\d+d \\d+"], "de_appeals": ["\\d+ DE\\. App\\. \\d+d \\d+"], "de_district": ["\\d+ DE\\. Dist\\. \\d+d \\d+"]}, "constitution": ["DE\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/de", "pinecone_namespace": "de", "neo4j_labels": ["Document", "Delaware", "State"]}}, "fl": {"code": "fl", "name": "Florida", "full_name": "State of Florida", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "FL", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Florida", "abbreviation": "FL Sup. Ct.", "court_id": "fl_supreme"}, {"level": 2, "name": "Florida Court of Appeals", "abbreviation": "FL App.", "court_id": "fl_appeals"}, {"level": 3, "name": "Florida District Court", "abbreviation": "FL Dist. Ct.", "court_id": "fl_district"}]}, "citation_formats": {"statute": ["FL\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["FL\\. Admin\\. Code § \\d+"], "case": {"fl_supreme": ["\\d+ FL\\. \\d+d \\d+"], "fl_appeals": ["\\d+ FL\\. App\\. \\d+d \\d+"], "fl_district": ["\\d+ FL\\. Dist\\. \\d+d \\d+"]}, "constitution": ["FL\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/fl", "pinecone_namespace": "fl", "neo4j_labels": ["Document", "Florida", "State"]}}, "ga": {"code": "ga", "name": "Georgia", "full_name": "State of Georgia", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "GA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Georgia", "abbreviation": "GA Sup. Ct.", "court_id": "ga_supreme"}, {"level": 2, "name": "Georgia Court of Appeals", "abbreviation": "GA App.", "court_id": "ga_appeals"}, {"level": 3, "name": "Georgia District Court", "abbreviation": "GA Dist. Ct.", "court_id": "ga_district"}]}, "citation_formats": {"statute": ["GA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["GA\\. Admin\\. Code § \\d+"], "case": {"ga_supreme": ["\\d+ GA\\. \\d+d \\d+"], "ga_appeals": ["\\d+ GA\\. App\\. \\d+d \\d+"], "ga_district": ["\\d+ GA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["GA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ga", "pinecone_namespace": "ga", "neo4j_labels": ["Document", "Georgia", "State"]}}, "hi": {"code": "hi", "name": "Hawaii", "full_name": "State of Hawaii", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "HI", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Hawaii", "abbreviation": "HI Sup. Ct.", "court_id": "hi_supreme"}, {"level": 2, "name": "Hawaii Court of Appeals", "abbreviation": "HI App.", "court_id": "hi_appeals"}, {"level": 3, "name": "Hawaii District Court", "abbreviation": "HI Dist. Ct.", "court_id": "hi_district"}]}, "citation_formats": {"statute": ["HI\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["HI\\. Admin\\. Code § \\d+"], "case": {"hi_supreme": ["\\d+ HI\\. \\d+d \\d+"], "hi_appeals": ["\\d+ HI\\. App\\. \\d+d \\d+"], "hi_district": ["\\d+ HI\\. Dist\\. \\d+d \\d+"]}, "constitution": ["HI\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/hi", "pinecone_namespace": "hi", "neo4j_labels": ["Document", "Hawaii", "State"]}}, "id": {"code": "id", "name": "Idaho", "full_name": "State of Idaho", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "ID", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Idaho", "abbreviation": "ID Sup. Ct.", "court_id": "id_supreme"}, {"level": 2, "name": "Idaho Court of Appeals", "abbreviation": "ID App.", "court_id": "id_appeals"}, {"level": 3, "name": "Idaho District Court", "abbreviation": "ID Dist. Ct.", "court_id": "id_district"}]}, "citation_formats": {"statute": ["ID\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["ID\\. Admin\\. Code § \\d+"], "case": {"id_supreme": ["\\d+ ID\\. \\d+d \\d+"], "id_appeals": ["\\d+ ID\\. App\\. \\d+d \\d+"], "id_district": ["\\d+ ID\\. Dist\\. \\d+d \\d+"]}, "constitution": ["ID\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/id", "pinecone_namespace": "id", "neo4j_labels": ["Document", "Idaho", "State"]}}, "il": {"code": "il", "name": "Illinois", "full_name": "State of Illinois", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "IL", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Illinois", "abbreviation": "IL Sup. Ct.", "court_id": "il_supreme"}, {"level": 2, "name": "Illinois Court of Appeals", "abbreviation": "IL App.", "court_id": "il_appeals"}, {"level": 3, "name": "Illinois District Court", "abbreviation": "IL Dist. Ct.", "court_id": "il_district"}]}, "citation_formats": {"statute": ["IL\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["IL\\. Admin\\. Code § \\d+"], "case": {"il_supreme": ["\\d+ IL\\. \\d+d \\d+"], "il_appeals": ["\\d+ IL\\. App\\. \\d+d \\d+"], "il_district": ["\\d+ IL\\. Dist\\. \\d+d \\d+"]}, "constitution": ["IL\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/il", "pinecone_namespace": "il", "neo4j_labels": ["Document", "Illinois", "State"]}}, "in": {"code": "in", "name": "Indiana", "full_name": "State of Indiana", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "IN", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Indiana", "abbreviation": "IN Sup. Ct.", "court_id": "in_supreme"}, {"level": 2, "name": "Indiana Court of Appeals", "abbreviation": "IN App.", "court_id": "in_appeals"}, {"level": 3, "name": "Indiana District Court", "abbreviation": "IN Dist. Ct.", "court_id": "in_district"}]}, "citation_formats": {"statute": ["IN\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["IN\\. Admin\\. Code § \\d+"], "case": {"in_supreme": ["\\d+ IN\\. \\d+d \\d+"], "in_appeals": ["\\d+ IN\\. App\\. \\d+d \\d+"], "in_district": ["\\d+ IN\\. Dist\\. \\d+d \\d+"]}, "constitution": ["IN\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/in", "pinecone_namespace": "in", "neo4j_labels": ["Document", "Indiana", "State"]}}, "ia": {"code": "ia", "name": "Iowa", "full_name": "State of Iowa", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "IA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Iowa", "abbreviation": "IA Sup. Ct.", "court_id": "ia_supreme"}, {"level": 2, "name": "Iowa Court of Appeals", "abbreviation": "IA App.", "court_id": "ia_appeals"}, {"level": 3, "name": "Iowa District Court", "abbreviation": "IA Dist. Ct.", "court_id": "ia_district"}]}, "citation_formats": {"statute": ["IA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["IA\\. Admin\\. Code § \\d+"], "case": {"ia_supreme": ["\\d+ IA\\. \\d+d \\d+"], "ia_appeals": ["\\d+ IA\\. App\\. \\d+d \\d+"], "ia_district": ["\\d+ IA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["IA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ia", "pinecone_namespace": "ia", "neo4j_labels": ["Document", "Iowa", "State"]}}, "ks": {"code": "ks", "name": "Kansas", "full_name": "State of Kansas", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "KS", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Kansas", "abbreviation": "KS Sup. Ct.", "court_id": "ks_supreme"}, {"level": 2, "name": "Kansas Court of Appeals", "abbreviation": "KS App.", "court_id": "ks_appeals"}, {"level": 3, "name": "Kansas District Court", "abbreviation": "KS Dist. Ct.", "court_id": "ks_district"}]}, "citation_formats": {"statute": ["KS\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["KS\\. Admin\\. Code § \\d+"], "case": {"ks_supreme": ["\\d+ KS\\. \\d+d \\d+"], "ks_appeals": ["\\d+ KS\\. App\\. \\d+d \\d+"], "ks_district": ["\\d+ KS\\. Dist\\. \\d+d \\d+"]}, "constitution": ["KS\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ks", "pinecone_namespace": "ks", "neo4j_labels": ["Document", "Kansas", "State"]}}, "ky": {"code": "ky", "name": "Kentucky", "full_name": "Commonwealth of Kentucky", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "KY", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Kentucky", "abbreviation": "KY Sup. Ct.", "court_id": "ky_supreme"}, {"level": 2, "name": "Kentucky Court of Appeals", "abbreviation": "KY App.", "court_id": "ky_appeals"}, {"level": 3, "name": "Kentucky District Court", "abbreviation": "KY Dist. Ct.", "court_id": "ky_district"}]}, "citation_formats": {"statute": ["KY\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["KY\\. Admin\\. Code § \\d+"], "case": {"ky_supreme": ["\\d+ KY\\. \\d+d \\d+"], "ky_appeals": ["\\d+ KY\\. App\\. \\d+d \\d+"], "ky_district": ["\\d+ KY\\. Dist\\. \\d+d \\d+"]}, "constitution": ["KY\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ky", "pinecone_namespace": "ky", "neo4j_labels": ["Document", "Kentucky", "State"]}}, "la": {"code": "la", "name": "Louisiana", "full_name": "State of Louisiana", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "LA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Louisiana", "abbreviation": "LA Sup. Ct.", "court_id": "la_supreme"}, {"level": 2, "name": "Louisiana Court of Appeals", "abbreviation": "LA App.", "court_id": "la_appeals"}, {"level": 3, "name": "Louisiana District Court", "abbreviation": "LA Dist. Ct.", "court_id": "la_district"}]}, "citation_formats": {"statute": ["LA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["LA\\. Admin\\. Code § \\d+"], "case": {"la_supreme": ["\\d+ LA\\. \\d+d \\d+"], "la_appeals": ["\\d+ LA\\. App\\. \\d+d \\d+"], "la_district": ["\\d+ LA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["LA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/la", "pinecone_namespace": "la", "neo4j_labels": ["Document", "Louisiana", "State"]}}, "me": {"code": "me", "name": "Maine", "full_name": "State of Maine", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "ME", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Maine", "abbreviation": "ME Sup. Ct.", "court_id": "me_supreme"}, {"level": 2, "name": "Maine Court of Appeals", "abbreviation": "ME App.", "court_id": "me_appeals"}, {"level": 3, "name": "Maine District Court", "abbreviation": "ME Dist. Ct.", "court_id": "me_district"}]}, "citation_formats": {"statute": ["ME\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["ME\\. Admin\\. Code § \\d+"], "case": {"me_supreme": ["\\d+ ME\\. \\d+d \\d+"], "me_appeals": ["\\d+ ME\\. App\\. \\d+d \\d+"], "me_district": ["\\d+ ME\\. Dist\\. \\d+d \\d+"]}, "constitution": ["ME\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/me", "pinecone_namespace": "me", "neo4j_labels": ["Document", "Maine", "State"]}}, "md": {"code": "md", "name": "Maryland", "full_name": "State of Maryland", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MD", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Maryland", "abbreviation": "MD Sup. Ct.", "court_id": "md_supreme"}, {"level": 2, "name": "Maryland Court of Appeals", "abbreviation": "MD App.", "court_id": "md_appeals"}, {"level": 3, "name": "Maryland District Court", "abbreviation": "MD Dist. Ct.", "court_id": "md_district"}]}, "citation_formats": {"statute": ["MD\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MD\\. Admin\\. Code § \\d+"], "case": {"md_supreme": ["\\d+ MD\\. \\d+d \\d+"], "md_appeals": ["\\d+ MD\\. App\\. \\d+d \\d+"], "md_district": ["\\d+ MD\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MD\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/md", "pinecone_namespace": "md", "neo4j_labels": ["Document", "Maryland", "State"]}}, "ma": {"code": "ma", "name": "Massachusetts", "full_name": "Commonwealth of Massachusetts", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Massachusetts", "abbreviation": "MA Sup. Ct.", "court_id": "ma_supreme"}, {"level": 2, "name": "Massachusetts Court of Appeals", "abbreviation": "MA App.", "court_id": "ma_appeals"}, {"level": 3, "name": "Massachusetts District Court", "abbreviation": "MA Dist. Ct.", "court_id": "ma_district"}]}, "citation_formats": {"statute": ["MA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MA\\. Admin\\. Code § \\d+"], "case": {"ma_supreme": ["\\d+ MA\\. \\d+d \\d+"], "ma_appeals": ["\\d+ MA\\. App\\. \\d+d \\d+"], "ma_district": ["\\d+ MA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ma", "pinecone_namespace": "ma", "neo4j_labels": ["Document", "Massachusetts", "State"]}}, "mi": {"code": "mi", "name": "Michigan", "full_name": "State of Michigan", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MI", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Michigan", "abbreviation": "MI Sup. Ct.", "court_id": "mi_supreme"}, {"level": 2, "name": "Michigan Court of Appeals", "abbreviation": "MI App.", "court_id": "mi_appeals"}, {"level": 3, "name": "Michigan District Court", "abbreviation": "MI Dist. Ct.", "court_id": "mi_district"}]}, "citation_formats": {"statute": ["MI\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MI\\. Admin\\. Code § \\d+"], "case": {"mi_supreme": ["\\d+ MI\\. \\d+d \\d+"], "mi_appeals": ["\\d+ MI\\. App\\. \\d+d \\d+"], "mi_district": ["\\d+ MI\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MI\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/mi", "pinecone_namespace": "mi", "neo4j_labels": ["Document", "Michigan", "State"]}}, "mn": {"code": "mn", "name": "Minnesota", "full_name": "State of Minnesota", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MN", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Minnesota", "abbreviation": "MN Sup. Ct.", "court_id": "mn_supreme"}, {"level": 2, "name": "Minnesota Court of Appeals", "abbreviation": "MN App.", "court_id": "mn_appeals"}, {"level": 3, "name": "Minnesota District Court", "abbreviation": "MN Dist. Ct.", "court_id": "mn_district"}]}, "citation_formats": {"statute": ["MN\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MN\\. Admin\\. Code § \\d+"], "case": {"mn_supreme": ["\\d+ MN\\. \\d+d \\d+"], "mn_appeals": ["\\d+ MN\\. App\\. \\d+d \\d+"], "mn_district": ["\\d+ M<PERSON>\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MN\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/mn", "pinecone_namespace": "mn", "neo4j_labels": ["Document", "Minnesota", "State"]}}, "ms": {"code": "ms", "name": "Mississippi", "full_name": "State of Mississippi", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MS", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Mississippi", "abbreviation": "MS Sup. Ct.", "court_id": "ms_supreme"}, {"level": 2, "name": "Mississippi Court of Appeals", "abbreviation": "MS App.", "court_id": "ms_appeals"}, {"level": 3, "name": "Mississippi District Court", "abbreviation": "MS Dist. Ct.", "court_id": "ms_district"}]}, "citation_formats": {"statute": ["MS\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MS\\. Admin\\. Code § \\d+"], "case": {"ms_supreme": ["\\d+ MS\\. \\d+d \\d+"], "ms_appeals": ["\\d+ MS\\. App\\. \\d+d \\d+"], "ms_district": ["\\d+ MS\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MS\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ms", "pinecone_namespace": "ms", "neo4j_labels": ["Document", "Mississippi", "State"]}}, "mo": {"code": "mo", "name": "Missouri", "full_name": "State of Missouri", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MO", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Missouri", "abbreviation": "MO Sup. Ct.", "court_id": "mo_supreme"}, {"level": 2, "name": "Missouri Court of Appeals", "abbreviation": "MO App.", "court_id": "mo_appeals"}, {"level": 3, "name": "Missouri District Court", "abbreviation": "MO Dist. Ct.", "court_id": "mo_district"}]}, "citation_formats": {"statute": ["MO\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MO\\. Admin\\. Code § \\d+"], "case": {"mo_supreme": ["\\d+ MO\\. \\d+d \\d+"], "mo_appeals": ["\\d+ MO\\. App\\. \\d+d \\d+"], "mo_district": ["\\d+ <PERSON>O\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MO\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/mo", "pinecone_namespace": "mo", "neo4j_labels": ["Document", "Missouri", "State"]}}, "mt": {"code": "mt", "name": "Montana", "full_name": "State of Montana", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "MT", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Montana", "abbreviation": "MT Sup. Ct.", "court_id": "mt_supreme"}, {"level": 2, "name": "Montana Court of Appeals", "abbreviation": "MT App.", "court_id": "mt_appeals"}, {"level": 3, "name": "Montana District Court", "abbreviation": "MT Dist. Ct.", "court_id": "mt_district"}]}, "citation_formats": {"statute": ["MT\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MT\\. Admin\\. Code § \\d+"], "case": {"mt_supreme": ["\\d+ MT\\. \\d+d \\d+"], "mt_appeals": ["\\d+ MT\\. App\\. \\d+d \\d+"], "mt_district": ["\\d+ MT\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MT\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/mt", "pinecone_namespace": "mt", "neo4j_labels": ["Document", "Montana", "State"]}}, "ne": {"code": "ne", "name": "Nebraska", "full_name": "State of Nebraska", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NE", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Nebraska", "abbreviation": "NE Sup. Ct.", "court_id": "ne_supreme"}, {"level": 2, "name": "Nebraska Court of Appeals", "abbreviation": "NE App.", "court_id": "ne_appeals"}, {"level": 3, "name": "Nebraska District Court", "abbreviation": "NE Dist. Ct.", "court_id": "ne_district"}]}, "citation_formats": {"statute": ["NE\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NE\\. Admin\\. Code § \\d+"], "case": {"ne_supreme": ["\\d+ NE\\. \\d+d \\d+"], "ne_appeals": ["\\d+ NE\\. App\\. \\d+d \\d+"], "ne_district": ["\\d+ NE\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NE\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ne", "pinecone_namespace": "ne", "neo4j_labels": ["Document", "Nebraska", "State"]}}, "nv": {"code": "nv", "name": "Nevada", "full_name": "State of Nevada", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NV", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Nevada", "abbreviation": "NV Sup. Ct.", "court_id": "nv_supreme"}, {"level": 2, "name": "Nevada Court of Appeals", "abbreviation": "NV App.", "court_id": "nv_appeals"}, {"level": 3, "name": "Nevada District Court", "abbreviation": "NV Dist. Ct.", "court_id": "nv_district"}]}, "citation_formats": {"statute": ["NV\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NV\\. Admin\\. Code § \\d+"], "case": {"nv_supreme": ["\\d+ NV\\. \\d+d \\d+"], "nv_appeals": ["\\d+ NV\\. App\\. \\d+d \\d+"], "nv_district": ["\\d+ NV\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NV\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nv", "pinecone_namespace": "nv", "neo4j_labels": ["Document", "Nevada", "State"]}}, "nh": {"code": "nh", "name": "New Hampshire", "full_name": "State of New Hampshire", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NH", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of New Hampshire", "abbreviation": "NH Sup. Ct.", "court_id": "nh_supreme"}, {"level": 2, "name": "New Hampshire Court of Appeals", "abbreviation": "NH App.", "court_id": "nh_appeals"}, {"level": 3, "name": "New Hampshire District Court", "abbreviation": "NH Dist. Ct.", "court_id": "nh_district"}]}, "citation_formats": {"statute": ["NH\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NH\\. Admin\\. Code § \\d+"], "case": {"nh_supreme": ["\\d+ NH\\. \\d+d \\d+"], "nh_appeals": ["\\d+ NH\\. App\\. \\d+d \\d+"], "nh_district": ["\\d+ NH\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NH\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nh", "pinecone_namespace": "nh", "neo4j_labels": ["Document", "NewHampshire", "State"]}}, "nj": {"code": "nj", "name": "New Jersey", "full_name": "State of New Jersey", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NJ", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of New Jersey", "abbreviation": "NJ Sup. Ct.", "court_id": "nj_supreme"}, {"level": 2, "name": "New Jersey Court of Appeals", "abbreviation": "NJ App.", "court_id": "nj_appeals"}, {"level": 3, "name": "New Jersey District Court", "abbreviation": "NJ Dist. Ct.", "court_id": "nj_district"}]}, "citation_formats": {"statute": ["NJ\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NJ\\. Admin\\. Code § \\d+"], "case": {"nj_supreme": ["\\d+ NJ\\. \\d+d \\d+"], "nj_appeals": ["\\d+ NJ\\. App\\. \\d+d \\d+"], "nj_district": ["\\d+ NJ\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NJ\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nj", "pinecone_namespace": "nj", "neo4j_labels": ["Document", "<PERSON><PERSON><PERSON><PERSON>", "State"]}}, "nm": {"code": "nm", "name": "New Mexico", "full_name": "State of New Mexico", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NM", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of New Mexico", "abbreviation": "NM Sup. Ct.", "court_id": "nm_supreme"}, {"level": 2, "name": "New Mexico Court of Appeals", "abbreviation": "NM App.", "court_id": "nm_appeals"}, {"level": 3, "name": "New Mexico District Court", "abbreviation": "NM Dist. Ct.", "court_id": "nm_district"}]}, "citation_formats": {"statute": ["NM\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NM\\. Admin\\. Code § \\d+"], "case": {"nm_supreme": ["\\d+ NM\\. \\d+d \\d+"], "nm_appeals": ["\\d+ NM\\. App\\. \\d+d \\d+"], "nm_district": ["\\d+ NM\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NM\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nm", "pinecone_namespace": "nm", "neo4j_labels": ["Document", "NewMexico", "State"]}}, "ny": {"code": "ny", "name": "New York", "full_name": "State of New York", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NY", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of New York", "abbreviation": "NY Sup. Ct.", "court_id": "ny_supreme"}, {"level": 2, "name": "New York Court of Appeals", "abbreviation": "NY App.", "court_id": "ny_appeals"}, {"level": 3, "name": "New York District Court", "abbreviation": "NY Dist. Ct.", "court_id": "ny_district"}]}, "citation_formats": {"statute": ["NY\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NY\\. Admin\\. Code § \\d+"], "case": {"ny_supreme": ["\\d+ NY\\. \\d+d \\d+"], "ny_appeals": ["\\d+ NY\\. App\\. \\d+d \\d+"], "ny_district": ["\\d+ NY\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NY\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ny", "pinecone_namespace": "ny", "neo4j_labels": ["Document", "NewYork", "State"]}}, "nc": {"code": "nc", "name": "North Carolina", "full_name": "State of North Carolina", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "NC", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of North Carolina", "abbreviation": "NC Sup. Ct.", "court_id": "nc_supreme"}, {"level": 2, "name": "North Carolina Court of Appeals", "abbreviation": "NC App.", "court_id": "nc_appeals"}, {"level": 3, "name": "North Carolina District Court", "abbreviation": "NC Dist. Ct.", "court_id": "nc_district"}]}, "citation_formats": {"statute": ["NC\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["NC\\. Admin\\. Code § \\d+"], "case": {"nc_supreme": ["\\d+ NC\\. \\d+d \\d+"], "nc_appeals": ["\\d+ NC\\. App\\. \\d+d \\d+"], "nc_district": ["\\d+ NC\\. Dist\\. \\d+d \\d+"]}, "constitution": ["NC\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nc", "pinecone_namespace": "nc", "neo4j_labels": ["Document", "NorthCarolina", "State"]}}, "nd": {"code": "nd", "name": "North Dakota", "full_name": "State of North Dakota", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "ND", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of North Dakota", "abbreviation": "ND Sup. Ct.", "court_id": "nd_supreme"}, {"level": 2, "name": "North Dakota Court of Appeals", "abbreviation": "ND App.", "court_id": "nd_appeals"}, {"level": 3, "name": "North Dakota District Court", "abbreviation": "ND Dist. Ct.", "court_id": "nd_district"}]}, "citation_formats": {"statute": ["ND\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["ND\\. Admin\\. Code § \\d+"], "case": {"nd_supreme": ["\\d+ ND\\. \\d+d \\d+"], "nd_appeals": ["\\d+ ND\\. App\\. \\d+d \\d+"], "nd_district": ["\\d+ ND\\. Dist\\. \\d+d \\d+"]}, "constitution": ["ND\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/nd", "pinecone_namespace": "nd", "neo4j_labels": ["Document", "NorthDakota", "State"]}}, "oh": {"code": "oh", "name": "Ohio", "full_name": "State of Ohio", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "OH", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Ohio", "abbreviation": "OH Sup. Ct.", "court_id": "oh_supreme"}, {"level": 2, "name": "Ohio Court of Appeals", "abbreviation": "OH App.", "court_id": "oh_appeals"}, {"level": 3, "name": "Ohio District Court", "abbreviation": "OH Dist. Ct.", "court_id": "oh_district"}]}, "citation_formats": {"statute": ["OH\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["OH\\. Admin\\. Code § \\d+"], "case": {"oh_supreme": ["\\d+ OH\\. \\d+d \\d+"], "oh_appeals": ["\\d+ OH\\. App\\. \\d+d \\d+"], "oh_district": ["\\d+ OH\\. Dist\\. \\d+d \\d+"]}, "constitution": ["OH\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/oh", "pinecone_namespace": "oh", "neo4j_labels": ["Document", "Ohio", "State"]}}, "ok": {"code": "ok", "name": "Oklahoma", "full_name": "State of Oklahoma", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "OK", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Oklahoma", "abbreviation": "OK Sup. Ct.", "court_id": "ok_supreme"}, {"level": 2, "name": "Oklahoma Court of Appeals", "abbreviation": "OK App.", "court_id": "ok_appeals"}, {"level": 3, "name": "Oklahoma District Court", "abbreviation": "OK Dist. Ct.", "court_id": "ok_district"}]}, "citation_formats": {"statute": ["OK\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["OK\\. Admin\\. Code § \\d+"], "case": {"ok_supreme": ["\\d+ OK\\. \\d+d \\d+"], "ok_appeals": ["\\d+ OK\\. App\\. \\d+d \\d+"], "ok_district": ["\\d+ OK\\. Dist\\. \\d+d \\d+"]}, "constitution": ["OK\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ok", "pinecone_namespace": "ok", "neo4j_labels": ["Document", "Oklahoma", "State"]}}, "or": {"code": "or", "name": "Oregon", "full_name": "State of Oregon", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "OR", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Oregon", "abbreviation": "OR Sup. Ct.", "court_id": "or_supreme"}, {"level": 2, "name": "Oregon Court of Appeals", "abbreviation": "OR App.", "court_id": "or_appeals"}, {"level": 3, "name": "Oregon District Court", "abbreviation": "OR Dist. Ct.", "court_id": "or_district"}]}, "citation_formats": {"statute": ["OR\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["OR\\. Admin\\. Code § \\d+"], "case": {"or_supreme": ["\\d+ OR\\. \\d+d \\d+"], "or_appeals": ["\\d+ OR\\. App\\. \\d+d \\d+"], "or_district": ["\\d+ OR\\. Dist\\. \\d+d \\d+"]}, "constitution": ["OR\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/or", "pinecone_namespace": "or", "neo4j_labels": ["Document", "Oregon", "State"]}}, "pa": {"code": "pa", "name": "Pennsylvania", "full_name": "Commonwealth of Pennsylvania", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "PA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Pennsylvania", "abbreviation": "PA Sup. Ct.", "court_id": "pa_supreme"}, {"level": 2, "name": "Pennsylvania Court of Appeals", "abbreviation": "PA App.", "court_id": "pa_appeals"}, {"level": 3, "name": "Pennsylvania District Court", "abbreviation": "PA Dist. Ct.", "court_id": "pa_district"}]}, "citation_formats": {"statute": ["PA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["PA\\. Admin\\. Code § \\d+"], "case": {"pa_supreme": ["\\d+ PA\\. \\d+d \\d+"], "pa_appeals": ["\\d+ PA\\. App\\. \\d+d \\d+"], "pa_district": ["\\d+ PA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["PA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/pa", "pinecone_namespace": "pa", "neo4j_labels": ["Document", "Pennsylvania", "State"]}}, "ri": {"code": "ri", "name": "Rhode Island", "full_name": "State of Rhode Island", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "RI", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Rhode Island", "abbreviation": "RI Sup. Ct.", "court_id": "ri_supreme"}, {"level": 2, "name": "Rhode Island Court of Appeals", "abbreviation": "RI App.", "court_id": "ri_appeals"}, {"level": 3, "name": "Rhode Island District Court", "abbreviation": "RI Dist. Ct.", "court_id": "ri_district"}]}, "citation_formats": {"statute": ["RI\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["RI\\. Admin\\. Code § \\d+"], "case": {"ri_supreme": ["\\d+ RI\\. \\d+d \\d+"], "ri_appeals": ["\\d+ RI\\. App\\. \\d+d \\d+"], "ri_district": ["\\d+ RI\\. Dist\\. \\d+d \\d+"]}, "constitution": ["RI\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ri", "pinecone_namespace": "ri", "neo4j_labels": ["Document", "RhodeIsland", "State"]}}, "sc": {"code": "sc", "name": "South Carolina", "full_name": "State of South Carolina", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "SC", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of South Carolina", "abbreviation": "SC Sup. Ct.", "court_id": "sc_supreme"}, {"level": 2, "name": "South Carolina Court of Appeals", "abbreviation": "SC App.", "court_id": "sc_appeals"}, {"level": 3, "name": "South Carolina District Court", "abbreviation": "SC Dist. Ct.", "court_id": "sc_district"}]}, "citation_formats": {"statute": ["SC\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["SC\\. Admin\\. Code § \\d+"], "case": {"sc_supreme": ["\\d+ SC\\. \\d+d \\d+"], "sc_appeals": ["\\d+ SC\\. App\\. \\d+d \\d+"], "sc_district": ["\\d+ SC\\. Dist\\. \\d+d \\d+"]}, "constitution": ["SC\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/sc", "pinecone_namespace": "sc", "neo4j_labels": ["Document", "SouthCarolina", "State"]}}, "sd": {"code": "sd", "name": "South Dakota", "full_name": "State of South Dakota", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "SD", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of South Dakota", "abbreviation": "SD Sup. Ct.", "court_id": "sd_supreme"}, {"level": 2, "name": "South Dakota Court of Appeals", "abbreviation": "SD App.", "court_id": "sd_appeals"}, {"level": 3, "name": "South Dakota District Court", "abbreviation": "SD Dist. Ct.", "court_id": "sd_district"}]}, "citation_formats": {"statute": ["SD\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["SD\\. Admin\\. Code § \\d+"], "case": {"sd_supreme": ["\\d+ SD\\. \\d+d \\d+"], "sd_appeals": ["\\d+ SD\\. App\\. \\d+d \\d+"], "sd_district": ["\\d+ SD\\. Dist\\. \\d+d \\d+"]}, "constitution": ["SD\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/sd", "pinecone_namespace": "sd", "neo4j_labels": ["Document", "SouthDakota", "State"]}}, "tn": {"code": "tn", "name": "Tennessee", "full_name": "State of Tennessee", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "TN", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Tennessee", "abbreviation": "TN Sup. Ct.", "court_id": "tn_supreme"}, {"level": 2, "name": "Tennessee Court of Appeals", "abbreviation": "TN App.", "court_id": "tn_appeals"}, {"level": 3, "name": "Tennessee District Court", "abbreviation": "TN Dist. Ct.", "court_id": "tn_district"}]}, "citation_formats": {"statute": ["TN\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["TN\\. Admin\\. Code § \\d+"], "case": {"tn_supreme": ["\\d+ TN\\. \\d+d \\d+"], "tn_appeals": ["\\d+ TN\\. App\\. \\d+d \\d+"], "tn_district": ["\\d+ TN\\. Dist\\. \\d+d \\d+"]}, "constitution": ["TN\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/tn", "pinecone_namespace": "tn", "neo4j_labels": ["Document", "Tennessee", "State"]}}, "tx": {"code": "tx", "name": "Texas", "full_name": "State of Texas", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "TX", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Texas", "abbreviation": "TX Sup. Ct.", "court_id": "tx_supreme"}, {"level": 2, "name": "Texas Court of Appeals", "abbreviation": "TX App.", "court_id": "tx_appeals"}, {"level": 3, "name": "Texas District Court", "abbreviation": "TX Dist. Ct.", "court_id": "tx_district"}]}, "citation_formats": {"statute": ["TX\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["TX\\. Admin\\. Code § \\d+"], "case": {"tx_supreme": ["\\d+ TX\\. \\d+d \\d+"], "tx_appeals": ["\\d+ TX\\. App\\. \\d+d \\d+"], "tx_district": ["\\d+ TX\\. Dist\\. \\d+d \\d+"]}, "constitution": ["TX\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/tx", "pinecone_namespace": "tx", "neo4j_labels": ["Document", "Texas", "State"]}}, "ut": {"code": "ut", "name": "Utah", "full_name": "State of Utah", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "UT", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Utah", "abbreviation": "UT Sup. Ct.", "court_id": "ut_supreme"}, {"level": 2, "name": "Utah Court of Appeals", "abbreviation": "UT App.", "court_id": "ut_appeals"}, {"level": 3, "name": "Utah District Court", "abbreviation": "UT Dist. Ct.", "court_id": "ut_district"}]}, "citation_formats": {"statute": ["UT\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["UT\\. Admin\\. Code § \\d+"], "case": {"ut_supreme": ["\\d+ UT\\. \\d+d \\d+"], "ut_appeals": ["\\d+ UT\\. App\\. \\d+d \\d+"], "ut_district": ["\\d+ UT\\. Dist\\. \\d+d \\d+"]}, "constitution": ["UT\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/ut", "pinecone_namespace": "ut", "neo4j_labels": ["Document", "Utah", "State"]}}, "vt": {"code": "vt", "name": "Vermont", "full_name": "State of Vermont", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "VT", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Vermont", "abbreviation": "VT Sup. Ct.", "court_id": "vt_supreme"}, {"level": 2, "name": "Vermont Court of Appeals", "abbreviation": "VT App.", "court_id": "vt_appeals"}, {"level": 3, "name": "Vermont District Court", "abbreviation": "VT Dist. Ct.", "court_id": "vt_district"}]}, "citation_formats": {"statute": ["VT\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["VT\\. Admin\\. Code § \\d+"], "case": {"vt_supreme": ["\\d+ VT\\. \\d+d \\d+"], "vt_appeals": ["\\d+ VT\\. App\\. \\d+d \\d+"], "vt_district": ["\\d+ VT\\. Dist\\. \\d+d \\d+"]}, "constitution": ["VT\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/vt", "pinecone_namespace": "vt", "neo4j_labels": ["Document", "Vermont", "State"]}}, "va": {"code": "va", "name": "Virginia", "full_name": "Commonwealth of Virginia", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "VA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Virginia", "abbreviation": "VA Sup. Ct.", "court_id": "va_supreme"}, {"level": 2, "name": "Virginia Court of Appeals", "abbreviation": "VA App.", "court_id": "va_appeals"}, {"level": 3, "name": "Virginia District Court", "abbreviation": "VA Dist. Ct.", "court_id": "va_district"}]}, "citation_formats": {"statute": ["VA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["VA\\. Admin\\. Code § \\d+"], "case": {"va_supreme": ["\\d+ VA\\. \\d+d \\d+"], "va_appeals": ["\\d+ VA\\. App\\. \\d+d \\d+"], "va_district": ["\\d+ VA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["VA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/va", "pinecone_namespace": "va", "neo4j_labels": ["Document", "Virginia", "State"]}}, "wa": {"code": "wa", "name": "Washington", "full_name": "State of Washington", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "WA", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Washington", "abbreviation": "WA Sup. Ct.", "court_id": "wa_supreme"}, {"level": 2, "name": "Washington Court of Appeals", "abbreviation": "WA App.", "court_id": "wa_appeals"}, {"level": 3, "name": "Washington District Court", "abbreviation": "WA Dist. Ct.", "court_id": "wa_district"}]}, "citation_formats": {"statute": ["WA\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["WA\\. Admin\\. Code § \\d+"], "case": {"wa_supreme": ["\\d+ WA\\. \\d+d \\d+"], "wa_appeals": ["\\d+ WA\\. App\\. \\d+d \\d+"], "wa_district": ["\\d+ WA\\. Dist\\. \\d+d \\d+"]}, "constitution": ["WA\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/wa", "pinecone_namespace": "wa", "neo4j_labels": ["Document", "Washington", "State"]}}, "wv": {"code": "wv", "name": "West Virginia", "full_name": "State of West Virginia", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "WV", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of West Virginia", "abbreviation": "WV Sup. Ct.", "court_id": "wv_supreme"}, {"level": 2, "name": "West Virginia Court of Appeals", "abbreviation": "WV App.", "court_id": "wv_appeals"}, {"level": 3, "name": "West Virginia District Court", "abbreviation": "WV Dist. Ct.", "court_id": "wv_district"}]}, "citation_formats": {"statute": ["WV\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["WV\\. Admin\\. Code § \\d+"], "case": {"wv_supreme": ["\\d+ WV\\. \\d+d \\d+"], "wv_appeals": ["\\d+ WV\\. App\\. \\d+d \\d+"], "wv_district": ["\\d+ WV\\. Dist\\. \\d+d \\d+"]}, "constitution": ["WV\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/wv", "pinecone_namespace": "wv", "neo4j_labels": ["Document", "WestVirginia", "State"]}}, "wi": {"code": "wi", "name": "Wisconsin", "full_name": "State of Wisconsin", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "WI", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Wisconsin", "abbreviation": "WI Sup. Ct.", "court_id": "wi_supreme"}, {"level": 2, "name": "Wisconsin Court of Appeals", "abbreviation": "WI App.", "court_id": "wi_appeals"}, {"level": 3, "name": "Wisconsin District Court", "abbreviation": "WI Dist. Ct.", "court_id": "wi_district"}]}, "citation_formats": {"statute": ["WI\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["WI\\. Admin\\. Code § \\d+"], "case": {"wi_supreme": ["\\d+ WI\\. \\d+d \\d+"], "wi_appeals": ["\\d+ WI\\. App\\. \\d+d \\d+"], "wi_district": ["\\d+ WI\\. Dist\\. \\d+d \\d+"]}, "constitution": ["WI\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/wi", "pinecone_namespace": "wi", "neo4j_labels": ["Document", "Wisconsin", "State"]}}, "wy": {"code": "wy", "name": "Wyoming", "full_name": "State of Wyoming", "level": "state", "parent_jurisdiction": "fed", "abbreviation": "WY", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Wyoming", "abbreviation": "WY Sup. Ct.", "court_id": "wy_supreme"}, {"level": 2, "name": "Wyoming Court of Appeals", "abbreviation": "WY App.", "court_id": "wy_appeals"}, {"level": 3, "name": "Wyoming District Court", "abbreviation": "WY Dist. Ct.", "court_id": "wy_district"}]}, "citation_formats": {"statute": ["WY\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["WY\\. Admin\\. Code § \\d+"], "case": {"wy_supreme": ["\\d+ WY\\. \\d+d \\d+"], "wy_appeals": ["\\d+ WY\\. App\\. \\d+d \\d+"], "wy_district": ["\\d+ WY\\. Dist\\. \\d+d \\d+"]}, "constitution": ["WY\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/wy", "pinecone_namespace": "wy", "neo4j_labels": ["Document", "Wyoming", "State"]}}, "dc": {"code": "dc", "name": "District of Columbia", "full_name": "District of Columbia", "level": "district", "parent_jurisdiction": "fed", "abbreviation": "DC", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of District of Columbia", "abbreviation": "DC Sup. Ct.", "court_id": "dc_supreme"}, {"level": 2, "name": "District of Columbia Court of Appeals", "abbreviation": "DC App.", "court_id": "dc_appeals"}, {"level": 3, "name": "District of Columbia District Court", "abbreviation": "DC Dist. Ct.", "court_id": "dc_district"}]}, "citation_formats": {"statute": ["DC\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["DC\\. Admin\\. Code § \\d+"], "case": {"dc_supreme": ["\\d+ DC\\. \\d+d \\d+"], "dc_appeals": ["\\d+ DC\\. App\\. \\d+d \\d+"], "dc_district": ["\\d+ DC\\. Dist\\. \\d+d \\d+"]}, "constitution": ["DC\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/dc", "pinecone_namespace": "dc", "neo4j_labels": ["Document", "DistrictofColumbia", "District"]}}, "pr": {"code": "pr", "name": "Puerto Rico", "full_name": "Commonwealth of Puerto Rico", "level": "territory", "parent_jurisdiction": "fed", "abbreviation": "PR", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Puerto Rico", "abbreviation": "PR Sup. Ct.", "court_id": "pr_supreme"}, {"level": 2, "name": "Puerto Rico Court of Appeals", "abbreviation": "PR App.", "court_id": "pr_appeals"}, {"level": 3, "name": "Puerto Rico District Court", "abbreviation": "PR Dist. Ct.", "court_id": "pr_district"}]}, "citation_formats": {"statute": ["PR\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["PR\\. Admin\\. Code § \\d+"], "case": {"pr_supreme": ["\\d+ PR\\. \\d+d \\d+"], "pr_appeals": ["\\d+ PR\\. App\\. \\d+d \\d+"], "pr_district": ["\\d+ PR\\. Dist\\. \\d+d \\d+"]}, "constitution": ["PR\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/pr", "pinecone_namespace": "pr", "neo4j_labels": ["Document", "PuertoRico", "Territory"]}}, "vi": {"code": "vi", "name": "U.S. Virgin Islands", "full_name": "U.S. Virgin Islands", "level": "territory", "parent_jurisdiction": "fed", "abbreviation": "VI", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of U.S. Virgin Islands", "abbreviation": "VI Sup. Ct.", "court_id": "vi_supreme"}, {"level": 2, "name": "U.S. Virgin Islands Court of Appeals", "abbreviation": "VI App.", "court_id": "vi_appeals"}, {"level": 3, "name": "U.S. Virgin Islands District Court", "abbreviation": "VI Dist. Ct.", "court_id": "vi_district"}]}, "citation_formats": {"statute": ["VI\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["VI\\. Admin\\. Code § \\d+"], "case": {"vi_supreme": ["\\d+ VI\\. \\d+d \\d+"], "vi_appeals": ["\\d+ VI\\. App\\. \\d+d \\d+"], "vi_district": ["\\d+ VI\\. Dist\\. \\d+d \\d+"]}, "constitution": ["VI\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/vi", "pinecone_namespace": "vi", "neo4j_labels": ["Document", "U.S.VirginIslands", "Territory"]}}, "gu": {"code": "gu", "name": "Guam", "full_name": "Territory of Guam", "level": "territory", "parent_jurisdiction": "fed", "abbreviation": "GU", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Guam", "abbreviation": "GU Sup. Ct.", "court_id": "gu_supreme"}, {"level": 2, "name": "Guam Court of Appeals", "abbreviation": "GU App.", "court_id": "gu_appeals"}, {"level": 3, "name": "Guam District Court", "abbreviation": "GU Dist. Ct.", "court_id": "gu_district"}]}, "citation_formats": {"statute": ["GU\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["GU\\. Admin\\. Code § \\d+"], "case": {"gu_supreme": ["\\d+ GU\\. \\d+d \\d+"], "gu_appeals": ["\\d+ GU\\. App\\. \\d+d \\d+"], "gu_district": ["\\d+ GU\\. Dist\\. \\d+d \\d+"]}, "constitution": ["GU\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/gu", "pinecone_namespace": "gu", "neo4j_labels": ["Document", "Guam", "Territory"]}}, "as": {"code": "as", "name": "American Samoa", "full_name": "Territory of American Samoa", "level": "territory", "parent_jurisdiction": "fed", "abbreviation": "AS", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of American Samoa", "abbreviation": "AS Sup. Ct.", "court_id": "as_supreme"}, {"level": 2, "name": "American Samoa Court of Appeals", "abbreviation": "AS App.", "court_id": "as_appeals"}, {"level": 3, "name": "American Samoa District Court", "abbreviation": "AS Dist. Ct.", "court_id": "as_district"}]}, "citation_formats": {"statute": ["AS\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["AS\\. Admin\\. Code § \\d+"], "case": {"as_supreme": ["\\d+ AS\\. \\d+d \\d+"], "as_appeals": ["\\d+ AS\\. App\\. \\d+d \\d+"], "as_district": ["\\d+ AS\\. Dist\\. \\d+d \\d+"]}, "constitution": ["AS\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/as", "pinecone_namespace": "as", "neo4j_labels": ["Document", "AmericanSamoa", "Territory"]}}, "mp": {"code": "mp", "name": "Northern Mariana Islands", "full_name": "Commonwealth of the Northern Mariana Islands", "level": "territory", "parent_jurisdiction": "fed", "abbreviation": "MP", "court_hierarchy": {"levels": [{"level": 1, "name": "Supreme Court of Northern Mariana Islands", "abbreviation": "MP <PERSON>p. <PERSON>t.", "court_id": "mp_supreme"}, {"level": 2, "name": "Northern Mariana Islands Court of Appeals", "abbreviation": "MP App.", "court_id": "mp_appeals"}, {"level": 3, "name": "Northern Mariana Islands District Court", "abbreviation": "MP Dist<PERSON> Ct.", "court_id": "mp_district"}]}, "citation_formats": {"statute": ["MP\\. [A-Z][a-z]+ Code § \\d+"], "regulation": ["MP\\. Admin\\. Code § \\d+"], "case": {"mp_supreme": ["\\d+ MP\\. \\d+d \\d+"], "mp_appeals": ["\\d+ MP\\. App\\. \\d+d \\d+"], "mp_district": ["\\d+ MP\\. Dist\\. \\d+d \\d+"]}, "constitution": ["MP\\. Const\\. art\\. [IVX]+"]}, "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "storage_config": {"gcs_prefix": "legal/mp", "pinecone_namespace": "mp", "neo4j_labels": ["Document", "NorthernMarianaIslands", "Territory"]}}}, "global_settings": {"default_jurisdiction": "tx", "supported_document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"], "cross_jurisdictional_citations": true, "jurisdiction_hierarchy_enabled": true, "storage_organization": {"gcs_structure": "{jurisdiction}/{doc_type}/{year}/{document_id}", "pinecone_namespace_pattern": "{jurisdiction}_{doc_type}", "neo4j_label_pattern": ["Document", "{<PERSON>ris<PERSON>}", "{DocType}"]}}}