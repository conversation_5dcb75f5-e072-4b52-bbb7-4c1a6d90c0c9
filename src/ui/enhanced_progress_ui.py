"""
Enhanced Progress UI
Rich terminal interface with real-time progress, storage status, and quality metrics
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from rich.console import Console
from rich.live import Live
from rich.layout import Layout
from rich.panel import Panel
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn
from rich.table import Table
from rich.text import Text
from rich.align import Align
from rich import box
import logging

logger = logging.getLogger(__name__)


class EnhancedProgressUI:
    """
    Rich terminal UI for Texas legal corpus processing
    """
    
    def __init__(self, refresh_rate: float = 2.0):
        self.console = Console()
        self.refresh_rate = refresh_rate
        self.live = None
        
        # Progress tracking
        self.stats = {
            'start_time': datetime.now(),
            'current_phase': 'initializing',
            'current_window': None,
            'windows_completed': 0,
            'total_windows': 0,
            'current_batch': 0,
            'total_batches': 0,
            'cases_fetched': 0,
            'cases_processed': 0,
            'cases_stored': 0,
            'fetch_rate': 0.0,
            'process_rate': 0.0,
            'success_rate': 0.0,
            'quality_score': 0.0,
            'retry_count': 0,
            'dlq_count': 0,
            'storage_status': {
                'supabase': {'count': 0, 'consistency': 1.0, 'status': '⏳'},
                'gcs': {'count': 0, 'consistency': 1.0, 'status': '⏳'},
                'pinecone': {'count': 0, 'consistency': 1.0, 'status': '⏳'},
                'neo4j': {'count': 0, 'consistency': 1.0, 'status': '⏳'}
            },
            'errors': [],
            'last_checkpoint': None
        }
        
        # Progress bars
        self.window_progress = Progress(
            TextColumn("[bold blue]Time Windows"),
            BarColumn(bar_width=40),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "({task.completed}/{task.total})",
            TimeRemainingColumn()
        )
        
        self.batch_progress = Progress(
            TextColumn("[bold green]Current Batch"),
            BarColumn(bar_width=40),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "({task.completed}/{task.total})"
        )
        
        # Task IDs
        self.window_task = None
        self.batch_task = None
    
    async def start(self):
        """Start the live display"""
        if self.live is None:
            self.live = Live(
                self._create_layout(),
                console=self.console,
                refresh_per_second=self.refresh_rate,
                screen=False
            )
            self.live.start()
            
            # Initialize progress tasks
            self.window_task = self.window_progress.add_task(
                "windows", total=100, completed=0
            )
            self.batch_task = self.batch_progress.add_task(
                "batch", total=100, completed=0
            )
    
    async def stop(self):
        """Stop the live display"""
        if self.live:
            self.live.stop()
            self.live = None
    
    def update_stats(self, new_stats: Dict[str, Any]):
        """Update statistics and refresh display"""
        self.stats.update(new_stats)
        
        # Update progress bars
        if self.window_task is not None and self.stats['total_windows'] > 0:
            self.window_progress.update(
                self.window_task,
                total=self.stats['total_windows'],
                completed=self.stats['windows_completed']
            )
        
        if self.batch_task is not None and self.stats['total_batches'] > 0:
            self.batch_progress.update(
                self.batch_task,
                total=self.stats['total_batches'],
                completed=self.stats['current_batch']
            )
        
        # Update live display
        if self.live:
            self.live.update(self._create_layout())
    
    def _create_layout(self) -> Layout:
        """Create the main layout"""
        # Create storage and quality layouts
        storage_layout = Layout(self._create_storage_section(), name="storage", ratio=1)
        quality_layout = Layout(self._create_quality_section(), name="quality", ratio=1)

        # Create middle section
        middle_layout = Layout(name="middle", size=12)
        middle_layout.split_row(storage_layout, quality_layout)

        # Create main layout
        layout = Layout()
        layout.split_column(
            Layout(self._create_header(), name="header", size=3),
            Layout(self._create_progress_section(), name="progress", size=8),
            middle_layout,
            Layout(self._create_footer(), name="footer", size=5)
        )

        return layout
    
    def _create_header(self) -> Panel:
        """Create header panel"""
        elapsed = datetime.now() - self.stats['start_time']
        
        header_text = Text()
        header_text.append("🏛️ Texas Legal Corpus Processing ", style="bold cyan")
        header_text.append(f"• Phase: {self.stats['current_phase'].title()} ", style="bold white")
        header_text.append(f"• Elapsed: {self._format_duration(elapsed)}", style="dim white")
        
        return Panel(
            Align.center(header_text),
            box=box.ROUNDED,
            style="cyan"
        )
    
    def _create_progress_section(self) -> Panel:
        """Create progress section with bars"""
        progress_layout = Layout()
        progress_layout.split_column(
            Layout(self.window_progress, size=2),
            Layout(self.batch_progress, size=2),
            Layout(self._create_current_status(), size=4)
        )
        
        return Panel(
            progress_layout,
            title="📊 Progress",
            box=box.ROUNDED
        )
    
    def _create_current_status(self) -> Table:
        """Create current status table"""
        table = Table(show_header=False, box=None, padding=(0, 1))
        table.add_column("Metric", style="cyan", width=20)
        table.add_column("Value", style="white", width=25)
        table.add_column("Rate", style="green", width=20)
        
        # Current window info
        current_window = self.stats.get('current_window', 'N/A')
        table.add_row("Current Window:", str(current_window), "")
        
        # Fetch statistics
        table.add_row(
            "Cases Fetched:",
            f"{self.stats['cases_fetched']:,}",
            f"{self.stats['fetch_rate']:.0f}/min"
        )
        
        # Process statistics
        table.add_row(
            "Cases Processed:",
            f"{self.stats['cases_processed']:,}",
            f"{self.stats['process_rate']:.0f}/min"
        )
        
        return table
    
    def _create_storage_section(self) -> Panel:
        """Create storage status section"""
        storage_table = Table(title="Storage System Status", box=box.SIMPLE)
        storage_table.add_column("System", style="cyan", width=12)
        storage_table.add_column("Count", justify="right", width=10)
        storage_table.add_column("Consistency", justify="right", width=12)
        storage_table.add_column("Status", justify="center", width=8)
        
        for system, data in self.stats['storage_status'].items():
            consistency_color = "green" if data['consistency'] > 0.98 else "yellow" if data['consistency'] > 0.95 else "red"
            
            storage_table.add_row(
                system.title(),
                f"{data['count']:,}",
                f"[{consistency_color}]{data['consistency']:.1%}[/{consistency_color}]",
                data['status']
            )
        
        return Panel(storage_table, box=box.ROUNDED)
    
    def _create_quality_section(self) -> Panel:
        """Create quality metrics section"""
        quality_text = Text()
        
        # Success rate
        success_color = "green" if self.stats['success_rate'] > 0.95 else "yellow" if self.stats['success_rate'] > 0.90 else "red"
        quality_text.append("Success Rate: ", style="white")
        quality_text.append(f"{self.stats['success_rate']:.1%}\n", style=success_color)
        
        # Quality score
        quality_color = "green" if self.stats['quality_score'] > 0.95 else "yellow" if self.stats['quality_score'] > 0.90 else "red"
        quality_text.append("Quality Score: ", style="white")
        quality_text.append(f"{self.stats['quality_score']:.1%}\n", style=quality_color)
        
        # Error tracking
        quality_text.append("DLQ Items: ", style="white")
        dlq_color = "green" if self.stats['dlq_count'] == 0 else "yellow" if self.stats['dlq_count'] < 10 else "red"
        quality_text.append(f"{self.stats['dlq_count']}\n", style=dlq_color)
        
        # Retry budget
        quality_text.append("Retry Count: ", style="white")
        retry_color = "green" if self.stats['retry_count'] < 10 else "yellow" if self.stats['retry_count'] < 50 else "red"
        quality_text.append(f"{self.stats['retry_count']}", style=retry_color)
        
        # Recent errors
        if self.stats['errors']:
            quality_text.append("\n\nRecent Errors:\n", style="bold red")
            for error in self.stats['errors'][-3:]:  # Show last 3 errors
                quality_text.append(f"• {error}\n", style="red")
        
        return Panel(
            quality_text,
            title="🎯 Quality Metrics",
            box=box.ROUNDED
        )
    
    def _create_footer(self) -> Panel:
        """Create footer with ETA and checkpoint info"""
        footer_text = Text()
        
        # Calculate ETA
        if self.stats['fetch_rate'] > 0 and self.stats['cases_fetched'] > 0:
            remaining_cases = max(0, 150000 - self.stats['cases_fetched'])  # Estimated total
            eta_minutes = remaining_cases / self.stats['fetch_rate']
            eta = datetime.now() + timedelta(minutes=eta_minutes)
            footer_text.append(f"⏱️  ETA: {eta.strftime('%H:%M:%S')} ", style="cyan")
            footer_text.append(f"({self._format_duration(timedelta(minutes=eta_minutes))} remaining)", style="dim cyan")
        else:
            footer_text.append("⏱️  ETA: Calculating...", style="dim cyan")
        
        # Checkpoint info
        if self.stats['last_checkpoint']:
            footer_text.append(f" • 💾 Last Checkpoint: {self.stats['last_checkpoint']}", style="dim white")
        
        return Panel(
            Align.center(footer_text),
            box=box.ROUNDED,
            style="dim"
        )
    
    def _format_duration(self, duration: timedelta) -> str:
        """Format duration as human-readable string"""
        total_seconds = int(duration.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    def log_error(self, error: str):
        """Log an error to the display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.stats['errors'].append(f"[{timestamp}] {error}")
        
        # Keep only last 10 errors
        if len(self.stats['errors']) > 10:
            self.stats['errors'] = self.stats['errors'][-10:]
    
    def set_phase(self, phase: str):
        """Set current processing phase"""
        self.stats['current_phase'] = phase
        logger.info(f"Phase changed to: {phase}")
    
    def set_window(self, window: str, window_num: int, total_windows: int):
        """Set current time window"""
        self.stats['current_window'] = window
        self.stats['windows_completed'] = window_num
        self.stats['total_windows'] = total_windows
    
    def update_batch_progress(self, current: int, total: int):
        """Update batch progress"""
        self.stats['current_batch'] = current
        self.stats['total_batches'] = total
    
    def update_rates(self, fetch_rate: float, process_rate: float):
        """Update processing rates"""
        self.stats['fetch_rate'] = fetch_rate
        self.stats['process_rate'] = process_rate
    
    def update_storage_status(self, system: str, count: int, consistency: float, status: str):
        """Update storage system status"""
        if system in self.stats['storage_status']:
            self.stats['storage_status'][system].update({
                'count': count,
                'consistency': consistency,
                'status': status
            })
    
    def save_checkpoint(self, checkpoint_id: str):
        """Record checkpoint save"""
        self.stats['last_checkpoint'] = checkpoint_id
        logger.info(f"Checkpoint saved: {checkpoint_id}")


# Convenience functions
def create_progress_ui(refresh_rate: float = 2.0) -> EnhancedProgressUI:
    """Create an enhanced progress UI"""
    return EnhancedProgressUI(refresh_rate)


async def demo_progress_ui():
    """Demo the progress UI"""
    ui = EnhancedProgressUI()
    await ui.start()
    
    try:
        # Simulate processing
        ui.set_phase("fetching")
        ui.set_window("2024-01", 1, 12)
        
        for i in range(100):
            ui.update_stats({
                'cases_fetched': i * 100,
                'cases_processed': i * 95,
                'fetch_rate': 450.0,
                'process_rate': 380.0,
                'success_rate': 0.96,
                'quality_score': 0.95
            })
            
            ui.update_storage_status('supabase', i * 95, 0.98, '✅')
            ui.update_batch_progress(i, 100)
            
            await asyncio.sleep(0.1)
        
        ui.set_phase("complete")
        await asyncio.sleep(2)
        
    finally:
        await ui.stop()


if __name__ == "__main__":
    asyncio.run(demo_progress_ui())
