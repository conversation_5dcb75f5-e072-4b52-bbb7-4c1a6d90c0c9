#!/usr/bin/env python3
"""
Fix Real Data Patterns
Create patterns specifically for the actual CourtListener text format
"""

import requests
import re
import os
from dotenv import load_dotenv

def examine_real_courtlistener_text_in_detail():
    """Examine real CourtListener text in detail to create exact patterns"""
    
    print("🔍 EXAMINING REAL COURTLISTENER TEXT IN DETAIL")
    print("=" * 60)
    
    load_dotenv()
    cl_api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not cl_api_key:
        print("❌ No CourtListener API key found")
        return None
    
    headers = {
        'Authorization': f'Token {cl_api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    try:
        # Get a real case
        response = requests.get(
            "https://www.courtlistener.com/api/rest/v4/opinions/",
            headers=headers,
            params={
                'court': 'ca5',
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 1
            },
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return None
        
        results = response.json().get('results', [])
        
        if not results:
            print("❌ No results from API")
            return None
        
        case = results[0]
        text = case.get('plain_text', '') or case.get('html', '')
        
        if not text:
            print("❌ No text content")
            return None
        
        print(f"📄 REAL CASE: {case.get('case_name', 'Unknown')}")
        print(f"   Court: {case.get('court', 'Unknown')}")
        print(f"   Text length: {len(text):,} characters")
        
        # Show more of the text structure
        print(f"\n📝 DETAILED TEXT STRUCTURE (first 2000 chars):")
        print("=" * 60)
        print(text[:2000])
        print("=" * 60)
        
        # Look for any mention of judges, justices, or names
        print(f"\n🔍 SEARCHING FOR ANY JUDGE/NAME MENTIONS:")
        
        # Search for any capitalized words that might be names
        capitalized_words = re.findall(r'\b[A-Z][a-z]+\b', text)
        print(f"   Capitalized words found: {len(capitalized_words)}")
        
        # Filter for potential names (2+ characters, not common words)
        common_words = {'The', 'Court', 'United', 'States', 'District', 'Case', 'No', 'Before', 'Judge', 'Justice', 'Order', 'Motion', 'Plaintiff', 'Defendant'}
        potential_names = [word for word in capitalized_words if word not in common_words and len(word) > 2]
        
        print(f"   Potential names: {potential_names[:20]}")  # Show first 20
        
        # Look for specific patterns that might contain judge names
        judge_search_patterns = [
            r'Judge\s+[A-Z][a-z]+',
            r'Justice\s+[A-Z][a-z]+',
            r'[A-Z][a-z]+,?\s+(?:Circuit|District|Magistrate)\s+Judge',
            r'(?:Chief|Senior)\s+(?:Judge|Justice)\s+[A-Z][a-z]+',
            r'Hon\.\s+[A-Z][a-z]+',
            r'The\s+Honorable\s+[A-Z][a-z]+',
        ]
        
        print(f"\n🔍 SPECIFIC JUDGE PATTERN SEARCH:")
        for pattern in judge_search_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                print(f"   ✅ '{pattern}' found: {matches}")
            else:
                print(f"   ❌ '{pattern}' - no matches")
        
        # Look for document structure clues
        print(f"\n🔍 DOCUMENT STRUCTURE ANALYSIS:")
        
        # Check what type of document this is
        doc_type_patterns = [
            (r'MEMORANDUM', 'Memorandum'),
            (r'ORDER', 'Order'),
            (r'OPINION', 'Opinion'),
            (r'REPORT\s+AND\s+RECOMMENDATION', 'Report and Recommendation'),
            (r'PER\s+CURIAM', 'Per Curiam'),
            (r'JUDGMENT', 'Judgment'),
        ]
        
        for pattern, doc_type in doc_type_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                print(f"   ✅ Document type: {doc_type}")
        
        # Look for signature blocks or author information
        print(f"\n🔍 SIGNATURE/AUTHOR ANALYSIS:")
        
        # Look at the end of the document for signatures
        text_end = text[-1000:]  # Last 1000 characters
        print(f"   Document end (last 500 chars):")
        print(f"   {text_end[-500:]}")
        
        return text
        
    except Exception as e:
        print(f"❌ Examination failed: {e}")
        return None


def create_targeted_patterns_for_real_data(sample_text):
    """Create targeted patterns based on actual real data"""
    
    print(f"\n🔧 CREATING TARGETED PATTERNS FOR REAL DATA")
    print("=" * 60)
    
    if not sample_text:
        print("❌ No sample text to analyze")
        return []
    
    # Analyze the actual text structure
    print(f"📊 REAL TEXT ANALYSIS:")
    
    # Check if it's a district court document
    is_district = 'DISTRICT COURT' in sample_text.upper()
    is_circuit = 'COURT OF APPEALS' in sample_text.upper() or 'CIRCUIT' in sample_text.upper()
    is_magistrate = 'MAGISTRATE' in sample_text.upper()
    
    print(f"   District Court: {'✅' if is_district else '❌'}")
    print(f"   Circuit Court: {'✅' if is_circuit else '❌'}")
    print(f"   Magistrate: {'✅' if is_magistrate else '❌'}")
    
    # Create patterns based on what we found
    targeted_patterns = []
    
    if is_district:
        # District court patterns
        targeted_patterns.extend([
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+(?:U\.S\.\s+)?District\s+Judge',
            r'(?:U\.S\.\s+)?District\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
            r'(?:Chief\s+)?District\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
        ])
    
    if is_magistrate:
        # Magistrate patterns
        targeted_patterns.extend([
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+(?:U\.S\.\s+)?Magistrate\s+Judge',
            r'(?:U\.S\.\s+)?Magistrate\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
        ])
    
    if is_circuit:
        # Circuit court patterns
        targeted_patterns.extend([
            r'Before\s+([A-Z]{2,}),?\s+([A-Z]{2,}),?\s+and\s+([A-Z]{2,}),?\s+Circuit\s+Judges',
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+Circuit\s+Judge',
        ])
    
    # Generic patterns that might work
    targeted_patterns.extend([
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
        r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
        r'Hon\.\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
        r'The\s+Honorable\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',
    ])
    
    print(f"\n📋 TARGETED PATTERNS CREATED:")
    for i, pattern in enumerate(targeted_patterns, 1):
        print(f"   {i}. {pattern}")
    
    # Test patterns on the real text
    print(f"\n🧪 TESTING TARGETED PATTERNS ON REAL TEXT:")
    
    found_judges = []
    for i, pattern in enumerate(targeted_patterns, 1):
        matches = re.findall(pattern, sample_text, re.IGNORECASE)
        if matches:
            print(f"   ✅ Pattern {i} found {len(matches)} matches:")
            for match in matches:
                if isinstance(match, tuple):
                    for name in match:
                        if name and name.strip():
                            found_judges.append(name.strip())
                            print(f"      - {name.strip()}")
                else:
                    found_judges.append(match.strip())
                    print(f"      - {match.strip()}")
        else:
            print(f"   ❌ Pattern {i} - no matches")
    
    print(f"\n📊 TARGETED PATTERN RESULTS:")
    print(f"   Total judges found: {len(found_judges)}")
    print(f"   Unique judges: {len(set(found_judges))}")
    
    if found_judges:
        print(f"   ✅ SUCCESS: Found judges in real data!")
        for judge in set(found_judges):
            print(f"      - {judge}")
    else:
        print(f"   ❌ FAILED: Still no judges found")
    
    return targeted_patterns


def main():
    """Fix real data patterns"""
    
    print("🔧 FIXING REAL DATA PATTERNS")
    print("=" * 80)
    print("🎯 Creating patterns specifically for actual CourtListener text")
    
    # Examine real text in detail
    sample_text = examine_real_courtlistener_text_in_detail()
    
    # Create targeted patterns
    targeted_patterns = create_targeted_patterns_for_real_data(sample_text)
    
    if targeted_patterns and sample_text:
        print(f"\n✅ REAL DATA PATTERN FIX: SUCCESS")
        print(f"✅ Created {len(targeted_patterns)} targeted patterns")
        print(f"✅ Ready to update judge extraction patterns")
        return True
    else:
        print(f"\n❌ REAL DATA PATTERN FIX: FAILED")
        print(f"❌ Could not create working patterns for real data")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
