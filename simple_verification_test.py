#!/usr/bin/env python3
"""
Simple test to verify one case works correctly
"""

import asyncio
from comprehensive_chunk_verification import ComprehensiveChunkVerifier

async def test_single_case():
    """Test a single case verification"""
    
    verifier = ComprehensiveChunkVerifier()
    
    # Get one test case
    cases = verifier.supabase.table('cases').select('*').like('batch_id', 'real_pinecone_test_%').limit(1).execute()
    
    if not cases.data:
        print("❌ No test cases found")
        return False
    
    case = cases.data[0]
    print(f"🔍 Testing single case: {case['id']}")
    
    result = await verifier._verify_single_case(case)
    
    print(f"\n📊 RESULT: {'✅ PASSED' if result else '❌ FAILED'}")
    
    return result

if __name__ == "__main__":
    success = asyncio.run(test_single_case())
    print(f"\nFinal result: {success}")
