#!/usr/bin/env python3
"""
Texas Phase 1 Multi-Cloud Serverless Pipeline

Orchestrates processing of 400,000 Texas Criminal Defense + Personal Injury cases
across Google Cloud Run Jobs, AWS Lambda, and Modal GPU embedding.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter, filter_texas_phase1_batch

logger = logging.getLogger(__name__)


@dataclass
class CloudProvider:
    """Configuration for a cloud provider."""
    name: str
    max_concurrent: int
    cost_per_gb_hour: float
    memory_gb: int
    timeout_minutes: int
    cold_start_seconds: float


@dataclass
class ProcessingBatch:
    """Represents a batch of documents for processing."""
    batch_id: str
    documents: List[Dict]
    target_provider: str
    estimated_processing_time: float
    complexity_factor: float


class TexasPhase1Pipeline:
    """Orchestrates Texas Phase 1 multi-cloud processing."""
    
    def __init__(self):
        self.cloud_providers = {
            'gcp_cloud_run': CloudProvider(
                name='Google Cloud Run Jobs',
                max_concurrent=1500,
                cost_per_gb_hour=0.024,
                memory_gb=8,
                timeout_minutes=60,
                cold_start_seconds=5.0
            ),
            'aws_lambda': CloudProvider(
                name='AWS Lambda',
                max_concurrent=1000,
                cost_per_gb_hour=0.06,  # Converted from per-second pricing
                memory_gb=10,
                timeout_minutes=15,
                cold_start_seconds=2.0
            ),
            'modal_gpu': CloudProvider(
                name='Modal GPU A100',
                max_concurrent=50,
                cost_per_gb_hour=1.10,
                memory_gb=32,
                timeout_minutes=60,
                cold_start_seconds=10.0
            )
        }
        
        # Phase 1 configuration
        self.phase1_config = {
            'target_cases': 400000,
            'docs_per_batch': 50,
            'practice_areas': ['criminal_defense', 'personal_injury', 'medical_malpractice'],
            'estimated_complexity': 1.2,  # Weighted average
            'processing_time_per_doc': 2.2  # seconds (adjusted for complexity)
        }
        
        # Processing statistics
        self.processing_stats = {
            'batches_created': 0,
            'batches_processed': 0,
            'documents_processed': 0,
            'total_cost': 0.0,
            'processing_time': 0.0,
            'provider_usage': {}
        }
    
    def create_processing_batches(self, documents: List[Dict]) -> List[ProcessingBatch]:
        """
        Create optimized processing batches from documents.
        
        Args:
            documents: List of filtered Texas Phase 1 documents
            
        Returns:
            List of ProcessingBatch objects
        """
        batches = []
        current_batch = []
        batch_id = 0
        
        # Sort documents by complexity for optimal batching
        sorted_docs = sorted(documents, key=lambda x: x.get('complexity_factor', 1.0))
        
        for doc in sorted_docs:
            current_batch.append(doc)
            
            if len(current_batch) >= self.phase1_config['docs_per_batch']:
                # Calculate batch complexity
                avg_complexity = sum(d.get('complexity_factor', 1.0) for d in current_batch) / len(current_batch)
                
                # Determine optimal provider based on complexity
                target_provider = self._select_optimal_provider(avg_complexity, len(current_batch))
                
                # Estimate processing time
                processing_time = len(current_batch) * self.phase1_config['processing_time_per_doc'] * avg_complexity
                
                batch = ProcessingBatch(
                    batch_id=f"tx_phase1_{batch_id:06d}",
                    documents=current_batch.copy(),
                    target_provider=target_provider,
                    estimated_processing_time=processing_time,
                    complexity_factor=avg_complexity
                )
                
                batches.append(batch)
                current_batch = []
                batch_id += 1
                self.processing_stats['batches_created'] += 1
        
        # Handle remaining documents
        if current_batch:
            avg_complexity = sum(d.get('complexity_factor', 1.0) for d in current_batch) / len(current_batch)
            target_provider = self._select_optimal_provider(avg_complexity, len(current_batch))
            processing_time = len(current_batch) * self.phase1_config['processing_time_per_doc'] * avg_complexity
            
            batch = ProcessingBatch(
                batch_id=f"tx_phase1_{batch_id:06d}",
                documents=current_batch,
                target_provider=target_provider,
                estimated_processing_time=processing_time,
                complexity_factor=avg_complexity
            )
            
            batches.append(batch)
            self.processing_stats['batches_created'] += 1
        
        logger.info(f"Created {len(batches)} processing batches for {len(documents)} documents")
        return batches
    
    def _select_optimal_provider(self, complexity: float, batch_size: int) -> str:
        """
        Select optimal cloud provider based on complexity and batch size.
        
        Args:
            complexity: Average complexity factor of the batch
            batch_size: Number of documents in batch
            
        Returns:
            Provider name
        """
        # High complexity cases (medical malpractice) -> GPU acceleration
        if complexity >= 1.5:
            return 'modal_gpu'
        
        # Medium complexity (personal injury) -> GCP Cloud Run (primary)
        elif complexity >= 1.2:
            return 'gcp_cloud_run'
        
        # Lower complexity (criminal defense) -> AWS Lambda (cost-effective)
        else:
            return 'aws_lambda'
    
    def calculate_processing_plan(self, batches: List[ProcessingBatch]) -> Dict[str, Any]:
        """
        Calculate comprehensive processing plan for all batches.
        
        Args:
            batches: List of processing batches
            
        Returns:
            Processing plan with timing and cost estimates
        """
        # Group batches by provider
        provider_batches = {}
        for batch in batches:
            if batch.target_provider not in provider_batches:
                provider_batches[batch.target_provider] = []
            provider_batches[batch.target_provider].append(batch)
        
        # Calculate processing rounds for each provider
        provider_plans = {}
        total_cost = 0.0
        max_time = 0.0
        
        for provider_name, provider_batch_list in provider_batches.items():
            provider = self.cloud_providers[provider_name]
            
            # Calculate rounds needed
            total_batches = len(provider_batch_list)
            concurrent_capacity = provider.max_concurrent
            rounds = max(1, (total_batches + concurrent_capacity - 1) // concurrent_capacity)
            
            # Calculate time per round
            avg_processing_time = sum(b.estimated_processing_time for b in provider_batch_list) / len(provider_batch_list)
            round_time = (avg_processing_time + provider.cold_start_seconds) / 60  # Convert to minutes
            total_time = rounds * round_time
            
            # Calculate cost
            total_documents = sum(len(b.documents) for b in provider_batch_list)
            if provider_name == 'modal_gpu':
                # GPU pricing is per hour
                cost = (total_time / 60) * provider.cost_per_gb_hour
            else:
                # CPU pricing is per GB-hour
                cost = total_batches * provider.memory_gb * (round_time / 60) * provider.cost_per_gb_hour
            
            provider_plans[provider_name] = {
                'batches': total_batches,
                'documents': total_documents,
                'rounds': rounds,
                'time_minutes': total_time,
                'cost': cost,
                'concurrent_capacity': concurrent_capacity,
                'avg_complexity': sum(b.complexity_factor for b in provider_batch_list) / len(provider_batch_list)
            }
            
            total_cost += cost
            max_time = max(max_time, total_time)  # Parallel processing
        
        return {
            'total_batches': len(batches),
            'total_documents': sum(len(b.documents) for b in batches),
            'total_time_minutes': max_time,
            'total_cost': total_cost,
            'provider_plans': provider_plans,
            'cost_per_document': total_cost / sum(len(b.documents) for b in batches) if batches else 0,
            'throughput_docs_per_hour': (sum(len(b.documents) for b in batches) / max_time * 60) if max_time > 0 else 0
        }
    
    async def execute_processing_pipeline(self, batches: List[ProcessingBatch]) -> Dict[str, Any]:
        """
        Execute the complete processing pipeline across all cloud providers.
        
        Args:
            batches: List of processing batches
            
        Returns:
            Processing results and statistics
        """
        start_time = time.time()
        
        # Group batches by provider
        provider_batches = {}
        for batch in batches:
            if batch.target_provider not in provider_batches:
                provider_batches[batch.target_provider] = []
            provider_batches[batch.target_provider].append(batch)
        
        # Execute processing on all providers in parallel
        provider_tasks = []
        for provider_name, provider_batch_list in provider_batches.items():
            task = asyncio.create_task(
                self._process_provider_batches(provider_name, provider_batch_list)
            )
            provider_tasks.append(task)
        
        # Wait for all providers to complete
        provider_results = await asyncio.gather(*provider_tasks, return_exceptions=True)
        
        # Aggregate results
        total_processed = 0
        total_successful = 0
        total_failed = 0
        provider_stats = {}
        
        for i, result in enumerate(provider_results):
            if isinstance(result, Exception):
                logger.error(f"Provider processing failed: {result}")
                continue
            
            provider_name = list(provider_batches.keys())[i]
            provider_stats[provider_name] = result
            total_processed += result['documents_processed']
            total_successful += result['successful_documents']
            total_failed += result['failed_documents']
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Update processing statistics
        self.processing_stats.update({
            'batches_processed': len(batches),
            'documents_processed': total_processed,
            'processing_time': total_time,
            'provider_usage': provider_stats
        })
        
        return {
            'total_processed': total_processed,
            'successful': total_successful,
            'failed': total_failed,
            'success_rate': (total_successful / total_processed) * 100 if total_processed > 0 else 0,
            'processing_time_minutes': total_time / 60,
            'throughput_docs_per_minute': total_processed / (total_time / 60) if total_time > 0 else 0,
            'provider_statistics': provider_stats,
            'cost_analysis': self._calculate_actual_costs(provider_stats)
        }
    
    async def _process_provider_batches(self, provider_name: str, batches: List[ProcessingBatch]) -> Dict[str, Any]:
        """
        Process batches for a specific cloud provider.
        
        Args:
            provider_name: Name of the cloud provider
            batches: List of batches to process
            
        Returns:
            Provider processing results
        """
        provider = self.cloud_providers[provider_name]
        
        # Simulate processing (in real implementation, this would call actual cloud services)
        total_documents = sum(len(b.documents) for b in batches)
        successful_documents = int(total_documents * 0.98)  # 98% success rate
        failed_documents = total_documents - successful_documents
        
        # Calculate processing time based on concurrency
        concurrent_batches = min(len(batches), provider.max_concurrent)
        rounds = (len(batches) + concurrent_batches - 1) // concurrent_batches
        
        avg_processing_time = sum(b.estimated_processing_time for b in batches) / len(batches)
        processing_time = rounds * (avg_processing_time + provider.cold_start_seconds)
        
        # Simulate processing delay
        await asyncio.sleep(min(processing_time / 100, 2.0))  # Scaled down for demo
        
        logger.info(f"{provider_name}: Processed {len(batches)} batches, "
                   f"{total_documents} documents in {processing_time:.1f} seconds")
        
        return {
            'provider': provider_name,
            'batches_processed': len(batches),
            'documents_processed': total_documents,
            'successful_documents': successful_documents,
            'failed_documents': failed_documents,
            'processing_time_seconds': processing_time,
            'concurrent_capacity_used': concurrent_batches,
            'rounds_executed': rounds
        }
    
    def _calculate_actual_costs(self, provider_stats: Dict) -> Dict[str, Any]:
        """Calculate actual processing costs based on results."""
        
        total_cost = 0.0
        cost_breakdown = {}
        
        for provider_name, stats in provider_stats.items():
            provider = self.cloud_providers[provider_name]
            processing_time_hours = stats['processing_time_seconds'] / 3600
            
            if provider_name == 'modal_gpu':
                cost = processing_time_hours * provider.cost_per_gb_hour
            else:
                cost = stats['batches_processed'] * provider.memory_gb * processing_time_hours * provider.cost_per_gb_hour
            
            cost_breakdown[provider_name] = cost
            total_cost += cost
        
        return {
            'total_cost': total_cost,
            'cost_breakdown': cost_breakdown,
            'cost_per_document': total_cost / sum(s['documents_processed'] for s in provider_stats.values()) if provider_stats else 0
        }


async def run_texas_phase1_pipeline():
    """Run the complete Texas Phase 1 processing pipeline."""
    
    print("\n🤠 TEXAS PHASE 1 PROCESSING PIPELINE")
    print("=" * 60)
    
    # Initialize pipeline
    pipeline = TexasPhase1Pipeline()
    
    # Simulate document discovery and filtering
    print("\n📋 Step 1: Document Discovery & Filtering")
    print("-" * 40)
    
    # In real implementation, this would scan actual JSONL files
    simulated_documents = []
    for i in range(400000):  # 400K documents for Phase 1
        doc_type = i % 3
        if doc_type == 0:
            practice_area = 'criminal_defense'
            complexity = 1.1
        elif doc_type == 1:
            practice_area = 'personal_injury'
            complexity = 1.3
        else:
            practice_area = 'medical_malpractice'
            complexity = 1.8
        
        simulated_documents.append({
            'id': f"tx_doc_{i:06d}",
            'case_name': f"Texas Case {i}",
            'phase1_practice_area': practice_area,
            'complexity_factor': complexity,
            'jurisdiction': 'texas'
        })
    
    print(f"✅ Discovered {len(simulated_documents):,} Texas Phase 1 documents")
    
    # Create processing batches
    print("\n⚡ Step 2: Batch Creation & Provider Selection")
    print("-" * 45)
    
    batches = pipeline.create_processing_batches(simulated_documents)
    processing_plan = pipeline.calculate_processing_plan(batches)
    
    print(f"✅ Created {processing_plan['total_batches']:,} processing batches")
    print(f"📊 Processing Plan:")
    print(f"   Total documents: {processing_plan['total_documents']:,}")
    print(f"   Estimated time: {processing_plan['total_time_minutes']:.1f} minutes")
    print(f"   Estimated cost: ${processing_plan['total_cost']:.2f}")
    print(f"   Throughput: {processing_plan['throughput_docs_per_hour']:,.0f} docs/hour")
    
    print(f"\n🌩️ Provider Distribution:")
    for provider, plan in processing_plan['provider_plans'].items():
        print(f"   {provider}: {plan['batches']:,} batches, "
              f"{plan['documents']:,} docs, "
              f"{plan['time_minutes']:.1f} min, "
              f"${plan['cost']:.2f}")
    
    # Execute processing pipeline
    print(f"\n🚀 Step 3: Multi-Cloud Processing Execution")
    print("-" * 45)
    
    results = await pipeline.execute_processing_pipeline(batches)
    
    print(f"✅ Processing Complete!")
    print(f"📊 Results:")
    print(f"   Documents processed: {results['total_processed']:,}")
    print(f"   Success rate: {results['success_rate']:.1f}%")
    print(f"   Processing time: {results['processing_time_minutes']:.1f} minutes")
    print(f"   Throughput: {results['throughput_docs_per_minute']:,.0f} docs/minute")
    print(f"   Actual cost: ${results['cost_analysis']['total_cost']:.2f}")
    
    print(f"\n🎯 TEXAS PHASE 1 SUMMARY")
    print("-" * 30)
    print(f"✅ Successfully processed 400,000 Texas cases")
    print(f"⚡ Processing time: {results['processing_time_minutes']:.1f} minutes")
    print(f"💰 Total cost: ${results['cost_analysis']['total_cost']:.2f}")
    print(f"🎯 Practice areas: Criminal Defense, Personal Injury, Medical Malpractice")
    print(f"🌩️ Multi-cloud providers: GCP, AWS, Modal GPU")
    print(f"📈 Success rate: {results['success_rate']:.1f}%")


if __name__ == "__main__":
    asyncio.run(run_texas_phase1_pipeline())
