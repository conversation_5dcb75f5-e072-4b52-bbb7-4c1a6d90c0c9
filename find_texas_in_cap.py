#!/usr/bin/env python3
"""
Search for Texas cases in CAP data by examining text content and file paths.
"""

import os
import gzip
import json
import re

def find_texas_in_cap():
    """Search for Texas cases in CAP data."""
    
    print("🔍 SEARCHING FOR TEXAS CASES IN CAP DATA")
    print("=" * 60)
    
    cap_dir = "data/caselaw_access_project"
    texas_cases = []
    
    # Check first few files
    for i in range(3):  # Check first 3 files
        filename = f"cap_{i:05d}.jsonl.gz"
        filepath = os.path.join(cap_dir, filename)
        
        if not os.path.exists(filepath):
            continue
        
        print(f"📁 Searching {filename}...")
        
        try:
            file_texas_count = 0
            total_cases = 0
            
            with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    if line.strip():
                        try:
                            case = json.loads(line)
                            total_cases += 1
                            
                            # Check for Texas in various places
                            texas_indicators = []
                            
                            # 1. Check file path/ID
                            case_id = case.get('id', '')
                            if 'tex' in case_id.lower():
                                texas_indicators.append('file_path')
                            
                            # 2. Check case text
                            text = case.get('text', '')
                            if text:
                                # Look for Texas court names
                                texas_patterns = [
                                    r'Texas\s+Supreme\s+Court',
                                    r'Court\s+of\s+Appeals\s+of\s+Texas',
                                    r'Texas\s+Court\s+of\s+Criminal\s+Appeals',
                                    r'District\s+Court.*Texas',
                                    r'State\s+of\s+Texas',
                                    r'v\.\s+Texas',
                                    r'Texas\s+v\.',
                                    r'Tex\.\s+App\.',
                                    r'Tex\.\s+Crim\.\s+App\.',
                                ]
                                
                                for pattern in texas_patterns:
                                    if re.search(pattern, text, re.IGNORECASE):
                                        texas_indicators.append(f'text_pattern:{pattern}')
                                        break
                            
                            # 3. Check URL for Texas court indicators
                            url = case.get('metadata', {}).get('url', '')
                            if 'tex' in url.lower():
                                texas_indicators.append('url')
                            
                            # If we found Texas indicators, this is likely a Texas case
                            if texas_indicators:
                                file_texas_count += 1
                                
                                if len(texas_cases) < 10:  # Keep first 10 for analysis
                                    case['texas_indicators'] = texas_indicators
                                    texas_cases.append(case)
                        
                        except json.JSONDecodeError:
                            continue
                    
                    # Sample first 1000 lines for speed
                    if line_num >= 1000:
                        break
            
            print(f"   📊 Found {file_texas_count}/{total_cases} potential Texas cases ({file_texas_count/total_cases*100:.1f}%)")
        
        except Exception as e:
            print(f"   ❌ Error reading {filename}: {e}")
    
    # Analyze found Texas cases
    if texas_cases:
        print(f"\n🎯 FOUND {len(texas_cases)} TEXAS CASES!")
        print("=" * 60)
        
        for i, case in enumerate(texas_cases[:5]):
            print(f"\n📋 TEXAS CASE {i+1}:")
            
            # Extract case name from text
            text = case.get('text', '')
            lines = text.split('\n')
            case_name = 'Unknown'
            for line in lines[:10]:  # Check first 10 lines
                line = line.strip()
                if line and len(line) > 10 and not line.isupper():
                    # This might be the case name
                    case_name = line[:100]
                    break
            
            print(f"   Case: {case_name}")
            print(f"   ID: {case.get('id', 'N/A')}")
            print(f"   URL: {case.get('metadata', {}).get('url', 'N/A')}")
            print(f"   Texas indicators: {case.get('texas_indicators', [])}")
            
            # Show snippet of text
            text_snippet = text[:200].replace('\n', ' ').strip()
            print(f"   Text snippet: {text_snippet}...")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   ✅ Texas cases found in CAP data!")
        print(f"   📈 Proceed with comprehensive Texas processing:")
        print(f"      • Court Listener API: Recent cases from 12 courts")
        print(f"      • CAP data: Historical Texas cases (estimated {len(texas_cases)*100:,}+ cases)")
        print(f"      • Combined processing with duplicate detection")
    
    else:
        print(f"\n⚠️  NO TEXAS CASES FOUND IN SAMPLE")
        print(f"   📈 Focus on Court Listener API processing only")
        print(f"   💡 CAP data might be organized differently or contain other jurisdictions")

if __name__ == "__main__":
    find_texas_in_cap()
