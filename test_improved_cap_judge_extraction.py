#!/usr/bin/env python3
"""
Improved CAP Judge Extraction Test
Tests enhanced CAP judge extraction with real data from different year periods
TARGET: Significantly improve performance beyond current baseline using enhanced metadata patterns
"""

import asyncio
import gzip
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.judge_extraction_service import JudgeExtractionService
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImprovedCAPJudgeExtractionTest:
    """Test improved CAP judge extraction with real data across different eras"""
    
    def __init__(self):
        self.judge_extractor = JudgeExtractionService()
        self.cap_data_dir = Path("data/caselaw_access_project")
        self.test_batch_id = f"improved_cap_judge_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def extract_real_cap_cases_by_era(self, target_cases_per_era: int = 10) -> Dict[str, List[Dict[str, Any]]]:
        """Extract real CAP cases from different eras for testing"""
        print(f"\n📖 EXTRACTING REAL CAP CASES BY ERA")
        print("=" * 60)
        
        era_cases = {
            '1940s': [],
            '1950s': [],
            '1960s': [],
            '1970s': [],
            '1980s': []
        }
        
        if not self.cap_data_dir.exists():
            print(f"❌ CAP data directory not found: {self.cap_data_dir}")
            return era_cases
        
        # Process CAP files
        for cap_file in self.cap_data_dir.glob("*.jsonl.gz"):
            print(f"   📄 Processing: {cap_file.name}")
            
            try:
                with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if line.strip():
                            try:
                                case = json.loads(line)
                                
                                # Extract year from decision_date
                                decision_date = case.get('decision_date')
                                if not decision_date:
                                    continue
                                
                                year = int(decision_date.split('-')[0])
                                
                                # Categorize by era
                                era = None
                                if 1940 <= year <= 1949:
                                    era = '1940s'
                                elif 1950 <= year <= 1959:
                                    era = '1950s'
                                elif 1960 <= year <= 1969:
                                    era = '1960s'
                                elif 1970 <= year <= 1979:
                                    era = '1970s'
                                elif 1980 <= year <= 1989:
                                    era = '1980s'
                                
                                if era and len(era_cases[era]) < target_cases_per_era:
                                    # Check if case has metadata author (our target improvement area)
                                    metadata = case.get('metadata', {})
                                    author = metadata.get('author', '')
                                    
                                    if author and author != 'PER CURIAM:':
                                        case_name = case.get('name', 'Unknown Case')[:50]
                                        court = case.get('court', {}).get('name', 'Unknown Court')[:40]
                                        
                                        print(f"      ✅ {era}: {case_name}...")
                                        print(f"         Court: {court}")
                                        print(f"         Author: {author}")
                                        print(f"         Year: {year}")
                                        
                                        era_cases[era].append(case)
                                
                                # Stop if we have enough cases for all eras
                                if all(len(cases) >= target_cases_per_era for cases in era_cases.values()):
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                            except (ValueError, KeyError):
                                continue
                
                # Stop processing files if we have enough cases
                if all(len(cases) >= target_cases_per_era for cases in era_cases.values()):
                    break
                    
            except Exception as e:
                print(f"      ❌ Error processing {cap_file.name}: {e}")
                continue
        
        # Summary
        print(f"\n📊 EXTRACTED CASES BY ERA:")
        for era, cases in era_cases.items():
            print(f"   {era}: {len(cases)} cases")
        
        return era_cases
    
    def test_improved_cap_extraction_by_era(self, era_cases: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict[str, Any]]:
        """Test improved CAP judge extraction across different eras"""
        print(f"\n🔍 TESTING IMPROVED CAP JUDGE EXTRACTION BY ERA")
        print("=" * 60)
        
        era_results = {}
        
        for era, cases in era_cases.items():
            if not cases:
                continue
                
            print(f"\n🕰️ TESTING {era.upper()} ERA ({len(cases)} cases)")
            print("-" * 40)
            
            results = {
                'total_cases': len(cases),
                'cases_with_judges': 0,
                'total_judges_found': 0,
                'judges_found': [],
                'avg_confidence': 0,
                'full_names_found': 0,
                'surnames_only_found': 0,
                'metadata_extractions': 0,
                'text_extractions': 0,
                'extraction_methods': {}
            }
            
            for i, case in enumerate(cases, 1):
                case_name = case.get('name', 'Unknown Case')[:50]
                metadata = case.get('metadata', {})
                author = metadata.get('author', '')
                
                print(f"   [{i}/{len(cases)}] {case_name}...")
                print(f"      Author: {author}")
                
                try:
                    # Extract judges using improved CAP extraction
                    judges = self.judge_extractor.extract_judges_from_cap(case)
                    
                    if judges:
                        results['cases_with_judges'] += 1
                        results['total_judges_found'] += len(judges)
                        
                        confidences = [j.confidence for j in judges]
                        avg_conf = sum(confidences) / len(confidences)
                        results['avg_confidence'] += avg_conf
                        
                        print(f"      ✅ Extracted {len(judges)} judges (avg confidence: {avg_conf:.2f})")
                        
                        for j, judge in enumerate(judges[:3], 1):  # Show top 3
                            method_icon = {
                                "metadata_author": "📋",
                                "text": "📝",
                                "pattern": "🔍"
                            }.get(judge.extraction_method, "❓")
                            
                            name_type = "FULL NAME" if judge.full_name else "SURNAME"
                            print(f"         {j}. {judge.name} ({name_type}, conf: {judge.confidence:.2f}) {method_icon}")
                            
                            # Track name types and methods
                            if judge.full_name:
                                results['full_names_found'] += 1
                            else:
                                results['surnames_only_found'] += 1
                            
                            if judge.extraction_method == "metadata_author":
                                results['metadata_extractions'] += 1
                            else:
                                results['text_extractions'] += 1
                            
                            # Track extraction methods
                            method = judge.extraction_method
                            results['extraction_methods'][method] = results['extraction_methods'].get(method, 0) + 1
                            
                            results['judges_found'].append({
                                'name': judge.name,
                                'full_name': judge.full_name,
                                'confidence': judge.confidence,
                                'method': judge.extraction_method,
                                'era': era
                            })
                    else:
                        print(f"      ❌ No judges extracted")
                        
                except Exception as e:
                    print(f"      ❌ Error extracting judges: {e}")
            
            # Calculate era statistics
            if results['cases_with_judges'] > 0:
                results['avg_confidence'] = results['avg_confidence'] / results['cases_with_judges']
            
            era_results[era] = results
        
        return era_results
    
    def analyze_improvement_results(self, era_results: Dict[str, Dict[str, Any]]):
        """Analyze and report improvement results"""
        print(f"\n📊 IMPROVED CAP EXTRACTION ANALYSIS")
        print("=" * 60)
        
        total_cases = sum(r['total_cases'] for r in era_results.values())
        total_with_judges = sum(r['cases_with_judges'] for r in era_results.values())
        total_judges = sum(r['total_judges_found'] for r in era_results.values())
        total_full_names = sum(r['full_names_found'] for r in era_results.values())
        total_surnames = sum(r['surnames_only_found'] for r in era_results.values())
        total_metadata = sum(r['metadata_extractions'] for r in era_results.values())
        
        overall_success_rate = (total_with_judges / total_cases * 100) if total_cases > 0 else 0
        full_name_rate = (total_full_names / total_judges * 100) if total_judges > 0 else 0
        surname_rate = (total_surnames / total_judges * 100) if total_judges > 0 else 0
        metadata_usage_rate = (total_metadata / total_judges * 100) if total_judges > 0 else 0
        
        print(f"📊 OVERALL RESULTS:")
        print(f"   Total cases tested: {total_cases}")
        print(f"   Cases with judges: {total_with_judges}")
        print(f"   Overall success rate: {overall_success_rate:.1f}%")
        print(f"   Total judges found: {total_judges}")
        print(f"   Full names: {total_full_names} ({full_name_rate:.1f}%)")
        print(f"   Surnames only: {total_surnames} ({surname_rate:.1f}%)")
        print(f"   Metadata extractions: {total_metadata} ({metadata_usage_rate:.1f}%)")
        
        print(f"\n📊 ERA-BY-ERA BREAKDOWN:")
        for era, results in era_results.items():
            if results['total_cases'] == 0:
                continue
                
            success_rate = (results['cases_with_judges'] / results['total_cases'] * 100)
            full_rate = (results['full_names_found'] / results['total_judges_found'] * 100) if results['total_judges_found'] > 0 else 0
            surname_rate = (results['surnames_only_found'] / results['total_judges_found'] * 100) if results['total_judges_found'] > 0 else 0
            
            print(f"   🕰️ {era}:")
            print(f"      Cases: {results['total_cases']} | Success: {success_rate:.1f}%")
            print(f"      Judges: {results['total_judges_found']} | Full names: {full_rate:.1f}% | Surnames: {surname_rate:.1f}%")
            print(f"      Avg confidence: {results['avg_confidence']:.2f}")
            print(f"      Methods: {results['extraction_methods']}")
        
        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        print(f"   Overall Success Rate: {overall_success_rate:.1f}%")
        print(f"   Metadata Usage: {metadata_usage_rate:.1f}% (NEW CAPABILITY)")
        print(f"   Full Name Extraction: {full_name_rate:.1f}%")
        print(f"   Surname Extraction: {surname_rate:.1f}%")
        
        # Success criteria: significant improvement in metadata usage and overall performance
        significant_improvement = (
            overall_success_rate >= 75.0 and  # Good overall success
            metadata_usage_rate >= 40.0       # Good metadata usage
        )
        
        print(f"\n🎉 IMPROVEMENT ASSESSMENT: {'✅ SIGNIFICANT IMPROVEMENT' if significant_improvement else '⚠️ NEEDS MORE WORK'}")
        
        return significant_improvement
    
    async def run_improved_cap_test(self):
        """Run comprehensive improved CAP judge extraction test"""
        print("🎯 IMPROVED CAP JUDGE EXTRACTION TEST")
        print("=" * 60)
        print("🎯 TARGET: Significantly improve CAP judge extraction performance")
        print("🎯 FOCUS: Better metadata author patterns like 'PECK, Circuit judge'")
        print(f"Test ID: {self.test_batch_id}")
        
        try:
            # Extract real CAP cases by era
            era_cases = self.extract_real_cap_cases_by_era(target_cases_per_era=10)
            
            if not any(era_cases.values()):
                print("❌ No CAP cases found - cannot proceed with test")
                return False
            
            # Test improved extraction
            era_results = self.test_improved_cap_extraction_by_era(era_cases)
            
            # Analyze results
            significant_improvement = self.analyze_improvement_results(era_results)
            
            print(f"\n🎉 IMPROVED CAP TEST RESULT: {'✅ SUCCESS' if significant_improvement else '⚠️ PARTIAL SUCCESS'}")
            
            return significant_improvement
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            return False


async def main():
    """Run the improved CAP judge extraction test"""
    test = ImprovedCAPJudgeExtractionTest()
    success = await test.run_improved_cap_test()
    
    if success:
        print("\n🎉 Improved CAP judge extraction shows significant improvement!")
    else:
        print("\n⚠️ Improved CAP judge extraction needs further refinement.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
