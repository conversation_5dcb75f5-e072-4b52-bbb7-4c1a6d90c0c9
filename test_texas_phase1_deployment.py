#!/usr/bin/env python3
"""
Texas Phase 1 Pre-Deployment Testing Suite

Comprehensive tests to validate all components before deployment:
1. Database connections (Supabase, Pinecone, Neo4j)
2. Document filtering accuracy
3. Processing pipeline functionality
4. Cloud provider configurations
5. Performance benchmarks
"""

import asyncio
import logging
import os
import sys
import time
from typing import Dict, List, Any, Optional
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter
from texas_phase1_serverless_pipeline import TexasPhase1Pipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TexasPhase1TestSuite:
    """Comprehensive test suite for Texas Phase 1 deployment."""
    
    def __init__(self):
        self.test_results = {
            'database_connections': {},
            'document_filtering': {},
            'processing_pipeline': {},
            'performance_benchmarks': {},
            'deployment_readiness': {}
        }
        
        # Load environment variables
        self.load_environment()
        
        # Test data
        self.sample_documents = self._create_sample_documents()
    
    def load_environment(self):
        """Load and validate environment variables."""
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'SUPABASE_URL', 'SUPABASE_KEY', 'PINECONE_API_KEY',
            'NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD',
            'VOYAGE_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables loaded successfully")
    
    def _create_sample_documents(self) -> List[Dict]:
        """Create sample documents for testing."""
        return [
            {
                'id': 'test_criminal_001',
                'case_name': 'State of Texas v. Johnson - Aggravated Assault',
                'text': 'The defendant was charged with aggravated assault under Texas Penal Code Section 22.02. The incident occurred in Harris County, Texas. The prosecution seeks conviction for this felony offense.',
                'court': 'Harris County Criminal District Court',
                'jurisdiction': 'texas',
                'date_filed': '2023-01-15'
            },
            {
                'id': 'test_personal_injury_001',
                'case_name': 'Smith v. ExxonMobil - Oil Field Accident Personal Injury',
                'text': 'Personal injury lawsuit arising from oil field accident in Texas. Plaintiff seeks damages for negligence, pain and suffering, and medical expenses. The accident occurred at a refinery in Texas.',
                'court': 'Harris County District Court',
                'jurisdiction': 'texas',
                'date_filed': '2023-02-20'
            },
            {
                'id': 'test_medical_malpractice_001',
                'case_name': 'Brown v. Methodist Hospital - Medical Malpractice',
                'text': 'Medical malpractice case involving surgical error at Methodist Hospital in Houston, Texas. Patient suffered complications due to physician negligence during surgery. Standard of care was breached.',
                'court': 'Harris County District Court',
                'jurisdiction': 'texas',
                'date_filed': '2023-03-10'
            },
            {
                'id': 'test_non_target_001',
                'case_name': 'ABC Corp v. XYZ Corp - Contract Dispute',
                'text': 'Commercial contract dispute between corporations regarding breach of contract terms and conditions.',
                'court': 'New York Supreme Court',
                'jurisdiction': 'new_york',
                'date_filed': '2023-04-05'
            }
        ]
    
    async def test_database_connections(self) -> Dict[str, bool]:
        """Test connections to all databases."""
        logger.info("🔍 Testing database connections...")
        
        results = {}
        
        # Test Supabase connection
        try:
            import supabase
            supabase_url = os.getenv('SUPABASE_URL')
            supabase_key = os.getenv('SUPABASE_KEY')
            
            sb_client = supabase.create_client(supabase_url, supabase_key)
            
            # Test query
            result = sb_client.table('cases').select('id').limit(1).execute()
            results['supabase'] = True
            logger.info("✅ Supabase connection successful")
            
        except Exception as e:
            results['supabase'] = False
            logger.error(f"❌ Supabase connection failed: {e}")
        
        # Test Pinecone connection
        try:
            import pinecone
            from pinecone import Pinecone
            
            pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
            
            # List indexes to test connection
            indexes = pc.list_indexes()
            results['pinecone'] = True
            logger.info("✅ Pinecone connection successful")
            
        except Exception as e:
            results['pinecone'] = False
            logger.error(f"❌ Pinecone connection failed: {e}")
        
        # Test Neo4j connection
        try:
            from neo4j import GraphDatabase
            
            driver = GraphDatabase.driver(
                os.getenv('NEO4J_URI'),
                auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
            )
            
            # Test query
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                
            driver.close()
            results['neo4j'] = True
            logger.info("✅ Neo4j connection successful")
            
        except Exception as e:
            results['neo4j'] = False
            logger.error(f"❌ Neo4j connection failed: {e}")
        
        # Test Voyage AI connection
        try:
            import voyageai
            
            voyage_client = voyageai.Client(api_key=os.getenv('VOYAGE_API_KEY'))
            
            # Test embedding generation
            result = voyage_client.embed(
                texts=["Test text for connection"],
                model="voyage-large-2"
            )
            
            results['voyage_ai'] = True
            logger.info("✅ Voyage AI connection successful")
            
        except Exception as e:
            results['voyage_ai'] = False
            logger.error(f"❌ Voyage AI connection failed: {e}")
        
        self.test_results['database_connections'] = results
        return results
    
    def test_document_filtering(self) -> Dict[str, Any]:
        """Test document filtering accuracy."""
        logger.info("🔍 Testing document filtering...")
        
        filter_engine = TexasPhase1Filter()
        
        results = {
            'total_documents': len(self.sample_documents),
            'filtered_documents': 0,
            'practice_area_accuracy': {},
            'texas_specific_matches': 0,
            'filter_performance': {}
        }
        
        start_time = time.time()
        
        # Test filtering
        filtered_docs, batch_stats = filter_engine.batch_filter_documents(self.sample_documents)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        results['filtered_documents'] = len(filtered_docs)
        results['filter_rate'] = batch_stats['filter_rate']
        results['average_complexity'] = batch_stats['avg_complexity']
        results['texas_specific_matches'] = batch_stats['texas_matches']
        
        # Validate expected results
        expected_results = {
            'test_criminal_001': 'criminal_defense',
            'test_personal_injury_001': 'personal_injury',
            'test_medical_malpractice_001': 'medical_malpractice'
        }
        
        accuracy_count = 0
        for doc in filtered_docs:
            doc_id = doc['id']
            if doc_id in expected_results:
                expected_area = expected_results[doc_id]
                actual_area = doc.get('phase1_practice_area')
                if expected_area == actual_area:
                    accuracy_count += 1
                    logger.info(f"✅ {doc_id}: {actual_area} (correct)")
                else:
                    logger.error(f"❌ {doc_id}: expected {expected_area}, got {actual_area}")
        
        results['practice_area_accuracy']['correct'] = accuracy_count
        results['practice_area_accuracy']['total'] = len(expected_results)
        results['practice_area_accuracy']['percentage'] = (accuracy_count / len(expected_results)) * 100
        
        results['filter_performance'] = {
            'processing_time_seconds': processing_time,
            'documents_per_second': len(self.sample_documents) / processing_time,
            'estimated_400k_time_minutes': (400000 / (len(self.sample_documents) / processing_time)) / 60
        }
        
        self.test_results['document_filtering'] = results
        
        logger.info(f"✅ Document filtering test completed")
        logger.info(f"   Accuracy: {results['practice_area_accuracy']['percentage']:.1f}%")
        logger.info(f"   Performance: {results['filter_performance']['documents_per_second']:.1f} docs/sec")
        
        return results
    
    async def test_processing_pipeline(self) -> Dict[str, Any]:
        """Test the processing pipeline functionality."""
        logger.info("🔍 Testing processing pipeline...")
        
        pipeline = TexasPhase1Pipeline()
        
        # Create test batches
        test_documents = self.sample_documents[:3]  # Use first 3 documents
        batches = pipeline.create_processing_batches(test_documents)
        
        # Calculate processing plan
        processing_plan = pipeline.calculate_processing_plan(batches)
        
        results = {
            'batch_creation': {
                'input_documents': len(test_documents),
                'created_batches': len(batches),
                'batch_creation_successful': len(batches) > 0
            },
            'processing_plan': {
                'total_documents': processing_plan['total_documents'],
                'estimated_time_minutes': processing_plan['total_time_minutes'],
                'estimated_cost': processing_plan['total_cost'],
                'provider_distribution': len(processing_plan['provider_plans'])
            },
            'pipeline_functionality': True
        }
        
        # Test pipeline execution (simulated)
        try:
            execution_results = await pipeline.execute_processing_pipeline(batches)
            results['execution_test'] = {
                'success': True,
                'processed_documents': execution_results['total_processed'],
                'success_rate': execution_results['success_rate']
            }
            logger.info("✅ Pipeline execution test successful")
        except Exception as e:
            results['execution_test'] = {
                'success': False,
                'error': str(e)
            }
            logger.error(f"❌ Pipeline execution test failed: {e}")
        
        self.test_results['processing_pipeline'] = results
        return results
    
    def test_performance_benchmarks(self) -> Dict[str, Any]:
        """Test performance benchmarks."""
        logger.info("🔍 Testing performance benchmarks...")
        
        # Simulate processing 1000 documents to estimate 400K performance
        test_doc_count = 1000
        
        filter_engine = TexasPhase1Filter()
        
        # Create test documents
        test_docs = []
        for i in range(test_doc_count):
            doc_type = i % 3
            if doc_type == 0:
                template = self.sample_documents[0]  # Criminal
            elif doc_type == 1:
                template = self.sample_documents[1]  # Personal Injury
            else:
                template = self.sample_documents[2]  # Medical Malpractice
            
            test_doc = template.copy()
            test_doc['id'] = f'perf_test_{i:06d}'
            test_docs.append(test_doc)
        
        # Benchmark filtering
        start_time = time.time()
        filtered_docs, batch_stats = filter_engine.batch_filter_documents(test_docs)
        end_time = time.time()
        
        filtering_time = end_time - start_time
        docs_per_second = len(test_docs) / filtering_time
        
        # Extrapolate to 400K documents
        estimated_400k_time = 400000 / docs_per_second
        
        results = {
            'test_document_count': test_doc_count,
            'filtering_performance': {
                'time_seconds': filtering_time,
                'docs_per_second': docs_per_second,
                'estimated_400k_time_seconds': estimated_400k_time,
                'estimated_400k_time_minutes': estimated_400k_time / 60
            },
            'filtering_accuracy': {
                'input_docs': len(test_docs),
                'output_docs': len(filtered_docs),
                'filter_rate': batch_stats['filter_rate'],
                'average_complexity': batch_stats['avg_complexity']
            },
            'performance_targets': {
                'target_time_minutes': 6,
                'meets_target': (estimated_400k_time / 60) <= 10,  # Allow 10 min buffer
                'speedup_vs_sequential': (400000 * 2) / estimated_400k_time  # vs 2 sec/doc sequential
            }
        }
        
        self.test_results['performance_benchmarks'] = results
        
        logger.info(f"✅ Performance benchmark completed")
        logger.info(f"   Filtering speed: {docs_per_second:.1f} docs/sec")
        logger.info(f"   Estimated 400K time: {estimated_400k_time/60:.1f} minutes")
        logger.info(f"   Meets target: {'✅' if results['performance_targets']['meets_target'] else '❌'}")
        
        return results
    
    def assess_deployment_readiness(self) -> Dict[str, Any]:
        """Assess overall deployment readiness."""
        logger.info("🔍 Assessing deployment readiness...")
        
        # Check all test results
        db_connections = self.test_results.get('database_connections', {})
        doc_filtering = self.test_results.get('document_filtering', {})
        pipeline = self.test_results.get('processing_pipeline', {})
        performance = self.test_results.get('performance_benchmarks', {})
        
        readiness_checks = {
            'database_connections_ready': all(db_connections.values()),
            'filtering_accuracy_good': doc_filtering.get('practice_area_accuracy', {}).get('percentage', 0) >= 90,
            'pipeline_functional': pipeline.get('pipeline_functionality', False),
            'performance_acceptable': performance.get('performance_targets', {}).get('meets_target', False),
            'environment_configured': True  # Already checked in load_environment
        }
        
        overall_ready = all(readiness_checks.values())
        
        results = {
            'readiness_checks': readiness_checks,
            'overall_ready': overall_ready,
            'deployment_recommendation': 'READY' if overall_ready else 'NOT READY',
            'blocking_issues': [
                check for check, status in readiness_checks.items() if not status
            ]
        }
        
        self.test_results['deployment_readiness'] = results
        
        if overall_ready:
            logger.info("🎉 DEPLOYMENT READY - All tests passed!")
        else:
            logger.warning(f"⚠️  DEPLOYMENT NOT READY - Issues: {results['blocking_issues']}")
        
        return results
    
    async def run_full_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        logger.info("🚀 Starting Texas Phase 1 Pre-Deployment Test Suite")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # Run all tests
            await self.test_database_connections()
            self.test_document_filtering()
            await self.test_processing_pipeline()
            self.test_performance_benchmarks()
            self.assess_deployment_readiness()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Generate summary report
            summary = {
                'test_suite_completed': True,
                'total_test_time_seconds': total_time,
                'test_results': self.test_results,
                'deployment_ready': self.test_results['deployment_readiness']['overall_ready']
            }
            
            self._print_test_summary(summary)
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return {
                'test_suite_completed': False,
                'error': str(e),
                'deployment_ready': False
            }
    
    def _print_test_summary(self, summary: Dict[str, Any]):
        """Print a comprehensive test summary."""
        logger.info("\n" + "=" * 60)
        logger.info("🤠 TEXAS PHASE 1 TEST SUMMARY")
        logger.info("=" * 60)
        
        # Database connections
        db_results = self.test_results['database_connections']
        logger.info(f"\n📊 Database Connections:")
        for db, status in db_results.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {db.title()}: {'Connected' if status else 'Failed'}")
        
        # Document filtering
        filter_results = self.test_results['document_filtering']
        accuracy = filter_results['practice_area_accuracy']['percentage']
        logger.info(f"\n🔍 Document Filtering:")
        logger.info(f"   ✅ Accuracy: {accuracy:.1f}%")
        logger.info(f"   ✅ Performance: {filter_results['filter_performance']['documents_per_second']:.1f} docs/sec")
        
        # Performance benchmarks
        perf_results = self.test_results['performance_benchmarks']
        estimated_time = perf_results['filtering_performance']['estimated_400k_time_minutes']
        logger.info(f"\n⚡ Performance Benchmarks:")
        logger.info(f"   ✅ Estimated 400K processing time: {estimated_time:.1f} minutes")
        logger.info(f"   ✅ Target met: {'Yes' if perf_results['performance_targets']['meets_target'] else 'No'}")
        
        # Deployment readiness
        readiness = self.test_results['deployment_readiness']
        logger.info(f"\n🚀 Deployment Readiness:")
        logger.info(f"   Status: {readiness['deployment_recommendation']}")
        
        if readiness['blocking_issues']:
            logger.info(f"   Blocking issues: {', '.join(readiness['blocking_issues'])}")
        
        logger.info(f"\n⏱️  Total test time: {summary['total_test_time_seconds']:.1f} seconds")
        
        if summary['deployment_ready']:
            logger.info("\n🎉 READY FOR DEPLOYMENT!")
            logger.info("   All systems tested and validated")
            logger.info("   Proceed with deployment when ready")
        else:
            logger.info("\n⚠️  NOT READY FOR DEPLOYMENT")
            logger.info("   Please fix blocking issues before deploying")


async def main():
    """Run the test suite."""
    test_suite = TexasPhase1TestSuite()
    results = await test_suite.run_full_test_suite()
    
    # Save results to file
    with open('texas_phase1_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"\n📄 Test results saved to: texas_phase1_test_results.json")
    
    return results['deployment_ready']


if __name__ == "__main__":
    deployment_ready = asyncio.run(main())
    sys.exit(0 if deployment_ready else 1)
