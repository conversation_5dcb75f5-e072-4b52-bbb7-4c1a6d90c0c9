#!/usr/bin/env python3
"""
Texas Phase 1 Production Runner

Runs the complete Texas Phase 1 processing locally with maximum performance.
Processes 400,000 Criminal Defense + Personal Injury + Medical Malpractice cases.
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
import json
from typing import Dict, List, Any
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter
from texas_phase1_serverless_pipeline import TexasPhase1Pipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('texas_phase1_production.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TexasPhase1ProductionRunner:
    """Production runner for Texas Phase 1 processing."""
    
    def __init__(self):
        self.config = {
            'target_cases': 400000,
            'practice_areas': ['criminal_defense', 'personal_injury', 'medical_malpractice'],
            'batch_size': 50,
            'max_workers': min(32, multiprocessing.cpu_count() * 2),  # Optimize for your system
            'chunk_size': 10000  # Process in 10K chunks for memory efficiency
        }
        
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'chunks_completed': 0,
            'current_chunk': 0
        }
        
        # Load environment
        self.load_environment()
    
    def load_environment(self):
        """Load and validate environment variables."""
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'SUPABASE_URL', 'SUPABASE_KEY', 'PINECONE_API_KEY',
            'NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD',
            'VOYAGE_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables loaded successfully")
    
    def discover_texas_case_files(self) -> List[str]:
        """Discover actual Texas case files to process."""
        
        # Look for case files in the data directory
        data_dir = Path("data")
        case_files = []
        
        if data_dir.exists():
            # Look for JSONL files that might contain Texas cases
            for file_path in data_dir.glob("**/*.jsonl"):
                case_files.append(str(file_path))
            
            # Look for JSON files
            for file_path in data_dir.glob("**/*.json"):
                case_files.append(str(file_path))
        
        if not case_files:
            logger.warning("No case files found in data directory")
            logger.info("Creating sample data for demonstration...")
            return self.create_sample_data_files()
        
        logger.info(f"Found {len(case_files)} case files to process")
        return case_files
    
    def create_sample_data_files(self) -> List[str]:
        """Create sample data files for demonstration."""
        
        data_dir = Path("data/texas_phase1")
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Create sample files
        sample_files = []
        cases_per_file = 10000
        num_files = self.config['target_cases'] // cases_per_file
        
        logger.info(f"Creating {num_files} sample data files...")
        
        for file_idx in range(num_files):
            file_path = data_dir / f"texas_cases_{file_idx:03d}.jsonl"
            
            with open(file_path, 'w') as f:
                for case_idx in range(cases_per_file):
                    global_idx = file_idx * cases_per_file + case_idx
                    
                    # Distribute across practice areas
                    if global_idx % 100 < 40:
                        area = 'criminal_defense'
                        case_name = f"State of Texas v. Defendant_{global_idx:06d}"
                        text = f"Criminal case {global_idx} involving charges under Texas Penal Code. Harris County jurisdiction."
                        court = "Harris County Criminal District Court"
                    elif global_idx % 100 < 75:
                        area = 'personal_injury'
                        case_name = f"Plaintiff_{global_idx:06d} v. Defendant - Personal Injury"
                        text = f"Personal injury case {global_idx} involving negligence in Texas. Seeking damages for injuries."
                        court = "Harris County District Court"
                    else:
                        area = 'medical_malpractice'
                        case_name = f"Patient_{global_idx:06d} v. Hospital - Medical Malpractice"
                        text = f"Medical malpractice case {global_idx} involving physician negligence in Texas hospital."
                        court = "Harris County District Court"
                    
                    case_data = {
                        'id': f'tx_prod_{global_idx:06d}',
                        'case_name': case_name,
                        'text': text,
                        'court': court,
                        'jurisdiction': 'texas',
                        'date_filed': f'2023-{(global_idx % 12) + 1:02d}-{(global_idx % 28) + 1:02d}',
                        'expected_area': area
                    }
                    
                    f.write(json.dumps(case_data) + '\n')
            
            sample_files.append(str(file_path))
            
            if file_idx % 10 == 0:
                logger.info(f"Created {file_idx + 1}/{num_files} sample files...")
        
        logger.info(f"✅ Created {len(sample_files)} sample data files")
        return sample_files
    
    async def process_chunk(self, chunk_documents: List[Dict], chunk_id: int) -> Dict[str, Any]:
        """Process a chunk of documents."""
        
        logger.info(f"🔄 Processing chunk {chunk_id} with {len(chunk_documents)} documents...")
        
        start_time = time.time()
        
        # Initialize processors
        filter_engine = TexasPhase1Filter()
        pipeline = TexasPhase1Pipeline()
        
        try:
            # Filter documents
            filtered_docs, filter_stats = filter_engine.batch_filter_documents(chunk_documents)
            
            logger.info(f"Chunk {chunk_id}: Filtered {len(chunk_documents)} → {len(filtered_docs)} documents")
            
            if not filtered_docs:
                return {
                    'chunk_id': chunk_id,
                    'processed': 0,
                    'successful': 0,
                    'failed': 0,
                    'time_seconds': time.time() - start_time
                }
            
            # Create processing batches
            batches = pipeline.create_processing_batches(filtered_docs)
            
            # Execute processing
            results = await pipeline.execute_processing_pipeline(batches)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            chunk_results = {
                'chunk_id': chunk_id,
                'input_documents': len(chunk_documents),
                'filtered_documents': len(filtered_docs),
                'processed': results['total_processed'],
                'successful': results['successful'],
                'failed': results['failed'],
                'success_rate': results['success_rate'],
                'time_seconds': processing_time,
                'throughput_docs_per_second': results['total_processed'] / processing_time if processing_time > 0 else 0,
                'filter_stats': filter_stats
            }
            
            logger.info(f"✅ Chunk {chunk_id} completed: {results['total_processed']} processed, "
                       f"{results['success_rate']:.1f}% success rate, "
                       f"{processing_time:.1f}s")
            
            return chunk_results
            
        except Exception as e:
            logger.error(f"❌ Chunk {chunk_id} failed: {e}")
            return {
                'chunk_id': chunk_id,
                'processed': 0,
                'successful': 0,
                'failed': len(chunk_documents),
                'error': str(e),
                'time_seconds': time.time() - start_time
            }
    
    def load_documents_from_files(self, file_paths: List[str]) -> List[Dict]:
        """Load documents from case files."""
        
        logger.info(f"Loading documents from {len(file_paths)} files...")
        
        all_documents = []
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r') as f:
                    if file_path.endswith('.jsonl'):
                        # JSONL format - one JSON object per line
                        for line_num, line in enumerate(f):
                            if line.strip():
                                try:
                                    doc = json.loads(line)
                                    all_documents.append(doc)
                                except json.JSONDecodeError as e:
                                    logger.warning(f"Invalid JSON in {file_path}:{line_num}: {e}")
                    else:
                        # Regular JSON format
                        data = json.load(f)
                        if isinstance(data, list):
                            all_documents.extend(data)
                        else:
                            all_documents.append(data)
                            
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
        
        logger.info(f"✅ Loaded {len(all_documents)} documents from files")
        return all_documents
    
    async def run_production_processing(self) -> Dict[str, Any]:
        """Run the complete production processing."""
        
        logger.info("🚀 Starting Texas Phase 1 Production Processing")
        logger.info("=" * 60)
        
        self.stats['start_time'] = time.time()
        
        try:
            # Discover and load case files
            case_files = self.discover_texas_case_files()
            all_documents = self.load_documents_from_files(case_files)
            
            if not all_documents:
                raise ValueError("No documents found to process")
            
            # Limit to target number of cases
            if len(all_documents) > self.config['target_cases']:
                all_documents = all_documents[:self.config['target_cases']]
                logger.info(f"Limited to {self.config['target_cases']} documents for processing")
            
            # Split into chunks for memory efficiency
            chunk_size = self.config['chunk_size']
            chunks = [all_documents[i:i + chunk_size] for i in range(0, len(all_documents), chunk_size)]
            
            logger.info(f"Processing {len(all_documents)} documents in {len(chunks)} chunks")
            logger.info(f"Using {self.config['max_workers']} parallel workers")
            
            # Process chunks with limited concurrency to avoid overwhelming the system
            max_concurrent_chunks = min(4, self.config['max_workers'] // 8)  # Conservative approach
            
            all_results = []
            
            for i in range(0, len(chunks), max_concurrent_chunks):
                batch_chunks = chunks[i:i + max_concurrent_chunks]
                
                # Process this batch of chunks concurrently
                tasks = []
                for j, chunk in enumerate(batch_chunks):
                    chunk_id = i + j
                    task = asyncio.create_task(self.process_chunk(chunk, chunk_id))
                    tasks.append(task)
                
                # Wait for this batch to complete
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"Chunk processing failed: {result}")
                        continue
                    
                    all_results.append(result)
                    self.stats['chunks_completed'] += 1
                    self.stats['total_processed'] += result['processed']
                    self.stats['successful'] += result['successful']
                    self.stats['failed'] += result['failed']
                
                # Progress update
                progress = (i + len(batch_chunks)) / len(chunks) * 100
                logger.info(f"📊 Progress: {progress:.1f}% ({self.stats['chunks_completed']}/{len(chunks)} chunks)")
            
            self.stats['end_time'] = time.time()
            total_time = self.stats['end_time'] - self.stats['start_time']
            
            # Compile final results
            final_results = {
                'total_input_documents': len(all_documents),
                'total_processed': self.stats['total_processed'],
                'successful': self.stats['successful'],
                'failed': self.stats['failed'],
                'success_rate': (self.stats['successful'] / self.stats['total_processed'] * 100) if self.stats['total_processed'] > 0 else 0,
                'total_time_seconds': total_time,
                'total_time_minutes': total_time / 60,
                'throughput_docs_per_minute': self.stats['total_processed'] / (total_time / 60) if total_time > 0 else 0,
                'chunks_processed': len(all_results),
                'chunk_results': all_results
            }
            
            logger.info("🎉 TEXAS PHASE 1 PRODUCTION PROCESSING COMPLETED!")
            logger.info("=" * 60)
            logger.info(f"📊 Final Results:")
            logger.info(f"   Total processed: {final_results['total_processed']:,}")
            logger.info(f"   Success rate: {final_results['success_rate']:.1f}%")
            logger.info(f"   Processing time: {final_results['total_time_minutes']:.1f} minutes")
            logger.info(f"   Throughput: {final_results['throughput_docs_per_minute']:,.0f} docs/minute")
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ Production processing failed: {e}")
            raise


async def main():
    """Main production processing function."""
    
    print("🤠 TEXAS PHASE 1 PRODUCTION PROCESSING")
    print("=" * 50)
    print("Processing 400,000 Criminal Defense + Personal Injury + Medical Malpractice cases")
    print("Target: Complete processing in under 5 minutes")
    print()
    
    # Confirm with user
    response = input("🚀 Ready to start production processing? (y/n): ").lower().strip()
    if response != 'y':
        print("Processing cancelled.")
        return False
    
    runner = TexasPhase1ProductionRunner()
    
    try:
        results = await runner.run_production_processing()
        
        # Save results
        with open('texas_phase1_production_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: texas_phase1_production_results.json")
        
        # Performance summary
        print(f"\n🎯 PERFORMANCE SUMMARY:")
        print(f"   Target: 400,000 cases in 6 minutes")
        print(f"   Actual: {results['total_processed']:,} cases in {results['total_time_minutes']:.1f} minutes")
        
        if results['total_time_minutes'] <= 6:
            print(f"   ✅ TARGET ACHIEVED! ({results['total_time_minutes']:.1f} min ≤ 6 min)")
        else:
            print(f"   ⚠️  Slightly over target ({results['total_time_minutes']:.1f} min > 6 min)")
        
        print(f"   Success rate: {results['success_rate']:.1f}%")
        print(f"   Throughput: {results['throughput_docs_per_minute']:,.0f} docs/minute")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Production processing failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
