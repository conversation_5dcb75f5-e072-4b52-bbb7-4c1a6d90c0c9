#!/usr/bin/env python3
"""
Phase 3A: CAP Data Verification
Tests the complete enhanced pipeline with REAL CAP (Case Law Access Project) data:
- All 4 storage systems (Supabase, GCS, Pinecone, Neo4j)
- Enhanced judge disambiguation features
- Legal relationship processing
- Cross-system data integrity
- Unified output schema (same as CourtListener)
"""

import asyncio
import logging
import os
import json
import gzip
import time
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import components
from source_agnostic_processor import SourceAgnosticProcessor
from proper_cross_system_verifier import ProperCrossSystemVerifier
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase3ACAPDataVerification:
    """Phase 3A: Complete CAP data verification with real historical cases"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Initialize verifier
        self.verifier = ProperCrossSystemVerifier()
        
        # CAP data directory
        self.cap_data_dir = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
        
        # Test batch ID
        self.test_batch_id = f"phase3a_cap_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
        if hasattr(self, 'verifier'):
            self.verifier.close()
    
    def find_real_cap_files(self, max_files: int = 3) -> List[Path]:
        """Find real CAP data files for testing"""
        
        print(f"\n📁 SEARCHING FOR REAL CAP DATA FILES")
        print("=" * 60)
        
        cap_path = Path(self.cap_data_dir)
        
        if not cap_path.exists():
            print(f"❌ CAP data directory not found: {cap_path}")
            return []
        
        # Look for compressed JSONL files
        cap_files = []
        
        # Search patterns for CAP files
        patterns = [
            "**/*.jsonl.gz",
            "**/*.json.gz", 
            "**/texas*.gz",
            "**/tx*.gz"
        ]
        
        for pattern in patterns:
            found_files = list(cap_path.glob(pattern))
            cap_files.extend(found_files)
            
            if found_files:
                print(f"   📄 Found {len(found_files)} files matching '{pattern}'")
        
        # Remove duplicates and limit
        unique_files = list(set(cap_files))[:max_files]
        
        print(f"\n📊 CAP FILES SELECTED FOR TESTING:")
        for i, file_path in enumerate(unique_files, 1):
            file_size = file_path.stat().st_size / (1024 * 1024)  # MB
            print(f"   {i}. {file_path.name}")
            print(f"      Path: {file_path}")
            print(f"      Size: {file_size:.1f} MB")
        
        return unique_files
    
    def extract_real_cap_cases(self, cap_files: List[Path], max_cases: int = 5) -> List[Dict[str, Any]]:
        """Extract real CAP cases from compressed files"""
        
        print(f"\n📖 EXTRACTING REAL CAP CASES")
        print("=" * 60)
        
        real_cases = []
        
        for file_path in cap_files:
            if len(real_cases) >= max_cases:
                break
                
            print(f"   📄 Processing: {file_path.name}")
            
            try:
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if len(real_cases) >= max_cases:
                            break
                        
                        if line_num > 100:  # Don't read entire large files
                            break
                        
                        try:
                            case_data = json.loads(line.strip())

                            # CAP data has simpler structure: direct 'text' field
                            opinion_text = case_data.get('text', '')
                            case_id = case_data.get('id', f'cap_case_{line_num}')

                            # Look for cases with substantial content and judge indicators
                            if (opinion_text and
                                len(opinion_text) > 2000 and
                                any(indicator in opinion_text.lower() for indicator in
                                    ['judge', 'justice', 'court', 'opinion', 'held', 'appellant', 'appellee'])):

                                # Extract case name from text (first line usually contains case name)
                                text_lines = opinion_text.strip().split('\n')
                                case_name = 'Unknown CAP Case'
                                for line in text_lines[:5]:  # Check first 5 lines
                                    line = line.strip()
                                    if line and 'v.' in line and len(line) < 100:
                                        case_name = line
                                        break

                                # Extract court info from text
                                court_name = 'Unknown Court'
                                for line in text_lines[:10]:  # Check first 10 lines
                                    line = line.strip().lower()
                                    if 'court' in line and ('texas' in line or 'appeals' in line):
                                        court_name = line.title()
                                        break

                                # Transform to processing format
                                processed_case = {
                                    'id': case_id.replace('/', '_').replace('.html', ''),
                                    'source': 'caselaw_access_project',
                                    'case_name': case_name,
                                    'court': 'tx_historical',  # Generic court for CAP data
                                    'court_name': court_name,
                                    'date_filed': '1950-01-01',  # Default historical date
                                    'jurisdiction': 'TX',
                                    'text': opinion_text,
                                    'precedential_status': 'Published',
                                    'historical_era': 'historical'
                                }

                                real_cases.append(processed_case)
                                print(f"      ✅ Extracted: {processed_case['case_name'][:50]}...")
                                print(f"         ID: {processed_case['id']}")
                                print(f"         Text length: {len(opinion_text):,} characters")
                                print(f"         Court: {processed_case['court_name']}")
                                print(f"         Source: CAP Historical Data")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"      ⚠️ Error processing line {line_num}: {e}")
                            continue
            
            except Exception as e:
                print(f"   ❌ Error reading {file_path.name}: {e}")
                continue
        
        print(f"\n✅ EXTRACTED {len(real_cases)} REAL CAP CASES")
        return real_cases
    
    async def test_cap_data_processing(self) -> bool:
        """Test CAP data processing through the complete enhanced pipeline"""
        
        print(f"\n🚀 PHASE 3A: CAP DATA PROCESSING TEST")
        print("=" * 80)
        print(f"🎯 Testing enhanced pipeline with REAL CAP historical data")
        
        try:
            # Step 1: Find and extract real CAP cases
            cap_files = self.find_real_cap_files(max_files=2)
            
            if not cap_files:
                print(f"❌ No CAP files found for testing")
                return False
            
            real_cap_cases = self.extract_real_cap_cases(cap_files, max_cases=4)
            
            if not real_cap_cases:
                print(f"❌ No CAP cases extracted for testing")
                return False
            
            case_ids = [case['id'] for case in real_cap_cases]
            
            # Step 2: Process CAP cases through enhanced pipeline
            print(f"\n🔄 Processing {len(real_cap_cases)} real CAP cases through enhanced pipeline...")
            
            processor = SourceAgnosticProcessor(
                self.supabase, 
                self.gcs_client, 
                self.pinecone_client, 
                self.neo4j_client,
                enable_legal_relationships=True,  # Enable ALL enhanced features
                legal_relationship_timeout=240    # 4 minute timeout for historical data
            )
            
            start_time = time.time()
            result = await processor.process_coherent_batch(
                raw_cases=real_cap_cases,
                source_type='caselaw_access_project',  # CAP source type
                batch_id=self.test_batch_id
            )
            processing_time = time.time() - start_time
            
            print(f"\n📊 CAP DATA PIPELINE PROCESSING RESULT:")
            print(f"   Success: {'✅' if result['success'] else '❌'}")
            print(f"   Cases processed: {result['processed']}")
            print(f"   Total processing time: {processing_time:.2f}s")
            
            if not result['success']:
                print(f"   ❌ CAP pipeline processing failed: {result}")
                return False
            
            # Step 3: Verify CAP data across all 4 systems
            return await self.verify_cap_cross_system_integrity(case_ids, real_cap_cases)
            
        except Exception as e:
            print(f"❌ CAP data processing test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_cap_cross_system_integrity(self, case_ids: List[str], real_cap_cases: List[Dict]) -> bool:
        """Verify CAP data integrity across all 4 storage systems"""
        
        print(f"\n🔍 VERIFYING CAP DATA CROSS-SYSTEM INTEGRITY")
        print("=" * 60)
        
        try:
            # Wait for all systems to be ready
            print(f"⏱️ Waiting for all systems to be ready for CAP verification...")
            await asyncio.sleep(15)  # Longer wait for CAP data
            
            # Run comprehensive cross-system verification
            print(f"🔍 Running cross-system verification for {len(case_ids)} CAP cases...")
            
            basic_report = await self.verifier.verify_batch(case_ids)
            
            print(f"\n🔍 Running detailed data integrity verification...")
            integrity_report = await self.verifier.verify_data_integrity(case_ids)
            
            # Analyze CAP-specific results
            return self._assess_cap_verification_results(basic_report, integrity_report, real_cap_cases)
            
        except Exception as e:
            print(f"❌ Error verifying CAP cross-system integrity: {e}")
            return False
    
    def _assess_cap_verification_results(self, basic_report: dict, integrity_report: dict, real_cap_cases: List[Dict]) -> bool:
        """Assess CAP data verification results"""
        
        print(f"\n📊 PHASE 3A CAP DATA VERIFICATION RESULTS")
        print("=" * 80)
        
        # Basic verification results
        basic_success = basic_report.get('overall_success', False)
        success_rate = basic_report.get('verification_summary', {}).get('success_rate', 0)
        avg_consistency = basic_report.get('verification_summary', {}).get('average_consistency_score', 0)
        
        print(f"🔍 CAP DATA CROSS-SYSTEM VERIFICATION:")
        print(f"   CAP Cases Verified: {len(real_cap_cases)}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Consistency: {avg_consistency:.1f}%")
        print(f"   Overall Success: {'✅' if basic_success else '❌'}")
        
        # System coverage with CAP data
        system_coverage = basic_report.get('system_coverage', {})
        system_success_rates = basic_report.get('system_success_rates', {})
        
        print(f"\n📊 CAP DATA SYSTEM COVERAGE:")
        for system, count in system_coverage.items():
            success_rate_sys = system_success_rates.get(system, 0)
            status = '✅' if success_rate_sys >= 80 else '❌'
            print(f"   {system.upper()}: {count}/{len(real_cap_cases)} cases ({success_rate_sys:.1f}%) {status}")
        
        # Data integrity with CAP data
        integrity_success = integrity_report.get('overall_integrity', False)
        integrity_summary = integrity_report.get('integrity_summary', {})
        
        print(f"\n🔍 CAP DATA INTEGRITY VERIFICATION:")
        print(f"   Overall Integrity: {'✅' if integrity_success else '❌'}")
        
        for check_type, results in integrity_summary.items():
            success_rate_int = results.get('success_rate', 0)
            status = '✅' if success_rate_int >= 80 else '❌'
            print(f"   {check_type.replace('_', ' ').title()}: {success_rate_int:.1f}% {status}")
        
        # CAP-specific analysis
        print(f"\n🏛️ CAP HISTORICAL DATA ANALYSIS:")
        for case in real_cap_cases:
            print(f"   📄 {case['case_name'][:50]}...")
            print(f"      Court: {case['court_name']}")
            print(f"      Date: {case['date_filed']} (Historical Era)")
            print(f"      Text: {len(case['text']):,} characters")
        
        # Enhanced features with CAP data
        enhanced_working = any(
            'enhanced_features' in str(results) for results in basic_report.get('detailed_results', [])
        )
        
        print(f"\n🔧 ENHANCED FEATURES WITH CAP DATA:")
        print(f"   Enhanced Judge Features: {'✅' if enhanced_working else '❌'}")
        print(f"   Legal Relationships: {'✅' if basic_success else '❌'}")
        print(f"   Cross-System Tracking: {'✅' if avg_consistency >= 75 else '❌'}")
        print(f"   Unified Output Schema: {'✅' if success_rate >= 80 else '❌'}")
        
        # Final assessment for CAP data
        cap_success = (
            basic_success and 
            integrity_success and 
            success_rate >= 75 and  # Slightly lower threshold for historical data
            avg_consistency >= 70
        )
        
        print(f"\n🎯 PHASE 3A CAP DATA FINAL ASSESSMENT:")
        print(f"   CAP Data Processing: {'✅' if basic_success else '❌'}")
        print(f"   Cross-System Integrity: {'✅' if integrity_success else '❌'}")
        print(f"   System Performance: {'✅' if success_rate >= 75 else '❌'} ({success_rate:.1f}%)")
        print(f"   Data Consistency: {'✅' if avg_consistency >= 70 else '❌'} ({avg_consistency:.1f}%)")
        print(f"   Overall Success: {'✅' if cap_success else '❌'}")
        
        if cap_success:
            print(f"\n🎉 PHASE 3A: COMPLETE SUCCESS WITH CAP DATA!")
            print(f"✅ All 4 systems verified with real CAP historical data")
            print(f"✅ Enhanced features working on historical cases")
            print(f"✅ Cross-system integrity maintained with CAP data")
            print(f"✅ Unified output schema confirmed (same as CourtListener)")
            print(f"✅ Ready for production use with CAP historical data")
        else:
            print(f"\n❌ PHASE 3A: FAILED WITH CAP DATA!")
            print(f"❌ Issues found in CAP data processing")
            print(f"❌ Further optimization needed for historical cases")
        
        return cap_success
    
    async def run_complete_phase3a_test(self) -> bool:
        """Run complete Phase 3A test with real CAP data"""
        
        print("🚀 PHASE 3A: COMPLETE CAP DATA VERIFICATION")
        print("=" * 80)
        print("🎯 OBJECTIVES:")
        print("   - Test enhanced pipeline with REAL CAP historical data")
        print("   - Verify all 4 storage systems with CAP data")
        print("   - Confirm enhanced features work on historical cases")
        print("   - Validate unified output schema (same as CourtListener)")
        print("   - Ensure production readiness with CAP data")
        
        try:
            # Test CAP data processing
            cap_success = await self.test_cap_data_processing()
            
            print(f"\n🎯 PHASE 3A FINAL ASSESSMENT:")
            print(f"   CAP Data Processing: {'✅' if cap_success else '❌'}")
            print(f"   Overall Success: {'✅' if cap_success else '❌'}")
            
            if cap_success:
                print(f"\n🎉 PHASE 3A: COMPLETE SUCCESS!")
                print(f"✅ CAP historical data processing verified")
                print(f"✅ All 4 storage systems working with CAP data")
                print(f"✅ Enhanced features operational on historical cases")
                print(f"✅ Unified output confirmed (seamless with CourtListener)")
                print(f"✅ Ready for Phase 3B: Cross-Source Consistency")
            else:
                print(f"\n❌ PHASE 3A: FAILED!")
                print(f"❌ Issues found in CAP data processing")
                print(f"❌ Must fix CAP issues before proceeding")
            
            return cap_success
            
        except Exception as e:
            print(f"❌ Complete Phase 3A test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up Phase 3A CAP test data"""
        
        print(f"\n🧹 CLEANING UP PHASE 3A CAP DATA TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase CAP test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                # Clean test cases
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                
                # Clean orphaned cited cases
                session.run('''
                    MATCH (cited:CitedCase)
                    WHERE NOT EXISTS { MATCH (cited)<-[]-() }
                    DELETE cited
                ''')
                
                # Clean orphaned judges
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j CAP test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run Phase 3A complete CAP data verification"""
    
    test = Phase3ACAPDataVerification()
    
    try:
        success = await test.run_complete_phase3a_test()
        return success
    finally:
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
