#!/usr/bin/env python3
"""
Complete fresh start - delete ALL case data from ALL databases.
This ensures perfect 100% consistency when we rebuild everything.
"""

import asyncio
import sys
import os
from pinecone import Pinecone

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector
from processing.storage.gcs_connector import GCSConnector
from processing.storage.supabase_connector import SupabaseConnector

async def cleanup_supabase():
    """Clean up ALL cases from Supabase."""
    
    try:
        print("🗑️  CLEANING UP SUPABASE (COMPLETE)")
        print("=" * 50)
        
        supabase = SupabaseConnector()
        
        # Count current cases
        response = supabase.client.table('cases').select('id', count='exact').execute()
        case_count = response.count
        print(f"   Current cases: {case_count:,}")
        
        if case_count > 0:
            # Delete all cases
            print("   🗑️  Deleting ALL cases...")
            response = supabase.client.table('cases').delete().neq('id', 'impossible_id_that_does_not_exist').execute()
            print("   ✅ All cases deleted")
            
            # Verify deletion
            response = supabase.client.table('cases').select('id', count='exact').execute()
            remaining_count = response.count
            print(f"   📊 Cases remaining: {remaining_count}")
        else:
            print("   ✅ No cases to delete")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Supabase: {e}")
        return False

async def cleanup_neo4j():
    """Clean up Neo4j Document nodes and related data."""
    
    try:
        print("\n🗑️  CLEANING UP NEO4J")
        print("=" * 50)
        
        neo4j = Neo4jConnector()
        
        with neo4j.driver.session() as session:
            # Count current nodes
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            doc_count = result.single()['count']
            print(f"   Current Document nodes: {doc_count:,}")
            
            # Delete all Document nodes and their relationships
            if doc_count > 0:
                print("   🗑️  Deleting all Document nodes...")
                result = session.run("MATCH (d:Document) DETACH DELETE d")
                print("   ✅ Document nodes deleted")
            
            # Delete Citation nodes
            result = session.run("MATCH (c:Citation) RETURN count(c) as count")
            citation_count = result.single()['count']
            print(f"   Current Citation nodes: {citation_count:,}")
            
            if citation_count > 0:
                print("   🗑️  Deleting all Citation nodes...")
                result = session.run("MATCH (c:Citation) DETACH DELETE c")
                print("   ✅ Citation nodes deleted")
            
            # Clean up any orphaned relationships
            print("   🗑️  Cleaning up orphaned relationships...")
            result = session.run("MATCH ()-[r:BELONGS_TO_PRACTICE_AREA]->() DELETE r")
            result = session.run("MATCH ()-[r:CITES]->() DELETE r")
            print("   ✅ Orphaned relationships cleaned")
            
            # Keep structural nodes (Courts, Jurisdictions, PracticeAreas)
            print("   ✅ Keeping structural nodes (Courts, Jurisdictions, etc.)")
            
            # Verify cleanup
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            remaining_docs = result.single()['count']
            
            result = session.run("MATCH (c:Citation) RETURN count(c) as count")
            remaining_citations = result.single()['count']
            
            print(f"   📊 Cleanup results:")
            print(f"     Document nodes remaining: {remaining_docs}")
            print(f"     Citation nodes remaining: {remaining_citations}")
        
        neo4j.close()
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Neo4j: {e}")
        return False

async def cleanup_pinecone():
    """Clean up Pinecone vectors."""
    
    try:
        print("\n🗑️  CLEANING UP PINECONE")
        print("=" * 50)
        
        # Initialize Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        
        # Get current stats
        stats = index.describe_index_stats()
        print(f"   Current vectors: {stats.total_vector_count:,}")
        
        if stats.total_vector_count > 0:
            # Delete all vectors
            print("   🗑️  Deleting all vectors...")
            index.delete(delete_all=True)
            print("   ✅ All vectors deleted")
            
            # Wait for deletion to propagate
            import time
            print("   ⏳ Waiting for deletion to propagate...")
            time.sleep(10)
            
            # Verify cleanup
            stats = index.describe_index_stats()
            print(f"   📊 Vectors remaining: {stats.total_vector_count}")
        else:
            print("   ✅ No vectors to delete")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Pinecone: {e}")
        return False

async def cleanup_gcs():
    """Clean up Google Cloud Storage files."""
    
    try:
        print("\n🗑️  CLEANING UP GOOGLE CLOUD STORAGE")
        print("=" * 50)
        
        gcs = GCSConnector()
        bucket = gcs.client.bucket(gcs.bucket_name)
        
        # Count current files
        all_blobs = list(bucket.list_blobs())
        print(f"   Current files: {len(all_blobs):,}")
        
        # Delete case-related files
        case_prefixes = [
            'caselaw_access/',
            'court_listener/', 
            'cases/'
        ]
        
        total_deleted = 0
        for prefix in case_prefixes:
            blobs = list(bucket.list_blobs(prefix=prefix))
            if blobs:
                print(f"   🗑️  Deleting {len(blobs)} files with prefix '{prefix}'...")
                for blob in blobs:
                    blob.delete()
                total_deleted += len(blobs)
                print(f"   ✅ Deleted {len(blobs)} files")
        
        # Keep statute files and other structural data
        print("   ✅ Keeping statute files and other structural data")
        
        print(f"   📊 Total files deleted: {total_deleted:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning GCS: {e}")
        return False

async def verify_complete_cleanup():
    """Verify all databases are completely clean."""
    
    try:
        print("\n✅ VERIFYING COMPLETE CLEANUP")
        print("=" * 50)
        
        # Check Supabase
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        supabase_count = response.count
        
        # Check Neo4j
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            neo4j_docs = result.single()['count']
            result = session.run("MATCH (c:Citation) RETURN count(c) as count")
            neo4j_citations = result.single()['count']
        neo4j.close()
        
        # Check Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        pinecone_count = stats.total_vector_count
        
        print(f"📊 CLEANUP VERIFICATION:")
        print(f"   Supabase cases: {supabase_count}")
        print(f"   Neo4j Document nodes: {neo4j_docs}")
        print(f"   Neo4j Citation nodes: {neo4j_citations}")
        print(f"   Pinecone vectors: {pinecone_count}")
        
        all_clean = (supabase_count == 0 and neo4j_docs == 0 and 
                    neo4j_citations == 0 and pinecone_count == 0)
        
        if all_clean:
            print(f"   ✅ PERFECT: All databases completely clean!")
        else:
            print(f"   ⚠️  Some data remains - may need manual cleanup")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error verifying cleanup: {e}")
        return False

async def main():
    """Main complete cleanup function."""
    
    print("🧹 COMPLETE FRESH START - DELETE ALL CASE DATA")
    print("=" * 70)
    print("⚠️  WARNING: This will delete ALL case data from ALL databases!")
    print("   - Supabase: ALL cases")
    print("   - Neo4j: All Document and Citation nodes")
    print("   - Pinecone: ALL vectors")
    print("   - GCS: All case files")
    print("   - Structural data: PRESERVED (Courts, Jurisdictions, Statutes)")
    print("=" * 70)
    
    # Perform complete cleanup
    supabase_success = await cleanup_supabase()
    neo4j_success = await cleanup_neo4j()
    pinecone_success = await cleanup_pinecone()
    gcs_success = await cleanup_gcs()
    
    # Verify cleanup
    all_clean = await verify_complete_cleanup()
    
    print(f"\n🎯 COMPLETE CLEANUP SUMMARY:")
    print(f"   Supabase cleanup: {'✅ Success' if supabase_success else '❌ Failed'}")
    print(f"   Neo4j cleanup: {'✅ Success' if neo4j_success else '❌ Failed'}")
    print(f"   Pinecone cleanup: {'✅ Success' if pinecone_success else '❌ Failed'}")
    print(f"   GCS cleanup: {'✅ Success' if gcs_success else '❌ Failed'}")
    print(f"   Verification: {'✅ All Clean' if all_clean else '⚠️  Partial'}")
    
    if all([supabase_success, neo4j_success, pinecone_success, gcs_success, all_clean]):
        print(f"\n🎉 COMPLETE FRESH START READY!")
        print(f"   ✅ All databases completely clean")
        print(f"   ✅ Structural data preserved")
        print(f"   🚀 Ready for fresh processing with perfect consistency")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Run Court Listener processor for priority states")
        print(f"   2. Run Caselaw Access Project processor")
        print(f"   3. Achieve perfect 100% consistency across all databases")
        print(f"   4. No more ID mismatches or historical inconsistencies!")
    else:
        print(f"\n⚠️  CLEANUP INCOMPLETE")
        print(f"   Some cleanup operations failed - check logs above")
        print(f"   Manual intervention may be required")

if __name__ == "__main__":
    # Double confirmation for complete deletion
    print("⚠️  FINAL WARNING: This will delete ALL case data from ALL databases!")
    print("   This action cannot be undone!")
    print("   You will need to reprocess all cases from scratch.")
    
    confirm1 = input("\nType 'DELETE ALL' to confirm complete cleanup: ")
    if confirm1 == 'DELETE ALL':
        confirm2 = input("Type 'I UNDERSTAND' to proceed: ")
        if confirm2 == 'I UNDERSTAND':
            asyncio.run(main())
        else:
            print("❌ Complete cleanup cancelled")
    else:
        print("❌ Complete cleanup cancelled")
