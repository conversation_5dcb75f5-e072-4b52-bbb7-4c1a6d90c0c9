#!/usr/bin/env python3
"""
Texas Real Cases Client

Sends real Texas cases to Google Cloud Run for processing.
"""

import asyncio
import aiohttp
import json
import logging
import time
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TexasRealCasesClient:
    """Client for processing real Texas cases via Cloud Run."""
    
    def __init__(self, service_url: str):
        self.service_url = service_url.rstrip('/')
        self.batch_size = 25
    
    async def process_all_cases(self, cases: List[Dict]) -> Dict[str, Any]:
        """Process all cases using Cloud Run service."""
        
        logger.info(f"🚀 Processing {len(cases)} real Texas cases...")
        
        # Create batches
        batches = [cases[i:i + self.batch_size] for i in range(0, len(cases), self.batch_size)]
        logger.info(f"Created {len(batches)} batches of {self.batch_size} cases each")
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            # Send batch processing request
            payload = {'batches': batches}
            
            async with session.post(
                f"{self.service_url}/batch-process",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=3600)  # 1 hour timeout
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    total_time = time.time() - start_time
                    
                    logger.info("✅ Processing complete!")
                    logger.info(f"   Total processed: {result['total_processed']:,}")
                    logger.info(f"   Total filtered: {result['total_filtered']:,}")
                    logger.info(f"   Total stored: {result['total_stored']:,}")
                    logger.info(f"   Processing time: {total_time:.1f} seconds")
                    logger.info(f"   Throughput: {result['throughput_cases_per_second']:,.0f} cases/second")
                    
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Processing failed: {response.status} - {error_text}")
                    return {'success': False, 'error': error_text}


async def main():
    """Main function to process real Texas cases."""
    
    # Load real cases
    cases_file = 'simple_texas_results.json'
    
    try:
        with open(cases_file, 'r') as f:
            cases = json.load(f)
        
        logger.info(f"Loaded {len(cases)} real Texas cases from {cases_file}")
        
    except FileNotFoundError:
        logger.error(f"Cases file not found: {cases_file}")
        logger.error("Please run texas_production_real_data.py first")
        return False
    
    # Get service URL
    service_url = input("Enter Cloud Run service URL: ").strip()
    
    if not service_url:
        logger.error("Service URL required")
        return False
    
    # Process cases
    client = TexasRealCasesClient(service_url)
    result = await client.process_all_cases(cases)
    
    if result.get('success'):
        # Save results
        with open('texas_real_cases_processing_results.json', 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info("📄 Results saved to texas_real_cases_processing_results.json")
        return True
    else:
        logger.error("Processing failed")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
