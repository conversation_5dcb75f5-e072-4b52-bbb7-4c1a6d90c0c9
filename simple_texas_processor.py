#!/usr/bin/env python3
"""
Simple Texas Case Processor

Processes actual Caselaw Access Project data to find Texas cases.
Uses the actual data structure we discovered.
"""

import gzip
import json
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter


def process_cap_file(file_path: Path, max_cases: int = 1000):
    """Process a single CAP file and find Texas cases."""
    
    print(f"🔍 Processing: {file_path.name}")
    
    filter_engine = TexasPhase1Filter()
    texas_cases = []
    total_cases = 0
    
    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if not line.strip():
                continue
            
            try:
                raw_case = json.loads(line)
                total_cases += 1
                
                # Extract basic info
                case_id = raw_case.get('id', '')
                case_text = raw_case.get('text', '')
                
                # Check if this case mentions Texas
                text_lower = case_text.lower()
                is_texas = any(indicator in text_lower for indicator in [
                    'texas', 'tex.', 'dallas', 'houston', 'austin', 'san antonio',
                    'harris county', 'travis county', 'tarrant county'
                ])
                
                if is_texas:
                    # Create case document
                    case_doc = {
                        'id': f'cap_{case_id}',
                        'case_name': case_id,  # Use ID as name for now
                        'text': case_text,
                        'court': 'Unknown',
                        'jurisdiction': 'texas',
                        'date_filed': '',
                        'source': 'caselaw_access_project'
                    }
                    
                    texas_cases.append(case_doc)
                    print(f"✅ Found Texas case: {case_id}")
                    
                    # Show a snippet
                    snippet = case_text[:200] + "..." if len(case_text) > 200 else case_text
                    print(f"   Snippet: {snippet}")
                    print()
                    
                    if len(texas_cases) >= max_cases:
                        break
                
                if total_cases % 10000 == 0:
                    print(f"   Processed {total_cases:,} cases, found {len(texas_cases)} Texas cases")
                    
            except json.JSONDecodeError:
                continue
            except Exception as e:
                print(f"   Error processing line {line_num}: {e}")
                continue
    
    print(f"📊 File summary: {total_cases:,} total cases, {len(texas_cases)} Texas cases")
    
    # Now filter Texas cases through Phase 1 filter
    if texas_cases:
        print(f"🔍 Filtering {len(texas_cases)} Texas cases through Phase 1 filter...")
        filtered_cases, filter_stats = filter_engine.batch_filter_documents(texas_cases)
        
        print(f"📊 Phase 1 Results:")
        print(f"   Criminal Defense: {filter_stats.get('criminal_defense', 0)}")
        print(f"   Personal Injury: {filter_stats.get('personal_injury', 0)}")
        print(f"   Medical Malpractice: {filter_stats.get('medical_malpractice', 0)}")
        print(f"   Total Phase 1 cases: {len(filtered_cases)}")
        
        return filtered_cases, filter_stats
    
    return [], {}


def main():
    """Main function."""
    
    print("🤠 SIMPLE TEXAS CASE PROCESSOR")
    print("=" * 40)
    
    # Find CAP files
    data_dir = Path("data/caselaw_access_project")
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return False
    
    cap_files = list(data_dir.glob("cap_*.jsonl.gz"))
    if not cap_files:
        print(f"❌ No CAP files found in {data_dir}")
        return False
    
    cap_files.sort()
    print(f"📁 Found {len(cap_files)} CAP files")
    
    # Process first few files
    all_texas_cases = []
    all_phase1_cases = []
    
    for i, file_path in enumerate(cap_files[:3]):  # Process first 3 files
        print(f"\n🔄 Processing file {i+1}/3: {file_path.name}")
        
        phase1_cases, stats = process_cap_file(file_path, max_cases=50)
        
        if phase1_cases:
            all_phase1_cases.extend(phase1_cases)
            print(f"✅ Added {len(phase1_cases)} Phase 1 cases from this file")
        else:
            print("ℹ️  No Phase 1 cases found in this file")
    
    # Final summary
    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"📊 Final Results:")
    print(f"   Total Phase 1 cases found: {len(all_phase1_cases)}")
    
    if all_phase1_cases:
        print(f"\n📄 Sample Phase 1 cases:")
        for i, case in enumerate(all_phase1_cases[:3]):
            print(f"   {i+1}. {case['id']}")
            snippet = case['text'][:150] + "..." if len(case['text']) > 150 else case['text']
            print(f"      {snippet}")
            print()
        
        # Save results
        with open('simple_texas_results.json', 'w') as f:
            json.dump(all_phase1_cases, f, indent=2)
        print(f"💾 Results saved to: simple_texas_results.json")
        
        return True
    else:
        print("⚠️  No Phase 1 cases found. The data might not contain Texas state court cases.")
        print("💡 The CAP dataset appears to contain primarily federal cases.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
