#!/usr/bin/env python3
"""
Test CAP 70s-80s Enhanced Judge Features
Tests enhanced judge extraction and features with CAP files from 1970s-1980s
"""

import asyncio
import logging
import os
import json
import gzip
import time
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import components
from source_agnostic_processor import SourceAgnosticProcessor
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CAPEnhancedJudgeTest:
    """Test enhanced judge features with CAP files from 70s-80s"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CAP data directory
        self.cap_data_dir = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
        
        # Test batch ID
        self.test_batch_id = f"cap_70s_80s_judges_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Target files with 70s-80s data
        self.target_files = [
            "cap_00000.jsonl.gz",  # 1973 case with TRASK, GOODWIN, WALLACE
            "cap_00001.jsonl.gz",  # 1979 case with MERRILL, KENNEDY, WILLIAMS
            "cap_00002.jsonl.gz",  # 1984 case with MINER
        ]
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def extract_targeted_cap_cases(self, max_cases: int = 3) -> List[Dict[str, Any]]:
        """Extract specific CAP cases from 70s-80s with good judge data"""
        
        print(f"\n📖 EXTRACTING TARGETED CAP CASES FROM 70s-80s")
        print("=" * 60)
        
        cap_cases = []
        
        for filename in self.target_files:
            if len(cap_cases) >= max_cases:
                break
                
            file_path = Path(self.cap_data_dir) / filename
            
            if not file_path.exists():
                print(f"   ⚠️ File not found: {filename}")
                continue
                
            print(f"   📄 Processing: {filename}")
            
            try:
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if len(cap_cases) >= max_cases:
                            break
                        
                        if line_num > 5:  # Only check first few lines per file
                            break
                        
                        try:
                            case_data = json.loads(line.strip())
                            text = case_data.get('text', '')
                            
                            # Look for 70s-80s cases with judge patterns
                            if (text and 
                                len(text) > 2000 and
                                any(year in text for year in ['197', '198']) and
                                any(pattern in text.upper() for pattern in ['CIRCUIT JUDGES', 'DISTRICT JUDGE', 'BEFORE'])):
                                
                                # Extract year for better dating
                                year_match = None
                                for line in text.split('\n')[:15]:
                                    if any(year in line for year in ['197', '198']):
                                        # Try to extract actual year
                                        import re
                                        year_search = re.search(r'(19[7-8]\d)', line)
                                        if year_search:
                                            year_match = year_search.group(1)
                                            break
                                
                                # Transform to processing format
                                processed_case = {
                                    'id': case_data.get('id', f'cap_70s_80s_{line_num}').replace('/', '_').replace('.html', ''),
                                    'source': 'caselaw_access_project',
                                    'case_name': self._extract_case_name(text),
                                    'court': self._extract_court_id(text),
                                    'court_name': self._extract_court_name(text),
                                    'date_filed': f'{year_match}-01-01' if year_match else '1975-01-01',
                                    'jurisdiction': 'US',
                                    'text': text,
                                    'precedential_status': 'Published',
                                    'historical_era': 'historical'
                                }
                                
                                cap_cases.append(processed_case)
                                print(f"      ✅ Extracted: {processed_case['case_name'][:50]}...")
                                print(f"         Year: {year_match or 'Unknown'}")
                                print(f"         Court: {processed_case['court_name']}")
                                print(f"         Text length: {len(text):,} characters")
                                
                                # Show judge patterns found
                                judge_lines = [line.strip() for line in text.split('\n')[:30] 
                                             if any(word in line.upper() for word in ['JUDGE', 'JUSTICE', 'CIRCUIT', 'DISTRICT']) 
                                             and len(line.strip()) < 100 and len(line.strip()) > 10]
                                print(f"         Judge patterns: {judge_lines[:2]}")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"      ⚠️ Error processing line {line_num}: {e}")
                            continue
            
            except Exception as e:
                print(f"   ❌ Error reading {filename}: {e}")
                continue
        
        print(f"\n✅ EXTRACTED {len(cap_cases)} TARGETED CAP CASES FROM 70s-80s")
        return cap_cases
    
    def _extract_case_name(self, text: str) -> str:
        """Extract case name from CAP text"""
        lines = text.strip().split('\n')
        for line in lines[:10]:
            line = line.strip()
            if line and 'v.' in line and len(line) < 100 and not any(word in line.lower() for word in ['court', 'circuit', 'district']):
                return line
        return 'Unknown CAP Case'
    
    def _extract_court_id(self, text: str) -> str:
        """Extract court ID from CAP text"""
        if 'ninth circuit' in text.lower():
            return 'ca9'
        elif 'circuit' in text.lower():
            return 'circuit_court'
        elif 'district' in text.lower():
            return 'district_court'
        return 'unknown_court'
    
    def _extract_court_name(self, text: str) -> str:
        """Extract court name from CAP text"""
        lines = text.strip().split('\n')
        for line in lines[:10]:
            line = line.strip()
            if 'court' in line.lower() and ('circuit' in line.lower() or 'district' in line.lower()):
                return line
        return 'Unknown Court'
    
    async def test_enhanced_judge_features(self) -> bool:
        """Test all enhanced judge features with 70s-80s CAP data"""
        
        print(f"\n🚀 TESTING ENHANCED JUDGE FEATURES WITH 70s-80s CAP DATA")
        print("=" * 80)
        print(f"🎯 Testing ALL enhanced judge features:")
        print(f"   - Full name extraction (when available)")
        print(f"   - Court assignment & geography")
        print(f"   - Confidence scoring")
        print(f"   - Career progression tracking")
        print(f"   - External validation")
        print(f"   - Multiple judge extraction")
        print(f"   - Relationship types (AUTHORED, PARTICIPATED, etc.)")
        
        try:
            # Step 1: Extract targeted CAP cases from 70s-80s
            cap_cases = self.extract_targeted_cap_cases(max_cases=3)
            
            if not cap_cases:
                print(f"❌ No 70s-80s CAP cases extracted")
                return False
            
            case_ids = [case['id'] for case in cap_cases]
            
            # Step 2: Process through enhanced pipeline
            print(f"\n🔄 Processing {len(cap_cases)} 70s-80s CAP cases with ALL enhanced features...")
            
            processor = SourceAgnosticProcessor(
                self.supabase, 
                self.gcs_client, 
                self.pinecone_client, 
                self.neo4j_client,
                enable_legal_relationships=True,  # Enable ALL enhanced features
                legal_relationship_timeout=240
            )
            
            start_time = time.time()
            result = await processor.process_coherent_batch(
                raw_cases=cap_cases,
                source_type='caselaw_access_project',
                batch_id=self.test_batch_id
            )
            processing_time = time.time() - start_time
            
            print(f"\n📊 70s-80s CAP PROCESSING RESULT:")
            print(f"   Success: {'✅' if result['success'] else '❌'}")
            print(f"   Cases processed: {result['processed']}")
            print(f"   Processing time: {processing_time:.2f}s")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Step 3: Analyze enhanced judge features
            return await self.analyze_enhanced_judge_features(case_ids, cap_cases)
            
        except Exception as e:
            print(f"❌ Enhanced judge features test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def analyze_enhanced_judge_features(self, case_ids: List[str], cap_cases: List[Dict]) -> bool:
        """Analyze all enhanced judge features from the processed cases"""
        
        print(f"\n🔍 ANALYZING ENHANCED JUDGE FEATURES")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get comprehensive judge data (using actual schema properties)
                result = session.run("""
                    MATCH (c:Case)-[r]->(j:Judge)
                    WHERE c.id IN $case_ids
                    RETURN c.id as case_id,
                           c.case_name as case_name,
                           c.court as court_id,
                           c.date_filed as case_date,
                           j.name as judge_name,
                           COALESCE(j.court_id, 'unknown') as judge_court,
                           COALESCE(j.court_name, 'Unknown Court') as judge_court_name,
                           COALESCE(j.jurisdiction, 'US') as judge_jurisdiction,
                           COALESCE(j.confidence_score, 50.0) as confidence_score,
                           COALESCE(j.career_progression_detected, false) as career_progression,
                           COALESCE(j.external_validated, false) as external_validated,
                           COALESCE(j.external_confidence, 0.0) as external_confidence,
                           COALESCE(j.case_count, 1) as case_count,
                           type(r) as relationship_type,
                           COALESCE(r.role, 'participated') as judge_role,
                           COALESCE(r.source, 'text_extraction') as relationship_source
                    ORDER BY c.id, j.name
                """, case_ids=case_ids)
                
                judge_data = list(result)
                
                print(f"📊 ENHANCED JUDGE ANALYSIS RESULTS:")
                print(f"   Total judge relationships: {len(judge_data)}")
                
                # Analyze by case
                case_judges = {}
                for record in judge_data:
                    case_id = record['case_id']
                    if case_id not in case_judges:
                        case_judges[case_id] = []
                    case_judges[case_id].append(record)
                
                total_judges = 0
                full_names_count = 0
                high_confidence_count = 0
                external_validated_count = 0
                career_progression_count = 0
                
                for case_id, judges in case_judges.items():
                    case_name = judges[0]['case_name'] if judges else 'Unknown'
                    case_date = judges[0]['case_date'] if judges else 'Unknown'
                    
                    print(f"\n📄 CASE: {case_name[:50]}... ({case_date})")
                    print(f"   Judges found: {len(judges)}")
                    
                    for judge in judges:
                        total_judges += 1
                        judge_name = judge['judge_name']
                        confidence = judge['confidence_score'] or 0
                        
                        # Check if full name (more than one word)
                        if len(judge_name.split()) > 1:
                            full_names_count += 1
                            name_type = "Full Name ✅"
                        else:
                            name_type = "Surname Only"
                        
                        # Check confidence level
                        if confidence >= 70:
                            high_confidence_count += 1
                            conf_level = "High ✅"
                        elif confidence >= 50:
                            conf_level = "Medium"
                        else:
                            conf_level = "Low"
                        
                        # Check enhanced features
                        if judge['external_validated']:
                            external_validated_count += 1
                        if judge['career_progression_detected']:
                            career_progression_count += 1
                        
                        print(f"      👨‍⚖️ {judge_name} ({name_type})")
                        print(f"         Court: {judge['judge_court_name'] or judge['judge_court'] or 'Unknown'}")
                        print(f"         Jurisdiction: {judge['judge_jurisdiction'] or 'Unknown'}")
                        print(f"         Confidence: {confidence:.1f}% ({conf_level})")
                        print(f"         Relationship: {judge['relationship_type']} ({judge['judge_role']})")
                        print(f"         External Validated: {'✅' if judge['external_validated'] else '❌'}")
                        print(f"         Career Progression: {'✅' if judge['career_progression_detected'] else '❌'}")
                        print(f"         Case Count: {judge['case_count'] or 1}")
                
                # Summary statistics
                print(f"\n📊 ENHANCED JUDGE FEATURES SUMMARY:")
                print(f"   Total judges processed: {total_judges}")
                print(f"   Full names extracted: {full_names_count}/{total_judges} ({full_names_count/total_judges*100:.1f}%)")
                print(f"   High confidence (≥70%): {high_confidence_count}/{total_judges} ({high_confidence_count/total_judges*100:.1f}%)")
                print(f"   External validated: {external_validated_count}/{total_judges} ({external_validated_count/total_judges*100:.1f}%)")
                print(f"   Career progression detected: {career_progression_count}/{total_judges} ({career_progression_count/total_judges*100:.1f}%)")
                
                # Success criteria
                success = (
                    total_judges >= 3 and  # At least 3 judges found
                    full_names_count > 0 and  # At least some full names
                    high_confidence_count >= total_judges * 0.5  # At least 50% high confidence
                )
                
                print(f"\n🎯 ENHANCED JUDGE FEATURES ASSESSMENT:")
                print(f"   Judge Extraction: {'✅' if total_judges >= 3 else '❌'} ({total_judges} judges)")
                print(f"   Name Quality: {'✅' if full_names_count > 0 else '❌'} ({full_names_count} full names)")
                print(f"   Confidence Scoring: {'✅' if high_confidence_count >= total_judges * 0.5 else '❌'} ({high_confidence_count/total_judges*100:.1f}% high)")
                print(f"   Enhanced Features: {'✅' if external_validated_count > 0 or career_progression_count > 0 else '❌'}")
                print(f"   Overall Success: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 ENHANCED JUDGE FEATURES: SUCCESS!")
                    print(f"✅ All enhanced judge features working with 70s-80s CAP data")
                    print(f"✅ Full names extracted when available")
                    print(f"✅ Court assignment and geography working")
                    print(f"✅ Confidence scoring operational")
                    print(f"✅ Enhanced features providing value")
                else:
                    print(f"\n❌ ENHANCED JUDGE FEATURES: NEEDS IMPROVEMENT!")
                    print(f"❌ Some enhanced features not working optimally")
                
                return success
                
        except Exception as e:
            print(f"❌ Error analyzing enhanced judge features: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data - DISABLED FOR DEBUGGING"""

        print(f"\n🧹 SKIPPING CLEANUP FOR DEBUGGING")
        print("=" * 60)
        print("   ⚠️ Cleanup disabled to investigate Neo4j persistence")
        print("   ⚠️ Test data will remain in database for analysis")

        # Temporarily disable cleanup to debug Neo4j persistence
        return

        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")

            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c',
                          batch_id=self.test_batch_id)

                # Clean orphaned nodes
                session.run('MATCH (cited:CitedCase) WHERE NOT EXISTS { MATCH (cited)<-[]-() } DELETE cited')
                session.run('MATCH (j:Judge) WHERE NOT EXISTS { MATCH (j)-[]-() } DELETE j')

            print("   ✅ Cleaned Neo4j test data")

        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run enhanced judge features test with 70s-80s CAP data"""
    
    test = CAPEnhancedJudgeTest()
    
    try:
        success = await test.test_enhanced_judge_features()
        return success
    finally:
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
