#!/usr/bin/env python3
"""
Debug Judge Extraction - Test judge pattern matching
"""

import re
from real_neo4j_client import RealNeo4jClient

def test_judge_patterns():
    """Test judge extraction patterns"""
    
    print("🔍 TESTING JUDGE EXTRACTION PATTERNS")
    print("=" * 60)
    
    # Test text with clear judge patterns
    test_text = '''
    BROWN v. BOARD OF EDUCATION OF TOPEKA
    
    Mr. Chief Justice <PERSON> delivered the opinion of the Court.
    
    These cases come to us from the States of Kansas, South Carolina, Virginia, and Delaware.
    
    Justice BLACK, with whom Justice <PERSON> and Justice STEWART join, dissenting.
    
    SMITH, Circuit Judge:
    
    This case involves the interpretation of federal sentencing guidelines.
    
    <PERSON><PERSON><PERSON>, Circuit Judge, concurring:
    
    I agree with the majority's conclusion.
    
    <PERSON><PERSON><PERSON><PERSON><PERSON>, District Judge, dissenting:
    
    I respectfully dissent.
    '''
    
    # Judge patterns
    judge_patterns = [
        r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'Chief\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'Circuit\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'District\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'([A-Z][A-Z]+),\s+(?:Justice|Judge)',  # WARREN, Justice
        r'Mr\.\s+(?:Justice|Judge)\s+([A-Z][A-Z]+)',  # Mr. Justice WARREN
        r'([A-Z][a-z]+),\s+J\.',  # Warren, J.
        r'([A-Z][a-z]+),\s+(?:Circuit|District)\s+Judge',  # Smith, Circuit Judge
    ]
    
    print(f"📄 Test text length: {len(test_text)} characters")
    print(f"📊 Testing {len(judge_patterns)} patterns")
    
    found_judges = []
    
    for i, pattern in enumerate(judge_patterns, 1):
        print(f"\n🔍 Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, test_text, re.IGNORECASE)
        pattern_matches = []
        
        for match in matches:
            judge_name = match.group(1).strip()
            pattern_matches.append(judge_name)
            found_judges.append(judge_name)
        
        print(f"   Matches: {pattern_matches}")
    
    print(f"\n📊 PATTERN TESTING SUMMARY:")
    print(f"   Total matches: {len(found_judges)}")
    print(f"   Unique judges: {list(set(found_judges))}")
    
    return len(found_judges) > 0


def test_neo4j_text_storage():
    """Test if text is being stored in Neo4j"""
    
    print(f"\n💾 TESTING NEO4J TEXT STORAGE")
    print("=" * 60)
    
    neo4j_client = RealNeo4jClient()
    
    try:
        with neo4j_client.driver.session() as session:
            # Get recent cases with text
            result = session.run('''
                MATCH (c:Case)
                WHERE c.text IS NOT NULL AND c.text <> ""
                RETURN c.id as case_id, c.case_name as case_name,
                       size(c.text) as text_length,
                       substring(c.text, 0, 200) as text_sample
                ORDER BY c.created_at DESC
                LIMIT 5
            ''')
            
            cases_with_text = list(result)
            
            print(f"📊 Cases with text: {len(cases_with_text)}")
            
            for i, record in enumerate(cases_with_text, 1):
                print(f"\n   {i}. {record['case_id']}")
                print(f"      Name: {record['case_name']}")
                print(f"      Text length: {record['text_length']:,} characters")
                print(f"      Sample: {record['text_sample'][:100]}...")
            
            # Get recent cases without text
            result = session.run('''
                MATCH (c:Case)
                WHERE c.text IS NULL OR c.text = ""
                RETURN count(c) as count
            ''')
            
            cases_without_text = result.single()['count']
            print(f"\n📊 Cases without text: {cases_without_text}")
            
            return len(cases_with_text) > 0
            
    except Exception as e:
        print(f"❌ Error testing Neo4j text storage: {e}")
        return False
    
    finally:
        neo4j_client.close()


def test_judge_extraction_on_real_case():
    """Test judge extraction on a real case from Neo4j"""
    
    print(f"\n🔍 TESTING JUDGE EXTRACTION ON REAL CASE")
    print("=" * 60)
    
    neo4j_client = RealNeo4jClient()
    
    try:
        with neo4j_client.driver.session() as session:
            # Get a case with text
            result = session.run('''
                MATCH (c:Case)
                WHERE c.text IS NOT NULL AND c.text <> ""
                AND size(c.text) > 1000
                RETURN c.id as case_id, c.case_name as case_name, c.text as text
                LIMIT 1
            ''')
            
            record = result.single()
            
            if not record:
                print("   ❌ No cases with text found")
                return False
            
            case_id = record['case_id']
            case_name = record['case_name']
            text = record['text']
            
            print(f"📄 Testing case: {case_id}")
            print(f"   Name: {case_name}")
            print(f"   Text length: {len(text):,} characters")
            
            # Test judge extraction patterns
            judge_patterns = [
                r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'Chief\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'([A-Z][A-Z]+),\s+(?:Justice|Judge)',
                r'Mr\.\s+(?:Justice|Judge)\s+([A-Z][A-Z]+)',
                r'([A-Z][a-z]+),\s+(?:Circuit|District)\s+Judge',
            ]
            
            found_judges = []
            search_text = text[:3000]  # First 3000 chars
            
            for pattern in judge_patterns:
                matches = re.finditer(pattern, search_text, re.IGNORECASE)
                for match in matches:
                    judge_name = match.group(1).strip()
                    if len(judge_name) > 2 and len(judge_name) < 50:
                        found_judges.append(judge_name)
            
            print(f"\n📊 Judge extraction results:")
            print(f"   Judges found: {len(found_judges)}")
            print(f"   Judge names: {list(set(found_judges))}")
            
            # Show text sample where judges might be
            print(f"\n📄 Text sample (first 500 chars):")
            print(f"   {text[:500]}...")
            
            return len(found_judges) > 0
            
    except Exception as e:
        print(f"❌ Error testing judge extraction: {e}")
        return False
    
    finally:
        neo4j_client.close()


def main():
    """Run all debug tests"""
    
    print("🐛 JUDGE EXTRACTION DEBUG TESTS")
    print("=" * 80)
    
    # Test 1: Pattern matching
    pattern_success = test_judge_patterns()
    
    # Test 2: Neo4j text storage
    storage_success = test_neo4j_text_storage()
    
    # Test 3: Real case extraction
    extraction_success = test_judge_extraction_on_real_case()
    
    print(f"\n📊 DEBUG TEST SUMMARY:")
    print(f"   Pattern matching: {'✅' if pattern_success else '❌'}")
    print(f"   Neo4j text storage: {'✅' if storage_success else '❌'}")
    print(f"   Real case extraction: {'✅' if extraction_success else '❌'}")
    
    if all([pattern_success, storage_success, extraction_success]):
        print(f"\n✅ All debug tests passed - judge extraction should work!")
        return 0
    else:
        print(f"\n❌ Some debug tests failed - judge extraction needs fixes")
        return 1


if __name__ == "__main__":
    exit(main())
