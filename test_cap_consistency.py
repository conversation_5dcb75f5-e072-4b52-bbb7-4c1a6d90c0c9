#!/usr/bin/env python3
"""
Test CAP data processing for consistency before full-scale processing.
Process a small sample to verify all databases stay in sync.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.append('src')

from processing.caselaw_access_processor import CaselawAccessProcessor

async def test_cap_small_sample():
    """Test CAP processing with a small sample to verify consistency."""
    
    print("🧪 TESTING CAP DATA PROCESSING CONSISTENCY")
    print("=" * 60)
    
    try:
        # Initialize processor
        processor = CaselawAccessProcessor()
        
        # Check what CAP data we have available
        cap_data_dir = Path("data/caselaw_access_project")
        if not cap_data_dir.exists():
            print(f"❌ CAP data directory not found: {cap_data_dir}")
            return False
        
        # Find available jurisdictions
        jurisdictions = [d for d in cap_data_dir.iterdir() if d.is_dir()]
        print(f"📂 Available CAP jurisdictions: {len(jurisdictions)}")
        for jurisdiction in jurisdictions[:5]:  # Show first 5
            print(f"   {jurisdiction.name}")
        
        if not jurisdictions:
            print("❌ No CAP jurisdiction directories found")
            return False
        
        # Test with first jurisdiction, small sample
        test_jurisdiction = jurisdictions[0]
        print(f"\n🔍 Testing with jurisdiction: {test_jurisdiction.name}")
        
        # Find HTML files in this jurisdiction
        html_files = list(test_jurisdiction.rglob("*.html"))
        print(f"📄 Found {len(html_files)} HTML files")
        
        if len(html_files) == 0:
            print("❌ No HTML files found in test jurisdiction")
            return False
        
        # Test with first 5 files only
        test_files = html_files[:5]
        print(f"🧪 Testing with {len(test_files)} files")
        
        # Get baseline database counts
        print(f"\n📊 BASELINE DATABASE COUNTS:")
        baseline_counts = await get_database_counts()
        print(f"   Supabase: {baseline_counts['supabase']}")
        print(f"   Neo4j: {baseline_counts['neo4j']}")
        print(f"   Pinecone: {baseline_counts['pinecone']}")
        
        # Process test files
        print(f"\n🔄 PROCESSING TEST FILES:")
        processed_count = 0
        error_count = 0
        
        for i, html_file in enumerate(test_files):
            try:
                print(f"   Processing {i+1}/{len(test_files)}: {html_file.name}")
                
                # Process single file
                success = await processor.process_file(str(html_file))
                
                if success:
                    processed_count += 1
                    print(f"     ✅ Success")
                else:
                    error_count += 1
                    print(f"     ⚠️  Skipped (duplicate or error)")
                
            except Exception as e:
                error_count += 1
                print(f"     ❌ Error: {e}")
        
        # Get final database counts
        print(f"\n📊 FINAL DATABASE COUNTS:")
        final_counts = await get_database_counts()
        print(f"   Supabase: {final_counts['supabase']} (+{final_counts['supabase'] - baseline_counts['supabase']})")
        print(f"   Neo4j: {final_counts['neo4j']} (+{final_counts['neo4j'] - baseline_counts['neo4j']})")
        print(f"   Pinecone: {final_counts['pinecone']} (+{final_counts['pinecone'] - baseline_counts['pinecone']})")
        
        # Verify consistency
        supabase_increase = final_counts['supabase'] - baseline_counts['supabase']
        neo4j_increase = final_counts['neo4j'] - baseline_counts['neo4j']
        
        print(f"\n🎯 CONSISTENCY CHECK:")
        print(f"   Files processed: {processed_count}")
        print(f"   Supabase increase: {supabase_increase}")
        print(f"   Neo4j increase: {neo4j_increase}")
        
        if supabase_increase == neo4j_increase == processed_count:
            print(f"   ✅ PERFECT CONSISTENCY: All databases in sync!")
            consistency_rate = 100.0
        elif supabase_increase == neo4j_increase:
            print(f"   ✅ GOOD CONSISTENCY: Supabase and Neo4j in sync")
            consistency_rate = (min(supabase_increase, neo4j_increase) / max(supabase_increase, neo4j_increase, 1)) * 100
        else:
            print(f"   ❌ INCONSISTENCY DETECTED: Databases out of sync")
            consistency_rate = 0.0
        
        print(f"   📊 Consistency rate: {consistency_rate:.1f}%")
        
        return consistency_rate >= 95.0
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def get_database_counts():
    """Get current counts from all databases."""
    
    counts = {'supabase': 0, 'neo4j': 0, 'pinecone': 0}
    
    try:
        # Supabase count
        from processing.storage.supabase_connector import SupabaseConnector
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        counts['supabase'] = response.count
        
        # Neo4j count
        from processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            counts['neo4j'] = result.single()['count']
        neo4j.close()
        
        # Pinecone count
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        counts['pinecone'] = stats.total_vector_count
        
    except Exception as e:
        print(f"⚠️  Error getting database counts: {e}")
    
    return counts

async def main():
    """Main test function."""
    
    print("🧪 CAP DATA CONSISTENCY TEST")
    print("=" * 70)
    print("Testing CAP processing with small sample before full-scale processing")
    print("=" * 70)
    
    success = await test_cap_small_sample()
    
    print(f"\n🎯 TEST RESULTS:")
    if success:
        print(f"   ✅ CAP PROCESSING READY FOR SCALE")
        print(f"   ✅ Consistency verified with test sample")
        print(f"   🚀 Safe to process full CAP dataset")
        
        print(f"\n📋 RECOMMENDED NEXT STEPS:")
        print(f"   1. Process full CAP dataset with confidence")
        print(f"   2. Expect perfect consistency across all databases")
        print(f"   3. Monitor processing for any issues")
    else:
        print(f"   ❌ CAP PROCESSING NEEDS FIXES")
        print(f"   ❌ Consistency issues detected")
        print(f"   🔧 Fix issues before full-scale processing")
        
        print(f"\n📋 RECOMMENDED ACTIONS:")
        print(f"   1. Debug consistency issues")
        print(f"   2. Fix CAP processing pipeline")
        print(f"   3. Re-test before scaling")

if __name__ == "__main__":
    asyncio.run(main())
