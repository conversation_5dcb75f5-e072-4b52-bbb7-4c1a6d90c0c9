#!/usr/bin/env python3
"""
Small Batch Processing Test

This script tests the enhanced processing system with a small batch of cases
from the Caselaw Access Project to validate the full pipeline before
processing the complete 6.7M dataset.
"""

import os
import json
import gzip
import logging
import asyncio
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
import sys
sys.path.append('/Users/<USER>/Documents/GitHub/texas-laws-personalinjury')

from src.processing.caselaw_access_processor import CaselawAccessProcessor

class SmallBatchTester:
    """Test the processing system with a small batch of real data"""
    
    def __init__(self, test_size: int = 10):
        self.test_size = test_size
        self.processor = None
        self.results = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'duplicates': 0,
            'practice_areas': {},
            'jurisdictions': {},
            'errors': []
        }
        
    def initialize_processor(self):
        """Initialize the enhanced processor"""
        try:
            self.processor = CaselawAccessProcessor()
            logger.info("✅ Enhanced processor initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize processor: {e}")
            return False
    
    def get_sample_cases(self, max_cases: int = 10) -> List[Dict[str, Any]]:
        """Extract a small sample of cases from the first data file"""
        
        data_dir = Path("data/caselaw_access_project")
        if not data_dir.exists():
            logger.error(f"❌ Data directory not found: {data_dir}")
            return []
        
        # Get first data file
        data_files = list(data_dir.glob("cap_*.jsonl.gz"))
        if not data_files:
            logger.error("❌ No data files found in data directory")
            return []
        
        data_files.sort()
        first_file = data_files[0]
        
        logger.info(f"📄 Reading sample cases from: {first_file}")
        
        sample_cases = []
        try:
            with gzip.open(first_file, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if len(sample_cases) >= max_cases:
                        break
                    
                    try:
                        case_data = json.loads(line.strip())
                        sample_cases.append(case_data)
                        
                        # Log progress
                        if line_num % 100 == 0:
                            logger.info(f"  📝 Read {line_num} lines, collected {len(sample_cases)} cases")
                            
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ Invalid JSON on line {line_num}: {e}")
                        continue
                    except Exception as e:
                        logger.error(f"❌ Error reading line {line_num}: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"❌ Error reading file {first_file}: {e}")
            return []
        
        logger.info(f"✅ Successfully extracted {len(sample_cases)} sample cases")
        return sample_cases
    
    def analyze_sample_cases(self, sample_cases: List[Dict[str, Any]]):
        """Analyze the sample cases to understand the data"""
        
        logger.info("🔍 Analyzing sample cases...")
        
        jurisdictions = set()
        case_types = set()
        years = set()
        
        for case in sample_cases:
            # Extract jurisdiction
            if 'jurisdiction' in case:
                jurisdictions.add(case['jurisdiction'])
            
            # Extract case type/court info
            if 'court' in case:
                case_types.add(case['court'])
            
            # Extract year
            if 'decision_date' in case:
                try:
                    year = case['decision_date'][:4]
                    years.add(year)
                except:
                    pass
        
        logger.info(f"📊 Sample Analysis:")
        logger.info(f"  - Jurisdictions: {sorted(jurisdictions)}")
        logger.info(f"  - Courts: {len(case_types)} unique courts")
        logger.info(f"  - Years: {sorted(years)}")
        
        # Show first case structure
        if sample_cases:
            first_case = sample_cases[0]
            logger.info(f"📋 First case structure:")
            for key in sorted(first_case.keys()):
                value = first_case[key]
                if isinstance(value, str) and len(value) > 100:
                    value = value[:100] + "..."
                logger.info(f"  - {key}: {type(value).__name__} = {value}")
    
    async def process_sample_batch(self, sample_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process the sample batch using the enhanced processor"""
        
        logger.info(f"🚀 Starting batch processing of {len(sample_cases)} cases...")
        
        start_time = datetime.now()
        
        for i, case_data in enumerate(sample_cases, 1):
            logger.info(f"📝 Processing case {i}/{len(sample_cases)}...")
            
            try:
                # Convert to processor format
                doc = self.processor.normalize_document(case_data)
                
                # Classify practice area
                practice_area = self.processor._classify_practice_area(doc)
                
                # Track practice areas
                if practice_area not in self.results['practice_areas']:
                    self.results['practice_areas'][practice_area] = 0
                self.results['practice_areas'][practice_area] += 1
                
                # Track jurisdictions
                jurisdiction = doc.jurisdiction or 'unknown'
                if jurisdiction not in self.results['jurisdictions']:
                    self.results['jurisdictions'][jurisdiction] = 0
                self.results['jurisdictions'][jurisdiction] += 1
                
                # Test document validation
                is_valid = self.processor._validate_document(doc)
                if not is_valid:
                    logger.warning(f"⚠️ Document {doc.id} failed validation")
                    self.results['failed'] += 1
                    continue
                
                # Test namespace generation
                namespace = self.processor._determine_vector_namespace(doc, practice_area)
                
                logger.info(f"  ✅ Case {i}: {doc.case_name[:50]}...")
                logger.info(f"    - Jurisdiction: {jurisdiction}")
                logger.info(f"    - Practice Area: {practice_area}")
                logger.info(f"    - Namespace: {namespace}")
                logger.info(f"    - Word Count: {doc.word_count}")
                
                # Simulate processing success
                self.results['successful'] += 1
                
            except Exception as e:
                logger.error(f"❌ Error processing case {i}: {e}")
                self.results['errors'].append(f"Case {i}: {str(e)}")
                self.results['failed'] += 1
            
            self.results['total_processed'] += 1
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"⏱️ Processing completed in {duration:.2f} seconds")
        
        return {
            'duration_seconds': duration,
            'cases_per_second': len(sample_cases) / duration if duration > 0 else 0,
            'estimated_total_time_hours': (6700000 * duration / len(sample_cases)) / 3600 if len(sample_cases) > 0 else 0
        }
    
    def generate_report(self, performance_stats: Dict[str, Any]):
        """Generate a comprehensive test report"""
        
        logger.info("\n" + "="*60)
        logger.info("🎯 SMALL BATCH PROCESSING TEST REPORT")
        logger.info("="*60)
        
        # Processing Statistics
        logger.info(f"\n📊 Processing Statistics:")
        logger.info(f"  - Total Cases: {self.results['total_processed']}")
        logger.info(f"  - Successful: {self.results['successful']}")
        logger.info(f"  - Failed: {self.results['failed']}")
        logger.info(f"  - Success Rate: {(self.results['successful'] / self.results['total_processed'] * 100):.1f}%")
        
        # Performance Metrics
        logger.info(f"\n⚡ Performance Metrics:")
        logger.info(f"  - Duration: {performance_stats['duration_seconds']:.2f} seconds")
        logger.info(f"  - Processing Rate: {performance_stats['cases_per_second']:.2f} cases/second")
        logger.info(f"  - Estimated Total Time: {performance_stats['estimated_total_time_hours']:.1f} hours for 6.7M cases")
        
        # Practice Area Distribution
        logger.info(f"\n🏛️ Practice Area Distribution:")
        for area, count in sorted(self.results['practice_areas'].items()):
            percentage = (count / self.results['total_processed'] * 100)
            logger.info(f"  - {area}: {count} cases ({percentage:.1f}%)")
        
        # Jurisdiction Distribution
        logger.info(f"\n🌐 Jurisdiction Distribution:")
        for jurisdiction, count in sorted(self.results['jurisdictions'].items()):
            percentage = (count / self.results['total_processed'] * 100)
            logger.info(f"  - {jurisdiction}: {count} cases ({percentage:.1f}%)")
        
        # Error Summary
        if self.results['errors']:
            logger.info(f"\n❌ Errors ({len(self.results['errors'])}):")
            for error in self.results['errors'][:5]:  # Show first 5 errors
                logger.info(f"  - {error}")
            if len(self.results['errors']) > 5:
                logger.info(f"  ... and {len(self.results['errors']) - 5} more errors")
        
        # Recommendations
        logger.info(f"\n💡 Recommendations:")
        success_rate = (self.results['successful'] / self.results['total_processed'] * 100)
        
        if success_rate >= 95:
            logger.info("  ✅ Excellent success rate! System is ready for full processing.")
        elif success_rate >= 90:
            logger.info("  ⚠️ Good success rate, but monitor errors during full processing.")
        else:
            logger.info("  ❌ Low success rate. Review errors before full processing.")
        
        if performance_stats['estimated_total_time_hours'] > 72:
            logger.info("  ⚠️ Processing may take >72 hours. Consider optimizing batch size.")
        
        logger.info("="*60)
    
    async def run_test(self):
        """Run the complete small batch test"""
        
        logger.info("🧪 Starting Small Batch Processing Test...")
        
        # 1. Initialize processor
        if not self.initialize_processor():
            return False
        
        # 2. Get sample cases
        sample_cases = self.get_sample_cases(self.test_size)
        if not sample_cases:
            return False
        
        # 3. Analyze sample
        self.analyze_sample_cases(sample_cases)
        
        # 4. Process sample batch
        performance_stats = await self.process_sample_batch(sample_cases)
        
        # 5. Generate report
        self.generate_report(performance_stats)
        
        # 6. Determine if ready for full processing
        success_rate = (self.results['successful'] / self.results['total_processed'] * 100)
        
        if success_rate >= 90:
            logger.info("🎉 Small batch test PASSED! System is ready for full processing.")
            return True
        else:
            logger.error("❌ Small batch test FAILED! Fix issues before full processing.")
            return False

async def main():
    """Main test execution"""
    
    # Test with 10 cases first
    tester = SmallBatchTester(test_size=10)
    
    try:
        success = await tester.run_test()
        return success
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)