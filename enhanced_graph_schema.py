#!/usr/bin/env python3
"""
Enhanced Neo4j Graph Schema for Legal Case Relationships

This module defines and implements a comprehensive graph schema that includes:
1. Comprehensive Judge modeling with career tracking
2. Rich case relationship types (cites, follows, distinguishes, overrules, etc.)
3. Enhanced case-judge relationships (authored, joined, dissented)
4. Temporal relationship tracking
5. Authority and influence modeling
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from src.processing.storage.neo4j_connector import Neo4jConnector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class JudgeInfo:
    """Comprehensive judge information."""
    name: str
    id: Optional[str] = None
    court_id: Optional[str] = None
    jurisdiction: Optional[str] = None
    appointment_date: Optional[str] = None
    tenure_start: Optional[str] = None
    tenure_end: Optional[str] = None
    appointing_authority: Optional[str] = None
    judicial_philosophy: Optional[str] = None
    specializations: List[str] = None
    active: bool = True
    
    def __post_init__(self):
        if self.specializations is None:
            self.specializations = []


@dataclass
class CaseRelationship:
    """Rich case relationship information."""
    citing_case_id: str
    cited_case_id: str
    relationship_type: str  # cites, follows, distinguishes, overrules, etc.
    citation_text: Optional[str] = None
    context: Optional[str] = None
    strength: str = "medium"  # weak, medium, strong
    temporal_relationship: Optional[str] = None  # precedent, contemporary, subsequent
    jurisdictional_weight: float = 1.0
    confidence_score: float = 0.8


class EnhancedGraphSchema:
    """Enhanced graph schema manager for legal case relationships."""
    
    def __init__(self):
        """Initialize enhanced graph schema."""
        self.neo4j = Neo4jConnector()
        
        # Define comprehensive relationship types
        self.case_relationship_types = {
            'CITES': 'General citation relationship',
            'FOLLOWS': 'Case follows precedent established by cited case',
            'DISTINGUISHES': 'Case distinguishes itself from cited case',
            'OVERRULES': 'Case explicitly overrules cited case',
            'REVERSES': 'Case reverses lower court decision',
            'AFFIRMS': 'Case affirms lower court decision',
            'REMANDS': 'Case remands to lower court',
            'CRITICIZES': 'Case criticizes reasoning in cited case',
            'QUESTIONS': 'Case questions validity of cited case',
            'RELIES_ON': 'Case heavily relies on cited case reasoning',
            'ANALOGIZES': 'Case draws analogy to cited case',
            'SUPERSEDES': 'Case supersedes cited case through new legislation/ruling'
        }
        
        # Define judge-case relationship types
        self.judge_case_relationships = {
            'AUTHORED': 'Judge authored the majority opinion',
            'JOINED': 'Judge joined the majority opinion',
            'CONCURRED': 'Judge wrote or joined concurring opinion',
            'DISSENTED': 'Judge wrote or joined dissenting opinion',
            'CONCURRED_IN_PART': 'Judge concurred in part, dissented in part',
            'PRESIDED': 'Judge presided over the case (chief judge)',
            'PARTICIPATED': 'Judge participated in decision but role unclear'
        }
        
        logger.info("✅ Enhanced Graph Schema initialized")
        logger.info(f"   Case relationship types: {len(self.case_relationship_types)}")
        logger.info(f"   Judge-case relationships: {len(self.judge_case_relationships)}")
    
    def initialize_enhanced_schema(self) -> bool:
        """Initialize the enhanced Neo4j schema with all constraints and indexes."""
        
        logger.info("🏗️ Initializing enhanced Neo4j schema...")
        
        try:
            with self.neo4j.driver.session() as session:
                
                # 1. Enhanced Case node constraints and indexes
                logger.info("   Creating Case node schema...")
                
                session.run("""
                    CREATE CONSTRAINT case_id_unique IF NOT EXISTS
                    FOR (c:Case) REQUIRE c.id IS UNIQUE
                """)
                
                session.run("""
                    CREATE INDEX case_jurisdiction_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.jurisdiction)
                """)
                
                session.run("""
                    CREATE INDEX case_practice_area_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.practice_area)
                """)
                
                session.run("""
                    CREATE INDEX case_date_filed_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.date_filed)
                """)
                
                session.run("""
                    CREATE INDEX case_court_idx IF NOT EXISTS
                    FOR (c:Case) ON (c.court_id)
                """)
                
                # 2. Enhanced Judge node constraints and indexes
                logger.info("   Creating Judge node schema...")
                
                session.run("""
                    CREATE CONSTRAINT judge_id_unique IF NOT EXISTS
                    FOR (j:Judge) REQUIRE j.id IS UNIQUE
                """)
                
                session.run("""
                    CREATE INDEX judge_name_idx IF NOT EXISTS
                    FOR (j:Judge) ON (j.name)
                """)
                
                session.run("""
                    CREATE INDEX judge_court_idx IF NOT EXISTS
                    FOR (j:Judge) ON (j.court_id)
                """)
                
                session.run("""
                    CREATE INDEX judge_jurisdiction_idx IF NOT EXISTS
                    FOR (j:Judge) ON (j.jurisdiction)
                """)
                
                session.run("""
                    CREATE INDEX judge_active_idx IF NOT EXISTS
                    FOR (j:Judge) ON (j.active)
                """)
                
                # 3. Court node enhancements
                logger.info("   Creating Court node schema...")
                
                session.run("""
                    CREATE CONSTRAINT court_id_unique IF NOT EXISTS
                    FOR (c:Court) REQUIRE c.id IS UNIQUE
                """)
                
                session.run("""
                    CREATE INDEX court_jurisdiction_idx IF NOT EXISTS
                    FOR (c:Court) ON (c.jurisdiction)
                """)
                
                session.run("""
                    CREATE INDEX court_level_idx IF NOT EXISTS
                    FOR (c:Court) ON (c.level)
                """)
                
                # 4. Practice Area nodes
                logger.info("   Creating Practice Area schema...")
                
                session.run("""
                    CREATE CONSTRAINT practice_area_name_unique IF NOT EXISTS
                    FOR (pa:PracticeArea) REQUIRE pa.name IS UNIQUE
                """)
                
                # 5. Citation relationship indexes
                logger.info("   Creating relationship indexes...")
                
                session.run("""
                    CREATE INDEX case_citation_strength_idx IF NOT EXISTS
                    FOR ()-[r:CITES]-() ON (r.strength)
                """)
                
                session.run("""
                    CREATE INDEX case_citation_confidence_idx IF NOT EXISTS
                    FOR ()-[r:CITES]-() ON (r.confidence_score)
                """)
                
                logger.info("✅ Enhanced Neo4j schema initialized successfully")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error initializing enhanced schema: {e}")
            return False
    
    def create_or_update_judge(self, judge_info: JudgeInfo) -> bool:
        """Create or update a judge node with comprehensive information."""
        
        try:
            with self.neo4j.driver.session() as session:
                
                # Generate judge ID if not provided
                judge_id = judge_info.id or f"judge_{judge_info.name.lower().replace(' ', '_')}"
                
                query = """
                MERGE (j:Judge {id: $judge_id})
                ON CREATE SET 
                    j.name = $name,
                    j.court_id = $court_id,
                    j.jurisdiction = $jurisdiction,
                    j.appointment_date = $appointment_date,
                    j.tenure_start = $tenure_start,
                    j.tenure_end = $tenure_end,
                    j.appointing_authority = $appointing_authority,
                    j.judicial_philosophy = $judicial_philosophy,
                    j.specializations = $specializations,
                    j.active = $active,
                    j.created_at = datetime()
                ON MATCH SET 
                    j.name = $name,
                    j.court_id = $court_id,
                    j.jurisdiction = $jurisdiction,
                    j.appointment_date = $appointment_date,
                    j.tenure_start = $tenure_start,
                    j.tenure_end = $tenure_end,
                    j.appointing_authority = $appointing_authority,
                    j.judicial_philosophy = $judicial_philosophy,
                    j.specializations = $specializations,
                    j.active = $active,
                    j.updated_at = datetime()
                RETURN j.id
                """
                
                result = session.run(query, {
                    'judge_id': judge_id,
                    'name': judge_info.name,
                    'court_id': judge_info.court_id,
                    'jurisdiction': judge_info.jurisdiction,
                    'appointment_date': judge_info.appointment_date,
                    'tenure_start': judge_info.tenure_start,
                    'tenure_end': judge_info.tenure_end,
                    'appointing_authority': judge_info.appointing_authority,
                    'judicial_philosophy': judge_info.judicial_philosophy,
                    'specializations': judge_info.specializations,
                    'active': judge_info.active
                })
                
                if result.single():
                    # Link judge to court if court_id provided
                    if judge_info.court_id:
                        session.run("""
                            MATCH (j:Judge {id: $judge_id})
                            MATCH (c:Court {id: $court_id})
                            MERGE (j)-[:SERVES_ON]->(c)
                        """, judge_id=judge_id, court_id=judge_info.court_id)
                    
                    logger.info(f"✅ Created/updated judge: {judge_info.name}")
                    return True
                
        except Exception as e:
            logger.error(f"❌ Error creating/updating judge {judge_info.name}: {e}")
            return False
        
        return False
    
    def create_judge_case_relationship(self, judge_id: str, case_id: str, 
                                     relationship_type: str, metadata: Optional[Dict] = None) -> bool:
        """Create a relationship between a judge and a case."""
        
        if relationship_type not in self.judge_case_relationships:
            logger.warning(f"Unknown judge-case relationship type: {relationship_type}")
            return False
        
        try:
            with self.neo4j.driver.session() as session:
                
                # Create the relationship with metadata
                query = f"""
                MATCH (j:Judge {{id: $judge_id}})
                MATCH (c:Case {{id: $case_id}})
                MERGE (j)-[r:{relationship_type}]->(c)
                ON CREATE SET 
                    r.created_at = datetime()
                """
                
                # Add metadata properties if provided
                if metadata:
                    for key, value in metadata.items():
                        query += f", r.{key} = ${key}"
                
                query += " RETURN r"
                
                params = {
                    'judge_id': judge_id,
                    'case_id': case_id
                }
                
                if metadata:
                    params.update(metadata)
                
                result = session.run(query, params)
                
                if result.single():
                    logger.info(f"✅ Created {relationship_type} relationship: {judge_id} -> {case_id}")
                    return True
                
        except Exception as e:
            logger.error(f"❌ Error creating judge-case relationship: {e}")
            return False
        
        return False
    
    def create_enhanced_case_relationship(self, relationship: CaseRelationship) -> bool:
        """Create an enhanced relationship between two cases."""
        
        if relationship.relationship_type not in self.case_relationship_types:
            logger.warning(f"Unknown case relationship type: {relationship.relationship_type}")
            return False
        
        try:
            with self.neo4j.driver.session() as session:
                
                query = f"""
                MATCH (c1:Case {{id: $citing_case_id}})
                MATCH (c2:Case {{id: $cited_case_id}})
                MERGE (c1)-[r:{relationship.relationship_type}]->(c2)
                ON CREATE SET 
                    r.citation_text = $citation_text,
                    r.context = $context,
                    r.strength = $strength,
                    r.temporal_relationship = $temporal_relationship,
                    r.jurisdictional_weight = $jurisdictional_weight,
                    r.confidence_score = $confidence_score,
                    r.created_at = datetime()
                ON MATCH SET 
                    r.citation_text = $citation_text,
                    r.context = $context,
                    r.strength = $strength,
                    r.temporal_relationship = $temporal_relationship,
                    r.jurisdictional_weight = $jurisdictional_weight,
                    r.confidence_score = $confidence_score,
                    r.updated_at = datetime()
                RETURN r
                """
                
                result = session.run(query, {
                    'citing_case_id': relationship.citing_case_id,
                    'cited_case_id': relationship.cited_case_id,
                    'citation_text': relationship.citation_text,
                    'context': relationship.context,
                    'strength': relationship.strength,
                    'temporal_relationship': relationship.temporal_relationship,
                    'jurisdictional_weight': relationship.jurisdictional_weight,
                    'confidence_score': relationship.confidence_score
                })
                
                if result.single():
                    logger.info(f"✅ Created {relationship.relationship_type}: {relationship.citing_case_id} -> {relationship.cited_case_id}")
                    return True
                
        except Exception as e:
            logger.error(f"❌ Error creating case relationship: {e}")
            return False
        
        return False
    
    def extract_and_store_judges_from_case(self, case_data: Dict) -> List[str]:
        """Extract judge information from case data and store in graph."""
        
        judge_ids = []
        
        try:
            # Extract judges from various possible fields
            judges_data = []
            
            # From 'judges' field (list of names or objects)
            if 'judges' in case_data and case_data['judges']:
                judges_list = case_data['judges']
                if isinstance(judges_list, list):
                    for judge in judges_list:
                        if isinstance(judge, str):
                            judges_data.append({'name': judge})
                        elif isinstance(judge, dict):
                            judges_data.append(judge)
            
            # From 'author' field (opinion author)
            if 'author' in case_data and case_data['author']:
                author = case_data['author']
                if isinstance(author, str):
                    judges_data.append({'name': author, 'role': 'author'})
                elif isinstance(author, dict):
                    author['role'] = 'author'
                    judges_data.append(author)
            
            # From 'panel' field (panel of judges)
            if 'panel' in case_data and case_data['panel']:
                panel = case_data['panel']
                if isinstance(panel, list):
                    for judge in panel:
                        if isinstance(judge, str):
                            judges_data.append({'name': judge, 'role': 'panel'})
                        elif isinstance(judge, dict):
                            judge['role'] = 'panel'
                            judges_data.append(judge)
            
            # Create judge nodes and relationships
            for judge_data in judges_data:
                judge_name = judge_data.get('name', '').strip()
                if not judge_name:
                    continue
                
                # Create comprehensive judge info
                judge_info = JudgeInfo(
                    name=judge_name,
                    court_id=case_data.get('court_id'),
                    jurisdiction=case_data.get('jurisdiction'),
                    active=True
                )
                
                # Create or update judge
                if self.create_or_update_judge(judge_info):
                    judge_id = f"judge_{judge_name.lower().replace(' ', '_')}"
                    judge_ids.append(judge_id)
                    
                    # Determine relationship type based on role
                    role = judge_data.get('role', 'participated')
                    relationship_type = 'AUTHORED' if role == 'author' else 'PARTICIPATED'
                    
                    # Create judge-case relationship
                    self.create_judge_case_relationship(
                        judge_id=judge_id,
                        case_id=case_data.get('id'),
                        relationship_type=relationship_type,
                        metadata={'role': role}
                    )
            
            logger.info(f"✅ Processed {len(judge_ids)} judges for case {case_data.get('id')}")
            
        except Exception as e:
            logger.error(f"❌ Error extracting judges from case {case_data.get('id')}: {e}")
        
        return judge_ids
    
    def get_case_authority_metrics(self, case_id: str) -> Dict[str, Any]:
        """Calculate authority metrics for a case based on citations and judge relationships."""
        
        try:
            with self.neo4j.driver.session() as session:
                
                # Get citation metrics
                citation_query = """
                MATCH (c:Case {id: $case_id})
                OPTIONAL MATCH (c)<-[r:CITES]-(citing:Case)
                OPTIONAL MATCH (c)-[r2:CITES]->(cited:Case)
                RETURN 
                    COUNT(DISTINCT citing) as incoming_citations,
                    COUNT(DISTINCT cited) as outgoing_citations,
                    AVG(r.confidence_score) as avg_citation_confidence,
                    COLLECT(DISTINCT r.strength) as citation_strengths
                """
                
                citation_result = session.run(citation_query, case_id=case_id).single()
                
                # Get judge authority metrics
                judge_query = """
                MATCH (c:Case {id: $case_id})<-[r]-(j:Judge)
                RETURN 
                    COUNT(DISTINCT j) as judge_count,
                    COLLECT(DISTINCT type(r)) as relationship_types,
                    COLLECT(DISTINCT j.name) as judge_names
                """
                
                judge_result = session.run(judge_query, case_id=case_id).single()
                
                # Calculate authority score
                incoming = citation_result['incoming_citations'] or 0
                outgoing = citation_result['outgoing_citations'] or 0
                judge_count = judge_result['judge_count'] or 0
                
                authority_score = (incoming * 2) + (judge_count * 0.5) - (outgoing * 0.1)
                
                return {
                    'case_id': case_id,
                    'incoming_citations': incoming,
                    'outgoing_citations': outgoing,
                    'judge_count': judge_count,
                    'authority_score': max(0, authority_score),
                    'avg_citation_confidence': citation_result['avg_citation_confidence'] or 0.0,
                    'citation_strengths': citation_result['citation_strengths'] or [],
                    'judge_relationship_types': judge_result['relationship_types'] or [],
                    'judges': judge_result['judge_names'] or []
                }
                
        except Exception as e:
            logger.error(f"❌ Error calculating authority metrics for {case_id}: {e}")
            return {'case_id': case_id, 'authority_score': 0.0}


def main():
    """Test the enhanced graph schema."""
    
    schema = EnhancedGraphSchema()
    
    # Initialize schema
    if schema.initialize_enhanced_schema():
        logger.info("✅ Enhanced graph schema test completed successfully")
        return True
    else:
        logger.error("❌ Enhanced graph schema test failed")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
