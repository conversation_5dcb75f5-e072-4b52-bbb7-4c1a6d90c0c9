#!/usr/bin/env python3
"""
Test that voyage-3-large configuration is working correctly
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from source_agnostic_processor import CoherentCase

def test_voyage3large_config():
    """Test voyage-3-large embedding configuration"""
    
    print("🔍 TESTING VOYAGE-3-LARGE CONFIGURATION")
    print("=" * 60)
    
    load_dotenv()
    
    # Check API key
    voyage_key = os.getenv("VOYAGE_API_KEY")
    if not voyage_key:
        print("❌ VOYAGE_API_KEY not found in .env")
        return False
    
    print(f"✅ Voyage API key found: {voyage_key[:10]}...")
    
    # Test embedding generation
    print("\n🧪 Testing embedding generation...")
    
    # Create a test case
    test_case_data = {
        'id': 'test_case_001',
        'text': 'This is a test case for voyage-3-large embedding generation. It should produce a 1024-dimensional vector.',
        'case_name': 'Test Case v. Voyage AI',
        'court': 'Test Court'
    }
    
    try:
        # Create CoherentCase
        coherent_case = CoherentCase(test_case_data, 'test_source')
        
        print(f"   Case ID: {coherent_case.id}")
        print(f"   Text length: {len(coherent_case.text_content)} chars")
        
        # Test chunking
        chunks = coherent_case._chunk_text(coherent_case.text_content)
        print(f"   Chunks created: {len(chunks)}")
        
        # Test vector generation
        vectors = coherent_case.to_pinecone_vectors()
        print(f"   Vectors created: {len(vectors)}")
        
        if len(vectors) > 0:
            first_vector = vectors[0]
            embedding = first_vector['values']
            
            print(f"   Vector ID: {first_vector['id']}")
            print(f"   Embedding dimensions: {len(embedding)}")
            print(f"   First 3 values: {embedding[:3]}")
            print(f"   Metadata: {first_vector['metadata']}")
            
            # Verify dimensions
            if len(embedding) == 1024:
                print("✅ Correct dimensions for voyage-3-large (1024)")
            else:
                print(f"❌ Wrong dimensions: {len(embedding)} (expected 1024)")
                return False
            
            # Verify non-zero values
            non_zero_count = sum(1 for x in embedding if abs(x) > 0.001)
            if non_zero_count > 500:  # Most values should be non-zero
                print(f"✅ Non-zero embedding values: {non_zero_count}/1024")
            else:
                print(f"❌ Too many zero values: {non_zero_count}/1024")
                return False
        
        print("\n🎯 VOYAGE-3-LARGE CONFIGURATION TEST: SUCCESS!")
        print("✅ API key configured")
        print("✅ Embedding generation working")
        print("✅ Correct dimensions (1024)")
        print("✅ Non-zero embeddings")
        print("✅ Ready for production use")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing voyage-3-large: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_voyage3large_config()
    if success:
        print("\n🎉 VOYAGE-3-LARGE CONFIGURATION: VERIFIED!")
    else:
        print("\n❌ VOYAGE-3-LARGE CONFIGURATION: FAILED!")
    
    exit(0 if success else 1)
