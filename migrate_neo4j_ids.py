#!/usr/bin/env python3
"""
Migrate Neo4j Document node IDs to match Supabase case IDs for consistency.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector
from processing.storage.supabase_connector import SupabaseConnector

async def analyze_id_mismatches():
    """Analyze ID format mismatches between Supabase and Neo4j."""
    
    try:
        print("🔍 ANALYZING ID FORMAT MISMATCHES")
        print("=" * 60)
        
        # Initialize connections
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        
        # Get all case IDs from Supabase (with pagination)
        print("📥 Fetching all case IDs from Supabase...")
        all_supabase_cases = {}
        offset = 0
        limit = 1000
        
        while True:
            response = supabase.client.table('cases').select('id, case_name, source').range(offset, offset + limit - 1).execute()
            if not response.data:
                break
            
            for case in response.data:
                all_supabase_cases[case['id']] = case
            
            print(f"   Fetched {len(response.data)} cases (total: {len(all_supabase_cases)})")
            
            if len(response.data) < limit:
                break
            
            offset += limit
        
        print(f"   Total Supabase cases: {len(all_supabase_cases):,}")
        
        # Get all Document node IDs from Neo4j
        print("📥 Fetching all Document node IDs from Neo4j...")
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN d.id as id")
            neo4j_ids = []
            null_id_count = 0
            
            for record in result:
                doc_id = record['id']
                if doc_id:
                    neo4j_ids.append(doc_id)
                else:
                    null_id_count += 1
        
        print(f"   Total Neo4j Document nodes: {len(neo4j_ids):,}")
        print(f"   Null ID nodes: {null_id_count}")
        
        # Analyze ID patterns
        supabase_patterns = {}
        neo4j_patterns = {}
        
        for case_id in all_supabase_cases.keys():
            if case_id.startswith('cap_'):
                supabase_patterns['cap_'] = supabase_patterns.get('cap_', 0) + 1
            elif case_id.startswith('cl_'):
                supabase_patterns['cl_'] = supabase_patterns.get('cl_', 0) + 1
            elif case_id.isdigit():
                supabase_patterns['numeric'] = supabase_patterns.get('numeric', 0) + 1
            else:
                supabase_patterns['other'] = supabase_patterns.get('other', 0) + 1
        
        for doc_id in neo4j_ids:
            if doc_id.startswith('cap_'):
                neo4j_patterns['cap_'] = neo4j_patterns.get('cap_', 0) + 1
            elif doc_id.startswith('cl_'):
                neo4j_patterns['cl_'] = neo4j_patterns.get('cl_', 0) + 1
            elif doc_id and doc_id.replace('-', '').replace('_', '').isalnum():
                neo4j_patterns['other'] = neo4j_patterns.get('other', 0) + 1
            else:
                neo4j_patterns['unknown'] = neo4j_patterns.get('unknown', 0) + 1
        
        print(f"\n📊 ID PATTERN ANALYSIS:")
        print(f"   Supabase patterns:")
        for pattern, count in sorted(supabase_patterns.items(), key=lambda x: x[1], reverse=True):
            print(f"     {pattern}: {count:,}")
        
        print(f"   Neo4j patterns:")
        for pattern, count in sorted(neo4j_patterns.items(), key=lambda x: x[1], reverse=True):
            print(f"     {pattern}: {count:,}")
        
        # Find potential matches (cap_ in Neo4j vs non-cap_ in Supabase)
        potential_matches = []
        for neo4j_id in neo4j_ids:
            if neo4j_id.startswith('cap_'):
                # Try to find corresponding Supabase ID
                clean_id = neo4j_id[4:]  # Remove 'cap_' prefix
                if clean_id in all_supabase_cases:
                    potential_matches.append((neo4j_id, clean_id))
        
        print(f"\n🔗 POTENTIAL ID MATCHES:")
        print(f"   Found {len(potential_matches)} potential matches")
        print(f"   Sample matches:")
        for i, (neo4j_id, supabase_id) in enumerate(potential_matches[:5]):
            print(f"     Neo4j: {neo4j_id} → Supabase: {supabase_id}")
        
        neo4j.close()
        
        return len(potential_matches), len(all_supabase_cases), len(neo4j_ids)
        
    except Exception as e:
        print(f"❌ Error analyzing mismatches: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0

async def create_migration_plan():
    """Create a plan for migrating Neo4j IDs to match Supabase."""
    
    print(f"\n🔧 MIGRATION PLAN:")
    print(f"   1. Identify all Document nodes with 'cap_' prefix in Neo4j")
    print(f"   2. Check if corresponding case exists in Supabase without 'cap_' prefix")
    print(f"   3. Update Neo4j Document node ID to match Supabase case ID")
    print(f"   4. Update all relationships pointing to the old ID")
    print(f"   5. Verify consistency after migration")
    
    print(f"\n⚠️  MIGRATION RISKS:")
    print(f"   - Relationship integrity must be maintained")
    print(f"   - Backup recommended before migration")
    print(f"   - Process should be atomic (all or nothing)")
    
    print(f"\n✅ MIGRATION BENEFITS:")
    print(f"   - 100% consistency between Supabase and Neo4j")
    print(f"   - Simplified ID management")
    print(f"   - Better data integrity")

async def main():
    """Main function."""
    matches, supabase_count, neo4j_count = await analyze_id_mismatches()
    await create_migration_plan()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Supabase cases: {supabase_count:,}")
    print(f"   Neo4j Document nodes: {neo4j_count:,}")
    print(f"   Potential ID matches: {matches:,}")
    
    if matches > 0:
        print(f"   📊 Migration potential: {matches:,} nodes can be aligned")
        print(f"   🔧 Recommendation: Execute ID migration for consistency")
    else:
        print(f"   ✅ No obvious ID mismatches found")

if __name__ == "__main__":
    asyncio.run(main())
