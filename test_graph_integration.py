#!/usr/bin/env python3
"""
Test Graph Integration
Demonstrates the graph sync integration with 10 test opinions.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from graph.graph_sync import write_opinion, write_citation, flush_all, get_stats

def test_graph_integration():
    """Test graph integration with 10 sample opinions."""
    
    print("Testing Graph Integration with 10 Sample Opinions")
    print("=" * 60)
    
    # Sample opinions data
    test_opinions = [
        {
            'opinion_id': 'test_cap_txsc_1985_001',
            'court_id': 'TXSC',
            'year_filed': 1985,
            'practice_areas': ['personal_injury'],
            'citation': '123 S.W.2d 456',
            'docket_number': '85-1234',
            'case_name': 'Smith v. Jones Construction Co.',
            'date_filed': '1985-03-15',
            'source': 'caselaw_access_project',
            'source_window': 'historical'
        },
        {
            'opinion_id': 'test_cl_txnd_2020_001',
            'court_id': 'TXND',
            'year_filed': 2020,
            'practice_areas': ['personal_injury', 'criminal_defense'],
            'citation': '456 F.3d 789',
            'docket_number': '4:20-cv-01234',
            'case_name': 'Thompson v. ABC Manufacturing Corp.',
            'date_filed': '2020-06-12',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cap_txsc_1991_002',
            'court_id': 'TXSC',
            'year_filed': 1991,
            'practice_areas': ['family_law'],
            'citation': '789 S.W.2d 123',
            'docket_number': '91-5678',
            'case_name': 'Johnson v. Johnson',
            'date_filed': '1991-07-08',
            'source': 'caselaw_access_project',
            'source_window': 'historical'
        },
        {
            'opinion_id': 'test_cl_ca5_2018_001',
            'court_id': 'CA5',
            'year_filed': 2018,
            'practice_areas': ['criminal_defense'],
            'citation': '321 F.3d 654',
            'docket_number': '18-40123',
            'case_name': 'United States v. Garcia',
            'date_filed': '2018-12-03',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cl_txcoa01_2022_001',
            'court_id': 'TXCOA01',
            'year_filed': 2022,
            'practice_areas': ['family_law'],
            'citation': '987 S.W.3d 321',
            'docket_number': '01-22-00456',
            'case_name': 'Davis v. Davis',
            'date_filed': '2022-04-18',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cl_txsd_2019_001',
            'court_id': 'TXSD',
            'year_filed': 2019,
            'practice_areas': ['estate_planning'],
            'citation': '654 F.Supp.3d 987',
            'docket_number': '4:19-cv-02345',
            'case_name': 'Wilson Trust v. Texas Department of Revenue',
            'date_filed': '2019-08-25',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cl_txcoa05_2021_001',
            'court_id': 'TXCOA05',
            'year_filed': 2021,
            'practice_areas': ['real_estate'],
            'citation': '147 S.W.3d 258',
            'docket_number': '05-21-00789',
            'case_name': 'Landlord Properties LLC v. Tenant',
            'date_filed': '2021-01-14',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cap_txsc_1987_001',
            'court_id': 'TXSC',
            'year_filed': 1987,
            'practice_areas': ['estate_planning'],
            'citation': '369 S.W.2d 741',
            'docket_number': '87-9012',
            'case_name': 'Estate of Williams v. First National Bank',
            'date_filed': '1987-05-14',
            'source': 'caselaw_access_project',
            'source_window': 'historical'
        },
        {
            'opinion_id': 'test_cl_txwd_2020_001',
            'court_id': 'TXWD',
            'year_filed': 2020,
            'practice_areas': ['bankruptcy'],
            'citation': '852 F.Supp.3d 147',
            'docket_number': '5:20-bk-03456',
            'case_name': 'Chapter 7 Trustee v. Debtor',
            'date_filed': '2020-11-09',
            'source': 'court_listener',
            'source_window': 'modern'
        },
        {
            'opinion_id': 'test_cl_tcca_2021_001',
            'court_id': 'TCCA',
            'year_filed': 2021,
            'practice_areas': ['criminal_defense'],
            'citation': '963 S.W.3d 852',
            'docket_number': 'PD-0789-21',
            'case_name': 'State v. Anderson',
            'date_filed': '2021-09-22',
            'source': 'court_listener',
            'source_window': 'modern'
        }
    ]
    
    # Test citations (some opinions cite others)
    test_citations = [
        ('test_cl_txnd_2020_001', 'test_cap_txsc_1985_001'),  # Modern cites historical
        ('test_cl_txcoa01_2022_001', 'test_cap_txsc_1991_002'),  # Family law precedent
        ('test_cl_ca5_2018_001', 'test_cl_tcca_2021_001'),  # Federal cites state criminal
        ('test_cl_txsd_2019_001', 'test_cap_txsc_1987_001'),  # Estate planning precedent
        ('test_cl_txcoa05_2021_001', 'test_cap_txsc_1985_001'),  # Real estate cites PI case
    ]
    
    print(f"Writing {len(test_opinions)} test opinions to graph...")
    
    # Write opinions to graph
    for i, opinion in enumerate(test_opinions, 1):
        write_opinion(opinion)
        print(f"  {i:2d}. {opinion['case_name']} ({opinion['court_id']} {opinion['year_filed']})")
    
    print(f"\nWriting {len(test_citations)} citation relationships...")
    
    # Write citations
    for i, (citing, cited) in enumerate(test_citations, 1):
        write_citation(citing, cited)
        citing_case = next(op['case_name'] for op in test_opinions if op['opinion_id'] == citing)
        cited_case = next(op['case_name'] for op in test_opinions if op['opinion_id'] == cited)
        print(f"  {i}. {citing_case} → {cited_case}")
    
    # Flush all operations
    print("\nFlushing all operations to Neo4j...")
    flush_all()
    
    # Get final statistics
    print("\nFinal Graph Statistics:")
    try:
        stats = get_stats()
        print(f"  Nodes: {stats['nodes']['total']:,}")
        print(f"    - Courts: {stats['nodes']['courts']}")
        print(f"    - Practice Areas: {stats['nodes']['practice_areas']}")
        print(f"    - Opinions: {stats['nodes']['opinions']:,}")
        print(f"  Relationships: {stats['relationships']['total']:,}")
        print(f"    - Court-Opinion: {stats['relationships']['hears_before']:,}")
        print(f"    - Opinion-PA: {stats['relationships']['has_pa']:,}")
        print(f"    - Citations: {stats['relationships']['cites']:,}")
    except Exception as e:
        print(f"  Could not retrieve stats (Neo4j not connected): {e}")
    
    print("\n✅ Graph integration test completed successfully!")
    print("\nTest demonstrates:")
    print("  • Automatic opinion sync to Neo4j")
    print("  • Court and practice area relationship creation")
    print("  • Citation network establishment")
    print("  • Batch processing with flush operations")
    print("  • Mixed historical (CAP) and modern (CL) data")
    print("  • Multi-practice area support")
    
    return True

if __name__ == "__main__":
    test_graph_integration()
