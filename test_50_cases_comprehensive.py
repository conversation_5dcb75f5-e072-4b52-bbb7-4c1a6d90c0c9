#!/usr/bin/env python3
"""
Comprehensive testing on 50+ real CAP cases
Tests chunking, linking, and robustness at scale
"""

import asyncio
import logging
import os
import json
import gzip
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import test components
from test_cap_enhanced_tracking import MockGCSClient, MockNeo4jClient
from test_cap_real_pinecone import RealPineconeClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Comprehensive50CasesTester:
    """Comprehensive testing on 50+ real CAP cases"""
    
    def __init__(self):
        load_dotenv()
        
        # Setup clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = MockGCSClient()
        # Use RealPineconeClient but need to update it for voyage3large index
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = MockNeo4jClient()
        
        # Initialize processor
        self.processor = SourceAgnosticProcessor(
            self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
        )
        
        logger.info("✅ Comprehensive 50-cases tester initialized")
    
    async def run_comprehensive_test(self, target_cases: int = 50):
        """Run comprehensive test on target number of cases"""
        
        print("📊 COMPREHENSIVE 50+ CASES TESTING")
        print("=" * 80)
        print(f"🎯 Target: {target_cases} cases")
        print(f"📁 Source: Real CAP data files")
        print(f"🔧 Features: Improved chunking + linking + robustness")
        
        # Clean existing test data
        logger.info("🧹 Cleaning existing test data...")
        try:
            self.supabase.table('cases').delete().like('batch_id', 'comprehensive_50_%').execute()
            logger.info("✅ Existing test data cleaned")
        except Exception as e:
            logger.warning(f"Warning cleaning test data: {e}")
        
        # Get 50+ real CAP cases
        raw_cases = self._get_real_cap_cases(target_cases)
        
        if len(raw_cases) < target_cases:
            print(f"⚠️ Only found {len(raw_cases)} cases (target: {target_cases})")
        else:
            print(f"✅ Found {len(raw_cases)} cases for testing")
        
        # Process in batches for robustness
        batch_size = 10
        all_results = []
        
        for batch_num in range(0, len(raw_cases), batch_size):
            batch_cases = raw_cases[batch_num:batch_num + batch_size]
            batch_id = f"comprehensive_50_{datetime.now().strftime('%Y%m%d_%H%M%S')}_batch_{batch_num//batch_size}"
            
            print(f"\n📦 PROCESSING BATCH {batch_num//batch_size + 1}")
            print(f"   Cases: {len(batch_cases)}")
            print(f"   Batch ID: {batch_id}")
            
            try:
                result = await self.processor.process_coherent_batch(
                    raw_cases=batch_cases,
                    source_type='caselaw_access_project',
                    batch_id=batch_id
                )
                
                all_results.append({
                    'batch_id': batch_id,
                    'cases_count': len(batch_cases),
                    'success': result['success'],
                    'result': result
                })
                
                if result['success']:
                    print(f"   ✅ Batch processed successfully")
                else:
                    print(f"   ❌ Batch failed: {result}")
                
            except Exception as e:
                print(f"   💥 Batch failed with exception: {e}")
                all_results.append({
                    'batch_id': batch_id,
                    'cases_count': len(batch_cases),
                    'success': False,
                    'error': str(e)
                })
        
        # Comprehensive verification
        print(f"\n🔍 COMPREHENSIVE VERIFICATION")
        print("=" * 60)
        
        verification_results = await self._verify_comprehensive_results(all_results)
        
        # Final summary
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE 50+ CASES TEST RESULTS")
        print("=" * 80)
        
        total_cases = sum(r['cases_count'] for r in all_results)
        successful_batches = sum(1 for r in all_results if r['success'])
        
        print(f"📈 PROCESSING RESULTS:")
        print(f"   Total cases processed: {total_cases}")
        print(f"   Successful batches: {successful_batches}/{len(all_results)}")
        print(f"   Success rate: {successful_batches/len(all_results)*100:.1f}%")
        
        print(f"\n🔍 VERIFICATION RESULTS:")
        for key, value in verification_results.items():
            status = "✅" if value else "❌"
            print(f"   {key}: {status}")
        
        overall_success = all(verification_results.values()) and successful_batches == len(all_results)
        
        if overall_success:
            print(f"\n🎉 COMPREHENSIVE 50+ CASES TEST: SUCCESS!")
            print(f"✅ All {total_cases} cases processed successfully")
            print(f"✅ All verification checks passed")
            print(f"✅ System ready for production scale")
        else:
            print(f"\n❌ COMPREHENSIVE 50+ CASES TEST: ISSUES FOUND")
            print(f"❌ Some cases or verifications failed")
            print(f"❌ System needs refinement")
        
        return overall_success
    
    def _get_real_cap_cases(self, target_count: int) -> list:
        """Get real CAP cases from files"""
        
        print(f"📚 Collecting {target_count} real CAP cases...")
        
        cap_files = list(Path("data/caselaw_access_project").glob("*.jsonl.gz"))
        raw_cases = []
        
        for cap_file in cap_files:
            if len(raw_cases) >= target_count:
                break
                
            print(f"   Reading {cap_file.name}...")
            
            try:
                with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if len(raw_cases) >= target_count:
                            break
                            
                        try:
                            raw_case = json.loads(line.strip())
                            text = raw_case.get('text', '')
                            
                            # Filter for substantial cases (avoid tiny cases)
                            if len(text) > 500 and 'texas' in text.lower():
                                raw_cases.append(raw_case)
                                
                                if len(raw_cases) % 10 == 0:
                                    print(f"      Found {len(raw_cases)} cases...")
                                    
                        except json.JSONDecodeError:
                            continue
                            
            except Exception as e:
                logger.warning(f"Error reading {cap_file}: {e}")
                continue
        
        print(f"✅ Collected {len(raw_cases)} real CAP cases")
        return raw_cases
    
    async def _verify_comprehensive_results(self, batch_results: list) -> dict:
        """Comprehensive verification of all results"""
        
        verification_results = {}
        
        # 1. Database consistency check
        print("🔍 Verifying database consistency...")
        try:
            all_cases = self.supabase.table('cases').select('id, batch_id').like('batch_id', 'comprehensive_50_%').execute()
            db_case_count = len(all_cases.data)
            expected_count = sum(r['cases_count'] for r in batch_results if r['success'])
            
            verification_results['database_consistency'] = db_case_count == expected_count
            print(f"   Database cases: {db_case_count}, Expected: {expected_count}")
            
        except Exception as e:
            verification_results['database_consistency'] = False
            print(f"   ❌ Database check failed: {e}")
        
        # 2. Pinecone vector consistency check
        print("🔍 Verifying Pinecone vector consistency...")
        try:
            sample_cases = all_cases.data[:5] if 'all_cases' in locals() else []
            vector_consistency = True
            
            for case in sample_cases:
                case_id = case['id']
                stats = self.pinecone_client.index.describe_index_stats()
                
                query_response = self.pinecone_client.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id},
                    top_k=100,
                    include_metadata=True,
                    namespace="tx"
                )
                
                if len(query_response.matches) == 0:
                    vector_consistency = False
                    break
            
            verification_results['vector_consistency'] = vector_consistency
            print(f"   Vector consistency: {'✅' if vector_consistency else '❌'}")
            
        except Exception as e:
            verification_results['vector_consistency'] = False
            print(f"   ❌ Vector check failed: {e}")
        
        # 3. Chunk size validation
        print("🔍 Verifying chunk sizes...")
        try:
            chunk_sizes_valid = True
            max_chunk_size = 0
            
            for case in sample_cases:
                case_id = case['id']
                
                query_response = self.pinecone_client.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id},
                    top_k=100,
                    include_metadata=True,
                    namespace="tx"
                )
                
                for vector in query_response.matches:
                    text_length = vector.metadata.get('text_length', 0)
                    if text_length > 1200:  # Character limit
                        chunk_sizes_valid = False
                    max_chunk_size = max(max_chunk_size, text_length)
            
            verification_results['chunk_sizes_valid'] = chunk_sizes_valid
            print(f"   Max chunk size: {max_chunk_size} chars, Valid: {'✅' if chunk_sizes_valid else '❌'}")
            
        except Exception as e:
            verification_results['chunk_sizes_valid'] = False
            print(f"   ❌ Chunk size check failed: {e}")
        
        # 4. Metadata completeness check
        print("🔍 Verifying metadata completeness...")
        try:
            metadata_complete = True
            
            for case in sample_cases[:3]:  # Check first 3 cases
                case_id = case['id']
                
                query_response = self.pinecone_client.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id},
                    top_k=5,
                    include_metadata=True,
                    namespace="tx"
                )
                
                for vector in query_response.matches:
                    metadata = vector.metadata
                    required_fields = ['case_id', 'chunk_index']
                    
                    for field in required_fields:
                        if field not in metadata:
                            metadata_complete = False
                            break
                    
                    if not metadata_complete:
                        break
                
                if not metadata_complete:
                    break
            
            verification_results['metadata_complete'] = metadata_complete
            print(f"   Metadata completeness: {'✅' if metadata_complete else '❌'}")
            
        except Exception as e:
            verification_results['metadata_complete'] = False
            print(f"   ❌ Metadata check failed: {e}")
        
        return verification_results


async def main():
    """Run comprehensive 50+ cases test"""
    
    tester = Comprehensive50CasesTester()
    
    print("🎯 COMPREHENSIVE 50+ CASES TESTING")
    print("This tests chunking, linking, and robustness at scale")
    print()
    
    success = await tester.run_comprehensive_test(target_cases=50)
    
    if success:
        print("\n🎉 COMPREHENSIVE 50+ CASES TEST PASSED!")
        print("✅ System ready for production scale")
        return 0
    else:
        print("\n❌ COMPREHENSIVE 50+ CASES TEST FAILED!")
        print("❌ System needs refinement before production")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
