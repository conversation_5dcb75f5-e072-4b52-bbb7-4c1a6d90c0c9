#!/usr/bin/env python3
"""
Direct test of CAP data processing consistency.
Process 100 records from CAP data and verify database synchronization.
"""

import asyncio
import json
import gzip
import sys
import os
from datetime import datetime
from dataclasses import dataclass

# Add the src directory to the path
sys.path.append('src')

@dataclass
class CaselawDocument:
    """Simplified document structure for testing."""
    id: str
    source: str
    added: datetime
    created: datetime
    author: str
    license: str
    url: str
    text: str
    case_name: str = ""
    jurisdiction: str = ""
    court: str = ""
    date_filed: datetime = None
    docket_number: str = ""
    practice_area: str = ""
    precedential_status: str = ""
    citation_count: int = 0
    judges: list = None
    parties: list = None
    
    def __post_init__(self):
        if self.judges is None:
            self.judges = []
        if self.parties is None:
            self.parties = []

async def test_cap_direct_processing():
    """Test CAP processing directly with database verification."""
    
    print("🧪 DIRECT CAP PROCESSING CONSISTENCY TEST")
    print("=" * 60)
    
    try:
        # Get baseline counts
        print("📊 BASELINE DATABASE COUNTS:")
        baseline = await get_database_counts()
        print(f"   Supabase: {baseline['supabase']:,}")
        print(f"   Neo4j: {baseline['neo4j']:,}")
        print(f"   Pinecone: {baseline['pinecone']:,}")
        
        # Read 100 records from CAP data
        cap_file = "data/caselaw_access_project/cap_00000.jsonl.gz"
        print(f"\n📂 Reading from: {cap_file}")
        
        test_records = []
        with gzip.open(cap_file, 'rt') as f:
            for i, line in enumerate(f):
                if i >= 100:  # Process first 100 records
                    break
                test_records.append(json.loads(line))
        
        print(f"📥 Loaded {len(test_records)} test records")
        
        # Initialize storage connectors directly
        print(f"\n🔧 Initializing storage connectors...")
        
        from processing.storage.supabase_connector import SupabaseConnector
        from processing.storage.neo4j_connector import Neo4jConnector
        from processing.storage.gcs_connector import GCSConnector
        from processing.storage.vector_store import PineconeConnector
        
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        gcs = GCSConnector()
        pinecone = PineconeConnector()
        
        print("✅ All connectors initialized")
        
        # Process records one by one
        print(f"\n🔄 PROCESSING RECORDS:")
        processed_count = 0
        error_count = 0
        
        for i, record in enumerate(test_records):
            try:
                # Convert to CaselawDocument
                doc = convert_cap_record(record)
                
                # Store in Supabase
                case_data = {
                    'id': doc.id,
                    'case_name': extract_case_name(doc.text),
                    'source': doc.source,
                    'full_text': doc.text,
                    'jurisdiction': 'unknown',  # Will be classified later
                    'practice_area': 'unknown',  # Will be classified later
                    'court': doc.author,
                    'created_at': doc.created.isoformat(),
                    'content_hash': generate_content_hash(doc.text)
                }
                
                response = supabase.client.table('cases').insert(case_data).execute()
                
                if response.data:
                    # Store in Neo4j
                    await store_in_neo4j(neo4j, doc)
                    
                    # Store in GCS
                    blob_name = f"caselaw_access/test/{doc.id}.txt"
                    gcs.store_text(doc.text, blob_name)
                    
                    processed_count += 1
                    if (processed_count % 20) == 0:
                        print(f"   ✅ Processed {processed_count}/100 ({processed_count}%)")
                else:
                    error_count += 1
                
            except Exception as e:
                error_count += 1
                if error_count <= 3:  # Show first 3 errors
                    print(f"   ❌ Error processing record {i+1}: {e}")
        
        # Get final counts
        print(f"\n📊 FINAL DATABASE COUNTS:")
        final = await get_database_counts()
        print(f"   Supabase: {final['supabase']:,} (+{final['supabase'] - baseline['supabase']:,})")
        print(f"   Neo4j: {final['neo4j']:,} (+{final['neo4j'] - baseline['neo4j']:,})")
        print(f"   Pinecone: {final['pinecone']:,} (+{final['pinecone'] - baseline['pinecone']:,})")
        
        # Verify consistency
        supabase_increase = final['supabase'] - baseline['supabase']
        neo4j_increase = final['neo4j'] - baseline['neo4j']
        
        print(f"\n🎯 CONSISTENCY VERIFICATION:")
        print(f"   Records processed: {processed_count}")
        print(f"   Errors: {error_count}")
        print(f"   Supabase increase: {supabase_increase}")
        print(f"   Neo4j increase: {neo4j_increase}")
        
        if supabase_increase == neo4j_increase == processed_count:
            print(f"   ✅ PERFECT CONSISTENCY ACHIEVED!")
            consistency_rate = 100.0
        elif supabase_increase == neo4j_increase:
            print(f"   ✅ GOOD CONSISTENCY (Supabase ↔ Neo4j)")
            consistency_rate = 95.0
        else:
            print(f"   ❌ CONSISTENCY ISSUE DETECTED")
            consistency_rate = 0.0
        
        # Close connections
        neo4j.close()
        
        return consistency_rate >= 95.0, processed_count, error_count
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 1

def convert_cap_record(record):
    """Convert CAP JSON record to CaselawDocument."""
    
    metadata = record.get('metadata', {})
    
    return CaselawDocument(
        id=record['id'].replace('/', '_').replace('.html', ''),
        source=record.get('source', 'Caselaw Access Project'),
        added=datetime.fromisoformat(record.get('added', '2024-01-01T00:00:00').replace('Z', '+00:00')),
        created=datetime.fromisoformat(record.get('created', '2024-01-01T00:00:00').replace('Z', '+00:00')),
        author=metadata.get('author', 'Unknown'),
        license=metadata.get('license', 'Public Domain'),
        url=metadata.get('url', ''),
        text=record.get('text', '')
    )

def extract_case_name(text):
    """Extract case name from text."""
    lines = text.strip().split('\n')
    for line in lines[:10]:  # Check first 10 lines
        line = line.strip()
        if ' v. ' in line or ' v ' in line:
            return line[:100]  # Limit length
    return "Unknown Case"

def generate_content_hash(text):
    """Generate content hash."""
    import hashlib
    return hashlib.md5(text.encode()).hexdigest()

async def store_in_neo4j(neo4j, doc):
    """Store document in Neo4j."""
    
    with neo4j.driver.session() as session:
        query = """
        CREATE (d:Document {
            id: $id,
            title: $title,
            source: $source,
            created_at: $created_at,
            content_hash: $content_hash
        })
        """
        
        session.run(query, {
            'id': doc.id,
            'title': extract_case_name(doc.text),
            'source': doc.source,
            'created_at': doc.created.isoformat(),
            'content_hash': generate_content_hash(doc.text)
        })

async def get_database_counts():
    """Get current database counts."""
    
    counts = {'supabase': 0, 'neo4j': 0, 'pinecone': 0}
    
    try:
        # Supabase
        from processing.storage.supabase_connector import SupabaseConnector
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        counts['supabase'] = response.count
        
        # Neo4j
        from processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            counts['neo4j'] = result.single()['count']
        neo4j.close()
        
        # Pinecone
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        counts['pinecone'] = stats.total_vector_count
        
    except Exception as e:
        print(f"⚠️  Error getting counts: {e}")
    
    return counts

async def main():
    """Main test function."""
    
    print("🧪 CAP DIRECT PROCESSING TEST")
    print("=" * 70)
    print("Testing 100 CAP records with direct database storage")
    print("=" * 70)
    
    success, processed, errors = await test_cap_direct_processing()
    
    print(f"\n🎯 TEST RESULTS:")
    if success:
        print(f"   ✅ CAP PROCESSING CONSISTENCY VERIFIED!")
        print(f"   ✅ Processed {processed} records successfully")
        print(f"   ✅ Databases synchronized")
        print(f"   🚀 READY FOR FULL CAP PROCESSING")
    else:
        print(f"   ❌ CONSISTENCY ISSUES DETECTED")
        print(f"   🔧 Need to fix before scaling")

if __name__ == "__main__":
    asyncio.run(main())
