#!/usr/bin/env python3
"""
Batch Classify Existing Cases
Updates practice area classification for existing cases in the database
"""

import os
import sys
import logging
from typing import Dict, List
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def batch_classify_cases(jurisdiction: str = 'tx', limit: int = None, dry_run: bool = False):
    """
    Batch classify existing cases for practice areas.
    
    Args:
        jurisdiction: Jurisdiction to process (default: 'tx')
        limit: Maximum number of cases to process (default: all)
        dry_run: If True, don't update database, just show what would be done
    """
    
    from supabase import create_client, Client
    from src.relationships.practice_area_classifier import PracticeAreaClassifier
    
    print(f"🔄 BATCH CLASSIFYING EXISTING CASES")
    print(f"Jurisdiction: {jurisdiction}")
    print(f"Limit: {limit or 'All cases'}")
    print(f"Mode: {'DRY RUN' if dry_run else 'LIVE UPDATE'}")
    print("=" * 60)
    
    # Initialize connections
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    supabase: Client = create_client(url, key)
    
    classifier = PracticeAreaClassifier()
    
    # Get cases that need classification
    query = supabase.table('cases').select('*').eq('jurisdiction', jurisdiction)
    
    # Filter for cases without practice area classification
    query = query.is_('primary_practice_area', 'null')
    
    if limit:
        query = query.limit(limit)
    
    result = query.execute()
    cases = result.data
    
    if not cases:
        print("✅ No cases found that need classification")
        return
    
    print(f"📊 Found {len(cases)} cases to classify")
    print()
    
    # Statistics
    stats = {
        'processed': 0,
        'classified': 0,
        'low_confidence': 0,
        'errors': 0,
        'practice_areas': {}
    }
    
    for i, case in enumerate(cases, 1):
        case_id = case.get('id', 'unknown')
        case_name = case.get('case_name', 'Unknown')
        
        print(f"[{i}/{len(cases)}] Processing: {case_id}")
        print(f"  Case: {case_name}")
        
        try:
            # Prepare text for classification
            text_parts = []
            
            # Add case name
            if case_name and case_name != 'Unknown':
                text_parts.append(case_name)
            
            # Add nature/case type
            nature = case.get('nature') or case.get('case_type', '')
            if nature:
                text_parts.append(nature)
            
            # Add any other available text fields
            for field in ['summary', 'description', 'docket_number']:
                value = case.get(field)
                if value and isinstance(value, str) and len(value.strip()) > 0:
                    text_parts.append(value)
            
            case_text = ' '.join(text_parts)
            
            if len(case_text.strip()) < 10:
                print(f"  ⚠️ Insufficient text for classification")
                stats['low_confidence'] += 1
                continue
            
            # Classify the case
            document = {
                "text": case_text,
                "content": case_text,
                "title": case_name,
                "case_name": case_name
            }
            
            primary_area, confidence, all_scores = classifier.classify_with_confidence(document)
            
            # Determine practice areas
            if confidence > 0.2:
                # Get multiple practice areas above threshold
                practice_areas = [area for area, score in all_scores if score >= 0.1][:3]
                if not practice_areas:
                    practice_areas = [primary_area]
                
                print(f"  ✅ Classified as: {primary_area} (confidence: {confidence:.3f})")
                print(f"  📋 Practice areas: {practice_areas}")
                
                # Update database
                if not dry_run:
                    update_data = {
                        'primary_practice_area': primary_area,
                        'practice_areas': practice_areas,
                        'updated_at': 'now()'
                    }
                    
                    update_result = supabase.table('cases').update(update_data).eq('id', case_id).execute()
                    
                    if update_result.data:
                        print(f"  💾 Updated in database")
                        stats['classified'] += 1
                        
                        # Track practice area distribution
                        stats['practice_areas'][primary_area] = stats['practice_areas'].get(primary_area, 0) + 1
                    else:
                        print(f"  ❌ Failed to update database")
                        stats['errors'] += 1
                else:
                    print(f"  🔍 Would update: {primary_area} | {practice_areas}")
                    stats['classified'] += 1
                    stats['practice_areas'][primary_area] = stats['practice_areas'].get(primary_area, 0) + 1
                    
            else:
                print(f"  ⚠️ Low confidence: {primary_area} ({confidence:.3f})")
                
                # Still update with 'general' for low confidence
                if not dry_run:
                    update_data = {
                        'primary_practice_area': 'general',
                        'practice_areas': ['general'],
                        'updated_at': 'now()'
                    }
                    supabase.table('cases').update(update_data).eq('id', case_id).execute()
                
                stats['low_confidence'] += 1
                stats['practice_areas']['general'] = stats['practice_areas'].get('general', 0) + 1
            
            stats['processed'] += 1
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            stats['errors'] += 1
        
        print()
    
    # Final statistics
    print("📊 BATCH CLASSIFICATION RESULTS")
    print("=" * 40)
    print(f"Total processed: {stats['processed']}")
    print(f"Successfully classified: {stats['classified']}")
    print(f"Low confidence: {stats['low_confidence']}")
    print(f"Errors: {stats['errors']}")
    print()
    
    if stats['practice_areas']:
        print("Practice Area Distribution:")
        for area, count in sorted(stats['practice_areas'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {area}: {count}")
    
    print()
    if dry_run:
        print("🔍 This was a DRY RUN - no changes were made to the database")
        print("Run with --live to actually update the database")
    else:
        print("✅ Database has been updated with practice area classifications")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Batch classify existing cases')
    parser.add_argument('--jurisdiction', '-j', default='tx', help='Jurisdiction to process (default: tx)')
    parser.add_argument('--limit', '-l', type=int, help='Maximum number of cases to process')
    parser.add_argument('--live', action='store_true', help='Actually update the database (default is dry run)')
    
    args = parser.parse_args()
    
    batch_classify_cases(
        jurisdiction=args.jurisdiction,
        limit=args.limit,
        dry_run=not args.live
    )
