#!/usr/bin/env python3
"""
Phase 1: Real Data Verification
Complete cross-system verification using real CourtListener data
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import the proper verifier
from proper_cross_system_verifier import ProperCrossSystemVerifier

# Import storage clients
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase1RealDataVerification:
    """Phase 1: Complete verification with real CourtListener data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Initialize proper verifier
        self.verifier = ProperCrossSystemVerifier()
        
        # Test batch ID
        self.test_batch_id = f"phase1_real_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
        if hasattr(self, 'verifier'):
            self.verifier.close()
    
    def fetch_real_courtlistener_cases(self, max_cases: int = 3) -> list:
        """Fetch real CourtListener cases with substantial content"""
        
        print(f"\n🌐 FETCHING REAL COURTLISTENER CASES")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        all_cases = []
        
        try:
            # Fetch from CA5 (Fifth Circuit) - known to have judge names
            print(f"   📡 Fetching from CA5 (Fifth Circuit)")
            
            response = requests.get(
                f"{self.cl_base_url}/opinions/",
                headers=headers,
                params={
                    'court': 'ca5',
                    'filed_after': '2020-01-01',
                    'ordering': '-date_filed',
                    'page_size': max_cases * 2  # Get extra to filter for quality
                },
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"   ❌ API request failed: {response.status_code}")
                return []
            
            results = response.json().get('results', [])
            
            for case in results:
                if len(all_cases) >= max_cases:
                    break
                
                text = case.get('plain_text', '') or case.get('html', '')
                
                # Filter for cases with substantial content and judge indicators
                if (text and 
                    len(text) > 2000 and 
                    ('judge' in text.lower() or 'justice' in text.lower() or 'honorable' in text.lower())):
                    
                    # Create processing format
                    case_data = {
                        'id': f"real_cl_ca5_{case.get('id', 'unknown')}",
                        'source': 'courtlistener',
                        'case_name': case.get('case_name', 'Unknown'),
                        'court': case.get('court', ''),
                        'court_name': f"U.S. Court of Appeals, Fifth Circuit",
                        'date_filed': case.get('date_filed', ''),
                        'jurisdiction': 'US',
                        'text': text,
                        'precedential_status': case.get('precedential_status', 'Unknown')
                    }
                    
                    all_cases.append(case_data)
                    print(f"   ✅ Added case: {case_data['case_name'][:60]}...")
                    print(f"      Text length: {len(text):,} characters")
                    print(f"      Has judge indicators: {'judge' in text.lower() or 'justice' in text.lower()}")
            
            time.sleep(1)  # Rate limiting
        
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
        
        print(f"✅ FETCHED {len(all_cases)} REAL COURTLISTENER CASES")
        return all_cases
    
    async def process_real_cases(self, cases: list) -> bool:
        """Process real cases through complete pipeline"""
        
        print(f"\n🔄 PROCESSING REAL CASES THROUGH COMPLETE PIPELINE")
        print("=" * 60)
        
        try:
            # Process through complete pipeline with ALL features enabled
            processor = SourceAgnosticProcessor(
                self.supabase, 
                self.gcs_client, 
                self.pinecone_client, 
                self.neo4j_client,
                enable_legal_relationships=True,  # Enable ALL features
                legal_relationship_timeout=120    # Longer timeout for real data
            )
            
            print(f"📊 Processing {len(cases)} real cases with ALL FEATURES ENABLED")
            print(f"   ⚖️ Legal relationships: ENABLED")
            print(f"   🔧 Enhanced judge features: ENABLED")
            print(f"   📊 Cross-system storage: ENABLED")
            
            result = await processor.process_coherent_batch(
                raw_cases=cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {'✅' if result['success'] else '❌'}")
            print(f"   Cases processed: {result['processed']}")
            
            if result['success']:
                print(f"✅ Real cases processed successfully through complete pipeline")
                return True
            else:
                print(f"❌ Real case processing failed: {result}")
                return False
                
        except Exception as e:
            print(f"❌ Error processing real cases: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def run_complete_real_data_verification(self) -> bool:
        """Run complete verification with real CourtListener data"""
        
        print("🔍 PHASE 1: COMPLETE REAL DATA VERIFICATION")
        print("=" * 80)
        print("🎯 GOAL: Verify ALL systems with REAL CourtListener data")
        print("📊 SYSTEMS: Supabase + GCS + Pinecone + Neo4j")
        print("🔧 FEATURES: All enhanced features + legal relationships")
        
        try:
            # Step 1: Fetch real CourtListener cases
            real_cases = self.fetch_real_courtlistener_cases(max_cases=3)
            
            if not real_cases:
                print(f"❌ No real cases fetched for verification")
                return False
            
            # Step 2: Process real cases through complete pipeline
            processing_success = await self.process_real_cases(real_cases)
            
            if not processing_success:
                print(f"❌ Real case processing failed")
                return False
            
            # Step 3: Wait for all systems to be ready
            print(f"\n⏱️ Waiting for all systems to be ready for verification...")
            await asyncio.sleep(10)  # Give systems time to be consistent
            
            # Step 4: Run comprehensive verification
            case_ids = [case['id'] for case in real_cases]
            
            print(f"\n🔍 STEP 1: BASIC CROSS-SYSTEM VERIFICATION")
            basic_report = await self.verifier.verify_batch(case_ids)
            
            print(f"\n🔍 STEP 2: DETAILED DATA INTEGRITY VERIFICATION")
            integrity_report = await self.verifier.verify_data_integrity(case_ids)
            
            # Step 5: Generate final assessment
            return self._assess_real_data_results(basic_report, integrity_report, len(real_cases))
            
        except Exception as e:
            print(f"❌ Complete real data verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _assess_real_data_results(self, basic_report: dict, integrity_report: dict, case_count: int) -> bool:
        """Assess real data verification results"""
        
        print(f"\n📊 PHASE 1 REAL DATA VERIFICATION RESULTS")
        print("=" * 80)
        
        # Basic verification results
        basic_success = basic_report.get('overall_success', False)
        success_rate = basic_report.get('verification_summary', {}).get('success_rate', 0)
        avg_consistency = basic_report.get('verification_summary', {}).get('average_consistency_score', 0)
        
        print(f"🔍 BASIC CROSS-SYSTEM VERIFICATION:")
        print(f"   Cases Verified: {case_count}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Consistency: {avg_consistency:.1f}%")
        print(f"   Overall Success: {'✅' if basic_success else '❌'}")
        
        # System coverage with real data
        system_coverage = basic_report.get('system_coverage', {})
        system_success_rates = basic_report.get('system_success_rates', {})
        
        print(f"\n📊 REAL DATA SYSTEM COVERAGE:")
        for system, count in system_coverage.items():
            success_rate_sys = system_success_rates.get(system, 0)
            status = '✅' if success_rate_sys >= 90 else '❌'
            print(f"   {system.upper()}: {count}/{case_count} cases ({success_rate_sys:.1f}%) {status}")
        
        # Data integrity with real data
        integrity_success = integrity_report.get('overall_integrity', False)
        integrity_summary = integrity_report.get('integrity_summary', {})
        
        print(f"\n🔍 REAL DATA INTEGRITY VERIFICATION:")
        print(f"   Overall Integrity: {'✅' if integrity_success else '❌'}")
        
        for check_type, results in integrity_summary.items():
            success_rate_int = results.get('success_rate', 0)
            status = '✅' if success_rate_int >= 90 else '❌'
            print(f"   {check_type.replace('_', ' ').title()}: {success_rate_int:.1f}% {status}")
        
        # Enhanced features verification
        enhanced_working = any(
            'enhanced_features' in str(results) for results in basic_report.get('detailed_results', [])
        )
        
        print(f"\n🔧 ENHANCED FEATURES WITH REAL DATA:")
        print(f"   Enhanced Judge Features: {'✅' if enhanced_working else '❌'}")
        print(f"   Legal Relationships: {'✅' if basic_success else '❌'}")
        print(f"   Cross-System Tracking: {'✅' if avg_consistency >= 75 else '❌'}")
        
        # Final assessment with real data
        overall_success = (
            basic_success and 
            integrity_success and 
            success_rate >= 80 and  # Slightly lower threshold for real data
            avg_consistency >= 75
        )
        
        print(f"\n🎯 PHASE 1 REAL DATA FINAL ASSESSMENT:")
        print(f"   Real Data Processing: {'✅' if basic_success else '❌'}")
        print(f"   Cross-System Integrity: {'✅' if integrity_success else '❌'}")
        print(f"   System Performance: {'✅' if success_rate >= 80 else '❌'} ({success_rate:.1f}%)")
        print(f"   Data Consistency: {'✅' if avg_consistency >= 75 else '❌'} ({avg_consistency:.1f}%)")
        print(f"   Overall Success: {'✅' if overall_success else '❌'}")
        
        if overall_success:
            print(f"\n🎉 PHASE 1: COMPLETE SUCCESS WITH REAL DATA!")
            print(f"✅ All 4 systems verified with real CourtListener data")
            print(f"✅ Enhanced features working on real cases")
            print(f"✅ Cross-system data integrity confirmed")
            print(f"✅ Legal relationships processing successfully")
            print(f"✅ Ready to proceed to Phase 2")
        else:
            print(f"\n❌ PHASE 1: FAILED WITH REAL DATA!")
            print(f"❌ Issues found in real data verification")
            print(f"❌ Must fix issues before proceeding to Phase 2")
        
        return overall_success
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP PHASE 1 REAL DATA TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run Phase 1 complete real data verification"""
    
    test = Phase1RealDataVerification()
    
    try:
        success = await test.run_complete_real_data_verification()
        return success
    finally:
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
