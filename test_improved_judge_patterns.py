#!/usr/bin/env python3
"""
Test Improved Judge <PERSON><PERSON>
Test the improved judge extraction patterns to avoid false positives
"""

import re

def test_improved_patterns():
    """Test improved judge patterns against known false positives"""
    
    print("🔍 TESTING IMPROVED JUDGE PATTERNS")
    print("=" * 60)
    
    # Sample text that caused false positives
    problematic_text = """
    THE UTAH COURT OF APPEALS
    
    COTTONWOOD HEIGHTS CITY,
           Petitioner,
                v.
   HONORABLE KRISTINE JOHNSON,
          Respondent.
          
    Before RYAN, HARRIS, and COTTONWOOD, Circuit Judges.
    
    RYAN, Circuit Judge:
    
    This case had announced new precedent. The judge presiding over 
    this matter was Judge <PERSON>. <PERSON> delivered 
    the opinion of the court.
    
    Judge <PERSON> wrote the majority opinion.
    """
    
    # Import the improved patterns
    from judge_relationship_enhancer import JudgeRelationshipEnhancer
    
    enhancer = JudgeRelationshipEnhancer()
    
    print(f"📄 TESTING TEXT:")
    print(f"   {problematic_text[:200]}...")
    
    # Extract judges using improved patterns
    judges = enhancer._extract_judges_from_text(problematic_text)
    
    print(f"\n📊 EXTRACTION RESULTS:")
    print(f"   Total judges found: {len(judges)}")
    
    for i, judge in enumerate(judges, 1):
        print(f"   {i}. {judge['name']} (role: {judge['role']}, source: {judge['source']})")
    
    # Expected results
    expected_judges = ['<PERSON> M. <PERSON>', 'Smith', '<PERSON> <PERSON>']
    false_positives = ['Cottonwood Heights', 'Had Announced', 'Presiding Over', 'Cottonwood']
    
    print(f"\n🎯 VALIDATION:")
    print(f"   Expected judges: {expected_judges}")
    print(f"   Should NOT extract: {false_positives}")
    
    found_names = [judge['name'] for judge in judges]
    
    # Check for expected judges
    expected_found = [name for name in expected_judges if any(name in found for found in found_names)]
    print(f"   ✅ Expected judges found: {expected_found}")
    
    # Check for false positives
    false_positives_found = [fp for fp in false_positives if any(fp in found for found in found_names)]
    print(f"   ❌ False positives found: {false_positives_found}")
    
    # Overall assessment
    success = len(expected_found) > 0 and len(false_positives_found) == 0
    
    print(f"\n🎯 PATTERN IMPROVEMENT ASSESSMENT:")
    print(f"   Expected judges extracted: {'✅' if len(expected_found) > 0 else '❌'}")
    print(f"   False positives eliminated: {'✅' if len(false_positives_found) == 0 else '❌'}")
    print(f"   Overall success: {'✅' if success else '❌'}")
    
    if success:
        print(f"\n🎉 IMPROVED PATTERNS: SUCCESS!")
        print(f"✅ False positives eliminated")
        print(f"✅ Real judges still extracted")
    else:
        print(f"\n⚠️ IMPROVED PATTERNS: NEEDS MORE WORK!")
        if false_positives_found:
            print(f"❌ Still extracting false positives: {false_positives_found}")
        if not expected_found:
            print(f"❌ Not extracting expected judges")
    
    enhancer.close()
    return success


def test_pattern_specificity():
    """Test individual patterns for specificity"""
    
    print(f"\n🔬 TESTING PATTERN SPECIFICITY")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        # Should match
        ("Justice Smith delivered the opinion", "Smith", True),
        ("Judge Ryan M. Harris wrote the majority", "Ryan M. Harris", True),
        ("Circuit Judge Barbara Lynn, delivered", "Barbara Lynn", True),
        ("Before RYAN, HARRIS, and WILLIAMS, Circuit Judges", "RYAN", True),
        ("Warren, J., concurring", "Warren", True),
        
        # Should NOT match
        ("Cottonwood Heights City", "", False),
        ("had announced new precedent", "", False),
        ("presiding over this matter", "", False),
        ("Judge presiding over", "", False),
        ("Justice announced", "", False),
    ]
    
    # Improved patterns
    patterns = [
        r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|concurred|dissented)',
        r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s*[,:]\s*(?:delivered|wrote|authored)',
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|presiding)',
        r'Before[^.]*?([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Circuit\s+)?Judge',
        r'([A-Z][a-z]+),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',
    ]
    
    print(f"📊 Testing {len(test_cases)} cases against {len(patterns)} patterns")
    
    correct_predictions = 0
    
    for i, (text, expected_name, should_match) in enumerate(test_cases, 1):
        found_match = False
        extracted_name = ""
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                extracted_name = match.group(1).strip()
                found_match = True
                break
        
        # Validate the result
        if should_match:
            correct = found_match and (expected_name in extracted_name or extracted_name in expected_name)
        else:
            correct = not found_match
        
        if correct:
            correct_predictions += 1
        
        status = "✅" if correct else "❌"
        print(f"   {i}. {status} '{text[:30]}...' → {'Found' if found_match else 'None'} ({extracted_name})")
    
    accuracy = correct_predictions / len(test_cases) * 100
    
    print(f"\n📊 PATTERN SPECIFICITY RESULTS:")
    print(f"   Correct predictions: {correct_predictions}/{len(test_cases)}")
    print(f"   Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 80:
        print(f"   ✅ Pattern specificity: GOOD")
    else:
        print(f"   ❌ Pattern specificity: NEEDS IMPROVEMENT")
    
    return accuracy >= 80


if __name__ == "__main__":
    print("🧪 IMPROVED JUDGE PATTERN TESTING")
    print("=" * 80)
    
    # Test 1: Overall pattern improvement
    test1_success = test_improved_patterns()
    
    # Test 2: Pattern specificity
    test2_success = test_pattern_specificity()
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"   Pattern improvement: {'✅' if test1_success else '❌'}")
    print(f"   Pattern specificity: {'✅' if test2_success else '❌'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 IMPROVED JUDGE PATTERNS: SUCCESS!")
        print(f"✅ False positives eliminated")
        print(f"✅ Pattern specificity improved")
        print(f"✅ Ready for production testing")
    else:
        print(f"\n⚠️ IMPROVED JUDGE PATTERNS: NEEDS MORE REFINEMENT!")
