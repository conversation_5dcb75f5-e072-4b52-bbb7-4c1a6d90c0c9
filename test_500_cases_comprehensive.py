#!/usr/bin/env python3
"""
Comprehensive 500-Case Test
Tests the complete enhanced processing pipeline with real cases
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor
from src.processing.cross_system_validator import CrossSystemValidator

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'test_500_cases_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MockGCSClient:
    """Mock GCS client that simulates storage operations"""
    def __init__(self):
        self.name = "mock_gcs"
        self.stored_objects = {}
        logger.info("✅ Mock GCS client initialized")
    
    def simulate_storage(self, object_name: str, content: str):
        """Simulate storing an object"""
        self.stored_objects[object_name] = {
            'content': content,
            'size': len(content),
            'timestamp': datetime.now().isoformat()
        }
        return True
    
    def get_object_count(self) -> int:
        """Get count of stored objects"""
        return len(self.stored_objects)


class MockPineconeClient:
    """Mock Pinecone client that simulates vector operations"""
    def __init__(self):
        self.name = "mock_pinecone"
        self.stored_vectors = {}
        logger.info("✅ Mock Pinecone client initialized")
    
    def simulate_upsert(self, vectors: list):
        """Simulate upserting vectors"""
        for vector in vectors:
            self.stored_vectors[vector['id']] = {
                'values': vector['values'],
                'metadata': vector['metadata'],
                'timestamp': datetime.now().isoformat()
            }
        return True
    
    def get_vector_count(self) -> int:
        """Get count of stored vectors"""
        return len(self.stored_vectors)
    
    def get_vectors_for_case(self, case_id: str) -> list:
        """Get all vectors for a specific case"""
        return [
            vector_id for vector_id in self.stored_vectors.keys()
            if vector_id.startswith(f"{case_id}_chunk_")
        ]


class MockNeo4jClient:
    """Mock Neo4j client that simulates graph operations"""
    def __init__(self):
        self.name = "mock_neo4j"
        self.stored_nodes = {}
        self.stored_relationships = []
        logger.info("✅ Mock Neo4j client initialized")
    
    def simulate_node_creation(self, node_id: str, properties: dict):
        """Simulate creating a node"""
        self.stored_nodes[node_id] = {
            'properties': properties,
            'timestamp': datetime.now().isoformat()
        }
        return True
    
    def get_node_count(self) -> int:
        """Get count of stored nodes"""
        return len(self.stored_nodes)


async def setup_test_clients():
    """Setup all storage clients for testing"""
    logger.info("🔧 Setting up test clients...")
    
    # Supabase client (real)
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
    
    supabase: Client = create_client(supabase_url, supabase_key)
    logger.info("✅ Supabase client initialized (REAL)")
    
    # Mock clients for testing
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    return supabase, gcs_client, pinecone_client, neo4j_client


async def clean_test_data(supabase):
    """Clean up any existing test data"""
    logger.info("🧹 Cleaning up existing test data...")
    
    try:
        # Delete test cases (those with batch_id starting with 'test_')
        result = supabase.table('cases').delete().like('batch_id', 'test_%').execute()
        deleted_count = len(result.data) if result.data else 0
        logger.info(f"✅ Cleaned up {deleted_count} existing test cases")
        
    except Exception as e:
        logger.warning(f"⚠️ Error cleaning test data: {e}")


async def run_500_case_test():
    """
    Run comprehensive test with 500 real cases
    """
    logger.info("🚀 Starting Comprehensive 500-Case Test")
    logger.info("=" * 80)
    
    # Get API key
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        raise ValueError("COURTLISTENER_API_KEY must be set in environment")
    
    # Setup test clients
    supabase, gcs_client, pinecone_client, neo4j_client = await setup_test_clients()
    
    # Clean existing test data
    await clean_test_data(supabase)
    
    # Initialize enhanced processor with small batch size for testing
    async with ChunkedCourtListenerProcessor(
        api_key=api_key,
        supabase_client=supabase,
        gcs_client=gcs_client,
        pinecone_client=pinecone_client,
        neo4j_client=neo4j_client,
        chunk_size=500,   # Process only 500 cases
        batch_size=50     # Small batches for detailed monitoring
    ) as processor:
        
        logger.info("🎯 Enhanced processor initialized for 500-case test")
        
        # Override the processor's storage pipeline to use our enhanced mock clients
        processor.storage_pipeline.gcs = gcs_client
        processor.storage_pipeline.pinecone = pinecone_client
        processor.storage_pipeline.neo4j = neo4j_client
        
        # Start processing - limit to recent data for faster testing
        start_time = datetime.now()
        
        try:
            results = await processor.process_jurisdiction_chunked(
                jurisdiction='tx',
                start_year=2024,  # Recent data only
                end_year=2024,
                resume=False      # Fresh start
            )
            
            # Processing complete
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("🎉 500-case test processing completed!")
            logger.info(f"📊 Results: {results}")
            logger.info(f"⏱️ Duration: {duration}")
            
            # Validate results
            validation_results = await validate_test_results(
                supabase, gcs_client, pinecone_client, neo4j_client
            )
            
            # Generate comprehensive report
            test_report = generate_test_report(results, validation_results, duration)
            
            return test_report
            
        except Exception as e:
            logger.error(f"❌ 500-case test failed: {e}")
            raise


async def validate_test_results(supabase, gcs_client, pinecone_client, neo4j_client):
    """Validate the test results across all systems"""
    logger.info("🔍 Validating test results across all systems...")
    
    validation_results = {}
    
    # Count cases in Supabase
    supabase_result = supabase.table('cases').select('*', count='exact').like('batch_id', 'test_%').execute()
    supabase_count = supabase_result.count or 0
    supabase_cases = supabase_result.data or []
    
    validation_results['supabase'] = {
        'count': supabase_count,
        'cases_with_gcs_path': len([c for c in supabase_cases if c.get('gcs_path')]),
        'cases_with_pinecone_id': len([c for c in supabase_cases if c.get('pinecone_id')]),
        'cases_with_neo4j_id': len([c for c in supabase_cases if c.get('neo4j_node_id')]),
        'total_vectors': sum([c.get('word_count', 0) for c in supabase_cases if c.get('word_count')]),
        'avg_vectors_per_case': sum([c.get('word_count', 0) for c in supabase_cases if c.get('word_count')]) / max(len([c for c in supabase_cases if c.get('word_count')]), 1)
    }
    
    # Count objects in mock GCS
    validation_results['gcs'] = {
        'count': gcs_client.get_object_count()
    }
    
    # Count vectors in mock Pinecone
    validation_results['pinecone'] = {
        'count': pinecone_client.get_vector_count()
    }
    
    # Count nodes in mock Neo4j
    validation_results['neo4j'] = {
        'count': neo4j_client.get_node_count()
    }
    
    # Calculate consistency scores
    expected_cases = supabase_count
    validation_results['consistency'] = {
        'supabase_gcs': validation_results['supabase']['cases_with_gcs_path'] / max(expected_cases, 1),
        'supabase_pinecone': validation_results['supabase']['cases_with_pinecone_id'] / max(expected_cases, 1),
        'supabase_neo4j': validation_results['supabase']['cases_with_neo4j_id'] / max(expected_cases, 1),
        'vector_ratio': validation_results['pinecone']['count'] / max(expected_cases, 1)
    }
    
    logger.info("✅ Validation complete")
    return validation_results


def generate_test_report(processing_results, validation_results, duration):
    """Generate comprehensive test report"""
    
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'duration': str(duration),
        'processing_results': processing_results,
        'validation_results': validation_results,
        'success_criteria': {},
        'recommendations': []
    }
    
    # Evaluate success criteria
    supabase_count = validation_results['supabase']['count']
    consistency = validation_results['consistency']
    
    report['success_criteria'] = {
        'cases_processed': supabase_count >= 400,  # At least 400 of 500 cases
        'gcs_consistency': consistency['supabase_gcs'] > 0.95,  # 95%+ have GCS paths
        'pinecone_consistency': consistency['supabase_pinecone'] > 0.95,  # 95%+ have vectors
        'neo4j_consistency': consistency['supabase_neo4j'] > 0.95,  # 95%+ have nodes
        'vector_ratio_reasonable': 2.0 <= consistency['vector_ratio'] <= 6.0  # 2-6 vectors per case
    }
    
    # Generate recommendations
    all_criteria_met = all(report['success_criteria'].values())
    
    if all_criteria_met:
        report['recommendations'] = [
            "✅ All success criteria met!",
            "🚀 Ready to proceed with full 20K batch processing",
            "🧹 Clean up test data and start production run",
            "📊 Monitor first few production batches closely"
        ]
    else:
        report['recommendations'] = [
            "⚠️ Some success criteria not met",
            "🔧 Review failed criteria and fix issues",
            "🧪 Re-run test after fixes",
            "❌ Do not proceed to production until all criteria pass"
        ]
    
    # Print report
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE 500-CASE TEST REPORT")
    print("=" * 80)
    print(f"⏱️ Duration: {duration}")
    print(f"📄 Cases Processed: {supabase_count}")
    print(f"🗂️ GCS Objects: {validation_results['gcs']['count']}")
    print(f"🔍 Pinecone Vectors: {validation_results['pinecone']['count']}")
    print(f"🕸️ Neo4j Nodes: {validation_results['neo4j']['count']}")
    print(f"📈 Avg Vectors/Case: {validation_results['supabase']['avg_vectors_per_case']:.1f}")
    
    print("\n🎯 SUCCESS CRITERIA:")
    for criterion, passed in report['success_criteria'].items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {criterion}: {status}")
    
    print("\n💡 RECOMMENDATIONS:")
    for rec in report['recommendations']:
        print(f"   {rec}")
    
    print("=" * 80)
    
    return report


async def main():
    """Main entry point"""
    load_dotenv()
    
    try:
        test_report = await run_500_case_test()
        
        # Save report to file
        import json
        report_file = f"test_500_cases_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(test_report, f, indent=2, default=str)
        
        logger.info(f"📄 Test report saved to: {report_file}")
        
        # Return success code based on criteria
        all_passed = all(test_report['success_criteria'].values())
        return 0 if all_passed else 1
        
    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
