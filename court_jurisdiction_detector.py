#!/usr/bin/env python3
"""
Court Jurisdiction Detector
Determines jurisdiction based on the DECIDING COURT, not just text mentions
"""

import os
import gzip
import json
import re
from pathlib import Path
from collections import defaultdict

class CourtJurisdictionDetector:
    """Detects jurisdiction based on the actual deciding court."""
    
    def __init__(self):
        """Initialize with court-specific patterns."""
        
        # Court patterns that indicate the deciding jurisdiction
        # These look for the actual court that made the decision
        self.court_jurisdiction_patterns = {
            'alabama': [
                r'supreme court of alabama',
                r'alabama supreme court',
                r'court of.*appeals.*alabama',
                r'alabama.*court of.*appeals',
                r'circuit court.*alabama',
                r'alabama.*circuit court'
            ],
            'alaska': [
                r'supreme court of alaska',
                r'alaska supreme court',
                r'court of.*appeals.*alaska',
                r'alaska.*court of.*appeals'
            ],
            'arizona': [
                r'supreme court of arizona',
                r'arizona supreme court',
                r'court of.*appeals.*arizona',
                r'arizona.*court of.*appeals'
            ],
            'arkansas': [
                r'supreme court of arkansas',
                r'arkansas supreme court',
                r'court of.*appeals.*arkansas',
                r'arkansas.*court of.*appeals'
            ],
            'california': [
                r'supreme court of california',
                r'california supreme court',
                r'court of.*appeal.*california',
                r'california.*court of.*appeal',
                r'superior court.*california',
                r'california.*superior court'
            ],
            'colorado': [
                r'supreme court of colorado',
                r'colorado supreme court',
                r'court of.*appeals.*colorado',
                r'colorado.*court of.*appeals'
            ],
            'connecticut': [
                r'supreme court.*connecticut',
                r'connecticut supreme court',
                r'appellate court.*connecticut',
                r'connecticut.*appellate court'
            ],
            'delaware': [
                r'supreme court.*delaware',
                r'delaware supreme court',
                r'superior court.*delaware',
                r'delaware.*superior court'
            ],
            'florida': [
                r'supreme court of florida',
                r'florida supreme court',
                r'district court of appeal.*florida',
                r'florida.*district court of appeal'
            ],
            'georgia': [
                r'supreme court of georgia',
                r'georgia supreme court',
                r'court of appeals of georgia',
                r'georgia.*court of appeals'
            ],
            'hawaii': [
                r'supreme court.*hawaii',
                r'hawaii.*supreme court',
                r'intermediate court of appeals.*hawaii'
            ],
            'idaho': [
                r'supreme court.*idaho',
                r'idaho.*supreme court',
                r'court of appeals.*idaho',
                r'idaho.*court of appeals'
            ],
            'illinois': [
                r'supreme court.*illinois',
                r'illinois.*supreme court',
                r'appellate court.*illinois',
                r'illinois.*appellate court'
            ],
            'indiana': [
                r'supreme court.*indiana',
                r'indiana.*supreme court',
                r'court of appeals.*indiana',
                r'indiana.*court of appeals'
            ],
            'iowa': [
                r'supreme court.*iowa',
                r'iowa.*supreme court',
                r'court of appeals.*iowa',
                r'iowa.*court of appeals'
            ],
            'kansas': [
                r'supreme court.*kansas',
                r'kansas.*supreme court',
                r'court of appeals.*kansas',
                r'kansas.*court of appeals'
            ],
            'kentucky': [
                r'supreme court.*kentucky',
                r'kentucky.*supreme court',
                r'court of appeals.*kentucky',
                r'kentucky.*court of appeals'
            ],
            'louisiana': [
                r'supreme court.*louisiana',
                r'louisiana.*supreme court',
                r'court of appeal.*louisiana',
                r'louisiana.*court of appeal'
            ],
            'maine': [
                r'supreme.*court.*maine',
                r'maine.*supreme.*court',
                r'superior court.*maine',
                r'maine.*superior court'
            ],
            'maryland': [
                r'court of appeals.*maryland',
                r'maryland.*court of appeals',
                r'court of special appeals.*maryland'
            ],
            'massachusetts': [
                r'supreme.*court.*massachusetts',
                r'massachusetts.*supreme.*court',
                r'appeals court.*massachusetts',
                r'massachusetts.*appeals court'
            ],
            'michigan': [
                r'supreme court.*michigan',
                r'michigan.*supreme court',
                r'court of appeals.*michigan',
                r'michigan.*court of appeals'
            ],
            'minnesota': [
                r'supreme court.*minnesota',
                r'minnesota.*supreme court',
                r'court of appeals.*minnesota',
                r'minnesota.*court of appeals'
            ],
            'mississippi': [
                r'supreme court.*mississippi',
                r'mississippi.*supreme court',
                r'court of appeals.*mississippi',
                r'mississippi.*court of appeals'
            ],
            'missouri': [
                r'supreme court.*missouri',
                r'missouri.*supreme court',
                r'court of appeals.*missouri',
                r'missouri.*court of appeals'
            ],
            'montana': [
                r'supreme court.*montana',
                r'montana.*supreme court'
            ],
            'nebraska': [
                r'supreme court.*nebraska',
                r'nebraska.*supreme court',
                r'court of appeals.*nebraska',
                r'nebraska.*court of appeals'
            ],
            'nevada': [
                r'supreme court.*nevada',
                r'nevada.*supreme court'
            ],
            'new_hampshire': [
                r'supreme court.*new hampshire',
                r'new hampshire.*supreme court'
            ],
            'new_jersey': [
                r'supreme court.*new jersey',
                r'new jersey.*supreme court',
                r'appellate division.*new jersey',
                r'superior court.*new jersey'
            ],
            'new_mexico': [
                r'supreme court.*new mexico',
                r'new mexico.*supreme court',
                r'court of appeals.*new mexico',
                r'new mexico.*court of appeals'
            ],
            'new_york': [
                r'court of appeals.*new york',
                r'new york.*court of appeals',
                r'appellate division.*new york',
                r'supreme court.*new york',  # Note: NY Supreme Court is trial level
                r'new york.*supreme court'
            ],
            'north_carolina': [
                r'supreme court.*north carolina',
                r'north carolina.*supreme court',
                r'court of appeals.*north carolina',
                r'north carolina.*court of appeals'
            ],
            'north_dakota': [
                r'supreme court.*north dakota',
                r'north dakota.*supreme court'
            ],
            'ohio': [
                r'supreme court.*ohio',
                r'ohio.*supreme court',
                r'court of appeals.*ohio',
                r'ohio.*court of appeals'
            ],
            'oklahoma': [
                r'supreme court.*oklahoma',
                r'oklahoma.*supreme court',
                r'court of.*appeals.*oklahoma',
                r'oklahoma.*court of.*appeals'
            ],
            'oregon': [
                r'supreme court.*oregon',
                r'oregon.*supreme court',
                r'court of appeals.*oregon',
                r'oregon.*court of appeals'
            ],
            'pennsylvania': [
                r'supreme court.*pennsylvania',
                r'pennsylvania.*supreme court',
                r'superior court.*pennsylvania',
                r'pennsylvania.*superior court',
                r'commonwealth court.*pennsylvania'
            ],
            'rhode_island': [
                r'supreme court.*rhode island',
                r'rhode island.*supreme court'
            ],
            'south_carolina': [
                r'supreme court.*south carolina',
                r'south carolina.*supreme court',
                r'court of appeals.*south carolina',
                r'south carolina.*court of appeals'
            ],
            'south_dakota': [
                r'supreme court.*south dakota',
                r'south dakota.*supreme court'
            ],
            'tennessee': [
                r'supreme court.*tennessee',
                r'tennessee.*supreme court',
                r'court of appeals.*tennessee',
                r'tennessee.*court of appeals'
            ],
            'texas': [
                r'supreme court of texas',
                r'texas.*supreme court',
                r'court of.*appeals.*texas',
                r'texas.*court of.*appeals',
                r'court of civil appeals.*texas',
                r'texas.*court of civil appeals',
                r'district court.*texas',
                r'texas.*district court'
            ],
            'utah': [
                r'supreme court.*utah',
                r'utah.*supreme court',
                r'court of appeals.*utah',
                r'utah.*court of appeals'
            ],
            'vermont': [
                r'supreme court.*vermont',
                r'vermont.*supreme court'
            ],
            'virginia': [
                r'supreme court.*virginia',
                r'virginia.*supreme court',
                r'court of appeals.*virginia',
                r'virginia.*court of appeals'
            ],
            'washington': [
                r'supreme court.*washington',
                r'washington.*supreme court',
                r'court of appeals.*washington',
                r'washington.*court of appeals'
            ],
            'west_virginia': [
                r'supreme court.*west virginia',
                r'west virginia.*supreme court'
            ],
            'wisconsin': [
                r'supreme court.*wisconsin',
                r'wisconsin.*supreme court',
                r'court of appeals.*wisconsin',
                r'wisconsin.*court of appeals'
            ],
            'wyoming': [
                r'supreme court.*wyoming',
                r'wyoming.*supreme court'
            ],
            'federal': [
                r'supreme court of the united states',
                r'united states supreme court',
                r'u\.s\. supreme court',
                r'united states court of appeals',
                r'u\.s\. court of appeals',
                r'united states district court',
                r'u\.s\. district court',
                r'federal circuit',
                r'court of appeals for the.*circuit',
                r'district court for the.*district'
            ],
            'district_of_columbia': [
                r'court of appeals.*district of columbia',
                r'd\.c\. circuit',
                r'district of columbia.*court',
                r'superior court.*district of columbia'
            ]
        }
        
        # Compile patterns for efficiency
        self.compiled_court_patterns = {}
        for jurisdiction, patterns in self.court_jurisdiction_patterns.items():
            combined_pattern = '|'.join(f'({pattern})' for pattern in patterns)
            self.compiled_court_patterns[jurisdiction] = re.compile(combined_pattern, re.IGNORECASE)
    
    def detect_deciding_court_jurisdiction(self, case_data):
        """Detect jurisdiction based on the deciding court."""
        
        text = case_data.get('text', '').lower()
        
        # Look for court identification patterns
        # Usually appears early in the case text
        text_start = text[:2000]  # Focus on first 2000 characters where court info usually appears
        
        detected_jurisdictions = []
        
        for jurisdiction, pattern in self.compiled_court_patterns.items():
            if pattern.search(text_start):
                detected_jurisdictions.append(jurisdiction)
        
        # Return the first (most specific) match
        if detected_jurisdictions:
            # Prioritize state courts over federal when both are detected
            if len(detected_jurisdictions) > 1 and 'federal' in detected_jurisdictions:
                non_federal = [j for j in detected_jurisdictions if j != 'federal']
                if non_federal:
                    return non_federal[0]
            return detected_jurisdictions[0]
        
        return 'unclassified'

def test_court_jurisdiction_detection():
    """Test the court jurisdiction detection on sample cases."""
    
    print("🧪 TESTING COURT JURISDICTION DETECTION")
    print("=" * 50)
    
    detector = CourtJurisdictionDetector()
    
    # Test cases with known court jurisdictions
    test_cases = [
        {
            'text': 'Supreme Court of Texas. This case involves...',
            'expected': 'texas'
        },
        {
            'text': 'Court of Civil Appeals of Texas. Galveston. The plaintiff...',
            'expected': 'texas'
        },
        {
            'text': 'United States Supreme Court. We granted certiorari...',
            'expected': 'federal'
        },
        {
            'text': 'Court of Appeals of New York. This appeal...',
            'expected': 'new_york'
        },
        {
            'text': 'Supreme Court of California. The issue before us...',
            'expected': 'california'
        }
    ]
    
    correct = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        case_data = {'text': test_case['text']}
        detected = detector.detect_deciding_court_jurisdiction(case_data)
        expected = test_case['expected']
        
        is_correct = detected == expected
        if is_correct:
            correct += 1
        
        print(f"Test {i}: {'✅' if is_correct else '❌'}")
        print(f"  Text: {test_case['text'][:50]}...")
        print(f"  Expected: {expected}")
        print(f"  Detected: {detected}")
        print()
    
    accuracy = (correct / total) * 100
    print(f"📊 Test Results: {correct}/{total} correct ({accuracy:.1f}%)")
    
    if accuracy >= 80:
        print("✅ Court jurisdiction detection is working well!")
    else:
        print("⚠️ Court jurisdiction detection needs improvement")
    
    return accuracy >= 80

if __name__ == "__main__":
    success = test_court_jurisdiction_detection()
    
    if success:
        print("\n🎯 Ready to verify on actual CAP files!")
    else:
        print("\n🔧 Need to improve detection patterns first")
