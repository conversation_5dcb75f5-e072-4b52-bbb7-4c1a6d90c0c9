#!/usr/bin/env python3
"""
Debug CourtListener People Endpoint
Figure out why names show as "Unknown" and how to properly access judge data
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def debug_people_endpoint():
    """Debug the People endpoint to understand proper usage"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🔍 DEBUGGING COURTLISTENER PEOPLE ENDPOINT")
    print("=" * 60)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # 1. Try different approaches to get people data
        print("\n👤 1. TRYING DIFFERENT PEOPLE ENDPOINT APPROACHES")
        print("-" * 50)
        
        approaches = [
            ("Basic people query", {"page_size": 3, "format": "json"}),
            ("With name fields", {"page_size": 3, "format": "json", "name_first__isnull": False}),
            ("Recent people", {"page_size": 3, "format": "json", "ordering": "-date_created"}),
            ("Judges only", {"page_size": 3, "format": "json", "positions__position_type": "jud"}),
            ("Federal judges", {"page_size": 3, "format": "json", "positions__court__jurisdiction": "F"}),
        ]
        
        for approach_name, params in approaches:
            print(f"\n🔍 {approach_name}:")
            try:
                people_url = f"{base_url}/people/"
                response = await client.get(people_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                people = data.get('results', [])
                
                print(f"   Found {len(people)} people")
                
                for person in people[:2]:  # Show first 2
                    name_full = person.get('name_full', 'NO_NAME_FULL')
                    name_first = person.get('name_first', 'NO_FIRST')
                    name_last = person.get('name_last', 'NO_LAST')
                    
                    print(f"   Person {person.get('id')}:")
                    print(f"     name_full: '{name_full}'")
                    print(f"     name_first: '{name_first}'")
                    print(f"     name_last: '{name_last}'")
                    
                    # Check positions
                    positions = person.get('positions', [])
                    if positions:
                        pos_url = positions[0] if isinstance(positions[0], str) else None
                        if pos_url:
                            print(f"     position_url: {pos_url}")
                            # Try to fetch position details
                            try:
                                pos_response = await client.get(pos_url)
                                if pos_response.status_code == 200:
                                    pos_data = pos_response.json()
                                    print(f"     position_type: {pos_data.get('position_type', 'Unknown')}")
                                    court = pos_data.get('court', {})
                                    if isinstance(court, dict):
                                        print(f"     court: {court.get('short_name', 'Unknown')}")
                            except Exception as e:
                                print(f"     ❌ Position fetch error: {e}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # 2. Try to find a specific well-known judge
        print(f"\n👨‍⚖️2. SEARCHING FOR WELL-KNOWN JUDGES")
        print("-" * 50)
        
        # Try searching for famous judges
        famous_judges = [
            "Roberts",  # Chief Justice John Roberts
            "Thomas",   # Justice Clarence Thomas
            "Ginsburg", # Justice Ruth Bader Ginsburg
            "Scalia",   # Justice Antonin Scalia
        ]
        
        for judge_name in famous_judges:
            print(f"\n🔍 Searching for '{judge_name}':")
            try:
                search_params = {
                    "name_last__icontains": judge_name,
                    "page_size": 5,
                    "format": "json"
                }
                
                response = await client.get(f"{base_url}/people/", params=search_params)
                response.raise_for_status()
                
                data = response.json()
                people = data.get('results', [])
                
                print(f"   Found {len(people)} matches")
                
                for person in people:
                    name_full = person.get('name_full', 'NO_NAME')
                    name_first = person.get('name_first', '')
                    name_last = person.get('name_last', '')
                    
                    print(f"   {person.get('id')}: '{name_full}' (first: '{name_first}', last: '{name_last}')")
                    
            except Exception as e:
                print(f"   ❌ Error searching for {judge_name}: {e}")
        
        # 3. Try to understand the relationship between opinions and people
        print(f"\n🔗 3. OPINION-PEOPLE RELATIONSHIP")
        print("-" * 50)
        
        try:
            # Get an opinion and see if we can trace it to a person
            opinions_url = f"{base_url}/opinions/"
            params = {
                'court': 'scotus',  # Supreme Court more likely to have author data
                'page_size': 5,
                'format': 'json',
                'author__isnull': False  # Only opinions with authors
            }
            
            response = await client.get(opinions_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            opinions = data.get('results', [])
            
            print(f"Found {len(opinions)} opinions with authors")
            
            for opinion in opinions:
                author_url = opinion.get('author')
                author_str = opinion.get('author_str', '')
                
                print(f"\nOpinion {opinion.get('id')}:")
                print(f"  author_str: '{author_str}'")
                print(f"  author_url: {author_url}")
                
                if author_url:
                    try:
                        author_response = await client.get(author_url)
                        if author_response.status_code == 200:
                            author_data = author_response.json()
                            name_full = author_data.get('name_full', 'NO_NAME')
                            print(f"  ✅ Author name: '{name_full}'")
                        else:
                            print(f"  ❌ Author fetch failed: {author_response.status_code}")
                    except Exception as e:
                        print(f"  ❌ Author fetch error: {e}")
                        
        except Exception as e:
            print(f"❌ Error exploring opinion-people relationship: {e}")
        
        # 4. Check if there are any permissions or special parameters needed
        print(f"\n🔐 4. CHECKING API PERMISSIONS AND PARAMETERS")
        print("-" * 50)
        
        try:
            # Try to get API info/schema
            info_endpoints = [
                f"{base_url}/",
                f"{base_url}/people/schema/",
                f"{base_url}/people/?format=api",
            ]
            
            for endpoint in info_endpoints:
                print(f"\n🔍 Checking {endpoint}:")
                try:
                    response = await client.get(endpoint)
                    print(f"   Status: {response.status_code}")
                    if response.status_code == 200:
                        content = response.text[:500]  # First 500 chars
                        print(f"   Content preview: {content}...")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    
        except Exception as e:
            print(f"❌ Error checking permissions: {e}")

if __name__ == "__main__":
    asyncio.run(debug_people_endpoint())
