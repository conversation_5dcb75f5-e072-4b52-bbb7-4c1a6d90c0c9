#!/usr/bin/env python3
"""
Texas Phase 1 Real Data Processor

Processes actual Caselaw Access Project data for Texas cases
focusing on Criminal Defense + Personal Injury + Medical Malpractice.
"""

import asyncio
import logging
import os
import sys
import time
import gzip
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('texas_phase1_real_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TexasRealDataProcessor:
    """Processes real Caselaw Access Project data for Texas Phase 1."""
    
    def __init__(self):
        self.data_dir = Path("data/caselaw_access_project")
        self.filter_engine = TexasPhase1Filter()
        
        self.stats = {
            'files_processed': 0,
            'total_cases_found': 0,
            'texas_cases_found': 0,
            'phase1_cases_filtered': 0,
            'criminal_defense': 0,
            'personal_injury': 0,
            'medical_malpractice': 0,
            'processing_time': 0.0
        }
        
        # Load environment
        self.load_environment()
    
    def load_environment(self):
        """Load and validate environment variables."""
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'SUPABASE_URL', 'SUPABASE_KEY', 'PINECONE_API_KEY',
            'NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD',
            'VOYAGE_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables loaded successfully")
    
    def discover_cap_files(self) -> List[Path]:
        """Discover all CAP data files."""
        
        if not self.data_dir.exists():
            raise FileNotFoundError(f"Data directory not found: {self.data_dir}")
        
        # Find all .jsonl.gz files
        cap_files = list(self.data_dir.glob("cap_*.jsonl.gz"))
        
        if not cap_files:
            raise FileNotFoundError(f"No CAP files found in {self.data_dir}")
        
        # Sort files for consistent processing order
        cap_files.sort()
        
        logger.info(f"Found {len(cap_files)} CAP data files")
        return cap_files
    
    def extract_case_data(self, raw_case: Dict) -> Optional[Dict]:
        """Extract relevant case data from CAP format."""
        
        try:
            # Extract basic case information
            case_id = raw_case.get('id')
            if not case_id:
                return None
            
            # Get case name
            case_name = raw_case.get('name', '').strip()
            if not case_name:
                case_name = raw_case.get('name_abbreviation', '').strip()
            
            # Get court information
            court_info = raw_case.get('court', {})
            court_name = court_info.get('name', '') if isinstance(court_info, dict) else str(court_info)
            
            # Get jurisdiction
            jurisdiction_info = raw_case.get('jurisdiction', {})
            if isinstance(jurisdiction_info, dict):
                jurisdiction = jurisdiction_info.get('name', '').lower()
                jurisdiction_slug = jurisdiction_info.get('name_long', '').lower()
            else:
                jurisdiction = str(jurisdiction_info).lower()
                jurisdiction_slug = jurisdiction
            
            # Check if this is a Texas case (state court OR federal case involving Texas)
            is_texas = False

            # Check jurisdiction
            if any(texas_indicator in jurisdiction.lower() or texas_indicator in jurisdiction_slug.lower()
                   for texas_indicator in ['texas', 'tex.', 'tx']):
                is_texas = True

            # Check court name for Texas references
            if not is_texas and court_name:
                if any(texas_indicator in court_name.lower()
                       for texas_indicator in ['texas', 'tex.', 'tx', 'dallas', 'houston', 'austin', 'san antonio', 'harris county', 'travis county']):
                    is_texas = True

            # Check case text for Texas references (first 500 chars for performance)
            if not is_texas and case_text:
                text_sample = case_text[:500].lower()
                if any(texas_indicator in text_sample
                       for texas_indicator in ['texas', 'tex.', 'dallas', 'houston', 'austin', 'san antonio', 'harris county']):
                    is_texas = True

            if not is_texas:
                return None
            
            # Get case text - initialize first
            case_text = ''

            # Try different text fields
            if 'casebody' in raw_case:
                casebody = raw_case['casebody']
                if isinstance(casebody, dict):
                    if 'data' in casebody:
                        case_text = self._extract_text_from_casebody(casebody['data'])
                    elif 'text' in casebody:
                        case_text = str(casebody['text'])

            # Fallback to other text fields
            if not case_text:
                for field in ['text', 'body_cache', 'html']:
                    if field in raw_case and raw_case[field]:
                        case_text = str(raw_case[field])
                        break

            # Ensure case_text is always a string
            if not case_text:
                case_text = ''
            
            # Get date information
            decision_date = raw_case.get('decision_date', '')
            
            # Create standardized case document
            case_doc = {
                'id': f'cap_{case_id}',
                'case_name': case_name,
                'text': case_text,
                'court': court_name,
                'jurisdiction': 'texas',
                'date_filed': decision_date,
                'original_id': case_id,
                'source': 'caselaw_access_project',
                'raw_jurisdiction': jurisdiction
            }
            
            return case_doc
            
        except Exception as e:
            logger.warning(f"Error extracting case data: {e}")
            return None
    
    def _extract_text_from_casebody(self, casebody_data) -> str:
        """Extract text from casebody data structure."""
        
        if isinstance(casebody_data, str):
            return casebody_data
        
        if isinstance(casebody_data, dict):
            # Try common text fields
            for field in ['text', 'html', 'content']:
                if field in casebody_data:
                    return str(casebody_data[field])
            
            # If it's structured data, try to extract text from opinions
            if 'opinions' in casebody_data:
                opinions = casebody_data['opinions']
                if isinstance(opinions, list) and opinions:
                    opinion_texts = []
                    for opinion in opinions:
                        if isinstance(opinion, dict) and 'text' in opinion:
                            opinion_texts.append(str(opinion['text']))
                    return ' '.join(opinion_texts)
        
        if isinstance(casebody_data, list):
            # If it's a list, try to extract text from each item
            text_parts = []
            for item in casebody_data:
                if isinstance(item, dict) and 'text' in item:
                    text_parts.append(str(item['text']))
                elif isinstance(item, str):
                    text_parts.append(item)
            return ' '.join(text_parts)
        
        return str(casebody_data)
    
    def process_cap_file(self, file_path: Path) -> Dict[str, Any]:
        """Process a single CAP file."""
        
        logger.info(f"Processing file: {file_path.name}")
        
        start_time = time.time()
        file_stats = {
            'file_name': file_path.name,
            'total_cases': 0,
            'texas_cases': 0,
            'phase1_cases': 0,
            'practice_areas': {
                'criminal_defense': 0,
                'personal_injury': 0,
                'medical_malpractice': 0
            },
            'processing_time': 0.0,
            'errors': 0
        }
        
        texas_cases = []
        
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if not line.strip():
                        continue
                    
                    try:
                        # Parse JSON line
                        raw_case = json.loads(line)
                        file_stats['total_cases'] += 1
                        
                        # Extract case data
                        case_doc = self.extract_case_data(raw_case)
                        
                        if case_doc:
                            file_stats['texas_cases'] += 1
                            texas_cases.append(case_doc)
                        
                        # Process in batches to avoid memory issues
                        if len(texas_cases) >= 1000:
                            batch_results = self._process_texas_cases_batch(texas_cases)
                            self._update_file_stats(file_stats, batch_results)
                            texas_cases = []  # Clear batch
                        
                    except json.JSONDecodeError as e:
                        file_stats['errors'] += 1
                        if file_stats['errors'] <= 5:  # Log first few errors
                            logger.warning(f"JSON decode error in {file_path.name}:{line_num}: {e}")
                    
                    except Exception as e:
                        file_stats['errors'] += 1
                        if file_stats['errors'] <= 5:
                            logger.warning(f"Processing error in {file_path.name}:{line_num}: {e}")
                
                # Process remaining cases
                if texas_cases:
                    batch_results = self._process_texas_cases_batch(texas_cases)
                    self._update_file_stats(file_stats, batch_results)
        
        except Exception as e:
            logger.error(f"Error processing file {file_path.name}: {e}")
            file_stats['file_error'] = str(e)
        
        file_stats['processing_time'] = time.time() - start_time
        
        logger.info(f"✅ Completed {file_path.name}: "
                   f"{file_stats['texas_cases']} Texas cases, "
                   f"{file_stats['phase1_cases']} Phase 1 cases, "
                   f"{file_stats['processing_time']:.1f}s")
        
        return file_stats
    
    def _process_texas_cases_batch(self, texas_cases: List[Dict]) -> Dict[str, Any]:
        """Process a batch of Texas cases through the Phase 1 filter."""
        
        if not texas_cases:
            return {'filtered_cases': [], 'batch_stats': {}}
        
        # Apply Phase 1 filtering
        filtered_cases, batch_stats = self.filter_engine.batch_filter_documents(texas_cases)
        
        return {
            'filtered_cases': filtered_cases,
            'batch_stats': batch_stats
        }
    
    def _update_file_stats(self, file_stats: Dict, batch_results: Dict):
        """Update file statistics with batch results."""
        
        batch_stats = batch_results.get('batch_stats', {})
        
        file_stats['phase1_cases'] += len(batch_results.get('filtered_cases', []))
        file_stats['practice_areas']['criminal_defense'] += batch_stats.get('criminal_defense', 0)
        file_stats['practice_areas']['personal_injury'] += batch_stats.get('personal_injury', 0)
        file_stats['practice_areas']['medical_malpractice'] += batch_stats.get('medical_malpractice', 0)
    
    async def process_all_cap_files(self, max_files: Optional[int] = None) -> Dict[str, Any]:
        """Process all CAP files for Texas Phase 1 cases."""
        
        logger.info("🚀 Starting Texas Phase 1 Real Data Processing")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Discover CAP files
        cap_files = self.discover_cap_files()
        
        if max_files:
            cap_files = cap_files[:max_files]
            logger.info(f"Limited to first {max_files} files for testing")
        
        logger.info(f"Processing {len(cap_files)} CAP files...")
        
        # Process files with limited concurrency
        max_concurrent = min(4, len(cap_files))  # Don't overwhelm the system
        
        all_file_results = []
        
        # Process files in batches
        with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # Submit all file processing tasks
            future_to_file = {
                executor.submit(self.process_cap_file, file_path): file_path 
                for file_path in cap_files
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    file_result = future.result()
                    all_file_results.append(file_result)
                    
                    # Update overall stats
                    self.stats['files_processed'] += 1
                    self.stats['total_cases_found'] += file_result['total_cases']
                    self.stats['texas_cases_found'] += file_result['texas_cases']
                    self.stats['phase1_cases_filtered'] += file_result['phase1_cases']
                    self.stats['criminal_defense'] += file_result['practice_areas']['criminal_defense']
                    self.stats['personal_injury'] += file_result['practice_areas']['personal_injury']
                    self.stats['medical_malpractice'] += file_result['practice_areas']['medical_malpractice']
                    
                    # Progress update
                    progress = (self.stats['files_processed'] / len(cap_files)) * 100
                    logger.info(f"📊 Progress: {progress:.1f}% "
                               f"({self.stats['files_processed']}/{len(cap_files)} files) - "
                               f"Found {self.stats['phase1_cases_filtered']} Phase 1 cases so far")
                
                except Exception as e:
                    logger.error(f"File processing failed for {file_path}: {e}")
        
        end_time = time.time()
        self.stats['processing_time'] = end_time - start_time
        
        # Compile final results
        final_results = {
            'processing_completed': True,
            'files_processed': self.stats['files_processed'],
            'total_cases_in_dataset': self.stats['total_cases_found'],
            'texas_cases_found': self.stats['texas_cases_found'],
            'phase1_cases_filtered': self.stats['phase1_cases_filtered'],
            'practice_area_breakdown': {
                'criminal_defense': self.stats['criminal_defense'],
                'personal_injury': self.stats['personal_injury'],
                'medical_malpractice': self.stats['medical_malpractice']
            },
            'processing_time_minutes': self.stats['processing_time'] / 60,
            'throughput_cases_per_minute': self.stats['total_cases_found'] / (self.stats['processing_time'] / 60) if self.stats['processing_time'] > 0 else 0,
            'texas_case_percentage': (self.stats['texas_cases_found'] / self.stats['total_cases_found'] * 100) if self.stats['total_cases_found'] > 0 else 0,
            'phase1_filter_rate': (self.stats['phase1_cases_filtered'] / self.stats['texas_cases_found'] * 100) if self.stats['texas_cases_found'] > 0 else 0,
            'file_results': all_file_results
        }
        
        logger.info("🎉 TEXAS PHASE 1 REAL DATA PROCESSING COMPLETED!")
        logger.info("=" * 60)
        logger.info(f"📊 Final Results:")
        logger.info(f"   Files processed: {final_results['files_processed']}")
        logger.info(f"   Total cases in dataset: {final_results['total_cases_in_dataset']:,}")
        logger.info(f"   Texas cases found: {final_results['texas_cases_found']:,} ({final_results['texas_case_percentage']:.1f}%)")
        logger.info(f"   Phase 1 cases filtered: {final_results['phase1_cases_filtered']:,} ({final_results['phase1_filter_rate']:.1f}%)")
        logger.info(f"   Processing time: {final_results['processing_time_minutes']:.1f} minutes")
        logger.info(f"   Throughput: {final_results['throughput_cases_per_minute']:,.0f} cases/minute")
        
        logger.info(f"\n🏛️ Practice Area Breakdown:")
        for area, count in final_results['practice_area_breakdown'].items():
            percentage = (count / final_results['phase1_cases_filtered'] * 100) if final_results['phase1_cases_filtered'] > 0 else 0
            logger.info(f"   {area.replace('_', ' ').title()}: {count:,} ({percentage:.1f}%)")
        
        return final_results


async def main():
    """Main function to run real data processing."""
    
    print("🤠 TEXAS PHASE 1 REAL DATA PROCESSING")
    print("=" * 50)
    print("Processing actual Caselaw Access Project data")
    print("Filtering for Criminal Defense + Personal Injury + Medical Malpractice")
    print()
    
    # Ask user for processing scope
    print("Processing options:")
    print("1. Test run (first 3 files only)")
    print("2. Full processing (all CAP files)")
    
    choice = input("Select option (1 or 2): ").strip()
    
    max_files = 3 if choice == '1' else None
    
    if choice == '1':
        print("🧪 Running test with first 3 CAP files...")
    else:
        print("🚀 Running full processing on all CAP files...")
        confirm = input("This may take a while. Continue? (y/n): ").lower().strip()
        if confirm != 'y':
            print("Processing cancelled.")
            return False
    
    processor = TexasRealDataProcessor()
    
    try:
        results = await processor.process_all_cap_files(max_files=max_files)
        
        # Save results
        output_file = 'texas_phase1_real_data_results.json'
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: {output_file}")
        
        # Summary
        print(f"\n🎯 PROCESSING SUMMARY:")
        print(f"   Real Texas cases found: {results['texas_cases_found']:,}")
        print(f"   Phase 1 cases identified: {results['phase1_cases_filtered']:,}")
        print(f"   Processing time: {results['processing_time_minutes']:.1f} minutes")
        print(f"   Success rate: {results['phase1_filter_rate']:.1f}% of Texas cases matched Phase 1 criteria")
        
        if results['phase1_cases_filtered'] > 0:
            print(f"\n✅ READY FOR HIGH-SPEED PROCESSING!")
            print(f"   Found {results['phase1_cases_filtered']:,} real Texas cases for Phase 1")
            print(f"   Can now process with multi-cloud serverless pipeline")
        else:
            print(f"\n⚠️  No Phase 1 cases found. May need to adjust filtering criteria.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real data processing failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
