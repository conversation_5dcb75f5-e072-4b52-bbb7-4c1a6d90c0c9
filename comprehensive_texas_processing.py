#!/usr/bin/env python3
"""
Comprehensive Texas Case Processing
Removes artificial limits and processes maximum available cases from both sources
"""

import os
import sys
import logging
import asyncio
from typing import Dict, List
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def comprehensive_texas_processing():
    """
    Process maximum available Texas cases from both Court Listener and CAP.
    Removes artificial limits to get true case count.
    """
    
    print("🚀 COMPREHENSIVE TEXAS CASE PROCESSING")
    print("Removing artificial limits to process maximum available cases")
    print("=" * 70)
    
    # Import processors
    from dual_source_coordinator import DualSourceCoordinator
    
    # Initialize coordinator
    coordinator = DualSourceCoordinator()
    
    # Override default limits
    print("📈 REMOVING PROCESSING LIMITS:")
    print("- Court Listener: Removing 1,000 case limit")
    print("- CAP: Processing all available Texas files")
    print("- Batch size: Increased for efficiency")
    print()
    
    # Check available CAP data first
    cap_data_dir = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
    
    if os.path.exists(cap_data_dir):
        # Count available CAP files
        cap_files = []
        for root, dirs, files in os.walk(cap_data_dir):
            for file in files:
                if file.endswith('.json') or file.endswith('.html'):
                    cap_files.append(os.path.join(root, file))
        
        print(f"📁 Found {len(cap_files):,} CAP files available for processing")
    else:
        print(f"⚠️ CAP data directory not found: {cap_data_dir}")
        cap_files = []
    
    # Estimate Court Listener availability
    print("🔍 Estimating Court Listener data availability...")
    
    try:
        from src.processing.providers.court_listener import CourtListenerConnector
        cl_connector = CourtListenerConnector()
        
        # Get Texas court IDs
        texas_courts = cl_connector.jurisdiction_courts.get('tx', [])
        print(f"📋 Texas courts in Court Listener: {len(texas_courts)}")
        
        # Estimate total cases (this is a rough estimate)
        estimated_cl_cases = len(texas_courts) * 1000  # Conservative estimate
        print(f"📊 Estimated Court Listener cases: {estimated_cl_cases:,}")
        
    except Exception as e:
        print(f"⚠️ Could not estimate Court Listener data: {e}")
        estimated_cl_cases = 0
    
    print()
    print("🎯 PROCESSING STRATEGY:")
    print("1. Process ALL available CAP files (no limits)")
    print("2. Fetch maximum Court Listener cases (respecting API limits)")
    print("3. Use Gemini classification during full processing")
    print("4. Store complete case content for high-confidence classification")
    print()
    
    # Ask for confirmation
    total_estimated = len(cap_files) + estimated_cl_cases
    print(f"📈 ESTIMATED TOTAL: {total_estimated:,} cases")
    print("⚠️ This will take several hours and use significant API quota")
    
    response = input("Continue with comprehensive processing? (y/N): ")
    if response.lower() != 'y':
        print("❌ Processing cancelled")
        return
    
    print()
    print("🚀 STARTING COMPREHENSIVE PROCESSING...")
    
    # Process with removed limits
    try:
        # Override the coordinator's limits
        if hasattr(coordinator, 'court_listener_processor'):
            # Remove case limits
            coordinator.court_listener_processor.target_cases_per_state = 100000
            coordinator.court_listener_processor.batch_size = 1000
            print("✅ Court Listener limits removed")
        
        if hasattr(coordinator, 'cap_processor'):
            # Process all files
            coordinator.cap_processor.max_files_per_batch = 10000
            print("✅ CAP processing limits removed")
        
        # Run comprehensive processing
        results = await coordinator.process_jurisdiction_comprehensive('tx')
        
        print()
        print("🎉 COMPREHENSIVE PROCESSING COMPLETED!")
        print("=" * 50)
        print(f"Court Listener cases: {results.cl_cases_processed:,}")
        print(f"CAP cases: {results.cap_cases_processed:,}")
        print(f"Total unique cases: {results.unique_cases_total:,}")
        print(f"Practice area coverage: {len(results.practice_area_coverage)} areas")
        
        # Show practice area breakdown
        if results.practice_area_coverage:
            print("\nPractice Area Distribution:")
            for area, coverage in results.practice_area_coverage.items():
                total = coverage.get('total', 0)
                if total > 0:
                    print(f"  {area}: {total:,} cases")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in comprehensive processing: {e}")
        print(f"❌ Processing failed: {e}")
        return None

def verify_comprehensive_results():
    """Verify the results of comprehensive processing."""
    
    print("\n🔍 VERIFYING COMPREHENSIVE RESULTS")
    print("=" * 40)
    
    try:
        from supabase import create_client, Client
        
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        supabase: Client = create_client(url, key)
        
        # Get updated case count
        result = supabase.table('cases').select('*', count='exact').eq('jurisdiction', 'tx').execute()
        total_cases = result.count
        
        print(f"📊 Total Texas cases after processing: {total_cases:,}")
        
        # Check if this is a significant improvement
        if total_cases > 10000:
            print("✅ Excellent! Significant improvement in case coverage")
        elif total_cases > 5000:
            print("✅ Good improvement in case coverage")
        elif total_cases > 1000:
            print("⚠️ Some improvement, but may need further investigation")
        else:
            print("❌ No significant improvement - need to investigate data sources")
        
        # Check practice area distribution
        cases_data = supabase.table('cases').select('primary_practice_area').eq('jurisdiction', 'tx').execute()
        
        practice_areas = {}
        for case in cases_data.data:
            area = case.get('primary_practice_area', 'None')
            practice_areas[area] = practice_areas.get(area, 0) + 1
        
        print("\n📋 Practice Area Distribution:")
        for area, count in sorted(practice_areas.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_cases) * 100
            print(f"  {area}: {count:,} ({percentage:.1f}%)")
        
        return total_cases
        
    except Exception as e:
        print(f"❌ Error verifying results: {e}")
        return 0

async def main():
    """Main execution function."""
    
    # Run comprehensive processing
    results = await comprehensive_texas_processing()
    
    if results:
        # Verify results
        final_count = verify_comprehensive_results()
        
        print(f"\n🎯 FINAL SUMMARY:")
        print(f"Texas cases processed: {final_count:,}")
        
        if final_count > 10000:
            print("🎉 SUCCESS: Comprehensive processing achieved significant coverage!")
        else:
            print("⚠️ May need to investigate data source availability")
    else:
        print("❌ Comprehensive processing failed")

if __name__ == "__main__":
    asyncio.run(main())
