#!/usr/bin/env python3
"""
Rate-Limited Court Listener Processing Script

This script processes Court Listener data while respecting the API rate limit of 5,000 queries per hour.
It processes all jurisdictions but spreads the work over time to stay within limits.
"""

import os
import sys
import logging
import time
import math
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.courtlistener_bulk_client import CourtListenerBulkClient
from src.processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rate_limited_courtlistener.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class RateLimitedCourtListenerProcessor:
    """
    Processes Court Listener data while respecting API rate limits.
    
    Court Listener API Limits:
    - 5,000 queries per hour for authenticated users
    - ~83 queries per minute
    - 0.72 seconds minimum between requests
    """
    
    def __init__(self):
        """Initialize the rate-limited processor"""
        self.client = CourtListenerBulkClient()
        self.checkpoint_manager = CheckpointManager()

        # Rate limiting configuration
        self.queries_per_hour = 5000
        self.queries_per_minute = 83
        self.min_delay_seconds = 0.72

        # Processing statistics
        self.total_queries_made = 0
        self.start_time = datetime.now()
        self.jurisdictions_processed = 0
        self.total_cases_processed = 0

        logger.info("Initialized rate-limited Court Listener processor with checkpoint support")
        logger.info(f"Rate limits: {self.queries_per_hour} queries/hour, {self.queries_per_minute} queries/minute")
    
    def calculate_processing_time(self, total_jurisdictions: int, cases_per_jurisdiction: int) -> Dict[str, any]:
        """
        Calculate estimated processing time based on rate limits.
        
        Args:
            total_jurisdictions: Number of jurisdictions to process
            cases_per_jurisdiction: Cases to fetch per jurisdiction
            
        Returns:
            Time estimation details
        """
        # Estimate queries needed per jurisdiction
        # This is approximate - actual queries depend on search results and pagination
        estimated_queries_per_jurisdiction = max(10, cases_per_jurisdiction // 50)  # Rough estimate
        
        total_estimated_queries = total_jurisdictions * estimated_queries_per_jurisdiction
        
        # Calculate time needed
        hours_needed = total_estimated_queries / self.queries_per_hour
        
        return {
            "total_jurisdictions": total_jurisdictions,
            "cases_per_jurisdiction": cases_per_jurisdiction,
            "estimated_queries_per_jurisdiction": estimated_queries_per_jurisdiction,
            "total_estimated_queries": total_estimated_queries,
            "estimated_hours": hours_needed,
            "estimated_completion": datetime.now() + timedelta(hours=hours_needed)
        }
    
    def process_jurisdictions_in_batches(self,
                                       batch_size: int = 5,
                                       cases_per_jurisdiction: int = 1000,
                                       max_hours: Optional[float] = None,
                                       resume_checkpoint: str = None) -> Dict[str, any]:
        """
        Process jurisdictions in batches to respect rate limits.
        
        Args:
            batch_size: Number of jurisdictions to process per batch
            cases_per_jurisdiction: Cases to fetch per jurisdiction
            max_hours: Maximum hours to run (None for no limit)
            resume_checkpoint: Checkpoint ID to resume from

        Returns:
            Processing results
        """
        logger.info("="*80)
        logger.info("STARTING RATE-LIMITED COURT LISTENER PROCESSING")
        logger.info("="*80)
        
        # Get all jurisdictions
        all_jurisdictions = self.client.all_jurisdictions
        jurisdiction_items = list(all_jurisdictions.items())

        # Handle checkpoint resume
        checkpoint_id = resume_checkpoint
        if resume_checkpoint:
            logger.info(f"Resuming from checkpoint: {resume_checkpoint}")
            checkpoint = self.checkpoint_manager.get_checkpoint(resume_checkpoint)
            if checkpoint:
                # Filter out already processed jurisdictions
                processed_jurisdictions = set(checkpoint.completed_items + checkpoint.failed_items)
                jurisdiction_items = [(code, name) for code, name in jurisdiction_items
                                    if code not in processed_jurisdictions]
                logger.info(f"Resuming with {len(jurisdiction_items)} remaining jurisdictions")
            else:
                logger.warning(f"Checkpoint {resume_checkpoint} not found, starting fresh")
                checkpoint_id = None

        # Create new checkpoint if not resuming
        if not checkpoint_id:
            checkpoint_id = self.checkpoint_manager.create_checkpoint(
                process_type="court_listener_rate_limited",
                total_items=len(all_jurisdictions),
                metadata={
                    "batch_size": batch_size,
                    "cases_per_jurisdiction": cases_per_jurisdiction,
                    "max_hours": max_hours
                }
            )
        
        # Calculate time estimation
        time_estimate = self.calculate_processing_time(
            len(jurisdiction_items), cases_per_jurisdiction
        )
        
        logger.info(f"Processing plan:")
        logger.info(f"  Total jurisdictions: {time_estimate['total_jurisdictions']}")
        logger.info(f"  Cases per jurisdiction: {time_estimate['cases_per_jurisdiction']}")
        logger.info(f"  Estimated queries: {time_estimate['total_estimated_queries']:,}")
        logger.info(f"  Estimated time: {time_estimate['estimated_hours']:.1f} hours")
        logger.info(f"  Estimated completion: {time_estimate['estimated_completion']}")
        
        if max_hours and time_estimate['estimated_hours'] > max_hours:
            logger.warning(f"Estimated time ({time_estimate['estimated_hours']:.1f}h) exceeds limit ({max_hours}h)")
            logger.warning("Consider reducing cases_per_jurisdiction or increasing max_hours")
        
        # Process in batches
        results = {}
        batch_count = math.ceil(len(jurisdiction_items) / batch_size)
        
        for batch_num in range(batch_count):
            # Check time limit
            if max_hours:
                elapsed_hours = (datetime.now() - self.start_time).total_seconds() / 3600
                if elapsed_hours >= max_hours:
                    logger.warning(f"Time limit reached ({max_hours}h). Stopping processing.")
                    break
            
            # Get batch of jurisdictions
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(jurisdiction_items))
            batch_jurisdictions = jurisdiction_items[start_idx:end_idx]
            
            logger.info(f"\n" + "="*60)
            logger.info(f"BATCH {batch_num + 1}/{batch_count}")
            logger.info(f"Processing jurisdictions {start_idx + 1}-{end_idx}")
            logger.info("="*60)
            
            # Process each jurisdiction in the batch
            for i, (jurisdiction, jurisdiction_name) in enumerate(batch_jurisdictions, 1):
                logger.info(f"\n[{start_idx + i}/{len(all_jurisdictions)}] Processing {jurisdiction} ({jurisdiction_name})")
                logger.info("-" * 50)

                # Update checkpoint with current item
                self.checkpoint_manager.update_checkpoint(
                    checkpoint_id,
                    current_item=jurisdiction
                )

                try:
                    # Process this jurisdiction
                    jurisdiction_results = self.client.process_missing_jurisdictions(
                        cases_per_jurisdiction=cases_per_jurisdiction
                    )

                    if jurisdiction in jurisdiction_results:
                        result = jurisdiction_results[jurisdiction]
                        results[jurisdiction] = result
                        self.total_cases_processed += result.cases_fetched
                        self.jurisdictions_processed += 1

                        # Update checkpoint with completed item
                        self.checkpoint_manager.update_checkpoint(
                            checkpoint_id,
                            completed_item=jurisdiction,
                            metadata_update={"last_processed_cases": result.cases_fetched}
                        )

                        logger.info(f"✅ {jurisdiction} completed:")
                        logger.info(f"   Cases processed: {result.cases_fetched}")
                        logger.info(f"   Status: {result.status}")
                        if result.error_message:
                            logger.warning(f"   Warning: {result.error_message}")
                    else:
                        logger.error(f"❌ {jurisdiction} not found in results")
                        # Mark as failed in checkpoint
                        self.checkpoint_manager.update_checkpoint(
                            checkpoint_id,
                            failed_item=jurisdiction
                        )

                except Exception as e:
                    logger.error(f"❌ Failed to process {jurisdiction}: {str(e)}")
                    results[jurisdiction] = {
                        "status": "failed",
                        "error": str(e),
                        "cases_fetched": 0
                    }

                    # Mark as failed in checkpoint
                    self.checkpoint_manager.update_checkpoint(
                        checkpoint_id,
                        failed_item=jurisdiction,
                        metadata_update={"last_error": str(e)}
                    )
            
            # Inter-batch delay to manage rate limits
            if batch_num < batch_count - 1:  # Don't delay after the last batch
                delay_minutes = 2  # Small delay between batches
                logger.info(f"\nBatch {batch_num + 1} complete. Waiting {delay_minutes} minutes before next batch...")
                time.sleep(delay_minutes * 60)
        
        # Calculate final statistics
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        # Complete or update checkpoint
        if self.jurisdictions_processed == len(all_jurisdictions):
            self.checkpoint_manager.complete_checkpoint(checkpoint_id)
            logger.info("✅ All jurisdictions processed - checkpoint completed")
        else:
            self.checkpoint_manager.update_checkpoint(
                checkpoint_id,
                status='paused',
                metadata_update={
                    "partial_completion": True,
                    "processed_count": self.jurisdictions_processed,
                    "total_count": len(all_jurisdictions)
                }
            )
            logger.info(f"⏸️ Processing paused - checkpoint saved ({checkpoint_id})")

        summary = {
            "source": "court_listener_rate_limited",
            "checkpoint_id": checkpoint_id,
            "start_time": self.start_time,
            "end_time": end_time,
            "duration_seconds": duration,
            "duration_hours": duration / 3600,
            "total_jurisdictions": len(all_jurisdictions),
            "jurisdictions_processed": self.jurisdictions_processed,
            "total_cases_processed": self.total_cases_processed,
            "average_cases_per_jurisdiction": self.total_cases_processed / max(1, self.jurisdictions_processed),
            "processing_rate_jurisdictions_per_hour": self.jurisdictions_processed / max(0.1, duration / 3600),
            "time_estimate": time_estimate,
            "jurisdiction_results": results,
            "progress_summary": self.checkpoint_manager.get_progress_summary(checkpoint_id)
        }

        return summary
    
    def process_single_jurisdiction(self, jurisdiction: str, cases_limit: int = 1000) -> Dict[str, any]:
        """
        Process a single jurisdiction with rate limiting.
        
        Args:
            jurisdiction: Jurisdiction code to process
            cases_limit: Maximum cases to fetch
            
        Returns:
            Processing results
        """
        logger.info(f"Processing single jurisdiction: {jurisdiction}")
        logger.info(f"Cases limit: {cases_limit}")
        
        start_time = datetime.now()
        
        try:
            # Process the jurisdiction
            jurisdiction_results = self.client.process_missing_jurisdictions(
                cases_per_jurisdiction=cases_limit
            )
            
            if jurisdiction in jurisdiction_results:
                result = jurisdiction_results[jurisdiction]
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                return {
                    "jurisdiction": jurisdiction,
                    "status": "success",
                    "cases_processed": result.cases_fetched,
                    "duration_seconds": duration,
                    "result_details": result
                }
            else:
                return {
                    "jurisdiction": jurisdiction,
                    "status": "not_found",
                    "error": "Jurisdiction not found in results",
                    "cases_processed": 0
                }
                
        except Exception as e:
            logger.error(f"Failed to process {jurisdiction}: {e}")
            return {
                "jurisdiction": jurisdiction,
                "status": "failed",
                "error": str(e),
                "cases_processed": 0
            }


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Rate-Limited Court Listener Processing")
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=5,
        help="Number of jurisdictions to process per batch (default: 5)"
    )
    parser.add_argument(
        "--cases-per-jurisdiction", 
        type=int, 
        default=1000,
        help="Cases to fetch per jurisdiction (default: 1000)"
    )
    parser.add_argument(
        "--max-hours", 
        type=float,
        help="Maximum hours to run (default: no limit)"
    )
    parser.add_argument(
        "--jurisdiction", 
        help="Process only a specific jurisdiction"
    )
    parser.add_argument(
        "--estimate-only",
        action="store_true",
        help="Only show time estimates, don't process"
    )
    parser.add_argument(
        "--resume",
        help="Resume from a specific checkpoint ID"
    )
    parser.add_argument(
        "--list-checkpoints",
        action="store_true",
        help="List available checkpoints"
    )
    
    args = parser.parse_args()
    
    processor = RateLimitedCourtListenerProcessor()

    try:
        if args.list_checkpoints:
            # List available checkpoints
            checkpoints = processor.checkpoint_manager.list_checkpoints("court_listener_rate_limited")

            print(f"\n" + "="*60)
            print("AVAILABLE CHECKPOINTS")
            print("="*60)

            if not checkpoints:
                print("No checkpoints found.")
            else:
                for checkpoint in checkpoints:
                    progress = processor.checkpoint_manager.get_progress_summary(checkpoint.checkpoint_id)
                    print(f"\nCheckpoint ID: {checkpoint.checkpoint_id}")
                    print(f"Status: {checkpoint.status}")
                    print(f"Progress: {progress['completed_items']}/{progress['total_items']} "
                          f"({progress['progress_percentage']:.1f}%)")
                    print(f"Success Rate: {progress['success_rate']:.1f}%")
                    print(f"Last Update: {checkpoint.last_update}")
                    print(f"Duration: {progress['duration']}")

                    if checkpoint.status in ['running', 'paused']:
                        print(f"🔄 Can be resumed with: --resume {checkpoint.checkpoint_id}")

            return

        elif args.estimate_only:
            # Show time estimates only
            all_jurisdictions = processor.client.all_jurisdictions
            estimate = processor.calculate_processing_time(
                len(all_jurisdictions), args.cases_per_jurisdiction
            )
            
            print(f"\n" + "="*60)
            print("PROCESSING TIME ESTIMATE")
            print("="*60)
            print(f"Total jurisdictions: {estimate['total_jurisdictions']}")
            print(f"Cases per jurisdiction: {estimate['cases_per_jurisdiction']}")
            print(f"Estimated queries: {estimate['total_estimated_queries']:,}")
            print(f"Estimated time: {estimate['estimated_hours']:.1f} hours")
            print(f"Estimated completion: {estimate['estimated_completion']}")
            
        elif args.jurisdiction:
            # Process single jurisdiction
            result = processor.process_single_jurisdiction(
                args.jurisdiction, args.cases_per_jurisdiction
            )
            
            print(f"\n" + "="*60)
            print("SINGLE JURISDICTION PROCESSING COMPLETE")
            print("="*60)
            print(f"Jurisdiction: {result['jurisdiction']}")
            print(f"Status: {result['status']}")
            print(f"Cases processed: {result['cases_processed']}")
            print(f"Duration: {result['duration_seconds']:.1f} seconds")
            
        else:
            # Process all jurisdictions in batches
            results = processor.process_jurisdictions_in_batches(
                batch_size=args.batch_size,
                cases_per_jurisdiction=args.cases_per_jurisdiction,
                max_hours=args.max_hours,
                resume_checkpoint=args.resume
            )
            
            print(f"\n" + "="*80)
            print("RATE-LIMITED COURT LISTENER PROCESSING COMPLETE")
            print("="*80)
            print(f"Total Jurisdictions: {results['total_jurisdictions']}")
            print(f"Processed: {results['jurisdictions_processed']}")
            print(f"Total Cases: {results['total_cases_processed']}")
            print(f"Duration: {results['duration_hours']:.1f} hours")
            print(f"Rate: {results['processing_rate_jurisdictions_per_hour']:.1f} jurisdictions/hour")
            print(f"Avg Cases/Jurisdiction: {results['average_cases_per_jurisdiction']:.0f}")
            print(f"Checkpoint ID: {results['checkpoint_id']}")

            # Show progress summary
            progress = results['progress_summary']
            print(f"\nProgress Summary:")
            print(f"  Completed: {progress['completed_items']}")
            print(f"  Failed: {progress['failed_items']}")
            print(f"  Remaining: {progress['remaining_items']}")
            print(f"  Success Rate: {progress['success_rate']:.1f}%")

            if progress['remaining_items'] > 0:
                print(f"\n🔄 To resume processing: python {sys.argv[0]} --resume {results['checkpoint_id']}")
            
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
