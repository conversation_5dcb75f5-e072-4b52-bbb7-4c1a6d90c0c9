#!/usr/bin/env python3
"""
Monitor the comprehensive Texas processing progress
"""
import os
import time
from datetime import datetime
from supabase import create_client
import pinecone
from dotenv import load_dotenv

def get_current_stats():
    """Get current database statistics"""
    load_dotenv()
    
    # Supabase stats
    supabase = create_client(
        os.getenv('SUPABASE_URL'),
        os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    )
    
    cases_result = supabase.table('cases').select('id', count='exact').execute()
    total_cases = cases_result.count
    
    # Get jurisdiction breakdown
    jurisdiction_result = supabase.table('cases').select('jurisdiction').execute()
    jurisdictions = {}
    for case in jurisdiction_result.data:
        jurisdiction = case.get('jurisdiction', 'unknown')
        jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1
    
    # Pinecone stats
    pc = pinecone.Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
    index = pc.Index('texas-laws-voyage3large')
    pinecone_stats = index.describe_index_stats()
    total_vectors = pinecone_stats.total_vector_count
    
    return {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_cases': total_cases,
        'total_vectors': total_vectors,
        'texas_cases': jurisdictions.get('tx', 0),
        'federal_cases': jurisdictions.get('fed', 0),
        'unknown_cases': jurisdictions.get('unknown', 0),
        'jurisdictions': jurisdictions
    }

def print_progress_report(stats):
    """Print formatted progress report"""
    print(f"\n{'='*60}")
    print(f"TEXAS PROCESSING PROGRESS REPORT")
    print(f"Time: {stats['timestamp']}")
    print(f"{'='*60}")
    print(f"📊 TOTAL PROGRESS:")
    print(f"   Cases in Supabase: {stats['total_cases']:,}")
    print(f"   Vectors in Pinecone: {stats['total_vectors']:,}")
    print(f"   Vector/Case Ratio: {stats['total_vectors']/max(stats['total_cases'], 1):.1f}")
    
    print(f"\n🎯 TEXAS-SPECIFIC:")
    print(f"   Texas Cases (tx): {stats['texas_cases']:,}")
    print(f"   Federal Cases (fed): {stats['federal_cases']:,}")
    print(f"   Unknown Jurisdiction: {stats['unknown_cases']:,}")
    
    print(f"\n🌎 TOP JURISDICTIONS:")
    sorted_jurisdictions = sorted(stats['jurisdictions'].items(), key=lambda x: x[1], reverse=True)
    for jurisdiction, count in sorted_jurisdictions[:10]:
        print(f"   {jurisdiction}: {count:,}")
    
    print(f"{'='*60}")

def monitor_processing(interval_minutes=30):
    """Monitor processing with periodic updates"""
    print("🚀 Starting Texas Processing Monitor...")
    print(f"📊 Checking progress every {interval_minutes} minutes")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            stats = get_current_stats()
            print_progress_report(stats)
            
            # Sleep for specified interval
            time.sleep(interval_minutes * 60)
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Error during monitoring: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        try:
            interval = int(sys.argv[1])
            monitor_processing(interval)
        except ValueError:
            print("Usage: python monitor_processing.py [interval_minutes]")
            print("Example: python monitor_processing.py 15")
    else:
        # Single check
        stats = get_current_stats()
        print_progress_report(stats)
