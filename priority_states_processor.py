#!/usr/bin/env python3
"""
Priority States Multi-Cloud Serverless Processor

Implements the recommended approach:
1. Start with Texas, New York, Florida (highest priority states)
2. Google Cloud Run Jobs as primary workhorse
3. AWS Lambda for overflow capacity
4. Modal/RunPod for GPU-accelerated embeddings
5. Dedicated DB writer service for quality assurance
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)


@dataclass
class StateProcessingConfig:
    """Configuration for processing a specific state."""
    state_code: str
    state_name: str
    estimated_cases: int
    priority: int
    file_patterns: List[str]
    processing_complexity: float  # 1.0 = normal, 2.0 = complex cases


class PriorityStatesProcessor:
    """Manages multi-cloud processing for priority states."""
    
    def __init__(self):
        self.priority_states = {
            'tx': StateProcessingConfig(
                state_code='tx',
                state_name='Texas',
                estimated_cases=800000,  # Large state, lots of cases
                priority=1,
                file_patterns=['tex_*.jsonl', '*texas*.jsonl', 'tx_*.jsonl'],
                processing_complexity=1.2  # Oil/gas, complex commercial law
            ),
            'ny': StateProcessingConfig(
                state_code='ny',
                state_name='New York',
                estimated_cases=600000,  # Financial center, high volume
                priority=2,
                file_patterns=['ny_*.jsonl', '*newyork*.jsonl', '*manhattan*.jsonl'],
                processing_complexity=1.5  # Complex financial/securities law
            ),
            'fl': StateProcessingConfig(
                state_code='fl',
                state_name='Florida',
                estimated_cases=500000,  # Large population, active courts
                priority=3,
                file_patterns=['fl_*.jsonl', '*florida*.jsonl', 'fla_*.jsonl'],
                processing_complexity=1.0  # Standard complexity
            )
        }
        
        self.cloud_configs = {
            'gcp_primary': {
                'provider': 'google_cloud_run_jobs',
                'max_concurrent': 1500,
                'memory_gb': 8,
                'cpu_count': 4,
                'timeout_minutes': 60,
                'cost_per_gb_hour': 0.024
            },
            'aws_overflow': {
                'provider': 'aws_lambda',
                'max_concurrent': 1000,
                'memory_mb': 10240,
                'timeout_minutes': 15,
                'cost_per_gb_second': 0.**********
            },
            'gpu_embedding': {
                'provider': 'modal_gpu',
                'gpu_type': 'A100',
                'max_concurrent': 50,
                'cost_per_gpu_hour': 1.10
            }
        }
    
    def calculate_state_processing_plan(self, state_code: str) -> Dict[str, Any]:
        """Calculate optimal processing plan for a specific state."""
        
        if state_code not in self.priority_states:
            raise ValueError(f"State {state_code} not in priority list")
        
        state_config = self.priority_states[state_code]
        
        # Calculate resource requirements
        total_cases = state_config.estimated_cases
        complexity_factor = state_config.processing_complexity
        
        # Adjust processing time based on complexity
        base_processing_time = 2.0  # seconds per case
        adjusted_processing_time = base_processing_time * complexity_factor
        
        # Calculate batch configuration
        docs_per_batch = 50
        total_batches = (total_cases + docs_per_batch - 1) // docs_per_batch
        
        # Primary processing on Google Cloud Run Jobs
        gcp_config = self.cloud_configs['gcp_primary']
        gcp_capacity = gcp_config['max_concurrent']
        
        # If we need more capacity, use AWS Lambda overflow
        aws_config = self.cloud_configs['aws_overflow']
        aws_capacity = aws_config['max_concurrent'] if total_batches > gcp_capacity else 0
        
        total_capacity = gcp_capacity + aws_capacity
        
        # Calculate processing time
        if total_batches <= total_capacity:
            # Single round processing
            processing_rounds = 1
            batches_per_round = total_batches
        else:
            # Multiple rounds needed
            processing_rounds = (total_batches + total_capacity - 1) // total_capacity
            batches_per_round = total_capacity
        
        round_time_minutes = (docs_per_batch * adjusted_processing_time) / 60
        total_time_hours = (processing_rounds * round_time_minutes) / 60
        
        # Calculate costs
        gcp_batches = min(total_batches, gcp_capacity * processing_rounds)
        aws_batches = max(0, total_batches - gcp_batches)
        
        gcp_cost = self._calculate_gcp_cost(gcp_batches, round_time_minutes, gcp_config)
        aws_cost = self._calculate_aws_cost(aws_batches, round_time_minutes, aws_config)
        gpu_cost = self._calculate_gpu_cost(total_cases, total_time_hours)
        
        total_cost = gcp_cost + aws_cost + gpu_cost
        
        return {
            'state': state_config.state_name,
            'state_code': state_code,
            'total_cases': total_cases,
            'complexity_factor': complexity_factor,
            'total_batches': total_batches,
            'processing_rounds': processing_rounds,
            'time_hours': total_time_hours,
            'costs': {
                'gcp_primary': gcp_cost,
                'aws_overflow': aws_cost,
                'gpu_embedding': gpu_cost,
                'total': total_cost
            },
            'resource_allocation': {
                'gcp_batches': gcp_batches,
                'aws_batches': aws_batches,
                'gpu_hours': total_time_hours
            },
            'throughput': {
                'cases_per_hour': total_cases / total_time_hours,
                'concurrent_functions': min(total_capacity, total_batches)
            }
        }
    
    def _calculate_gcp_cost(self, batches: int, round_time_minutes: float, config: Dict) -> float:
        """Calculate Google Cloud Run Jobs cost."""
        if batches == 0:
            return 0.0
        
        memory_gb = config['memory_gb']
        cpu_count = config['cpu_count']
        time_hours = round_time_minutes / 60
        
        # Cloud Run Jobs pricing: CPU + Memory
        cpu_cost = batches * cpu_count * time_hours * 0.024  # $0.024 per vCPU-hour
        memory_cost = batches * memory_gb * time_hours * 0.0025  # $0.0025 per GB-hour
        
        return cpu_cost + memory_cost
    
    def _calculate_aws_cost(self, batches: int, round_time_minutes: float, config: Dict) -> float:
        """Calculate AWS Lambda cost."""
        if batches == 0:
            return 0.0
        
        memory_mb = config['memory_mb']
        memory_gb = memory_mb / 1024
        time_seconds = round_time_minutes * 60
        
        # Lambda pricing: requests + compute time
        request_cost = batches * 0.0000002  # $0.20 per 1M requests
        compute_cost = batches * memory_gb * time_seconds * config['cost_per_gb_second']
        
        return request_cost + compute_cost
    
    def _calculate_gpu_cost(self, total_cases: int, total_time_hours: float) -> float:
        """Calculate GPU embedding cost using Modal."""
        # Assume 10% of processing time needs GPU for embeddings
        gpu_time_hours = total_time_hours * 0.1
        gpu_cost_per_hour = self.cloud_configs['gpu_embedding']['cost_per_gpu_hour']
        
        return gpu_time_hours * gpu_cost_per_hour
    
    def create_deployment_manifest(self, state_code: str) -> Dict[str, Any]:
        """Create deployment configuration for a state."""
        
        plan = self.calculate_state_processing_plan(state_code)
        state_config = self.priority_states[state_code]
        
        return {
            'deployment_name': f"caselaw-{state_code}-processing",
            'state_config': state_config.__dict__,
            'processing_plan': plan,
            'cloud_run_jobs': {
                'job_name': f"caselaw-processor-{state_code}",
                'task_count': plan['resource_allocation']['gcp_batches'],
                'parallelism': min(100, plan['resource_allocation']['gcp_batches']),
                'task_timeout': '3600s',
                'container_image': 'gcr.io/your-project/caselaw-processor:latest',
                'resources': {
                    'memory': '8Gi',
                    'cpu': '4',
                    'ephemeral_storage': '10Gi'
                },
                'env_vars': {
                    'STATE_CODE': state_code,
                    'BATCH_SIZE': '50',
                    'SUPABASE_URL': '${SUPABASE_URL}',
                    'PINECONE_API_KEY': '${PINECONE_API_KEY}',
                    'MODAL_TOKEN': '${MODAL_TOKEN}'
                }
            },
            'aws_lambda_overflow': {
                'function_name': f"caselaw-overflow-{state_code}",
                'runtime': 'python3.9',
                'memory_size': 10240,
                'timeout': 900,
                'reserved_concurrency': plan['resource_allocation']['aws_batches'],
                'environment': {
                    'STATE_CODE': state_code,
                    'BATCH_SIZE': '50'
                }
            } if plan['resource_allocation']['aws_batches'] > 0 else None,
            'modal_gpu_config': {
                'function_name': f"embedding_generator_{state_code}",
                'gpu': 'A100',
                'memory': 32768,
                'timeout': 3600
            }
        }


def analyze_priority_states():
    """Analyze processing requirements for priority states."""
    
    processor = PriorityStatesProcessor()
    
    print("\n🎯 PRIORITY STATES PROCESSING ANALYSIS")
    print("=" * 80)
    
    total_cases = 0
    total_cost = 0
    total_time = 0
    
    for state_code in ['tx', 'ny', 'fl']:
        plan = processor.calculate_state_processing_plan(state_code)
        
        print(f"\n📊 {plan['state']} ({state_code.upper()})")
        print("-" * 50)
        print(f"Cases: {plan['total_cases']:,}")
        print(f"Complexity: {plan['complexity_factor']:.1f}x")
        print(f"Processing time: {plan['time_hours']:.1f} hours")
        print(f"Total cost: ${plan['costs']['total']:.2f}")
        print(f"Throughput: {plan['throughput']['cases_per_hour']:,.0f} cases/hour")
        print(f"Concurrent functions: {plan['throughput']['concurrent_functions']:,}")
        
        print(f"\nResource allocation:")
        print(f"  GCP Cloud Run Jobs: {plan['resource_allocation']['gcp_batches']:,} batches")
        if plan['resource_allocation']['aws_batches'] > 0:
            print(f"  AWS Lambda overflow: {plan['resource_allocation']['aws_batches']:,} batches")
        print(f"  GPU hours needed: {plan['resource_allocation']['gpu_hours']:.1f}")
        
        print(f"\nCost breakdown:")
        print(f"  GCP primary: ${plan['costs']['gcp_primary']:.2f}")
        if plan['costs']['aws_overflow'] > 0:
            print(f"  AWS overflow: ${plan['costs']['aws_overflow']:.2f}")
        print(f"  GPU embedding: ${plan['costs']['gpu_embedding']:.2f}")
        
        total_cases += plan['total_cases']
        total_cost += plan['costs']['total']
        total_time = max(total_time, plan['time_hours'])  # Parallel processing
    
    print(f"\n🎯 COMBINED PRIORITY STATES SUMMARY")
    print("=" * 50)
    print(f"Total cases: {total_cases:,}")
    print(f"Total time (parallel): {total_time:.1f} hours")
    print(f"Total cost: ${total_cost:.2f}")
    print(f"Average cost per case: ${total_cost/total_cases:.4f}")
    print(f"Cases per dollar: {total_cases/total_cost:.0f}")
    
    # Compare to sequential processing
    sequential_days = total_cases / (50 * 24)  # 50 cases/hour single core
    speedup = (sequential_days * 24) / total_time
    
    print(f"\nSpeedup vs sequential: {speedup:.0f}x faster")
    print(f"Time saved: {sequential_days:.0f} days → {total_time:.1f} hours")


if __name__ == "__main__":
    analyze_priority_states()
