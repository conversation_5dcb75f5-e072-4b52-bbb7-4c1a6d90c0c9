#!/usr/bin/env python3
"""
Test Caselaw Access Project Processing

This script tests the Caselaw Access Project file processing capabilities.
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime
import json
import gzip

# Add project root to path
sys.path.append('/Users/<USER>/Documents/GitHub/texas-laws-personalinjury')

from src.processing.caselaw_access_processor import CaselawAccessProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_file_discovery():
    """Test that the processor can discover JSONL files."""
    logger.info("=== Testing File Discovery ===")
    
    try:
        processor = CaselawAccessProcessor(data_dir="data/caselaw_access_project")
        
        # Test file discovery
        files = processor.get_jsonl_files()
        
        logger.info(f"✅ Found {len(files)} JSONL files")
        if files:
            logger.info(f"  First file: {files[0].name}")
            logger.info(f"  Last file: {files[-1].name}")
        
        processor.close()
        return len(files) > 0
        
    except Exception as e:
        logger.error(f"❌ File discovery failed: {e}")
        return False

def test_single_file_parsing():
    """Test parsing a single JSONL file."""
    logger.info("=== Testing Single File Parsing ===")
    
    try:
        processor = CaselawAccessProcessor(data_dir="data/caselaw_access_project")
        
        # Get first file
        files = processor.get_jsonl_files()
        if not files:
            logger.error("❌ No files found")
            return False
        
        test_file = files[0]
        logger.info(f"Testing file: {test_file.name}")
        
        # Parse first few lines
        documents = []
        with gzip.open(test_file, 'rt', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 3:  # Only test first 3 documents
                    break
                
                try:
                    raw_data = json.loads(line.strip())
                    doc = processor.parse_caselaw_document(raw_data)
                    documents.append(doc)
                    logger.info(f"  Document {i+1}: {doc.case_name[:50]}...")
                except Exception as e:
                    logger.warning(f"  Failed to parse document {i+1}: {e}")
        
        logger.info(f"✅ Successfully parsed {len(documents)} documents")
        processor.close()
        return len(documents) > 0
        
    except Exception as e:
        logger.error(f"❌ Single file parsing failed: {e}")
        return False

def test_document_processing():
    """Test processing a few documents through the pipeline."""
    logger.info("=== Testing Document Processing ===")
    
    try:
        processor = CaselawAccessProcessor(data_dir="data/caselaw_access_project")
        
        # Get first file
        files = processor.get_jsonl_files()
        if not files:
            logger.error("❌ No files found")
            return False
        
        test_file = files[0]
        logger.info(f"Processing documents from: {test_file.name}")
        
        # Process first document
        with gzip.open(test_file, 'rt', encoding='utf-8') as f:
            line = f.readline().strip()
            if line:
                raw_data = json.loads(line)
                doc = processor.parse_caselaw_document(raw_data)
                
                # Test document processing
                logger.info(f"  Processing document: {doc.case_name[:30]}...")
                
                # Test practice area classification
                practice_area = processor._classify_practice_area(doc)
                logger.info(f"  Practice area: {practice_area}")
                
                # Test namespace determination
                namespace = processor._determine_vector_namespace(doc)
                logger.info(f"  Vector namespace: {namespace}")
                
                # Test temporal authority scoring
                authority_score = processor._calculate_temporal_authority_score(doc)
                logger.info(f"  Authority score: {authority_score}")
                
                logger.info("✅ Document processing pipeline working")
                processor.close()
                return True
        
    except Exception as e:
        logger.error(f"❌ Document processing failed: {e}")
        return False

def test_data_extraction():
    """Test extracting metadata from documents."""
    logger.info("=== Testing Data Extraction ===")
    
    try:
        processor = CaselawAccessProcessor(data_dir="data/caselaw_access_project")
        
        # Get first file
        files = processor.get_jsonl_files()
        if not files:
            logger.error("❌ No files found")
            return False
        
        test_file = files[0]
        
        # Extract metadata from first few documents
        metadata_samples = []
        with gzip.open(test_file, 'rt', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 5:  # Test first 5 documents
                    break
                
                try:
                    raw_data = json.loads(line.strip())
                    doc = processor.parse_caselaw_document(raw_data)
                    
                    metadata = {
                        'case_name': doc.case_name,
                        'jurisdiction': doc.jurisdiction,
                        'court': doc.court,
                        'date_filed': doc.date_filed,
                        'historical_era': doc.historical_era,
                        'text_length': len(doc.text) if doc.text else 0
                    }
                    metadata_samples.append(metadata)
                    
                except Exception as e:
                    logger.warning(f"Failed to extract metadata from document {i+1}: {e}")
        
        logger.info(f"✅ Extracted metadata from {len(metadata_samples)} documents")
        
        if metadata_samples:
            logger.info("Sample metadata:")
            for i, metadata in enumerate(metadata_samples):
                logger.info(f"  Document {i+1}:")
                logger.info(f"    Case: {metadata['case_name'][:40]}...")
                logger.info(f"    Jurisdiction: {metadata['jurisdiction']}")
                logger.info(f"    Court: {metadata['court'][:30]}...")
                logger.info(f"    Date: {metadata['date_filed']}")
                logger.info(f"    Era: {metadata['historical_era']}")
                logger.info(f"    Text length: {metadata['text_length']:,} chars")
        
        processor.close()
        return len(metadata_samples) > 0
        
    except Exception as e:
        logger.error(f"❌ Data extraction failed: {e}")
        return False

def test_system_performance():
    """Test system performance on sample data."""
    logger.info("=== Testing System Performance ===")
    
    try:
        processor = CaselawAccessProcessor(data_dir="data/caselaw_access_project")
        
        # Performance test: parse documents from first file
        files = processor.get_jsonl_files()
        if not files:
            logger.error("❌ No files found")
            return False
        
        test_file = files[0]
        
        start_time = datetime.now()
        document_count = 0
        
        with gzip.open(test_file, 'rt', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 50:  # Test first 50 documents
                    break
                
                try:
                    raw_data = json.loads(line.strip())
                    doc = processor.parse_caselaw_document(raw_data)
                    document_count += 1
                except Exception as e:
                    continue
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if duration > 0:
            processing_rate = document_count / duration
            logger.info(f"✅ Performance test completed:")
            logger.info(f"  Documents processed: {document_count}")
            logger.info(f"  Duration: {duration:.2f} seconds")
            logger.info(f"  Processing rate: {processing_rate:.1f} docs/second")
        
        processor.close()
        return document_count > 0
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False

def main():
    """Run all Caselaw Access Project processing tests."""
    logger.info("🧪 Starting Caselaw Access Project Processing Tests")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("=" * 60)
    
    tests = [
        ("File Discovery", test_file_discovery),
        ("Single File Parsing", test_single_file_parsing),
        ("Document Processing", test_document_processing),
        ("Data Extraction", test_data_extraction),
        ("System Performance", test_system_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            logger.info(f"Running {test_name}...")
            results[test_name] = test_func()
            logger.info(f"{'✅' if results[test_name] else '❌'} {test_name} completed\n")
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}\n")
            results[test_name] = False
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {passed/total*100:.1f}%")
    
    logger.info("\nDetailed results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
    
    if passed == total:
        logger.info("\n🎉 All tests passed! Caselaw Access Project processing is ready.")
    else:
        logger.info(f"\n⚠️ {total - passed} tests failed. Please review the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)