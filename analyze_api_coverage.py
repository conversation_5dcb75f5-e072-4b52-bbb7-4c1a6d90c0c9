#!/usr/bin/env python3
"""
Comprehensive CourtListener API Coverage Analysis
Analyze what data we're extracting vs what's available
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def analyze_api_coverage():
    """Analyze comprehensive API coverage for all available data"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🔍 COMPREHENSIVE COURTLISTENER API COVERAGE ANALYSIS")
    print("=" * 70)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # Get a comprehensive case with all related data
        print("\n📄 1. COMPREHENSIVE CASE DATA ANALYSIS")
        print("-" * 50)
        
        try:
            # Get an opinion with author
            opinions_url = f"{base_url}/opinions/"
            params = {
                'court': 'scotus',
                'page_size': 1,
                'format': 'json',
                'author__isnull': False
            }
            
            response = await client.get(opinions_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            opinions = data.get('results', [])
            
            if not opinions:
                print("❌ No opinions found")
                return
            
            opinion = opinions[0]
            print(f"📄 Opinion ID: {opinion.get('id')}")
            
            # Analyze all available fields
            all_fields = list(opinion.keys())
            print(f"📊 Total opinion fields: {len(all_fields)}")
            
            # Categorize fields
            categories = {
                'identification': ['id', 'resource_uri', 'absolute_url'],
                'content': ['plain_text', 'html', 'html_lawbox', 'html_columbia', 'html_anon_2020', 'xml_harvard', 'html_with_citations'],
                'judges': ['author', 'author_str', 'author_id', 'joined_by', 'joined_by_str'],
                'metadata': ['date_created', 'date_modified', 'date_filed', 'per_curiam', 'type', 'sha1', 'page_count'],
                'relationships': ['cluster', 'cluster_id', 'docket', 'opinions_cited'],
                'files': ['download_url', 'local_path', 'extracted_by_ocr'],
                'processing': ['ordering_key', 'main_version']
            }
            
            print(f"\n📊 OPINION FIELD CATEGORIES:")
            for category, expected_fields in categories.items():
                available = [f for f in expected_fields if f in all_fields]
                print(f"   {category.upper()}: {len(available)}/{len(expected_fields)} fields")
                for field in available:
                    value = opinion.get(field)
                    has_data = value is not None and value != '' and value != []
                    status = "✅" if has_data else "❌"
                    print(f"      {field}: {status}")
            
            # Get cluster data
            cluster_url = opinion.get('cluster')
            if cluster_url:
                print(f"\n📊 2. CLUSTER DATA ANALYSIS")
                print("-" * 50)
                
                cluster_response = await client.get(cluster_url)
                if cluster_response.status_code == 200:
                    cluster_data = cluster_response.json()
                    cluster_fields = list(cluster_data.keys())
                    print(f"📊 Total cluster fields: {len(cluster_fields)}")
                    
                    # Key cluster categories
                    cluster_categories = {
                        'case_info': ['case_name', 'case_name_short', 'case_name_full', 'slug'],
                        'judges': ['judges', 'panel', 'non_participating_judges'],
                        'dates': ['date_filed', 'date_filed_is_approximate', 'date_created', 'date_modified'],
                        'content': ['syllabus', 'headnotes', 'summary', 'disposition', 'history', 'procedural_history'],
                        'legal': ['citations', 'citation_count', 'precedential_status', 'scdb_id', 'scdb_decision_direction'],
                        'relationships': ['docket', 'docket_id', 'sub_opinions'],
                        'metadata': ['source', 'attorneys', 'nature_of_suit', 'posture', 'arguments', 'headmatter']
                    }
                    
                    print(f"\n📊 CLUSTER FIELD CATEGORIES:")
                    for category, expected_fields in cluster_categories.items():
                        available = [f for f in expected_fields if f in cluster_fields]
                        print(f"   {category.upper()}: {len(available)}/{len(expected_fields)} fields")
                        for field in available:
                            value = cluster_data.get(field)
                            has_data = value is not None and value != '' and value != []
                            status = "✅" if has_data else "❌"
                            print(f"      {field}: {status}")
            
            # Get author/people data
            author_url = opinion.get('author')
            if author_url:
                print(f"\n👤 3. PEOPLE/JUDGE DATA ANALYSIS")
                print("-" * 50)
                
                author_response = await client.get(author_url)
                if author_response.status_code == 200:
                    author_data = author_response.json()
                    author_fields = list(author_data.keys())
                    print(f"📊 Total people fields: {len(author_fields)}")
                    
                    # People categories
                    people_categories = {
                        'identity': ['name_first', 'name_middle', 'name_last', 'name_suffix', 'name_full'],
                        'biographical': ['date_dob', 'date_dod', 'dob_city', 'dob_state', 'dob_country', 'gender', 'religion', 'race'],
                        'professional': ['positions', 'educations', 'aba_ratings', 'political_affiliations'],
                        'metadata': ['fjc_id', 'slug', 'date_created', 'date_modified', 'date_completed'],
                        'financial': ['ftm_total_received', 'ftm_eid'],
                        'relationships': ['is_alias_of', 'sources'],
                        'media': ['has_photo']
                    }
                    
                    print(f"\n📊 PEOPLE FIELD CATEGORIES:")
                    for category, expected_fields in people_categories.items():
                        available = [f for f in expected_fields if f in author_fields]
                        print(f"   {category.upper()}: {len(available)}/{len(expected_fields)} fields")
                        for field in available:
                            value = author_data.get(field)
                            has_data = value is not None and value != '' and value != []
                            status = "✅" if has_data else "❌"
                            print(f"      {field}: {status}")
                    
                    # Check positions for court data
                    positions = author_data.get('positions', [])
                    if positions:
                        print(f"\n🏛️ COURT/POSITION DATA:")
                        pos_url = positions[0] if isinstance(positions[0], str) else None
                        if pos_url:
                            pos_response = await client.get(pos_url)
                            if pos_response.status_code == 200:
                                pos_data = pos_response.json()
                                pos_fields = list(pos_data.keys())
                                print(f"   📊 Position fields: {len(pos_fields)}")
                                
                                for field in pos_fields:
                                    value = pos_data.get(field)
                                    has_data = value is not None and value != '' and value != []
                                    status = "✅" if has_data else "❌"
                                    print(f"      {field}: {status}")
                                
                                # Check court data
                                court = pos_data.get('court', {})
                                if isinstance(court, dict):
                                    court_fields = list(court.keys())
                                    print(f"\n   🏛️ Court fields: {len(court_fields)}")
                                    for field in court_fields:
                                        value = court.get(field)
                                        has_data = value is not None and value != '' and value != []
                                        status = "✅" if has_data else "❌"
                                        print(f"         {field}: {status}")
        
        except Exception as e:
            print(f"❌ Error in analysis: {e}")
        
        # Summary of what we're currently extracting vs available
        print(f"\n📊 4. CURRENT EXTRACTION VS AVAILABLE DATA")
        print("-" * 50)
        
        current_extraction = {
            "✅ JUDGES": "100% - People API integration with full names",
            "🔍 COURTS": "Partial - Available in position data, not fully extracted",
            "❌ CASE METADATA": "Limited - Not extracting syllabus, headnotes, summary",
            "❌ LEGAL CITATIONS": "Not extracted - Available in cluster data",
            "❌ CASE DATES": "Basic - Could extract more date metadata",
            "❌ ATTORNEYS": "Not extracted - Available in cluster data",
            "❌ NATURE OF SUIT": "Not extracted - Available in cluster data",
            "❌ PRECEDENTIAL STATUS": "Not extracted - Available in cluster data",
            "❌ SCDB DATA": "Not extracted - Supreme Court Database integration available",
            "❌ BIOGRAPHICAL DATA": "Not extracted - Judge biographical data available"
        }
        
        print("📊 EXTRACTION STATUS:")
        for item, status in current_extraction.items():
            print(f"   {item}: {status}")
        
        print(f"\n🎯 RECOMMENDATIONS FOR ADDITIONAL API USAGE:")
        recommendations = [
            "🏛️ COURTS: Extract court names from position data (easy win)",
            "📄 CASE METADATA: Extract syllabus, headnotes, summary for better context",
            "📚 LEGAL CITATIONS: Extract citation data for case relationships",
            "👨‍💼 ATTORNEYS: Extract attorney information from cluster data",
            "⚖️ LEGAL METADATA: Extract nature_of_suit, precedential_status",
            "📊 SCDB DATA: Extract Supreme Court Database metadata",
            "👤 JUDGE BIOS: Extract biographical data for judge profiles"
        ]
        
        for rec in recommendations:
            print(f"   {rec}")

if __name__ == "__main__":
    asyncio.run(analyze_api_coverage())
