#!/usr/bin/env python3
"""
Enhanced Court Listener Processor

Processes Court Listener API data with:
1. Unlimited pagination for comprehensive coverage
2. Practice area filtering for targeted processing
3. Rate limiting compliance (5,000/hour)
4. Priority state focus (TX, NY, FL)
5. Quality assurance and validation
"""

import asyncio
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from src.processing.courtlistener_bulk_client import CourtListenerBulkClient
from src.processing.caselaw_access_processor import CaselawAccessProcessor
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingStats:
    """Statistics for Court Listener processing."""
    jurisdiction: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_fetched: int = 0
    total_processed: int = 0
    duplicates_prevented: int = 0
    errors: int = 0
    practice_areas: Dict[str, int] = None
    
    def __post_init__(self):
        if self.practice_areas is None:
            self.practice_areas = {}


class EnhancedCourtListenerProcessor:
    """Enhanced processor for comprehensive Court Listener data processing."""
    
    def __init__(self):
        """Initialize enhanced Court Listener processor."""
        
        # Core components
        self.court_listener = CourtListenerBulkClient()
        self.caselaw_processor = CaselawAccessProcessor()
        self.supabase = SupabaseConnector()
        self.checkpoint_manager = CheckpointManager()
        
        # Priority configuration
        self.priority_states = ['tx', 'ny', 'fl']
        self.priority_practice_areas = [
            'personal injury',
            'criminal defense', 
            'family law',
            'estate planning',
            'immigration law',
            'real estate',
            'bankruptcy'
        ]
        
        # Rate limiting (5,000 requests/hour = ~1.39 requests/second)
        self.rate_limit_delay = 0.72  # Seconds between requests (conservative)
        self.last_request_time = 0
        
        # Processing targets
        self.target_cases_per_state = 50000
        self.batch_size = 500  # Increased from 100 to 500 cases per batch for comprehensive coverage
        
        logger.info("✅ Enhanced Court Listener Processor initialized")
        logger.info(f"   Priority states: {self.priority_states}")
        logger.info(f"   Practice areas: {len(self.priority_practice_areas)}")
        logger.info(f"   Rate limit: {3600/self.rate_limit_delay:.0f} requests/hour")
    
    async def _rate_limit(self):
        """Ensure compliance with Court Listener rate limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def process_jurisdiction_comprehensive(self, jurisdiction: str, 
                                               resume_checkpoint: Optional[str] = None) -> ProcessingStats:
        """
        Process all relevant cases for a jurisdiction comprehensively.
        
        Args:
            jurisdiction: Jurisdiction code (tx, ny, fl)
            resume_checkpoint: Optional checkpoint to resume from
            
        Returns:
            Processing statistics
        """
        logger.info(f"🚀 Starting comprehensive processing for {jurisdiction.upper()}")
        
        stats = ProcessingStats(
            jurisdiction=jurisdiction,
            start_time=datetime.now()
        )
        
        # Create checkpoint for this jurisdiction
        checkpoint_id = f"cl_comprehensive_{jurisdiction}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Fetch ALL cases without practice area filtering for complete corpus
            logger.info(f"📋 Processing ALL cases for {jurisdiction} (no practice area filtering)")

            all_stats = await self._process_all_cases(
                jurisdiction=jurisdiction,
                checkpoint_id=checkpoint_id
            )

            # Set statistics
            stats.total_fetched = all_stats['fetched']
            stats.total_processed = all_stats['processed']
            stats.duplicates_prevented = all_stats['duplicates']
            stats.errors = all_stats['errors']
            stats.practice_areas['all_cases'] = all_stats['processed']

            # Save final checkpoint
            await self.checkpoint_manager.save_checkpoint(
                    checkpoint_id=checkpoint_id,
                    data={
                        'jurisdiction': jurisdiction,
                        'process_type': 'court_listener',
                        'completed_practice_areas': list(stats.practice_areas.keys()),
                        'stats': stats.__dict__,
                        'status': 'running',
                        'start_time': stats.start_time.isoformat(),
                        'total_items': stats.total_fetched,
                        'processed_items': stats.total_processed
                    }
                )

            logger.info(f"✅ Completed processing: {all_stats['processed']} total cases processed")

        except Exception as e:
            logger.error(f"❌ Error processing {jurisdiction}: {e}")
            stats.errors += 1
        
        finally:
            stats.end_time = datetime.now()
            duration = stats.end_time - stats.start_time
            
            logger.info(f"🎉 Completed {jurisdiction} processing in {duration}")
            logger.info(f"   Total fetched: {stats.total_fetched:,}")
            logger.info(f"   Total processed: {stats.total_processed:,}")
            logger.info(f"   Duplicates prevented: {stats.duplicates_prevented:,}")
            logger.info(f"   Errors: {stats.errors}")
        
        return stats
    
    async def _process_all_cases(self, jurisdiction: str, checkpoint_id: str) -> Dict[str, int]:
        """Process all cases for a jurisdiction without practice area filtering."""

        stats = {
            'fetched': 0,
            'processed': 0,
            'duplicates': 0,
            'errors': 0
        }

        try:
            # Fetch ALL cases without practice area filtering for complete corpus
            logger.info(f"   🔍 Fetching ALL cases from Court Listener (no practice area filtering)...")

            cases = self.court_listener.fetch_jurisdiction_cases(
                jurisdiction=jurisdiction,
                limit=0,  # No limit - fetch all available cases (respecting API rate limits)
                practice_areas=None  # No practice area filtering - get everything
            )
            
            stats['fetched'] = len(cases)
            logger.info(f"   📥 Fetched {len(cases):,} total cases")

            if not cases:
                logger.warning(f"   ⚠️ No cases found for {jurisdiction}")
                return stats
            
            # Process cases in batches
            for i in range(0, len(cases), self.batch_size):
                batch = cases[i:i + self.batch_size]
                batch_num = (i // self.batch_size) + 1
                total_batches = (len(cases) + self.batch_size - 1) // self.batch_size
                
                logger.info(f"   📦 Processing batch {batch_num}/{total_batches} ({len(batch)} cases)")
                
                batch_stats = await self._process_case_batch(batch, jurisdiction, 'all_cases')
                
                stats['processed'] += batch_stats['processed']
                stats['duplicates'] += batch_stats['duplicates']
                stats['errors'] += batch_stats['errors']
                
                # Rate limiting between batches
                await self._rate_limit()
                
                # Save progress checkpoint
                if batch_num % 10 == 0:  # Every 10 batches
                    await self.checkpoint_manager.save_checkpoint(
                        checkpoint_id=f"{checkpoint_id}_batch_{batch_num}",
                        data={
                            'jurisdiction': jurisdiction,
                            'practice_area': 'all_cases',
                            'batch_num': batch_num,
                            'total_batches': total_batches,
                            'stats': stats
                        }
                    )
        
        except Exception as e:
            logger.error(f"   ❌ Error processing all_cases for {jurisdiction}: {e}")
            stats['errors'] += 1
        
        return stats
    
    async def _process_case_batch(self, cases: List[Dict], jurisdiction: str,
                                practice_area: str) -> Dict[str, int]:
        """Process a batch of cases through the complete pipeline."""
        
        batch_stats = {
            'processed': 0,
            'duplicates': 0,
            'errors': 0
        }
        
        for case in cases:
            try:
                # Transform Court Listener case to CaselawDocument format
                case_doc = self._transform_court_listener_case(case, jurisdiction, practice_area)
                
                # Check for duplicates
                if await self.caselaw_processor.deduplicator.is_duplicate(case_doc, source='court_listener'):
                    batch_stats['duplicates'] += 1
                    continue

                # Process through the complete pipeline
                success = await self.caselaw_processor.process_document(case_doc)
                
                if success:
                    batch_stats['processed'] += 1
                else:
                    batch_stats['errors'] += 1
                    
            except Exception as e:
                logger.error(f"     ❌ Error processing case {case.get('id', 'unknown')}: {e}")
                batch_stats['errors'] += 1
        
        return batch_stats
    
    def _transform_court_listener_case(self, case: Dict, jurisdiction: str, 
                                     practice_area: str) -> Any:
        """Transform Court Listener case data to CaselawDocument format."""
        
        # Import here to avoid circular imports
        from src.processing.caselaw_access_processor import CaselawDocument
        
        # Extract scalar values from complex objects (Neo4j compatibility)
        jurisdiction_code = jurisdiction
        if isinstance(case.get('jurisdiction'), dict):
            jurisdiction_code = case['jurisdiction'].get('code', jurisdiction)
        
        court_id = None
        court_name = None
        if isinstance(case.get('court'), dict):
            court_id = case['court'].get('id')
            court_name = case['court'].get('name')
        
        # Create CaselawDocument with all required fields
        from datetime import datetime

        return CaselawDocument(
            # Required positional arguments
            id=f"cl_{case.get('id')}",
            source='court_listener',
            added=datetime.now(),
            created=datetime.now(),
            author=court_name or 'Unknown Court',
            license='public',
            url=case.get('url', ''),
            text=case.get('text', ''),

            # Optional fields
            case_name=case.get('case_name', ''),
            docket_number=case.get('docket_number', ''),
            date_filed=case.get('date_filed'),
            court=court_name or 'Unknown Court',
            jurisdiction=jurisdiction_code,
            practice_area=practice_area,
            court_listener_id=case.get('id'),
            precedential_status=case.get('precedential_status'),
            citation_count=case.get('citation_count', 0),
            judges=case.get('judges', []),
            parties=case.get('parties', [])
        )
    
    async def process_all_priority_states(self) -> Dict[str, ProcessingStats]:
        """Process all priority states comprehensively."""
        
        logger.info("🌟 STARTING COMPREHENSIVE COURT LISTENER PROCESSING")
        logger.info("=" * 60)
        logger.info(f"Priority states: {', '.join(self.priority_states)}")
        logger.info(f"Practice areas: {len(self.priority_practice_areas)}")
        logger.info(f"Target per state: {self.target_cases_per_state:,} cases")
        
        results = {}
        overall_start = datetime.now()
        
        for jurisdiction in self.priority_states:
            try:
                stats = await self.process_jurisdiction_comprehensive(jurisdiction)
                results[jurisdiction] = stats
                
                # Brief pause between states
                await asyncio.sleep(2.0)
                
            except Exception as e:
                logger.error(f"❌ Failed to process {jurisdiction}: {e}")
                results[jurisdiction] = ProcessingStats(
                    jurisdiction=jurisdiction,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    errors=1
                )
        
        # Final summary
        overall_duration = datetime.now() - overall_start
        total_processed = sum(stats.total_processed for stats in results.values())
        total_duplicates = sum(stats.duplicates_prevented for stats in results.values())
        total_errors = sum(stats.errors for stats in results.values())
        
        logger.info("\n🎉 COMPREHENSIVE PROCESSING COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"Total duration: {overall_duration}")
        logger.info(f"Total cases processed: {total_processed:,}")
        logger.info(f"Total duplicates prevented: {total_duplicates:,}")
        logger.info(f"Total errors: {total_errors}")
        
        for jurisdiction, stats in results.items():
            logger.info(f"\n{jurisdiction.upper()}:")
            logger.info(f"  Processed: {stats.total_processed:,}")
            logger.info(f"  Practice areas: {len(stats.practice_areas)}")
            for area, count in stats.practice_areas.items():
                logger.info(f"    {area}: {count:,}")
        
        return results


async def main():
    """Main function for enhanced Court Listener processing."""
    
    processor = EnhancedCourtListenerProcessor()
    results = await processor.process_all_priority_states()
    
    # Return success if at least one state processed successfully
    success = any(stats.total_processed > 0 for stats in results.values())
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
