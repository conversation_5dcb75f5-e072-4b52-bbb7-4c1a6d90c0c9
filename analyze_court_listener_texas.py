#!/usr/bin/env python3
"""
Analyze Court Listener Texas Cases
Check how many Texas cases are actually available vs what we're processing
"""

import os
import requests
import time
from dotenv import load_dotenv
from collections import defaultdict

load_dotenv()

class CourtListenerTexasAnalyzer:
    """Analyze Texas case availability in Court Listener."""
    
    def __init__(self):
        """Initialize with Court Listener API."""
        
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        if not self.api_key:
            raise ValueError("COURTLISTENER_API_KEY not found in environment")
        
        self.base_url = "https://www.courtlistener.com/api/rest/v3"
        self.headers = {
            'Authorization': f'Token {self.api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        # Texas court identifiers from Court Listener
        self.texas_courts = {
            # State Courts
            'tex': 'Texas Supreme Court',
            'texcrimapp': 'Texas Court of Criminal Appeals', 
            'texapp1': 'Texas Court of Appeals, 1st District',
            'texapp2': 'Texas Court of Appeals, 2nd District',
            'texapp3': 'Texas Court of Appeals, 3rd District',
            'texapp4': 'Texas Court of Appeals, 4th District',
            'texapp5': 'Texas Court of Appeals, 5th District',
            'texapp6': 'Texas Court of Appeals, 6th District',
            'texapp7': 'Texas Court of Appeals, 7th District',
            'texapp8': 'Texas Court of Appeals, 8th District',
            'texapp9': 'Texas Court of Appeals, 9th District',
            'texapp10': 'Texas Court of Appeals, 10th District',
            'texapp11': 'Texas Court of Appeals, 11th District',
            'texapp12': 'Texas Court of Appeals, 12th District',
            'texapp13': 'Texas Court of Appeals, 13th District',
            'texapp14': 'Texas Court of Appeals, 14th District',
            
            # Federal Courts in Texas
            'txed': 'E.D. Texas',
            'txnd': 'N.D. Texas', 
            'txsd': 'S.D. Texas',
            'txwd': 'W.D. Texas',
            'ca5': '5th Circuit (covers Texas)',
        }
    
    def get_court_case_count(self, court_id: str) -> int:
        """Get case count for a specific court."""
        
        try:
            # Use opinions endpoint to get case count
            url = f"{self.base_url}/opinions/"
            params = {
                'court': court_id,
                'format': 'json',
                'count': 'on',  # Get count only
                'page_size': 1   # Minimal data transfer
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('count', 0)
            else:
                print(f"  ⚠️ Error for {court_id}: HTTP {response.status_code}")
                return 0
                
        except Exception as e:
            print(f"  ❌ Error for {court_id}: {e}")
            return 0
    
    def analyze_texas_coverage(self):
        """Analyze Texas case coverage across all Texas courts."""
        
        print("🏛️ ANALYZING COURT LISTENER TEXAS COVERAGE")
        print("=" * 60)
        
        total_texas_cases = 0
        court_breakdown = {}
        
        print("📊 Checking each Texas court...")
        
        for court_id, court_name in self.texas_courts.items():
            print(f"  🔍 {court_name} ({court_id})...", end=" ")
            
            case_count = self.get_court_case_count(court_id)
            court_breakdown[court_id] = {
                'name': court_name,
                'count': case_count
            }
            total_texas_cases += case_count
            
            print(f"{case_count:,} cases")
            
            # Small delay to respect rate limits
            time.sleep(0.2)
        
        print(f"\n📊 COURT LISTENER TEXAS ANALYSIS RESULTS")
        print("=" * 60)
        
        print(f"🎯 Total Texas Cases Available: {total_texas_cases:,}")
        
        print(f"\n🏛️ BREAKDOWN BY COURT TYPE:")
        
        # State courts
        state_total = 0
        print("  📋 STATE COURTS:")
        for court_id, info in court_breakdown.items():
            if court_id not in ['txed', 'txnd', 'txsd', 'txwd', 'ca5']:
                count = info['count']
                state_total += count
                if count > 0:
                    print(f"    {info['name']}: {count:,}")
        
        print(f"    STATE TOTAL: {state_total:,}")
        
        # Federal courts
        federal_total = 0
        print("\n  ⚖️ FEDERAL COURTS:")
        for court_id, info in court_breakdown.items():
            if court_id in ['txed', 'txnd', 'txsd', 'txwd', 'ca5']:
                count = info['count']
                federal_total += count
                if count > 0:
                    print(f"    {info['name']}: {count:,}")
        
        print(f"    FEDERAL TOTAL: {federal_total:,}")
        
        # Top courts by volume
        print(f"\n📈 TOP 10 COURTS BY VOLUME:")
        sorted_courts = sorted(court_breakdown.items(), key=lambda x: x[1]['count'], reverse=True)
        for i, (court_id, info) in enumerate(sorted_courts[:10], 1):
            if info['count'] > 0:
                print(f"  {i:2d}. {info['name']}: {info['count']:,}")
        
        # Compare to our current processing
        current_processed = 246  # What we currently get
        
        print(f"\n🔍 COMPARISON TO CURRENT PROCESSING:")
        print(f"  Available in Court Listener: {total_texas_cases:,}")
        print(f"  Currently processed: {current_processed:,}")
        
        if total_texas_cases > current_processed:
            missing = total_texas_cases - current_processed
            improvement = (missing / current_processed) * 100 if current_processed > 0 else float('inf')
            print(f"  Missing cases: {missing:,}")
            print(f"  Potential improvement: {improvement:.0f}%")
            print(f"  🚨 MAJOR ISSUE: We're missing {missing:,} Texas cases from Court Listener!")
        else:
            print(f"  ✅ Processing appears complete")
        
        print(f"\n💡 ASSESSMENT:")
        if total_texas_cases > 10000:
            print("🎉 EXCELLENT: Court Listener has massive Texas coverage!")
            print("📊 This confirms Texas is a major jurisdiction in the system")
        elif total_texas_cases > 1000:
            print("✅ GOOD: Substantial Texas coverage available")
        else:
            print("⚠️ LIMITED: Fewer Texas cases than expected")
        
        if total_texas_cases > current_processed * 2:
            print("🚨 CRITICAL: Court Listener processing is severely underperforming")
            print("🔧 Need to investigate and fix Court Listener Texas detection")
        
        return {
            'total_available': total_texas_cases,
            'current_processed': current_processed,
            'court_breakdown': court_breakdown,
            'state_total': state_total,
            'federal_total': federal_total
        }

def main():
    """Main execution function."""
    
    try:
        analyzer = CourtListenerTexasAnalyzer()
        results = analyzer.analyze_texas_coverage()
        
        print(f"\n🎯 CONCLUSION:")
        print(f"Court Listener contains {results['total_available']:,} Texas cases")
        print(f"Combined with CAP's 101,047 cases, total available: {results['total_available'] + 101047:,}")
        
        if results['total_available'] > results['current_processed'] * 2:
            print(f"\n🚨 ACTION REQUIRED:")
            print(f"Fix Court Listener processing to capture all {results['total_available']:,} available cases")
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure COURTLISTENER_API_KEY is set in your .env file")
        return None

if __name__ == "__main__":
    results = main()
