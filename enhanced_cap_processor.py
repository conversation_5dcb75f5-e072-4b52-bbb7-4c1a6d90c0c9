#!/usr/bin/env python3
"""
Enhanced Case Law Access Project (CAP) Processor

Processes local CAP data files with:
1. Comprehensive file parsing (JSONL.gz format)
2. Practice area classification
3. Priority state focus (TX, NY, FL)
4. Quality assurance and validation

Note: Cross-source deduplication removed - 1994 cutoff prevents CAP/CourtListener overlap
"""

import asyncio
import gzip
import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Generator
from dataclasses import dataclass

from src.processing.caselaw_access_processor import CaselawAccessProcessor
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CAPProcessingStats:
    """Statistics for CAP data processing."""
    jurisdiction: str
    start_time: datetime
    end_time: Optional[datetime] = None
    files_processed: int = 0
    total_cases: int = 0
    processed_cases: int = 0
    duplicates_prevented: int = 0
    errors: int = 0
    practice_areas: Dict[str, int] = None
    
    def __post_init__(self):
        if self.practice_areas is None:
            self.practice_areas = {}


class EnhancedCAPProcessor:
    """Enhanced processor for Case Law Access Project data."""
    
    def __init__(self, data_dir: str = "data/caselaw_access_project",
                 target_state: str = 'tx', max_cases: int = None):
        """Initialize enhanced CAP processor with configurable parameters."""

        self.data_dir = Path(data_dir)

        # Core components
        self.caselaw_processor = CaselawAccessProcessor()
        self.supabase = SupabaseConnector()
        self.checkpoint_manager = CheckpointManager()

        # Configurable parameters
        self.target_state = target_state.lower()
        self.priority_states = ['tx', 'ny', 'fl']
        self.priority_practice_areas = [
            'personal injury',
            'criminal defense', 
            'family law',
            'estate planning',
            'immigration law',
            'real estate',
            'bankruptcy'
        ]
        
        # Processing configuration - now configurable
        self.batch_size = 50  # Cases per batch
        self.max_cases_per_file = max_cases or 10000  # Configurable limit per file
        
        # Jurisdiction mapping for CAP data
        self.jurisdiction_mapping = {
            'tex': 'tx',
            'texas': 'tx',
            'ny': 'ny',
            'new-york': 'ny',
            'newyork': 'ny',
            'fl': 'fl',
            'fla': 'fl',
            'florida': 'fl'
        }
        
        logger.info("✅ Enhanced CAP Processor initialized")
        logger.info(f"   Data directory: {self.data_dir}")
        logger.info(f"   Priority states: {self.priority_states}")
        logger.info(f"   Practice areas: {len(self.priority_practice_areas)}")
    
    def discover_cap_files(self) -> List[Path]:
        """Discover all CAP data files (mixed jurisdictions)."""

        logger.info(f"🔍 Discovering CAP files in {self.data_dir}")

        cap_files = []

        if not self.data_dir.exists():
            logger.warning(f"⚠️ CAP data directory not found: {self.data_dir}")
            return cap_files

        # Look for all JSONL.gz files (mixed jurisdictions)
        for file_path in self.data_dir.rglob("*.jsonl.gz"):
            cap_files.append(file_path)
            logger.info(f"   📁 Found CAP file: {file_path.name}")

        logger.info(f"✅ Discovered {len(cap_files)} CAP files (mixed jurisdictions)")
        logger.info(f"   Will filter for {self.target_state.upper()} cases during processing")

        return cap_files
    
    def _extract_jurisdiction_from_path(self, file_path: Path) -> Optional[str]:
        """Extract jurisdiction from file path or name."""
        
        path_str = str(file_path).lower()
        
        # Check filename and path components
        for cap_jurisdiction, standard_jurisdiction in self.jurisdiction_mapping.items():
            if cap_jurisdiction in path_str:
                return standard_jurisdiction
        
        # Check for state abbreviations in filename
        filename = file_path.stem.lower()
        for jurisdiction in self.priority_states:
            if jurisdiction in filename:
                return jurisdiction
        
        return None
    
    def parse_cap_file(self, file_path: Path, max_cases: Optional[int] = None,
                      filter_jurisdiction: str = None) -> Generator[Dict, None, None]:
        """Parse a CAP JSONL.gz file and yield case records, optionally filtering by jurisdiction."""

        logger.info(f"📖 Parsing CAP file: {file_path.name}")
        if filter_jurisdiction:
            logger.info(f"   🔍 Filtering for {filter_jurisdiction.upper()} cases")

        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                case_count = 0
                filtered_count = 0

                for line_num, line in enumerate(f, 1):
                    try:
                        if not line.strip():
                            continue

                        case_data = json.loads(line)

                        # Filter by jurisdiction if specified
                        if filter_jurisdiction:
                            if not self._is_target_jurisdiction_case(case_data, filter_jurisdiction):
                                continue
                            filtered_count += 1

                        yield case_data
                        case_count += 1

                        # Limit cases per file if specified
                        if max_cases and case_count >= max_cases:
                            logger.info(f"   📊 Reached max cases limit ({max_cases}) for {file_path.name}")
                            break
                            
                    except json.JSONDecodeError as e:
                        logger.warning(f"   ⚠️ JSON decode error at line {line_num}: {e}")
                        continue
                    except Exception as e:
                        logger.error(f"   ❌ Error processing line {line_num}: {e}")
                        continue
                
                if filter_jurisdiction:
                    logger.info(f"   ✅ Parsed {case_count:,} {filter_jurisdiction.upper()} cases from {file_path.name} (filtered from {line_num:,} total)")
                else:
                    logger.info(f"   ✅ Parsed {case_count:,} cases from {file_path.name}")

        except Exception as e:
            logger.error(f"❌ Error reading CAP file {file_path}: {e}")

    def _is_target_jurisdiction_case(self, case_data: Dict, target_jurisdiction: str) -> bool:
        """Check if a CAP case belongs to the target jurisdiction using hybrid detection."""

        # Import hybrid detector (lazy import to avoid circular dependencies)
        try:
            from hybrid_court_jurisdiction_detector import HybridCourtJurisdictionDetector

            # Initialize detector if not already done
            if not hasattr(self, '_hybrid_detector'):
                self._hybrid_detector = HybridCourtJurisdictionDetector()

            # Detect jurisdiction using hybrid approach
            detected_jurisdiction = self._hybrid_detector.detect_jurisdiction(case_data)

            # Map target jurisdiction codes to our jurisdiction names
            jurisdiction_mapping = {
                'tx': 'texas',
                'ny': 'new_york',
                'fl': 'florida',
                'ca': 'california',
                'il': 'illinois',
                'pa': 'pennsylvania',
                'oh': 'ohio',
                'federal': 'federal',
                'us': 'federal'
            }

            target_mapped = jurisdiction_mapping.get(target_jurisdiction.lower(), target_jurisdiction.lower())

            # Check if detected jurisdiction matches target
            return detected_jurisdiction == target_mapped

        except Exception as e:
            logger.warning(f"Error in hybrid jurisdiction detection: {e}")
            # Fallback to simple text search if hybrid detector fails
            return self._fallback_jurisdiction_check(case_data, target_jurisdiction)

    def _fallback_jurisdiction_check(self, case_data: Dict, target_jurisdiction: str) -> bool:
        """Fallback jurisdiction check using simple text patterns."""

        text = case_data.get('text', '').lower()

        # Simple fallback patterns
        if target_jurisdiction.lower() == 'tx':
            return any(pattern in text for pattern in [
                'texas supreme court', 'court of appeals of texas', 'court of civil appeals of texas'
            ])
        elif target_jurisdiction.lower() == 'ny':
            return any(pattern in text for pattern in [
                'new york', 'court of appeals of new york'
            ])
        elif target_jurisdiction.lower() == 'fl':
            return any(pattern in text for pattern in [
                'florida', 'supreme court of florida'
            ])

        return False
    
    async def process_cap_file(self, file_path: Path, jurisdiction: str, 
                              checkpoint_id: str) -> CAPProcessingStats:
        """Process a single CAP file through the complete pipeline."""
        
        logger.info(f"🚀 Processing CAP file: {file_path.name} ({jurisdiction})")
        
        stats = CAPProcessingStats(
            jurisdiction=jurisdiction,
            start_time=datetime.now(),
            files_processed=1
        )
        
        try:
            # Parse cases from file, filtering for target jurisdiction
            cases = list(self.parse_cap_file(
                file_path,
                max_cases=self.max_cases_per_file,
                filter_jurisdiction=jurisdiction
            ))
            stats.total_cases = len(cases)
            
            if not cases:
                logger.warning(f"   ⚠️ No cases found in {file_path.name}")
                return stats
            
            # Process cases in batches
            for i in range(0, len(cases), self.batch_size):
                batch = cases[i:i + self.batch_size]
                batch_num = (i // self.batch_size) + 1
                total_batches = (len(cases) + self.batch_size - 1) // self.batch_size
                
                logger.info(f"   📦 Processing batch {batch_num}/{total_batches} ({len(batch)} cases)")
                
                batch_stats = await self._process_cap_batch(batch, jurisdiction)
                
                stats.processed_cases += batch_stats['processed']
                stats.duplicates_prevented += batch_stats['duplicates']
                stats.errors += batch_stats['errors']
                
                # Update practice area counts
                for area, count in batch_stats['practice_areas'].items():
                    stats.practice_areas[area] = stats.practice_areas.get(area, 0) + count
                
                # Save progress checkpoint
                if batch_num % 20 == 0:  # Every 20 batches
                    await self.checkpoint_manager.save_checkpoint(
                        checkpoint_id=f"{checkpoint_id}_batch_{batch_num}",
                        data={
                            'file_path': str(file_path),
                            'jurisdiction': jurisdiction,
                            'process_type': 'cap_processor',
                            'batch_num': batch_num,
                            'total_batches': total_batches,
                            'stats': stats.__dict__,
                            'status': 'running',
                            'start_time': stats.start_time.isoformat(),
                            'total_items': stats.total_cases,
                            'processed_items': stats.processed_cases
                        }
                    )
        
        except Exception as e:
            logger.error(f"❌ Error processing CAP file {file_path}: {e}")
            stats.errors += 1
        
        finally:
            stats.end_time = datetime.now()
            duration = stats.end_time - stats.start_time
            
            logger.info(f"✅ Completed {file_path.name} in {duration}")
            logger.info(f"   Cases processed: {stats.processed_cases:,}/{stats.total_cases:,}")
            logger.info(f"   Duplicates prevented: {stats.duplicates_prevented:,}")
            logger.info(f"   Errors: {stats.errors}")
        
        return stats
    
    async def _process_cap_batch(self, cases: List[Dict], jurisdiction: str) -> Dict[str, Any]:
        """Process a batch of CAP cases through the complete pipeline."""
        
        batch_stats = {
            'processed': 0,
            'duplicates': 0,
            'errors': 0,
            'practice_areas': {}
        }
        
        for case in cases:
            try:
                # Transform CAP case to CaselawDocument format
                case_doc = self._transform_cap_case(case, jurisdiction)
                
                if not case_doc:
                    batch_stats['errors'] += 1
                    continue
                
                # Check for duplicates (cross-source check removed - 1994 cutoff prevents overlap)
                if await self.caselaw_processor.deduplicator.is_duplicate(case_doc):
                    batch_stats['duplicates'] += 1
                    continue
                
                # Process through the complete pipeline
                success = await self.caselaw_processor.process_document(case_doc)
                
                if success:
                    batch_stats['processed'] += 1
                    
                    # Track practice area
                    practice_area = case_doc.practice_area or 'general'
                    batch_stats['practice_areas'][practice_area] = batch_stats['practice_areas'].get(practice_area, 0) + 1
                else:
                    batch_stats['errors'] += 1
                    
            except Exception as e:
                logger.error(f"     ❌ Error processing CAP case {case.get('id', 'unknown')}: {e}")
                batch_stats['errors'] += 1
        
        return batch_stats
    
    def _transform_cap_case(self, case: Dict, jurisdiction: str) -> Optional[Any]:
        """Transform CAP case data to CaselawDocument format."""
        
        try:
            # Import here to avoid circular imports
            from src.processing.caselaw_access_processor import CaselawDocument
            
            # Extract essential fields
            case_id = case.get('id')
            if not case_id:
                return None
            
            # Extract case name
            case_name = case.get('name') or case.get('name_abbreviation', '')
            
            # Extract court information
            court_info = case.get('court', {})
            court_name = court_info.get('name') if isinstance(court_info, dict) else str(court_info)
            
            # Extract date
            date_filed = case.get('decision_date') or case.get('date_filed')
            
            # Extract text content
            text_content = ""
            if 'casebody' in case and case['casebody']:
                casebody = case['casebody']
                if isinstance(casebody, dict):
                    text_content = casebody.get('data', {}).get('text', '') if casebody.get('data') else ''
                else:
                    text_content = str(casebody)
            
            # Extract citations
            citations = case.get('citations', [])
            citation_count = len(citations) if isinstance(citations, list) else 0
            
            # Create CaselawDocument with all required fields
            from datetime import datetime

            return CaselawDocument(
                # Required positional arguments
                id=f"cap_{case_id}",
                source='caselaw_access_project',
                added=datetime.now(),
                created=datetime.now(),
                author=court_name or 'Unknown Court',
                license='public',
                url=case.get('url', ''),
                text=text_content,

                # Optional fields
                case_name=case_name,
                docket_number=case.get('docket_number', ''),
                date_filed=date_filed,
                court=court_name or 'Unknown Court',
                jurisdiction=jurisdiction,
                cap_id=case_id,
                citation_count=citation_count,
                citations=citations,
                volume=case.get('volume'),
                reporter=case.get('reporter'),
                page=case.get('first_page')
            )
            
        except Exception as e:
            logger.error(f"Error transforming CAP case {case.get('id', 'unknown')}: {e}")
            return None
    
    async def process_jurisdiction_comprehensive(self, jurisdiction: str) -> CAPProcessingStats:
        """Process all CAP files filtering for a specific jurisdiction."""

        logger.info(f"🚀 Starting comprehensive CAP processing for {jurisdiction.upper()}")

        # Discover all CAP files (mixed jurisdictions)
        cap_files = self.discover_cap_files()

        if not cap_files:
            logger.warning(f"⚠️ No CAP files found")
            return CAPProcessingStats(
                jurisdiction=jurisdiction,
                start_time=datetime.now(),
                end_time=datetime.now()
            )
        
        # Aggregate statistics
        overall_stats = CAPProcessingStats(
            jurisdiction=jurisdiction,
            start_time=datetime.now()
        )
        
        # Create checkpoint for this jurisdiction
        checkpoint_id = f"cap_comprehensive_{jurisdiction}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Process each file, filtering for target jurisdiction
            for file_num, file_path in enumerate(cap_files, 1):
                logger.info(f"📁 Processing file {file_num}/{len(cap_files)}: {file_path.name} (filtering for {jurisdiction.upper()})")
                
                file_stats = await self.process_cap_file(
                    file_path=file_path,
                    jurisdiction=jurisdiction,
                    checkpoint_id=f"{checkpoint_id}_file_{file_num}"
                )
                
                # Aggregate statistics
                overall_stats.files_processed += file_stats.files_processed
                overall_stats.total_cases += file_stats.total_cases
                overall_stats.processed_cases += file_stats.processed_cases
                overall_stats.duplicates_prevented += file_stats.duplicates_prevented
                overall_stats.errors += file_stats.errors
                
                # Merge practice area counts
                for area, count in file_stats.practice_areas.items():
                    overall_stats.practice_areas[area] = overall_stats.practice_areas.get(area, 0) + count
                
                # Save checkpoint after each file
                await self.checkpoint_manager.save_checkpoint(
                    checkpoint_id=checkpoint_id,
                    data={
                        'jurisdiction': jurisdiction,
                        'process_type': 'cap_processor',
                        'files_completed': file_num,
                        'total_files': len(jurisdiction_files),
                        'stats': overall_stats.__dict__,
                        'status': 'running',
                        'start_time': overall_stats.start_time.isoformat(),
                        'total_items': overall_stats.total_cases,
                        'processed_items': overall_stats.processed_cases
                    }
                )
        
        except Exception as e:
            logger.error(f"❌ Error processing CAP data for {jurisdiction}: {e}")
            overall_stats.errors += 1
        
        finally:
            overall_stats.end_time = datetime.now()
            duration = overall_stats.end_time - overall_stats.start_time
            
            logger.info(f"🎉 Completed CAP processing for {jurisdiction} in {duration}")
            logger.info(f"   Files processed: {overall_stats.files_processed}")
            logger.info(f"   Cases processed: {overall_stats.processed_cases:,}/{overall_stats.total_cases:,}")
            logger.info(f"   Duplicates prevented: {overall_stats.duplicates_prevented:,}")
            logger.info(f"   Practice areas: {len(overall_stats.practice_areas)}")
        
        return overall_stats
    
    async def process_all_priority_states(self) -> Dict[str, CAPProcessingStats]:
        """Process CAP data for all priority states."""
        
        logger.info("🌟 STARTING COMPREHENSIVE CAP DATA PROCESSING")
        logger.info("=" * 60)
        logger.info(f"Priority states: {', '.join(self.priority_states)}")
        logger.info(f"Data directory: {self.data_dir}")
        
        results = {}
        overall_start = datetime.now()
        
        for jurisdiction in self.priority_states:
            try:
                stats = await self.process_jurisdiction_comprehensive(jurisdiction)
                results[jurisdiction] = stats
                
                # Brief pause between states
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"❌ Failed to process CAP data for {jurisdiction}: {e}")
                results[jurisdiction] = CAPProcessingStats(
                    jurisdiction=jurisdiction,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    errors=1
                )
        
        # Final summary
        overall_duration = datetime.now() - overall_start
        total_processed = sum(stats.processed_cases for stats in results.values())
        total_duplicates = sum(stats.duplicates_prevented for stats in results.values())
        total_errors = sum(stats.errors for stats in results.values())
        
        logger.info("\n🎉 COMPREHENSIVE CAP PROCESSING COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"Total duration: {overall_duration}")
        logger.info(f"Total cases processed: {total_processed:,}")
        logger.info(f"Total duplicates prevented: {total_duplicates:,}")
        logger.info(f"Total errors: {total_errors}")
        
        return results

    async def process_target_state(self) -> CAPProcessingStats:
        """Process CAP data for the configured target state."""

        logger.info(f"🎯 PROCESSING CAP DATA FOR {self.target_state.upper()}")
        logger.info("=" * 60)

        # Process the target state comprehensively
        stats = await self.process_jurisdiction_comprehensive(self.target_state)

        logger.info(f"🎉 {self.target_state.upper()} CAP processing complete!")
        logger.info(f"   Files processed: {stats.files_processed}")
        logger.info(f"   Total cases: {stats.total_cases:,}")
        logger.info(f"   Processed cases: {stats.processed_cases:,}")
        logger.info(f"   Duplicates prevented: {stats.duplicates_prevented:,}")
        logger.info(f"   Errors: {stats.errors:,}")

        return stats


async def main():
    """Main function for enhanced CAP processing with configurable state."""

    import argparse

    parser = argparse.ArgumentParser(description='Enhanced CAP Processor')
    parser.add_argument('--state', default='tx', help='Target state (tx, ny, fl)')
    parser.add_argument('--max-cases', type=int, help='Maximum cases per file')
    parser.add_argument('--all-states', action='store_true', help='Process all priority states')

    args = parser.parse_args()

    processor = EnhancedCAPProcessor(
        target_state=args.state,
        max_cases=args.max_cases
    )

    if args.all_states:
        # Process all priority states
        results = await processor.process_all_priority_states()
        success = any(stats.processed_cases > 0 for stats in results.values())
    else:
        # Process target state only
        stats = await processor.process_target_state()
        success = stats.processed_cases > 0

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
