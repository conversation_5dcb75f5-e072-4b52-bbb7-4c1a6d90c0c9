#!/usr/bin/env python3
"""
Execute Phase 1: Court Listener Data Expansion

Process high priority jurisdictions to expand coverage from 3/57 to 57/57 jurisdictions.
This is the first phase of the production deployment plan.
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
sys.path.append('/Users/<USER>/Documents/GitHub/texas-laws-personalinjury')

from src.processing.courtlistener_bulk_client import CourtListenerBulkClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def execute_phase1_high_priority():
    """Execute Phase 1: Process high priority states first."""
    logger.info("🚀 EXECUTING PHASE 1: COURT LISTENER DATA EXPANSION")
    logger.info("=" * 70)
    
    # Initialize client
    client = CourtListenerBulkClient()
    
    # High priority states (population, legal significance, case volume)
    high_priority_states = [
        'fl',  # Florida
        'pa',  # Pennsylvania  
        'il',  # Illinois
        'oh',  # Ohio
        'mi',  # Michigan
        'ga',  # Georgia
        'nc',  # North Carolina
        'va',  # Virginia
        'wa',  # Washington
        'az'   # Arizona
    ]
    
    logger.info(f"Processing {len(high_priority_states)} high priority states:")
    for state in high_priority_states:
        logger.info(f"  - {state}: {client.all_jurisdictions[state]}")
    
    # Process each high priority state
    results = {}
    total_cases_processed = 0
    
    for i, jurisdiction in enumerate(high_priority_states, 1):
        jurisdiction_name = client.all_jurisdictions[jurisdiction]
        
        logger.info(f"\n[{i}/{len(high_priority_states)}] Processing {jurisdiction} ({jurisdiction_name})")
        logger.info("-" * 50)
        
        try:
            # Process this jurisdiction with higher limits
            jurisdiction_results = client.process_missing_jurisdictions(cases_per_jurisdiction=10000)
            
            if jurisdiction in jurisdiction_results:
                result = jurisdiction_results[jurisdiction]
                results[jurisdiction] = result
                total_cases_processed += result.cases_fetched
                
                logger.info(f"✅ {jurisdiction} completed:")
                logger.info(f"   Cases processed: {result.cases_fetched}")
                logger.info(f"   Status: {result.status}")
                if result.error_message:
                    logger.warning(f"   Error: {result.error_message}")
            else:
                logger.error(f"❌ {jurisdiction} not found in results")
                
        except Exception as e:
            logger.error(f"❌ Error processing {jurisdiction}: {e}")
            results[jurisdiction] = {
                'error': str(e),
                'status': 'failed'
            }
    
    return results, total_cases_processed

def execute_phase1_federal_territories():
    """Execute Phase 1: Process federal courts and territories."""
    logger.info("\n🏛️  PROCESSING FEDERAL COURTS AND TERRITORIES")
    logger.info("=" * 70)
    
    # Initialize client
    client = CourtListenerBulkClient()
    
    # Federal and territories (high legal significance)
    federal_territories = [
        'us',  # Federal
        'dc',  # District of Columbia
        'pr',  # Puerto Rico
        'vi',  # Virgin Islands
        'gu',  # Guam
        'as',  # American Samoa
        'mp'   # Northern Mariana Islands
    ]
    
    logger.info(f"Processing {len(federal_territories)} federal/territory jurisdictions:")
    for jurisdiction in federal_territories:
        logger.info(f"  - {jurisdiction}: {client.all_jurisdictions[jurisdiction]}")
    
    # Process each federal/territory jurisdiction
    results = {}
    total_cases_processed = 0
    
    for i, jurisdiction in enumerate(federal_territories, 1):
        jurisdiction_name = client.all_jurisdictions[jurisdiction]
        
        logger.info(f"\n[{i}/{len(federal_territories)}] Processing {jurisdiction} ({jurisdiction_name})")
        logger.info("-" * 50)
        
        try:
            # Federal cases may have higher volume
            cases_to_fetch = 20000 if jurisdiction == 'us' else 10000

            # Process this jurisdiction
            jurisdiction_results = client.process_missing_jurisdictions(cases_per_jurisdiction=cases_to_fetch)
            
            if jurisdiction in jurisdiction_results:
                result = jurisdiction_results[jurisdiction]
                results[jurisdiction] = result
                total_cases_processed += result.cases_fetched
                
                logger.info(f"✅ {jurisdiction} completed:")
                logger.info(f"   Cases processed: {result.cases_fetched}")
                logger.info(f"   Status: {result.status}")
                if result.error_message:
                    logger.warning(f"   Error: {result.error_message}")
            else:
                logger.error(f"❌ {jurisdiction} not found in results")
                
        except Exception as e:
            logger.error(f"❌ Error processing {jurisdiction}: {e}")
            results[jurisdiction] = {
                'error': str(e),
                'status': 'failed'
            }
    
    return results, total_cases_processed

def execute_phase1_remaining_states():
    """Execute Phase 1: Process remaining states."""
    logger.info("\n🗺️  PROCESSING REMAINING STATES")
    logger.info("=" * 70)
    
    # Initialize client
    client = CourtListenerBulkClient()
    
    # All processed jurisdictions so far
    already_processed = {
        'ca', 'tx', 'ny',  # Existing coverage
        'fl', 'pa', 'il', 'oh', 'mi', 'ga', 'nc', 'va', 'wa', 'az',  # High priority
        'us', 'dc', 'pr', 'vi', 'gu', 'as', 'mp'  # Federal/territories
    }
    
    # Remaining states
    remaining_states = client.missing_jurisdictions - already_processed
    remaining_states = sorted(list(remaining_states))
    
    logger.info(f"Processing {len(remaining_states)} remaining states:")
    for state in remaining_states:
        logger.info(f"  - {state}: {client.all_jurisdictions[state]}")
    
    # Process remaining states in batches
    results = {}
    total_cases_processed = 0
    
    for i, jurisdiction in enumerate(remaining_states, 1):
        jurisdiction_name = client.all_jurisdictions[jurisdiction]
        
        logger.info(f"\n[{i}/{len(remaining_states)}] Processing {jurisdiction} ({jurisdiction_name})")
        logger.info("-" * 50)
        
        try:
            # Process this jurisdiction with higher limits
            jurisdiction_results = client.process_missing_jurisdictions(cases_per_jurisdiction=5000)
            
            if jurisdiction in jurisdiction_results:
                result = jurisdiction_results[jurisdiction]
                results[jurisdiction] = result
                total_cases_processed += result.cases_fetched
                
                logger.info(f"✅ {jurisdiction} completed:")
                logger.info(f"   Cases processed: {result.cases_fetched}")
                logger.info(f"   Status: {result.status}")
                if result.error_message:
                    logger.warning(f"   Error: {result.error_message}")
            else:
                logger.error(f"❌ {jurisdiction} not found in results")
                
        except Exception as e:
            logger.error(f"❌ Error processing {jurisdiction}: {e}")
            results[jurisdiction] = {
                'error': str(e),
                'status': 'failed'
            }
    
    return results, total_cases_processed

def save_phase1_results(high_priority_results, federal_results, remaining_results):
    """Save Phase 1 results to file."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"phase1_courtlistener_results_{timestamp}.json"
    
    combined_results = {
        'timestamp': datetime.now().isoformat(),
        'phase': 'Phase 1: Court Listener Data Expansion',
        'high_priority_states': high_priority_results,
        'federal_territories': federal_results,
        'remaining_states': remaining_results,
        'summary': {
            'total_jurisdictions_processed': len(high_priority_results) + len(federal_results) + len(remaining_results),
            'high_priority_cases': sum(r.cases_fetched for r in high_priority_results.values() if hasattr(r, 'cases_fetched')),
            'federal_cases': sum(r.cases_fetched for r in federal_results.values() if hasattr(r, 'cases_fetched')),
            'remaining_cases': sum(r.cases_fetched for r in remaining_results.values() if hasattr(r, 'cases_fetched'))
        }
    }
    
    # Convert dataclass objects to dictionaries for JSON serialization
    def convert_to_dict(obj):
        if hasattr(obj, '__dict__'):
            return obj.__dict__
        return obj
    
    # Save to file
    with open(results_file, 'w') as f:
        json.dump(combined_results, f, indent=2, default=convert_to_dict)
    
    logger.info(f"💾 Phase 1 results saved to: {results_file}")
    return results_file

def main():
    """Execute complete Phase 1 deployment."""
    start_time = datetime.now()
    
    logger.info("🚀 STARTING PHASE 1: COURT LISTENER DATA EXPANSION")
    logger.info(f"Timestamp: {start_time.isoformat()}")
    logger.info("=" * 70)
    
    try:
        # Execute Phase 1 in three sub-phases
        logger.info("Phase 1 consists of three sub-phases:")
        logger.info("  1. High Priority States (10 jurisdictions)")
        logger.info("  2. Federal Courts and Territories (7 jurisdictions)")
        logger.info("  3. Remaining States (37 jurisdictions)")
        logger.info("")
        
        # Sub-phase 1: High Priority States
        high_priority_results, high_priority_cases = execute_phase1_high_priority()
        
        # Sub-phase 2: Federal Courts and Territories
        federal_results, federal_cases = execute_phase1_federal_territories()
        
        # Sub-phase 3: Remaining States
        remaining_results, remaining_cases = execute_phase1_remaining_states()
        
        # Save results
        results_file = save_phase1_results(high_priority_results, federal_results, remaining_results)
        
        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        total_cases = high_priority_cases + federal_cases + remaining_cases
        total_jurisdictions = len(high_priority_results) + len(federal_results) + len(remaining_results)
        
        logger.info("\n" + "=" * 70)
        logger.info("✅ PHASE 1 COMPLETED SUCCESSFULLY!")
        logger.info("=" * 70)
        logger.info(f"📊 Final Results:")
        logger.info(f"  Total jurisdictions processed: {total_jurisdictions}")
        logger.info(f"  High priority cases: {high_priority_cases:,}")
        logger.info(f"  Federal/territory cases: {federal_cases:,}")
        logger.info(f"  Remaining state cases: {remaining_cases:,}")
        logger.info(f"  Total cases processed: {total_cases:,}")
        logger.info(f"  Processing duration: {duration}")
        logger.info(f"  Results saved to: {results_file}")
        logger.info("")
        logger.info("🎯 Next Step: Execute Phase 2 (Caselaw Access Project Processing)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 1 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)