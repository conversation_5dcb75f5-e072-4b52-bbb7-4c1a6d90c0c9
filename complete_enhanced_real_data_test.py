#!/usr/bin/env python3
"""
Complete Enhanced Real Data Test
Re-test the enhanced disambiguation system on real CourtListener and CAP data
"""

import asyncio
import logging
import os
import requests
import time
import json
import gzip
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CompleteEnhancedRealDataTest:
    """Complete re-test of enhanced disambiguation on real data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # CAP data path
        self.cap_data_path = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
        
        # Test batch ID
        self.test_batch_id = f"complete_enhanced_real_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_real_courtlistener_cases(self, limit: int = 3) -> list:
        """Fetch real CourtListener cases for enhanced testing"""
        
        print(f"🌐 FETCHING REAL COURTLISTENER CASES FOR ENHANCED TEST")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        real_cases = []
        
        try:
            # Get recent federal cases from multiple circuits for geographic testing
            courts = ['ca5', 'ca2', 'ca9', 'scotus']  # Different geographic regions
            
            for court in courts:
                if len(real_cases) >= limit:
                    break
                
                print(f"   🔍 Searching {court} court...")
                
                response = requests.get(
                    f"{self.cl_base_url}/opinions/",
                    headers=headers,
                    params={
                        'court': court,
                        'filed_after': '2020-01-01',
                        'ordering': '-date_filed',
                        'page_size': 5
                    },
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"   ❌ API request failed for {court}: {response.status_code}")
                    continue
                
                results = response.json().get('results', [])
                
                for opinion_data in results[:1]:  # 1 case per court
                    try:
                        case_name = opinion_data.get('case_name', 'Unknown')
                        text = opinion_data.get('plain_text', '') or opinion_data.get('html', '')
                        
                        if not text or len(text) < 500:
                            continue
                        
                        # Create processing format
                        case_data = {
                            'id': f"enhanced_real_{court}_{opinion_data.get('id', 'unknown')}",
                            'source': 'courtlistener',
                            'case_name': case_name,
                            'court': court,
                            'court_name': self._get_court_name(court),
                            'date_filed': opinion_data.get('date_filed', ''),
                            'jurisdiction': 'US',
                            'text': text,
                            'precedential_status': opinion_data.get('precedential_status', 'Unknown')
                        }
                        
                        real_cases.append(case_data)
                        
                        print(f"   ✅ {court}: {case_name[:40]}...")
                        print(f"      Date: {case_data['date_filed']}")
                        print(f"      Text length: {len(text):,} characters")
                        
                        time.sleep(0.3)  # Rate limit
                        
                    except Exception as e:
                        print(f"   ❌ Error processing {court} case: {e}")
                        continue
            
            print(f"\n📊 REAL COURTLISTENER CASES FOR ENHANCED TEST:")
            print(f"   Total cases: {len(real_cases)}")
            print(f"   Courts covered: {set(case['court'] for case in real_cases)}")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
            return []
    
    def load_real_cap_cases(self, limit: int = 2) -> list:
        """Load real CAP cases for enhanced testing"""
        
        print(f"\n📂 LOADING REAL CAP CASES FOR ENHANCED TEST")
        print("=" * 60)
        
        real_cases = []
        
        try:
            # Look for CAP data files
            cap_files = []
            if os.path.exists(self.cap_data_path):
                for file in os.listdir(self.cap_data_path):
                    if file.startswith('cap_') and file.endswith('.jsonl.gz'):
                        cap_files.append(os.path.join(self.cap_data_path, file))
            
            if not cap_files:
                print(f"   ❌ No CAP files found in {self.cap_data_path}")
                return []
            
            print(f"   📁 Found {len(cap_files)} CAP files")
            
            # Load cases from first file
            for file_path in cap_files[:1]:  # Just first file
                print(f"   📄 Loading from: {os.path.basename(file_path)}")
                
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if len(real_cases) >= limit:
                            break
                        
                        try:
                            case_data = json.loads(line.strip())
                            
                            # Check if case has substantial text and judge indicators
                            text = case_data.get('text', '')
                            if text and len(text) > 1000:
                                text_lower = text.lower()
                                judge_indicators = ['judge', 'justice', 'court', 'opinion', 'delivered']
                                
                                if any(indicator in text_lower for indicator in judge_indicators):
                                    # Add source and processing info
                                    case_data['source'] = 'caselaw_access_project'
                                    case_data['id'] = f"enhanced_cap_{case_data.get('id', line_num)}"
                                    real_cases.append(case_data)
                                    
                                    print(f"   ✅ CAP Case {len(real_cases)}: {case_data.get('name', 'Unknown')[:40]}...")
                                    print(f"      Text length: {len(text):,} characters")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            continue
                
                if real_cases:
                    break  # Found cases in first file
            
            print(f"\n📊 REAL CAP CASES FOR ENHANCED TEST:")
            print(f"   Total cases: {len(real_cases)}")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error loading real CAP cases: {e}")
            return []
    
    def _get_court_name(self, court_id: str) -> str:
        """Get full court name from court ID"""
        court_names = {
            'scotus': 'Supreme Court of the United States',
            'ca5': 'U.S. Court of Appeals, Fifth Circuit',
            'ca2': 'U.S. Court of Appeals, Second Circuit',
            'ca9': 'U.S. Court of Appeals, Ninth Circuit',
        }
        return court_names.get(court_id, f'Court {court_id}')
    
    async def run_complete_enhanced_test(self) -> bool:
        """Run complete enhanced disambiguation test on real data"""
        
        print(f"\n🔄 COMPLETE ENHANCED REAL DATA TEST")
        print("=" * 60)
        
        try:
            # Fetch real data from both sources
            cl_cases = self.fetch_real_courtlistener_cases(3)
            cap_cases = self.load_real_cap_cases(2)
            
            all_real_cases = cl_cases + cap_cases
            
            if not all_real_cases:
                print(f"❌ No real cases found for enhanced testing")
                return False
            
            print(f"\n📊 ENHANCED REAL DATA SUMMARY:")
            print(f"   CourtListener cases: {len(cl_cases)}")
            print(f"   CAP cases: {len(cap_cases)}")
            print(f"   Total real cases: {len(all_real_cases)}")
            print(f"   Geographic coverage: {set(case.get('court', 'unknown') for case in all_real_cases)}")
            
            # Process through enhanced pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing real data through enhanced disambiguation...")
            
            # Process CourtListener cases
            if cl_cases:
                print(f"\n📄 Processing {len(cl_cases)} real CourtListener cases...")
                cl_result = await processor.process_coherent_batch(
                    raw_cases=cl_cases,
                    source_type='courtlistener',
                    batch_id=f"{self.test_batch_id}_cl"
                )
                print(f"   CourtListener Result: Success={cl_result['success']}, Processed={cl_result['processed']}")
            
            # Process CAP cases
            if cap_cases:
                print(f"\n📄 Processing {len(cap_cases)} real CAP cases...")
                cap_result = await processor.process_coherent_batch(
                    raw_cases=cap_cases,
                    source_type='caselaw_access_project',
                    batch_id=f"{self.test_batch_id}_cap"
                )
                print(f"   CAP Result: Success={cap_result['success']}, Processed={cap_result['processed']}")
            
            # Verify enhanced disambiguation on real data
            return await self.verify_enhanced_real_data()
            
        except Exception as e:
            print(f"❌ Complete enhanced real data test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_enhanced_real_data(self) -> bool:
        """Verify enhanced disambiguation worked on real data"""
        
        print(f"\n🔍 VERIFYING ENHANCED DISAMBIGUATION ON REAL DATA")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get all judges from enhanced real data test
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case)
                        WHERE c.batch_id STARTS WITH $batch_prefix
                        AND type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_prefix=self.test_batch_id)
                
                all_judges = list(result)
                
                print(f"📊 1. ENHANCED REAL DATA JUDGES:")
                print(f"   Total judges found: {len(all_judges)}")
                
                # Analyze enhanced ID patterns
                enhanced_features = {
                    'geographic_regions': set(),
                    'temporal_eras': set(),
                    'court_types': set(),
                }
                
                for judge in all_judges:
                    judge_id = judge['judge_id']
                    words = len(judge['judge_name'].split())
                    word_status = "✅ Full" if words > 1 else "⚠️ Partial"
                    
                    print(f"      - {judge['judge_name']} ({word_status}, {words} words)")
                    print(f"        Enhanced ID: {judge_id}")
                    print(f"        Court: {judge['judge_court']}")
                    print(f"        Cases: {judge['case_count']}")
                    
                    # Extract enhanced features from ID
                    id_parts = judge_id.split('_')
                    if len(id_parts) >= 4:
                        enhanced_features['geographic_regions'].add(id_parts[2])  # Region part
                        enhanced_features['temporal_eras'].add(id_parts[3])       # Era part
                    
                    # Extract court type
                    court = judge['judge_court'] or ''
                    if 'circuit' in court.lower():
                        enhanced_features['court_types'].add('circuit')
                    elif 'district' in court.lower():
                        enhanced_features['court_types'].add('district')
                    elif 'supreme' in court.lower():
                        enhanced_features['court_types'].add('supreme')
                
                print(f"\n📊 2. ENHANCED FEATURES ANALYSIS:")
                print(f"   Geographic regions: {len(enhanced_features['geographic_regions'])} ({list(enhanced_features['geographic_regions'])})")
                print(f"   Temporal eras: {len(enhanced_features['temporal_eras'])} ({list(enhanced_features['temporal_eras'])})")
                print(f"   Court types: {len(enhanced_features['court_types'])} ({list(enhanced_features['court_types'])})")
                
                # Check for potential same-name judges in different regions
                judge_names = {}
                for judge in all_judges:
                    name = judge['judge_name']
                    if name in judge_names:
                        judge_names[name].append(judge)
                    else:
                        judge_names[name] = [judge]
                
                same_name_groups = {name: judges for name, judges in judge_names.items() if len(judges) > 1}
                
                print(f"\n📊 3. GEOGRAPHIC DISAMBIGUATION ANALYSIS:")
                if same_name_groups:
                    print(f"   Same-name judge groups: {len(same_name_groups)}")
                    for name, judges in same_name_groups.items():
                        print(f"      {name}: {len(judges)} judges")
                        for judge in judges:
                            region = judge['judge_id'].split('_')[2] if len(judge['judge_id'].split('_')) > 2 else 'unknown'
                            print(f"         - {judge['judge_id']} (region: {region})")
                else:
                    print(f"   No same-name judges found (good for disambiguation)")
                
                # Enhanced success criteria
                total_judges = len(all_judges)
                full_name_judges = [j for j in all_judges if len(j['judge_name'].split()) > 1]
                full_name_ratio = len(full_name_judges) / total_judges if total_judges > 0 else 0
                
                enhanced_success_criteria = [
                    total_judges > 0,  # Judges found
                    len(enhanced_features['geographic_regions']) > 0,  # Geographic disambiguation working
                    len(enhanced_features['temporal_eras']) > 0,  # Temporal disambiguation working
                    all('judge_' in judge['judge_id'] for judge in all_judges),  # Enhanced ID format
                ]
                
                success = all(enhanced_success_criteria)
                
                print(f"\n🎯 ENHANCED REAL DATA VERIFICATION:")
                print(f"   Judges found: {'✅' if total_judges > 0 else '❌'} ({total_judges})")
                print(f"   Geographic regions: {'✅' if len(enhanced_features['geographic_regions']) > 0 else '❌'} ({len(enhanced_features['geographic_regions'])})")
                print(f"   Temporal eras: {'✅' if len(enhanced_features['temporal_eras']) > 0 else '❌'} ({len(enhanced_features['temporal_eras'])})")
                print(f"   Enhanced ID format: {'✅' if all('judge_' in judge['judge_id'] for judge in all_judges) else '❌'}")
                print(f"   Full name ratio: {'✅' if full_name_ratio > 0 else '⚠️'} ({full_name_ratio:.1%})")
                print(f"   Overall success: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 ENHANCED REAL DATA TEST: SUCCESS!")
                    print(f"✅ Enhanced disambiguation working on real CourtListener + CAP data")
                    print(f"✅ Geographic and temporal disambiguation operational")
                    print(f"✅ Enhanced ID format implemented in production")
                    print(f"✅ {total_judges} real judges with enhanced disambiguation")
                    
                    if len(enhanced_features['geographic_regions']) > 1:
                        print(f"✅ BONUS: Multi-region disambiguation working!")
                    if len(enhanced_features['court_types']) > 1:
                        print(f"✅ BONUS: Multi-court-type disambiguation working!")
                else:
                    print(f"\n⚠️ ENHANCED REAL DATA TEST: NEEDS REFINEMENT!")
                    if total_judges == 0:
                        print(f"❌ No judges found in real data")
                
                return success
                
        except Exception as e:
            print(f"❌ Enhanced real data verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP ENHANCED REAL DATA TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().like('batch_id', f'{self.test_batch_id}%').execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case) WHERE c.batch_id STARTS WITH $batch_prefix DETACH DELETE c', 
                          batch_prefix=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run complete enhanced real data test"""
    
    print("🧪 COMPLETE ENHANCED REAL DATA TEST")
    print("=" * 80)
    print("🎯 Re-testing enhanced disambiguation on real CourtListener + CAP data")
    print("📋 Testing: Geographic + Temporal + Career progression + Enhanced IDs")
    
    test = CompleteEnhancedRealDataTest()
    
    try:
        # Run complete enhanced test
        success = await test.run_complete_enhanced_test()
        
        if success:
            print(f"\n🎉 COMPLETE ENHANCED REAL DATA TEST: SUCCESS!")
            print(f"✅ Enhanced disambiguation fully validated on real data")
            print(f"✅ Geographic and temporal disambiguation working")
            print(f"✅ Enhanced ID format operational in production")
            print(f"✅ Ready for production deployment with enhanced features")
            return True
        else:
            print(f"\n❌ COMPLETE ENHANCED REAL DATA TEST: FAILED!")
            print(f"❌ Enhanced disambiguation needs more work on real data")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
