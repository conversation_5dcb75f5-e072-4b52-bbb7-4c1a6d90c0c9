#!/usr/bin/env python3
"""
Check what New York bankruptcy courts we could add.
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv()

def check_ny_bankruptcy_courts():
    """Check available NY bankruptcy courts."""
    
    api_key = os.getenv("COURTLISTENER_API_KEY")
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    headers = {
        "Authorization": f"Token {api_key}",
        "User-Agent": "TexasLawsPersonalInjury/1.0"
    }
    
    print("🔍 CHECKING NEW YORK BANKRUPTCY COURTS")
    print("=" * 50)
    
    # Search for NY bankruptcy courts
    try:
        params = {
            'full_name__icontains': 'new york',
            'jurisdiction': 'FB',  # Federal Bankruptcy
            'format': 'json',
            'page_size': 20
        }
        response = requests.get(f"{base_url}/courts/", headers=headers, params=params)
        response.raise_for_status()
        
        courts = response.json()
        ny_bankruptcy_courts = courts.get('results', [])
        
        print(f"✅ Found {len(ny_bankruptcy_courts)} NY bankruptcy courts:")
        
        valid_courts = []
        for court in ny_bankruptcy_courts:
            court_id = court.get('id', 'N/A')
            full_name = court.get('full_name', 'N/A')
            
            # Test if court has cases
            try:
                params = {
                    'docket__court': court_id,
                    'format': 'json',
                    'page_size': 1,
                    'date_filed__gte': '2020-01-01'
                }
                
                cases_response = requests.get(f"{base_url}/clusters/", headers=headers, params=params)
                
                if cases_response.status_code == 200:
                    cases = cases_response.json()
                    case_count = len(cases.get('results', []))
                    print(f"   ✅ {court_id}: {full_name} - {case_count} cases available")
                    valid_courts.append(court_id)
                else:
                    print(f"   ⚠️  {court_id}: {full_name} - Error fetching cases")
            
            except Exception as e:
                print(f"   ❌ {court_id}: Error - {e}")
        
        print(f"\n💡 SUGGESTED EXPANDED NY COURT LIST:")
        current_ny_courts = [
            'ny',         # NY Court of Appeals
            'nyappdiv',   # NY Appellate Division
            'nynd',       # N.D. New York
            'nyed',       # E.D. New York
            'nysd',       # S.D. New York
            'nywd',       # W.D. New York
            'ca2'         # 2nd Circuit Court of Appeals
        ]
        
        expanded_ny_courts = current_ny_courts + valid_courts
        
        print(f"   Current: {len(current_ny_courts)} courts")
        print(f"   Expanded: {len(expanded_ny_courts)} courts")
        print(f"   New list: {expanded_ny_courts}")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_ny_bankruptcy_courts()
