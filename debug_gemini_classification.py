#!/usr/bin/env python3
"""
Debug Gemini Classification Issues
Find out why Gemini is only classifying 11% of cases
"""

import os
import gzip
import json
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

def debug_gemini_responses():
    """Debug what's happening with Gemini responses."""
    
    print("🔍 DEBUGGING GEMINI CLASSIFICATION ISSUES")
    print("=" * 60)
    
    # Configure Gemini
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in .env file")
        return
    
    print(f"✅ Found Gemini API key: {api_key[:20]}...")
    
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-2.0-flash')
    
    # Load a few sample cases from CAP
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    if not cap_files:
        print("❌ No CAP files found")
        return
    
    print(f"📁 Loading samples from: {cap_files[0].name}")
    
    sample_cases = []
    with gzip.open(cap_files[0], 'rt', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if line.strip() and len(sample_cases) < 5:
                try:
                    case_data = json.loads(line)
                    sample_cases.append(case_data)
                except:
                    continue
            if len(sample_cases) >= 5:
                break
    
    print(f"📊 Loaded {len(sample_cases)} sample cases")
    print()
    
    # Test each case with detailed debugging
    for i, case_data in enumerate(sample_cases, 1):
        print(f"🧪 DEBUGGING CASE {i}")
        print("=" * 30)
        
        case_text = case_data.get('text', '')
        text_excerpt = case_text[:1000]  # First 1000 chars
        
        print(f"📝 Case text preview:")
        print(f"   Length: {len(case_text)} characters")
        print(f"   First 200 chars: {text_excerpt[:200]}...")
        print()
        
        # Create a simpler, more direct prompt
        simple_prompt = f"""Look at this legal case text and identify the court that decided it.

Case text:
{text_excerpt}

What court decided this case? Look for phrases like "Supreme Court of [State]", "[State] Court of Appeals", "U.S. District Court", etc.

Respond with just the state name (like "texas", "california", "new_york") or "federal" for federal courts. If you can't determine it, respond with "unknown".

Court jurisdiction:"""
        
        try:
            print("🤖 Sending to Gemini...")
            response = model.generate_content(simple_prompt)
            
            if response and response.text:
                raw_response = response.text.strip()
                print(f"✅ Raw Gemini response: '{raw_response}'")
                
                # Simple parsing
                response_lower = raw_response.lower().strip()
                
                # Check if it's a valid jurisdiction
                valid_jurisdictions = {
                    'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 
                    'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 
                    'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana', 
                    'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 
                    'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 
                    'new hampshire', 'new jersey', 'new mexico', 'new york', 
                    'north carolina', 'north dakota', 'ohio', 'oklahoma', 'oregon', 
                    'pennsylvania', 'rhode island', 'south carolina', 'south dakota', 
                    'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 
                    'west virginia', 'wisconsin', 'wyoming', 'federal', 'district of columbia'
                }
                
                # Clean up response
                cleaned_response = response_lower.replace('_', ' ').strip()
                
                if cleaned_response in valid_jurisdictions:
                    print(f"✅ Valid jurisdiction detected: {cleaned_response}")
                elif any(state in cleaned_response for state in valid_jurisdictions):
                    # Find the state mentioned
                    detected_state = next(state for state in valid_jurisdictions if state in cleaned_response)
                    print(f"✅ Jurisdiction found in response: {detected_state}")
                else:
                    print(f"❌ Invalid/unrecognized response: '{cleaned_response}'")
                    print(f"   Not in valid jurisdictions list")
            else:
                print("❌ No response from Gemini")
                
        except Exception as e:
            print(f"❌ Error calling Gemini: {e}")
        
        print()
        print("-" * 50)
        print()
    
    # Test with an obvious case
    print("🧪 TESTING WITH OBVIOUS CASE")
    print("=" * 40)
    
    obvious_case = """HUTSON v. BASSETT et al.
No. 794.
Court of Civil Appeals of Texas. Eastland.
Jan. 30, 1931.
Turner, Seaberry & Springer, of Eastland, for appellant."""
    
    obvious_prompt = f"""What court decided this case?

{obvious_case}

Respond with just the state name or "federal":"""
    
    try:
        response = model.generate_content(obvious_prompt)
        if response and response.text:
            print(f"✅ Obvious case response: '{response.text.strip()}'")
        else:
            print("❌ No response for obvious case")
    except Exception as e:
        print(f"❌ Error with obvious case: {e}")
    
    print()
    print("🎯 DIAGNOSIS COMPLETE")
    print("Check the responses above to see what's going wrong with Gemini classification")

if __name__ == "__main__":
    debug_gemini_responses()
