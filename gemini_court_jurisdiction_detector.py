#!/usr/bin/env python3
"""
Gemini-Powered Court Jurisdiction Detector
Uses Gemini 2.5 Pro to identify the deciding court's jurisdiction
"""

import os
import json
import re
from typing import Dict, Optional, Tuple
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

class GeminiCourtJurisdictionDetector:
    """Uses Gemini 2.5 Pro to detect court jurisdiction."""
    
    def __init__(self):
        """Initialize Gemini client."""
        
        # Configure Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        
        # US jurisdictions for validation
        self.valid_jurisdictions = {
            'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 
            'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 
            'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana', 
            'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 
            'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 
            'new_hampshire', 'new_jersey', 'new_mexico', 'new_york', 
            'north_carolina', 'north_dakota', 'ohio', 'oklahoma', 'oregon', 
            'pennsylvania', 'rhode_island', 'south_carolina', 'south_dakota', 
            'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 
            'west_virginia', 'wisconsin', 'wyoming', 'federal', 'district_of_columbia'
        }
    
    def create_jurisdiction_prompt(self, case_text: str) -> str:
        """Create a prompt for Gemini to identify court jurisdiction."""
        
        # Use first 1500 characters where court info typically appears
        text_excerpt = case_text[:1500]
        
        prompt = f"""You are a legal expert analyzing court cases. Your task is to identify the PRIMARY JURISDICTION of the court that decided this case.

IMPORTANT: Identify the DECIDING COURT's jurisdiction, not courts or laws mentioned in the case text.

Case Text Excerpt:
{text_excerpt}

Instructions:
1. Look for the court that actually decided this case (usually mentioned early in the text)
2. Ignore references to other courts, laws, or jurisdictions mentioned in the opinion
3. Focus on phrases like "Supreme Court of [State]", "[State] Court of Appeals", "U.S. District Court", etc.
4. Return ONLY the jurisdiction where the deciding court is located

Valid Jurisdictions:
- US States: alabama, alaska, arizona, arkansas, california, colorado, connecticut, delaware, florida, georgia, hawaii, idaho, illinois, indiana, iowa, kansas, kentucky, louisiana, maine, maryland, massachusetts, michigan, minnesota, mississippi, missouri, montana, nebraska, nevada, new_hampshire, new_jersey, new_mexico, new_york, north_carolina, north_dakota, ohio, oklahoma, oregon, pennsylvania, rhode_island, south_carolina, south_dakota, tennessee, texas, utah, vermont, virginia, washington, west_virginia, wisconsin, wyoming
- Federal: federal
- DC: district_of_columbia

Response Format:
Return ONLY the jurisdiction name (lowercase, underscores for spaces) and confidence level:
jurisdiction: [jurisdiction_name]
confidence: [high/medium/low]
reasoning: [brief explanation of why this jurisdiction was identified]

Example:
jurisdiction: texas
confidence: high
reasoning: Case decided by "Supreme Court of Texas" as stated in the header"""

        return prompt
    
    def detect_jurisdiction_with_gemini(self, case_data: Dict) -> Tuple[str, str, str]:
        """Use Gemini to detect court jurisdiction."""
        
        case_text = case_data.get('text', '')
        if not case_text or len(case_text.strip()) < 50:
            return 'unclassified', 'low', 'Insufficient text'
        
        try:
            prompt = self.create_jurisdiction_prompt(case_text)
            response = self.model.generate_content(prompt)
            
            if not response or not response.text:
                return 'unclassified', 'low', 'No response from Gemini'
            
            # Parse Gemini response
            response_text = response.text.strip().lower()
            
            jurisdiction = 'unclassified'
            confidence = 'low'
            reasoning = 'Could not parse response'
            
            # Extract jurisdiction
            if 'jurisdiction:' in response_text:
                jurisdiction_line = [line for line in response_text.split('\n') if 'jurisdiction:' in line][0]
                jurisdiction = jurisdiction_line.split('jurisdiction:')[1].strip()
                
                # Validate jurisdiction
                if jurisdiction not in self.valid_jurisdictions:
                    jurisdiction = 'unclassified'
            
            # Extract confidence
            if 'confidence:' in response_text:
                confidence_line = [line for line in response_text.split('\n') if 'confidence:' in line][0]
                confidence = confidence_line.split('confidence:')[1].strip()
                if confidence not in ['high', 'medium', 'low']:
                    confidence = 'low'
            
            # Extract reasoning
            if 'reasoning:' in response_text:
                reasoning_line = [line for line in response_text.split('\n') if 'reasoning:' in line][0]
                reasoning = reasoning_line.split('reasoning:')[1].strip()
            
            return jurisdiction, confidence, reasoning
            
        except Exception as e:
            return 'unclassified', 'low', f'Error: {str(e)}'
    
    def detect_jurisdiction_batch(self, cases_data: list, max_cases: int = 100) -> list:
        """Detect jurisdiction for a batch of cases."""
        
        results = []
        
        for i, case_data in enumerate(cases_data[:max_cases], 1):
            print(f"Processing case {i}/{min(len(cases_data), max_cases)}...")
            
            jurisdiction, confidence, reasoning = self.detect_jurisdiction_with_gemini(case_data)
            
            result = {
                'case_id': case_data.get('id', f'case_{i}'),
                'jurisdiction': jurisdiction,
                'confidence': confidence,
                'reasoning': reasoning,
                'text_preview': case_data.get('text', '')[:200]
            }
            
            results.append(result)
            
            # Show progress
            if jurisdiction != 'unclassified':
                print(f"  ✅ {jurisdiction} ({confidence} confidence)")
            else:
                print(f"  ❌ Unclassified: {reasoning}")
        
        return results

def test_gemini_court_detection():
    """Test Gemini court jurisdiction detection on sample cases."""
    
    print("🧠 TESTING GEMINI COURT JURISDICTION DETECTION")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            'id': 'test_1',
            'text': '''HUTSON v. BASSETT et al.
No. 794.
Court of Civil Appeals of Texas. Eastland.
Jan. 30, 1931.
Turner, Seaberry & Springer, of Eastland, for appellant.
The case involves a dispute over property rights...'''
        },
        {
            'id': 'test_2', 
            'text': '''AMERICAN MUT. LIABILITY INS. CO. et al. v. THOMAS.
No. 9489.
Court of Civil Appeals of Texas. Galveston.
Jan. 15, 1931.
Rehearing Denied Feb. 5, 1931.
Baker, Botts, Parker & Garwood, of Houston, for appellants.
This insurance case involves...'''
        },
        {
            'id': 'test_3',
            'text': '''Brown v. Board of Education
347 U.S. 483 (1954)
Supreme Court of the United States
Argued December 9, 1952
Decided May 17, 1954
This landmark case addresses segregation in public schools...'''
        }
    ]
    
    try:
        detector = GeminiCourtJurisdictionDetector()
        
        print("🧪 Testing on sample cases...")
        results = detector.detect_jurisdiction_batch(test_cases)
        
        print(f"\n📊 GEMINI DETECTION RESULTS:")
        print("=" * 40)
        
        for result in results:
            print(f"Case: {result['case_id']}")
            print(f"  Jurisdiction: {result['jurisdiction']}")
            print(f"  Confidence: {result['confidence']}")
            print(f"  Reasoning: {result['reasoning']}")
            print(f"  Text Preview: {result['text_preview']}...")
            print()
        
        # Calculate accuracy
        expected_results = ['texas', 'texas', 'federal']
        actual_results = [r['jurisdiction'] for r in results]
        
        correct = sum(1 for expected, actual in zip(expected_results, actual_results) if expected == actual)
        accuracy = (correct / len(expected_results)) * 100
        
        print(f"🎯 ACCURACY: {correct}/{len(expected_results)} ({accuracy:.1f}%)")
        
        if accuracy >= 80:
            print("✅ Gemini court detection is working excellently!")
            print("🚀 Ready to test on actual CAP files!")
            return True
        else:
            print("⚠️ Gemini detection needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Gemini detection: {e}")
        print("💡 Make sure GEMINI_API_KEY is set in your .env file")
        return False

if __name__ == "__main__":
    success = test_gemini_court_detection()
    
    if success:
        print("\n🎉 Gemini-powered court jurisdiction detection is ready!")
        print("💡 This should achieve 90%+ classification rate vs 48.2% with regex")
    else:
        print("\n🔧 Need to fix Gemini setup before proceeding")
