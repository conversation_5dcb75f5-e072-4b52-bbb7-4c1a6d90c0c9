#!/usr/bin/env python3
"""
Test 1: Basic CourtListener Processing using existing working code
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import existing working components
from src.api.courtlistener.client import Court<PERSON><PERSON><PERSON><PERSON>lient
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CourtListenerExistingCodeTest:
    """Test CourtListener processing using existing working code"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Initialize existing CourtListener client
        self.cl_client = CourtListenerClient()
        
        # Test batch ID
        self.test_batch_id = f"cl_existing_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def get_courtlistener_test_cases(self, num_cases: int = 5) -> list:
        """Get CourtListener test cases using existing working client"""
        
        print(f"📡 FETCHING COURTLISTENER CASES (EXISTING CODE)")
        print("=" * 60)
        
        test_cases = []
        
        # Define test courts (mix of levels)
        test_courts = [
            {"court": "scotus", "name": "Supreme Court of the United States"},
            {"court": "ca5", "name": "U.S. Court of Appeals, Fifth Circuit"},
            {"court": "txnd", "name": "U.S. District Court, Northern District of Texas"},
            {"court": "ca9", "name": "U.S. Court of Appeals, Ninth Circuit"},
            {"court": "nynd", "name": "U.S. District Court, Northern District of New York"}
        ]
        
        for court_info in test_courts[:num_cases]:
            court_id = court_info["court"]
            court_name = court_info["name"]
            
            print(f"\n📄 Fetching case from {court_name} ({court_id})")
            
            try:
                # Use existing method to get cases from this court
                response = self.cl_client.get_clusters(
                    court=court_id,
                    page_size=1,
                    ordering="-citation_count"
                )
                
                if response.get('results'):
                    # Get the first case
                    case_data = response['results'][0]
                    
                    # Transform to our expected format
                    transformed_case = self.transform_case_data(case_data, court_info)
                    
                    if transformed_case:
                        test_cases.append(transformed_case)
                        print(f"   ✅ Retrieved: {transformed_case.get('case_name', 'Unknown')}")
                        print(f"      Text length: {len(transformed_case.get('text', '')):,} characters")
                    else:
                        print(f"   ⚠️ No usable text content")
                else:
                    print(f"   ⚠️ No cases found for {court_id}")
                    
            except Exception as e:
                print(f"   ❌ Error fetching from {court_id}: {e}")
                continue
        
        print(f"\n📊 COURTLISTENER FETCH SUMMARY:")
        print(f"   Successfully retrieved: {len(test_cases)} cases")
        print(f"   Courts represented: {len(set(case.get('court') for case in test_cases))}")
        
        return test_cases
    
    def transform_case_data(self, case_data: dict, court_info: dict) -> dict:
        """Transform CourtListener case to our format"""
        
        try:
            # Extract text content from opinions
            text_content = ""
            
            # Check for plain_text in the case data
            if case_data.get('plain_text'):
                text_content = case_data['plain_text']
            elif case_data.get('text'):
                text_content = case_data['text']
            elif case_data.get('html_with_citations'):
                # Strip HTML tags for plain text
                import re
                text_content = re.sub(r'<[^>]+>', '', case_data['html_with_citations'])
            
            # If no text in main case, try to get from sub_opinions
            if not text_content and case_data.get('sub_opinions'):
                for opinion_url in case_data.get('sub_opinions', [])[:1]:  # Just get first opinion
                    try:
                        opinion_id = opinion_url.split('/')[-2]
                        opinion = self.cl_client.get_opinion_raw(opinion_id, full_case=True)
                        if opinion.get('plain_text'):
                            text_content = opinion['plain_text']
                            break
                        elif opinion.get('html_with_citations'):
                            import re
                            text_content = re.sub(r'<[^>]+>', '', opinion['html_with_citations'])
                            break
                    except:
                        continue
            
            if not text_content or len(text_content) < 500:
                return None
            
            # Transform to our format (make ID unique by including court)
            transformed_case = {
                'id': f"cl_{court_info['court']}_{case_data.get('id', 'unknown')}",
                'source': 'courtlistener',
                'case_name': case_data.get('case_name', 'Unknown Case'),
                'court': court_info['court'],
                'court_name': court_info['name'],
                'date_filed': case_data.get('date_filed'),
                'jurisdiction': 'US',  # Federal
                'text': text_content,
                'plain_text': text_content,
                'judges': self.extract_judges_from_case(case_data),
                'citations': case_data.get('citations', []),
                'cl_cluster_id': case_data.get('id'),
                'docket_number': case_data.get('docket_number', ''),
                'precedential_status': case_data.get('precedential_status', 'Unknown')
            }
            
            return transformed_case
            
        except Exception as e:
            logger.error(f"Error transforming CourtListener case: {e}")
            return None
    
    def extract_judges_from_case(self, case_data: dict) -> list:
        """Extract judge information from CourtListener case data"""
        
        judges = []
        
        # From panel
        if case_data.get('panel'):
            for judge in case_data['panel']:
                if isinstance(judge, dict):
                    judges.append({
                        'name': judge.get('name_full', judge.get('name_last', 'Unknown')),
                        'role': 'panel'
                    })
                elif isinstance(judge, str):
                    judges.append({
                        'name': judge,
                        'role': 'panel'
                    })
        
        return judges
    
    async def test_basic_processing(self) -> bool:
        """Test basic CourtListener processing through all storage systems"""
        
        print(f"\n🔄 BASIC COURTLISTENER PROCESSING TEST")
        print("=" * 60)
        
        try:
            # Get test cases using existing code
            test_cases = self.get_courtlistener_test_cases(5)
            
            if not test_cases:
                print("❌ No CourtListener test cases retrieved")
                return False
            
            print(f"\n📊 Processing {len(test_cases)} CourtListener cases")
            
            # Show case summary
            for i, case in enumerate(test_cases, 1):
                print(f"   {i}. {case.get('case_name', 'Unknown')} ({case.get('court', 'Unknown')})")
                print(f"      Date: {case.get('date_filed', 'Unknown')}")
                print(f"      Text length: {len(case.get('text', '')):,} characters")
                print(f"      Judges: {len(case.get('judges', []))}")
            
            # Process through existing pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing through full pipeline...")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify all storage systems
            return await self.verify_all_storage_systems()
            
        except Exception as e:
            print(f"❌ Basic processing test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_all_storage_systems(self) -> bool:
        """Verify data exists in all 4 storage systems"""
        
        print(f"\n🔍 VERIFYING ALL STORAGE SYSTEMS")
        print("=" * 60)
        
        try:
            # 1. Supabase verification
            print(f"📊 1. SUPABASE VERIFICATION:")
            cases = self.supabase.table('cases').select('*').eq('batch_id', self.test_batch_id).execute()
            supabase_count = len(cases.data)
            print(f"   Cases stored: {supabase_count}")
            
            if supabase_count == 0:
                print(f"   ❌ No cases found in Supabase")
                return False
            
            # Show sample case data
            sample_case = cases.data[0]
            print(f"   Sample case: {sample_case.get('case_name', 'N/A')}")
            print(f"   Court: {sample_case.get('court', 'N/A')}")
            print(f"   Source: {sample_case.get('source', 'N/A')}")
            
            # 2. GCS verification
            print(f"\n📊 2. GCS VERIFICATION:")
            gcs_paths = [case.get('gcs_path') for case in cases.data if case.get('gcs_path')]
            gcs_files_exist = 0
            
            for gcs_path in gcs_paths:
                if gcs_path:
                    try:
                        if gcs_path.startswith('gs://'):
                            bucket_path = gcs_path.split('/', 3)[-1]
                        else:
                            bucket_path = gcs_path
                        
                        blob = self.gcs_client.bucket.blob(bucket_path)
                        if blob.exists():
                            gcs_files_exist += 1
                    except:
                        pass
            
            print(f"   GCS files verified: {gcs_files_exist}/{len(gcs_paths)}")
            
            # 3. Neo4j verification
            print(f"\n📊 3. NEO4J VERIFICATION:")
            with self.neo4j_client.driver.session() as session:
                # Count case nodes
                result = session.run('MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as count', 
                                   batch_id=self.test_batch_id)
                neo4j_count = result.single()['count']
                print(f"   Case nodes: {neo4j_count}")
                
                # Check for enhanced data
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})
                    WHERE c.case_name IS NOT NULL AND c.case_name <> ""
                    RETURN count(c) as enriched_count
                ''', batch_id=self.test_batch_id)
                enriched_count = result.single()['enriched_count']
                print(f"   Cases with metadata: {enriched_count}")
            
            # 4. Cross-system consistency check
            print(f"\n📊 4. CROSS-SYSTEM CONSISTENCY:")
            consistency_issues = []
            
            if supabase_count != gcs_files_exist:
                consistency_issues.append(f"Supabase ({supabase_count}) vs GCS ({gcs_files_exist})")
            
            if supabase_count != neo4j_count:
                consistency_issues.append(f"Supabase ({supabase_count}) vs Neo4j ({neo4j_count})")
            
            if consistency_issues:
                print(f"   ❌ Consistency issues:")
                for issue in consistency_issues:
                    print(f"      - {issue}")
                return False
            else:
                print(f"   ✅ Perfect consistency across all systems")
            
            print(f"\n🎉 ALL STORAGE SYSTEMS VERIFIED!")
            print(f"✅ CourtListener data successfully processed across all 4 systems")
            
            return True
            
        except Exception as e:
            print(f"❌ Storage verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
            print("   ✅ Cleaned Neo4j test nodes")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run CourtListener basic processing test using existing code"""
    
    print("🧪 COURTLISTENER BASIC PROCESSING TEST (EXISTING CODE)")
    print("=" * 80)
    print("🎯 Testing CourtListener data using existing working client")
    
    test = CourtListenerExistingCodeTest()
    
    try:
        # Run the test
        success = await test.test_basic_processing()
        
        if success:
            print(f"\n🎉 COURTLISTENER BASIC PROCESSING: SUCCESS!")
            print(f"✅ All storage systems working with CourtListener data")
            return True
        else:
            print(f"\n❌ COURTLISTENER BASIC PROCESSING: FAILED!")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
