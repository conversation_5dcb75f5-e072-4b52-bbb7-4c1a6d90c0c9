#!/usr/bin/env python3
"""
Analyze what Texas data is available in the CAP files.
"""

import os
import gzip
import json
from collections import defaultdict

def analyze_texas_cap_data():
    """Analyze Texas cases in CAP data files."""
    
    print("🔍 ANALYZING TEXAS CAP DATA")
    print("=" * 50)
    
    cap_dir = "data/caselaw_access_project"
    texas_cases = []
    file_stats = {}
    
    # Check each CAP file
    for i in range(16):  # cap_00000.jsonl.gz to cap_00015.jsonl.gz
        filename = f"cap_{i:05d}.jsonl.gz"
        filepath = os.path.join(cap_dir, filename)
        
        if not os.path.exists(filepath):
            continue
        
        print(f"📁 Checking {filename}...")
        
        try:
            texas_count = 0
            total_count = 0
            
            with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    if line.strip():
                        try:
                            case = json.loads(line)
                            total_count += 1
                            
                            # Check if this is a Texas case
                            jurisdiction = case.get('jurisdiction', {})
                            if isinstance(jurisdiction, dict):
                                name = jurisdiction.get('name', '').lower()
                                name_long = jurisdiction.get('name_long', '').lower()
                                
                                if 'texas' in name or 'texas' in name_long:
                                    texas_count += 1
                                    if len(texas_cases) < 10:  # Keep first 10 for analysis
                                        texas_cases.append(case)
                        
                        except json.JSONDecodeError:
                            continue
                    
                    # Sample first 1000 lines for speed
                    if line_num >= 1000:
                        break
            
            file_stats[filename] = {
                'total_sampled': total_count,
                'texas_cases': texas_count,
                'percentage': (texas_count / total_count * 100) if total_count > 0 else 0
            }
            
            print(f"   📊 {texas_count}/{total_count} Texas cases ({texas_count/total_count*100:.1f}%)")
        
        except Exception as e:
            print(f"   ❌ Error reading {filename}: {e}")
    
    # Summary
    total_texas = sum(stats['texas_cases'] for stats in file_stats.values())
    total_sampled = sum(stats['total_sampled'] for stats in file_stats.values())
    
    print(f"\n📊 SUMMARY:")
    print(f"   Files analyzed: {len(file_stats)}")
    print(f"   Total cases sampled: {total_sampled:,}")
    print(f"   Texas cases found: {total_texas:,}")
    print(f"   Texas percentage: {total_texas/total_sampled*100:.1f}%")
    
    # Estimate total Texas cases
    if total_texas > 0:
        # Rough estimate based on sampling
        estimated_total_texas = total_texas * 10  # Since we only sampled ~1000 lines per file
        print(f"   Estimated total Texas cases: {estimated_total_texas:,}")
    
    # Analyze sample Texas cases
    if texas_cases:
        print(f"\n📋 SAMPLE TEXAS CASES:")
        
        # Analyze courts
        courts = defaultdict(int)
        years = defaultdict(int)
        
        for i, case in enumerate(texas_cases[:5]):
            name = case.get('name', 'N/A')
            court = case.get('court', {}).get('name', 'N/A')
            decision_date = case.get('decision_date', 'N/A')
            year = decision_date.split('-')[0] if '-' in str(decision_date) else 'unknown'
            
            courts[court] += 1
            years[year] += 1
            
            print(f"   {i+1}. {name[:60]}...")
            print(f"      Court: {court}")
            print(f"      Date: {decision_date}")
            print()
        
        print(f"📊 TEXAS CASE ANALYSIS:")
        print(f"   Courts represented: {len(courts)}")
        for court, count in sorted(courts.items()):
            print(f"      {court}: {count} cases")
        
        print(f"   Years represented: {len(years)}")
        for year, count in sorted(years.items()):
            print(f"      {year}: {count} cases")
    
    print(f"\n🎯 RECOMMENDATION:")
    if total_texas > 50:
        print(f"   ✅ Sufficient Texas CAP data available for processing")
        print(f"   📈 Proceed with full Texas processing combining:")
        print(f"      • Court Listener API (recent cases, 12 courts)")
        print(f"      • CAP data (historical cases, estimated {estimated_total_texas:,} cases)")
    else:
        print(f"   ⚠️  Limited Texas CAP data found")
        print(f"   📈 Focus on Court Listener API processing")

if __name__ == "__main__":
    analyze_texas_cap_data()
