#!/usr/bin/env python3
"""
Test Court Listener Integration

This script tests the Court Listener bulk client integration with the processing pipeline.
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append('/Users/<USER>/Documents/GitHub/texas-laws-personalinjury')

from src.processing.courtlistener_bulk_client import CourtListenerBulkClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_jurisdiction_fetch():
    """Test fetching data from a single jurisdiction."""
    logger.info("=== Testing Single Jurisdiction Fetch ===")
    
    # Initialize client
    client = CourtListenerBulkClient()
    
    # Test with a small state (Wyoming) to avoid rate limiting
    test_jurisdiction = 'wy'
    
    try:
        # Fetch sample cases
        logger.info(f"Fetching sample cases for {test_jurisdiction}...")
        cases = client.fetch_jurisdiction_cases(test_jurisdiction, limit=5)
        
        if cases:
            logger.info(f"✅ Successfully fetched {len(cases)} cases for {test_jurisdiction}")
            
            # Show sample case structure
            if cases:
                logger.info("Sample case structure:")
                sample_case = cases[0]
                logger.info(f"  ID: {sample_case.get('id')}")
                logger.info(f"  Case Name: {sample_case.get('case_name', 'N/A')}")
                logger.info(f"  Date Filed: {sample_case.get('date_filed', 'N/A')}")
                logger.info(f"  Court: {sample_case.get('court', {}).get('full_name', 'N/A')}")
                logger.info(f"  URL: {sample_case.get('absolute_url', 'N/A')}")
            
        else:
            logger.warning(f"⚠️ No cases found for {test_jurisdiction}")
            
    except Exception as e:
        logger.error(f"❌ Error fetching cases for {test_jurisdiction}: {e}")
        return False
    
    return True

def test_data_transformation():
    """Test data transformation from Court Listener to internal format."""
    logger.info("=== Testing Data Transformation ===")
    
    # Sample Court Listener case data
    sample_court_listener_case = {
        'id': 12345,
        'case_name': 'Test v. Example',
        'case_name_short': 'Test v. Example',
        'date_filed': '2023-01-15',
        'court': {
            'full_name': 'Wyoming Supreme Court',
            'short_name': 'Wyo. S. Ct.'
        },
        'citations': [
            {'volume': '123', 'reporter': 'P.3d', 'page': '456'}
        ],
        'sub_opinions': [
            {'text': 'This is a test opinion text...'}
        ],
        'absolute_url': 'https://www.courtlistener.com/opinion/12345/test-v-example/',
        'docket_number': '2023-CV-001'
    }
    
    try:
        client = CourtListenerBulkClient()
        
        # Test transformation
        transformed = client._transform_court_listener_case(sample_court_listener_case, 'wy')
        
        logger.info("✅ Transformation successful!")
        logger.info(f"  Transformed case name: {transformed.get('name')}")
        logger.info(f"  Transformed jurisdiction: {transformed.get('jurisdiction')}")
        logger.info(f"  Transformed court: {transformed.get('court')}")
        logger.info(f"  Transformed source: {transformed.get('source')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in transformation: {e}")
        return False

def test_processing_estimates():
    """Test processing time estimates."""
    logger.info("=== Testing Processing Estimates ===")
    
    try:
        client = CourtListenerBulkClient()
        
        # Get processing estimates
        estimate = client.get_processing_estimate()
        
        logger.info("✅ Processing estimates:")
        logger.info(f"  Missing jurisdictions: {estimate['missing_jurisdictions']}")
        logger.info(f"  Estimated cases: {estimate['estimated_cases']:,}")
        logger.info(f"  Processing time: {estimate['processing_time_hours']:.2f} hours")
        logger.info(f"  API time: {estimate['api_time_hours']:.2f} hours")
        logger.info(f"  Total time: {estimate['total_time_hours']:.2f} hours")
        
        # Get processing plan
        plan = client.create_processing_plan()
        
        logger.info("✅ Processing plan:")
        logger.info(f"  Total estimated cases: {plan['overview']['estimated_cases']:,}")
        logger.info(f"  Total estimated time: {plan['overview']['estimated_time_hours']:.2f} hours")
        
        logger.info("  Phases:")
        for phase in plan['phases']:
            logger.info(f"    {phase['name']}: {len(phase['jurisdictions'])} jurisdictions")
            logger.info(f"      Priority: {phase['priority']}")
            logger.info(f"      Estimated cases: {phase['estimated_cases']:,}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in processing estimates: {e}")
        return False

def test_api_connectivity():
    """Test API connectivity and authentication."""
    logger.info("=== Testing API Connectivity ===")
    
    try:
        client = CourtListenerBulkClient()
        
        # Test basic API connectivity
        test_url = f"{client.base_url}/courts/"
        response = client.session.get(test_url, params={'format': 'json', 'page_size': 1})
        
        if response.status_code == 200:
            logger.info("✅ API connectivity successful!")
            logger.info(f"  Status code: {response.status_code}")
            logger.info(f"  Response contains data: {len(response.json().get('results', [])) > 0}")
            return True
        else:
            logger.error(f"❌ API connectivity failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing API connectivity: {e}")
        return False

def main():
    """Run all Court Listener integration tests."""
    logger.info("🧪 Starting Court Listener Integration Tests")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("=" * 60)
    
    tests = [
        ("API Connectivity", test_api_connectivity),
        ("Processing Estimates", test_processing_estimates),
        ("Data Transformation", test_data_transformation),
        ("Single Jurisdiction Fetch", test_single_jurisdiction_fetch),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            logger.info(f"Running {test_name}...")
            results[test_name] = test_func()
            logger.info(f"{'✅' if results[test_name] else '❌'} {test_name} completed\n")
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}\n")
            results[test_name] = False
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {passed/total*100:.1f}%")
    
    logger.info("\nDetailed results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
    
    if passed == total:
        logger.info("\n🎉 All tests passed! Court Listener integration is ready.")
    else:
        logger.info(f"\n⚠️ {total - passed} tests failed. Please review the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)