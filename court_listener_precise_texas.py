#!/usr/bin/env python3
"""
Court Listener Precise Texas Processing
Use court-based filtering like CAP data to get only Texas court decisions
"""

import os
import requests
import time
from dotenv import load_dotenv
from typing import List, Dict, Optional

load_dotenv()

class PreciseCourtListenerProcessor:
    """Precise Court Listener processor using court-based filtering like CAP."""
    
    def __init__(self):
        """Initialize with Court Listener API."""
        
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        if not self.api_key:
            raise ValueError("COURTLISTENER_API_KEY not found in environment")
        
        self.base_url = "https://www.courtlistener.com/api/rest/v4"
        self.headers = {
            'Authorization': f'Token {self.api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        # Texas courts from your screenshots - precise court IDs
        self.texas_courts = {
            # State Courts (from screenshots)
            'tex': 'Texas Supreme Court',
            'texcrimapp': 'Texas Court of Criminal Appeals',
            'texapp': 'Court of Appeals of Texas',  # General appeals court
            'texspecrev': 'Texas Special Court of Review',
            'texjpmdl': 'Texas Judicial Panel on Multidistrict Litigation',
            'texag': 'Texas Attorney General Reports',
            
            # Federal District Courts in Texas (from screenshots)
            'txed': 'E.D. Texas (Eastern District)',
            'txnd': 'N.D. Texas (Northern District)', 
            'txsd': 'S.D. Texas (Southern District)',
            'txwd': 'W.D. Texas (Western District)',
            
            # Federal Appeals covering Texas
            'ca5': '5th Circuit Court of Appeals (covers Texas)',
        }
        
        print(f"🔑 API Key: {self.api_key[:10]}...{self.api_key[-4:]}")
        print(f"🏛️ Targeting {len(self.texas_courts)} specific Texas courts")
    
    def get_cases_by_specific_court(self, court_id: str, page_size: int = 100, 
                                   cursor: Optional[str] = None) -> Dict:
        """Get cases from a specific Texas court."""
        
        try:
            url = f"{self.base_url}/search/"
            params = {
                'q': f'court_id:{court_id}',  # Precise court filtering
                'type': 'o',   # Opinions
                'page_size': page_size,
                'ordering': '-dateFiled',  # Most recent first
                'format': 'json'
            }
            
            # Add cursor for pagination if provided
            if cursor:
                params['cursor'] = cursor
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Error for court {court_id}: HTTP {response.status_code}")
                return {}
                
        except Exception as e:
            print(f"❌ Error for court {court_id}: {e}")
            return {}
    
    def get_all_texas_courts_combined(self, page_size: int = 100, 
                                     cursor: Optional[str] = None) -> Dict:
        """Get cases from all Texas courts in one query using OR logic."""
        
        try:
            url = f"{self.base_url}/search/"
            
            # Build OR query for all Texas courts
            court_ids = list(self.texas_courts.keys())
            court_query = f"court_id:({' OR '.join(court_ids)})"
            
            params = {
                'q': court_query,
                'type': 'o',   # Opinions
                'page_size': page_size,
                'ordering': '-dateFiled',  # Most recent first
                'format': 'json'
            }
            
            # Add cursor for pagination if provided
            if cursor:
                params['cursor'] = cursor
            
            print(f"🔍 Query: {court_query}")
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Error for combined query: HTTP {response.status_code}")
                return {}
                
        except Exception as e:
            print(f"❌ Error for combined query: {e}")
            return {}
    
    def analyze_court_coverage(self):
        """Analyze coverage for each Texas court individually."""
        
        print("🏛️ ANALYZING TEXAS COURT COVERAGE")
        print("=" * 60)
        
        court_stats = {}
        total_cases = 0
        
        for court_id, court_name in self.texas_courts.items():
            print(f"🔍 {court_name} ({court_id})...", end=" ")
            
            # Get count for this court
            results = self.get_cases_by_specific_court(court_id, page_size=1)
            count = results.get('count', 0)
            court_stats[court_id] = {
                'name': court_name,
                'count': count
            }
            total_cases += count
            
            print(f"{count:,} cases")
            
            # Small delay to respect rate limits
            time.sleep(0.2)
        
        print(f"\n📊 COURT COVERAGE ANALYSIS")
        print("=" * 60)
        
        # Sort courts by case count
        sorted_courts = sorted(court_stats.items(), key=lambda x: x[1]['count'], reverse=True)
        
        print(f"🎯 Total Texas court cases: {total_cases:,}")
        
        print(f"\n📈 TOP COURTS BY VOLUME:")
        for i, (court_id, info) in enumerate(sorted_courts, 1):
            if info['count'] > 0:
                percentage = (info['count'] / total_cases * 100) if total_cases > 0 else 0
                print(f"  {i:2d}. {info['name']}: {info['count']:,} ({percentage:.1f}%)")
        
        # Test combined query
        print(f"\n🔗 TESTING COMBINED COURT QUERY...")
        combined_results = self.get_all_texas_courts_combined(page_size=20)
        combined_count = combined_results.get('count', 0)
        combined_batch = len(combined_results.get('results', []))
        
        print(f"   ✅ Combined query returned: {combined_count:,} total cases")
        print(f"   📄 First batch size: {combined_batch} cases")
        
        if combined_results.get('results'):
            print(f"   📋 Sample cases from combined query:")
            for i, case in enumerate(combined_results['results'][:3], 1):
                court_name = self.texas_courts.get(case.get('court_id', ''), 'Unknown Court')
                print(f"      {i}. {case.get('caseName', 'Unknown')[:50]}...")
                print(f"         Court: {court_name} ({case.get('court_id', 'Unknown')})")
        
        # Compare approaches
        print(f"\n📊 COMPARISON:")
        print(f"   Individual court totals: {total_cases:,}")
        print(f"   Combined query total: {combined_count:,}")
        print(f"   Broad 'texas' search: 1,068,742")
        print(f"   Current processing: 246")
        
        if combined_count > 0:
            improvement = ((combined_count - 246) / 246 * 100) if 246 > 0 else float('inf')
            print(f"   Improvement with court filtering: {improvement:.0f}%")
        
        return {
            'court_stats': court_stats,
            'total_individual': total_cases,
            'combined_total': combined_count,
            'combined_batch_size': combined_batch
        }
    
    def test_pagination_with_court_filter(self, max_pages: int = 3):
        """Test pagination with precise court filtering."""
        
        print(f"\n🔄 TESTING COURT-FILTERED PAGINATION")
        print("=" * 50)
        
        cursor = None
        total_retrieved = 0
        court_distribution = {}
        
        for page in range(max_pages):
            print(f"📄 Page {page + 1}...")
            
            results = self.get_all_texas_courts_combined(page_size=100, cursor=cursor)
            
            if not results or not results.get('results'):
                print(f"   ❌ No results on page {page + 1}")
                break
            
            batch_size = len(results['results'])
            total_retrieved += batch_size
            
            # Analyze court distribution in this batch
            for case in results['results']:
                court_id = case.get('court_id', 'unknown')
                court_distribution[court_id] = court_distribution.get(court_id, 0) + 1
            
            print(f"   ✅ Retrieved {batch_size} cases (total: {total_retrieved})")
            
            # Show sample case from this page
            if results['results']:
                sample = results['results'][0]
                court_name = self.texas_courts.get(sample.get('court_id', ''), 'Unknown')
                print(f"   📋 Sample: {sample.get('caseName', 'Unknown')[:40]}...")
                print(f"       Court: {court_name}")
            
            # Get next cursor
            next_url = results.get('next')
            if next_url and 'cursor=' in next_url:
                cursor = next_url.split('cursor=')[1].split('&')[0]
                print(f"   🔗 Next cursor available")
            else:
                print(f"   🏁 No more pages available")
                break
            
            # Small delay to respect rate limits
            time.sleep(0.5)
        
        print(f"\n📊 PAGINATION TEST RESULTS:")
        print(f"   Pages tested: {page + 1}")
        print(f"   Total cases retrieved: {total_retrieved}")
        print(f"   Average per page: {total_retrieved / (page + 1):.1f}")
        
        print(f"\n🏛️ COURT DISTRIBUTION IN SAMPLE:")
        for court_id, count in sorted(court_distribution.items(), key=lambda x: x[1], reverse=True):
            court_name = self.texas_courts.get(court_id, f'Unknown ({court_id})')
            percentage = (count / total_retrieved * 100) if total_retrieved > 0 else 0
            print(f"   {court_name}: {count} ({percentage:.1f}%)")
        
        return total_retrieved, court_distribution

def main():
    """Main execution function."""
    
    try:
        processor = PreciseCourtListenerProcessor()
        
        # Analyze court coverage
        coverage_results = processor.analyze_court_coverage()
        
        # Test pagination with court filtering
        paginated_total, court_dist = processor.test_pagination_with_court_filter(max_pages=3)
        
        print(f"\n🎯 CONCLUSIONS:")
        print(f"✅ Court-based filtering works like CAP approach")
        print(f"✅ {coverage_results['combined_total']:,} Texas court cases available")
        print(f"✅ Pagination works with court filtering")
        print(f"✅ Avoids multi-state cases (unlike broad 'texas' search)")
        
        print(f"\n📊 COMPARISON WITH BROAD SEARCH:")
        broad_total = 1068742
        court_total = coverage_results['combined_total']
        difference = broad_total - court_total
        
        print(f"   Broad 'texas' search: {broad_total:,}")
        print(f"   Court-filtered search: {court_total:,}")
        print(f"   Difference: {difference:,} (likely multi-state cases)")
        print(f"   Court filtering precision: {(court_total/broad_total*100):.1f}%")
        
        print(f"\n🔧 RECOMMENDED APPROACH:")
        print(f"✅ Use court-based filtering (like CAP)")
        print(f"✅ Target specific Texas courts only")
        print(f"✅ Avoid broad text search that captures multi-state cases")
        print(f"✅ Implement cursor-based pagination")
        print(f"✅ Remove date restrictions for full historical coverage")
        
        return coverage_results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    results = main()
