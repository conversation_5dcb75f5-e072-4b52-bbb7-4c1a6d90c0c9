#!/usr/bin/env python3
"""
Test Improved Real Patterns
Test the improved patterns that match real federal court document formats
"""

from judge_relationship_enhancer import JudgeRelationshipEnhancer

def test_real_court_patterns():
    """Test patterns on real federal court document formats"""
    
    print("🔍 TESTING IMPROVED REAL COURT PATTERNS")
    print("=" * 60)
    
    # Real federal court document formats
    real_court_texts = [
        # Format 1: Before pattern with multiple judges
        """
        No. 23-40582
        
        UNITED STATES COURT OF APPEALS
        FOR THE FIFTH CIRCUIT
        
        HARRIS v. UNITED STATES
        
        Before DIAZ, HARRIS, and WILSON, Circuit Judges.
        
        PER CURIAM:
        
        A<PERSON>lant challenges the district court's denial.
        """,
        
        # Format 2: Before pattern with two judges
        """
        No. 24-12345
        
        UNITED STATES COURT OF APPEALS
        FOR THE SECOND CIRCUIT
        
        SMITH v. JONES
        
        Before MARTINEZ and RODRIGUEZ, Circuit Judges.
        
        MARTINEZ, Circuit Judge:
        
        We reverse the district court's judgment.
        """,
        
        # Format 3: Single judge
        """
        No. 24-67890
        
        UNITED STATES DISTRICT COURT
        NORTHERN DISTRICT OF <PERSON>EX<PERSON>
        
        JOHNSON v. <PERSON><PERSON><PERSON><PERSON><PERSON>
        
        Before THOMPSON, Circuit Judge.
        
        MEMORAND<PERSON> OPINION AND ORDER
        """,
        
        # Format 4: Mixed with traditional patterns
        """
        SUPREME COURT OF THE UNITED STATES
        
        BROWN v. BOARD OF EDUCATION
        
        Mr. Chief Justice Earl Warren delivered the opinion of the Court.
        
        Justice Hugo L. Black, with whom Justice William O. Douglas joins, concurring.
        """
    ]
    
    enhancer = JudgeRelationshipEnhancer()
    
    total_judges = 0
    total_full_names = 0
    
    for i, text in enumerate(real_court_texts, 1):
        print(f"\n📄 TEST CASE {i}:")
        print(f"   Text: {text.strip()[:100]}...")
        
        # Extract judges
        judges = enhancer._extract_judges_from_text(text)
        
        print(f"   📊 Judges extracted: {len(judges)}")
        
        case_full_names = 0
        for judge in judges:
            words = len(judge['name'].split())
            word_status = "✅ Full" if words > 1 else "❌ Partial"
            print(f"      - {judge['name']} ({word_status}, {words} words, role: {judge['role']})")
            
            total_judges += 1
            if words > 1:
                case_full_names += 1
                total_full_names += 1
        
        case_ratio = case_full_names / len(judges) if judges else 0
        print(f"   📊 Full name ratio: {case_ratio:.1%} ({case_full_names}/{len(judges)})")
    
    enhancer.close()
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Total judges: {total_judges}")
    print(f"   Full names: {total_full_names}")
    print(f"   Full name ratio: {total_full_names/total_judges*100:.1f}%" if total_judges > 0 else "   No judges found")
    
    # Success criteria
    success = (
        total_judges > 0 and
        total_full_names >= total_judges * 0.7  # At least 70% full names
    )
    
    if success:
        print(f"\n🎉 IMPROVED REAL PATTERNS: SUCCESS!")
        print(f"✅ Real court document patterns working")
        print(f"✅ {total_full_names/total_judges*100:.1f}% full name ratio")
    else:
        print(f"\n⚠️ IMPROVED REAL PATTERNS: NEEDS MORE WORK!")
        if total_judges == 0:
            print(f"❌ No judges extracted")
        else:
            print(f"❌ Low full name ratio: {total_full_names/total_judges*100:.1f}%")
    
    return success


def test_specific_before_patterns():
    """Test the specific Before patterns"""
    
    print(f"\n🔬 TESTING SPECIFIC BEFORE PATTERNS")
    print("=" * 60)
    
    import re
    
    # Test the specific patterns
    before_patterns = [
        r'Before\s+(?:[A-Z][A-Z]+,\s+)*([A-Z][A-Z]+),\s+(?:and\s+)?([A-Z][A-Z]+),\s+Circuit\s+Judges',
        r'Before\s+(?:[A-Z][A-Z]+,\s+)*([A-Z][A-Z]+)\s+and\s+([A-Z][A-Z]+),\s+Circuit\s+Judges',
        r'Before\s+([A-Z][A-Z]+),\s+Circuit\s+Judge',
    ]
    
    test_texts = [
        "Before DIAZ, HARRIS, and WILSON, Circuit Judges.",
        "Before MARTINEZ and RODRIGUEZ, Circuit Judges.",
        "Before THOMPSON, Circuit Judge.",
        "Before SMITH, JONES, and WILLIAMS, Circuit Judges.",
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n   Test {i}: {text}")
        
        found_match = False
        for j, pattern in enumerate(before_patterns, 1):
            match = re.search(pattern, text)
            if match:
                print(f"      ✅ Pattern {j} matches:")
                print(f"         Groups: {match.groups()}")
                for k, group in enumerate(match.groups(), 1):
                    if group:
                        print(f"         Judge {k}: {group}")
                found_match = True
                break
        
        if not found_match:
            print(f"      ❌ No patterns match")
    
    return True


def main():
    """Run improved real pattern tests"""
    
    print("🧪 IMPROVED REAL COURT PATTERN TESTING")
    print("=" * 80)
    print("🎯 Testing patterns that match actual federal court document formats")
    
    # Test specific before patterns
    test_specific_before_patterns()
    
    # Test on real court document formats
    success = test_real_court_patterns()
    
    if success:
        print(f"\n🎉 READY TO TEST ON REAL DATA!")
        print(f"✅ Patterns improved for real federal court documents")
        return True
    else:
        print(f"\n⚠️ PATTERNS NEED MORE REFINEMENT!")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
