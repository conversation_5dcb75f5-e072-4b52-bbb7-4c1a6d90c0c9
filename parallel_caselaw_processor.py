#!/usr/bin/env python3
"""
Parallel Case Law Processing System

This system enables processing multiple states/jurisdictions in parallel
while maintaining data quality and avoiding resource conflicts.
"""

import asyncio
import argparse
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from processing.caselaw_access_processor import CaselawAccessProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('parallel_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ParallelCaselawProcessor:
    """Manages parallel processing of multiple jurisdictions."""
    
    def __init__(self, data_dir: str = "data/caselaw_access_project"):
        self.data_dir = Path(data_dir)
        self.results = {}
        
    def discover_jurisdictions(self) -> Dict[str, List[Path]]:
        """
        Discover available jurisdictions and their files.
        
        Returns:
            Dict mapping jurisdiction codes to file lists
        """
        jurisdictions = {}
        
        # Scan for jurisdiction-specific directories or files
        for file_path in self.data_dir.rglob("*.jsonl"):
            # Extract jurisdiction from file path
            # Assuming pattern like: f2d_XXX -> federal, state_XX -> state codes
            file_name = file_path.name
            
            if file_name.startswith('f2d_') or file_name.startswith('f3d_'):
                jurisdiction = 'federal'
            elif file_name.startswith('cal_'):
                jurisdiction = 'california'
            elif file_name.startswith('tex_'):
                jurisdiction = 'texas'
            elif file_name.startswith('ny_'):
                jurisdiction = 'new_york'
            elif file_name.startswith('fl_'):
                jurisdiction = 'florida'
            else:
                # Try to extract from directory structure
                parts = file_path.parts
                if len(parts) > 2:
                    jurisdiction = parts[-2]  # Parent directory name
                else:
                    jurisdiction = 'unknown'
            
            if jurisdiction not in jurisdictions:
                jurisdictions[jurisdiction] = []
            jurisdictions[jurisdiction].append(file_path)
        
        logger.info(f"Discovered {len(jurisdictions)} jurisdictions:")
        for jurisdiction, files in jurisdictions.items():
            logger.info(f"  {jurisdiction}: {len(files)} files")
        
        return jurisdictions
    
    def estimate_processing_time(self, jurisdictions: Dict[str, List[Path]]) -> Dict[str, float]:
        """
        Estimate processing time for each jurisdiction.
        
        Args:
            jurisdictions: Dict mapping jurisdiction to file lists
            
        Returns:
            Dict mapping jurisdiction to estimated hours
        """
        estimates = {}
        
        for jurisdiction, files in jurisdictions.items():
            # Rough estimates based on file count and typical document density
            file_count = len(files)
            
            if jurisdiction == 'federal':
                # Federal cases are typically longer and more complex
                estimated_hours = file_count * 0.5  # 30 minutes per file
            else:
                # State cases are typically shorter
                estimated_hours = file_count * 0.25  # 15 minutes per file
            
            estimates[jurisdiction] = estimated_hours
        
        return estimates
    
    def create_processing_plan(self, 
                             jurisdictions: Dict[str, List[Path]], 
                             max_parallel: int = 4) -> List[List[Tuple[str, List[Path]]]]:
        """
        Create an optimal processing plan for parallel execution.
        
        Args:
            jurisdictions: Available jurisdictions and files
            max_parallel: Maximum parallel processes
            
        Returns:
            List of processing batches
        """
        estimates = self.estimate_processing_time(jurisdictions)
        
        # Sort jurisdictions by estimated time (longest first)
        sorted_jurisdictions = sorted(
            jurisdictions.items(), 
            key=lambda x: estimates[x[0]], 
            reverse=True
        )
        
        # Create balanced batches
        batches = []
        current_batch = []
        current_batch_time = 0
        max_batch_time = max(estimates.values()) if estimates else 1
        
        for jurisdiction, files in sorted_jurisdictions:
            estimated_time = estimates[jurisdiction]
            
            if (len(current_batch) < max_parallel and 
                current_batch_time + estimated_time <= max_batch_time * 1.5):
                current_batch.append((jurisdiction, files))
                current_batch_time += estimated_time
            else:
                if current_batch:
                    batches.append(current_batch)
                current_batch = [(jurisdiction, files)]
                current_batch_time = estimated_time
        
        if current_batch:
            batches.append(current_batch)
        
        logger.info(f"Created {len(batches)} processing batches:")
        for i, batch in enumerate(batches, 1):
            batch_jurisdictions = [j for j, _ in batch]
            logger.info(f"  Batch {i}: {batch_jurisdictions}")
        
        return batches


def process_jurisdiction_worker(jurisdiction: str, files: List[Path], data_dir: str) -> Dict[str, any]:
    """
    Worker function to process a single jurisdiction.
    
    Args:
        jurisdiction: Jurisdiction code
        files: List of files to process
        data_dir: Data directory path
        
    Returns:
        Processing results
    """
    # Set up logging for this worker
    worker_logger = logging.getLogger(f"worker_{jurisdiction}")
    handler = logging.FileHandler(f'processing_{jurisdiction}.log')
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    worker_logger.addHandler(handler)
    worker_logger.setLevel(logging.INFO)
    
    worker_logger.info(f"Starting processing for {jurisdiction} with {len(files)} files")
    
    try:
        # Create processor for this jurisdiction
        processor = CaselawAccessProcessor(data_dir)
        
        # Process files for this jurisdiction
        total_results = {'success': 0, 'failed': 0, 'duplicates': 0}
        
        for file_path in files:
            try:
                # Use asyncio for this file
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                file_results = loop.run_until_complete(processor.process_file(file_path))
                
                for key in total_results:
                    total_results[key] += file_results.get(key, 0)
                
                worker_logger.info(f"Completed file {file_path.name}: {file_results}")
                
            except Exception as e:
                worker_logger.error(f"Error processing file {file_path}: {e}")
                total_results['failed'] += 1
            finally:
                loop.close()
        
        worker_logger.info(f"Completed {jurisdiction}: {total_results}")
        return {
            'jurisdiction': jurisdiction,
            'results': total_results,
            'status': 'completed'
        }
        
    except Exception as e:
        worker_logger.error(f"Fatal error processing {jurisdiction}: {e}")
        return {
            'jurisdiction': jurisdiction,
            'results': {'success': 0, 'failed': len(files), 'duplicates': 0},
            'status': 'failed',
            'error': str(e)
        }


async def main():
    """Main parallel processing function."""
    parser = argparse.ArgumentParser(description='Parallel Case Law Processing')
    parser.add_argument('--data-dir', default='data/caselaw_access_project',
                       help='Data directory containing JSONL files')
    parser.add_argument('--max-parallel', type=int, default=4,
                       help='Maximum parallel processes')
    parser.add_argument('--jurisdictions', nargs='*',
                       help='Specific jurisdictions to process (default: all)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show processing plan without executing')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = ParallelCaselawProcessor(args.data_dir)
    
    # Discover available jurisdictions
    jurisdictions = processor.discover_jurisdictions()
    
    if not jurisdictions:
        logger.error("No jurisdictions found!")
        return
    
    # Filter to specific jurisdictions if requested
    if args.jurisdictions:
        jurisdictions = {k: v for k, v in jurisdictions.items() 
                        if k in args.jurisdictions}
        logger.info(f"Filtered to requested jurisdictions: {list(jurisdictions.keys())}")
    
    # Create processing plan
    batches = processor.create_processing_plan(jurisdictions, args.max_parallel)
    
    # Show estimates
    estimates = processor.estimate_processing_time(jurisdictions)
    total_estimated_hours = sum(estimates.values())
    parallel_estimated_hours = total_estimated_hours / args.max_parallel
    
    logger.info(f"\nProcessing Estimates:")
    logger.info(f"  Total sequential time: {total_estimated_hours:.1f} hours")
    logger.info(f"  Parallel time ({args.max_parallel} processes): {parallel_estimated_hours:.1f} hours")
    logger.info(f"  Speedup: {total_estimated_hours/parallel_estimated_hours:.1f}x")
    
    if args.dry_run:
        logger.info("Dry run completed - no processing performed")
        return
    
    # Execute processing batches
    all_results = {}
    
    for batch_num, batch in enumerate(batches, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"STARTING BATCH {batch_num}/{len(batches)}")
        logger.info(f"{'='*60}")
        
        # Process batch in parallel
        with ProcessPoolExecutor(max_workers=len(batch)) as executor:
            # Submit all jobs in this batch
            future_to_jurisdiction = {
                executor.submit(
                    process_jurisdiction_worker, 
                    jurisdiction, 
                    files, 
                    str(processor.data_dir)
                ): jurisdiction
                for jurisdiction, files in batch
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_jurisdiction):
                jurisdiction = future_to_jurisdiction[future]
                try:
                    result = future.result()
                    all_results[jurisdiction] = result
                    logger.info(f"✅ Completed {jurisdiction}: {result['results']}")
                except Exception as e:
                    logger.error(f"❌ Failed {jurisdiction}: {e}")
                    all_results[jurisdiction] = {
                        'jurisdiction': jurisdiction,
                        'results': {'success': 0, 'failed': 0, 'duplicates': 0},
                        'status': 'failed',
                        'error': str(e)
                    }
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info("PARALLEL PROCESSING COMPLETE")
    logger.info(f"{'='*60}")
    
    total_success = sum(r['results']['success'] for r in all_results.values())
    total_failed = sum(r['results']['failed'] for r in all_results.values())
    total_duplicates = sum(r['results']['duplicates'] for r in all_results.values())
    
    logger.info(f"Overall Results:")
    logger.info(f"  Successful: {total_success:,}")
    logger.info(f"  Failed: {total_failed:,}")
    logger.info(f"  Duplicates: {total_duplicates:,}")
    logger.info(f"  Total: {total_success + total_failed + total_duplicates:,}")
    
    # Save detailed results
    results_file = f"parallel_processing_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    logger.info(f"Detailed results saved to: {results_file}")


if __name__ == "__main__":
    asyncio.run(main())
