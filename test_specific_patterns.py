#!/usr/bin/env python3
"""
Test Specific Patterns
Test the exact patterns that should capture full names
"""

import re

def test_specific_failing_patterns():
    """Test the specific patterns that should capture full names"""
    
    print("🔍 TESTING SPECIFIC FAILING PATTERNS")
    print("=" * 60)
    
    # Test cases that should work
    test_cases = [
        {
            'text': 'Judge <PERSON> wrote the preliminary ruling.',
            'pattern': r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+)\s+(?:delivered|wrote|authored|presiding)',
            'expected': '<PERSON>',
            'description': 'Judge + Full <PERSON> + wrote'
        },
        {
            'text': 'Mr. Chief Justice <PERSON> delivered the opinion of the Court.',
            'pattern': r'Mr\.\s+(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+)\s+delivered',
            'expected': '<PERSON>',
            'description': 'Mr. Chief Justice + Full Name + delivered'
        },
        {
            'text': '<PERSON>, J<PERSON>, concurring in part.',
            'pattern': r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',
            'expected': '<PERSON> <PERSON> <PERSON>',
            'description': 'Full Name + J. + concurring'
        },
        {
            'text': '<PERSON>, C.J., delivered the opinion.',
            'pattern': r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+C\.?J\.,?\s+(?:delivered|wrote)',
            'expected': 'Earl Warren',
            'description': 'Full Name + C.J. + delivered'
        }
    ]
    
    print(f"📊 Testing {len(test_cases)} specific patterns:")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n   {i}. {case['description']}")
        print(f"      Text: {case['text']}")
        print(f"      Pattern: {case['pattern']}")
        print(f"      Expected: '{case['expected']}'")
        
        # Test the pattern
        match = re.search(case['pattern'], case['text'], re.IGNORECASE)
        
        if match:
            extracted = match.group(1).strip()
            success = extracted == case['expected']
            status = "✅" if success else "❌"
            print(f"      Result: {status} Extracted '{extracted}'")
            
            if not success:
                print(f"         Expected: '{case['expected']}'")
                print(f"         Got: '{extracted}'")
        else:
            print(f"      Result: ❌ No match found")
            
            # Debug: try simpler patterns
            print(f"      Debug: Testing simpler patterns...")
            
            # Try without the multi-word requirement
            simple_pattern = case['pattern'].replace('(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+', '(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*')
            simple_match = re.search(simple_pattern, case['text'], re.IGNORECASE)
            
            if simple_match:
                simple_extracted = simple_match.group(1).strip()
                print(f"         Simple pattern matches: '{simple_extracted}'")
            else:
                print(f"         Even simple pattern fails")


def test_regex_components():
    """Test individual regex components"""
    
    print(f"\n🔬 TESTING REGEX COMPONENTS")
    print("=" * 60)
    
    # Test the multi-word name pattern
    name_pattern = r'[A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+'
    
    test_names = [
        'Sarah Michelle Johnson',
        'Earl Warren',
        'Rick A. Warren',
        'Hugo L. Black',
        'Johnson',  # Should not match (single word)
        'Warren'    # Should not match (single word)
    ]
    
    print(f"📊 Testing multi-word name pattern: {name_pattern}")
    
    for name in test_names:
        match = re.fullmatch(name_pattern, name)
        expected_match = len(name.split()) > 1
        success = bool(match) == expected_match
        status = "✅" if success else "❌"
        
        print(f"   {status} '{name}' → {'Match' if match else 'No match'} (expected: {'Match' if expected_match else 'No match'})")


def test_working_solution():
    """Test a working solution with corrected patterns"""
    
    print(f"\n🔧 TESTING WORKING SOLUTION")
    print("=" * 60)
    
    # Corrected patterns that should work
    corrected_patterns = [
        # More flexible patterns that allow for text after action words
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+)\s+(?:delivered|wrote|authored)',
        r'Mr\.\s+(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+)\s+delivered',
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+C\.?J\.,?\s+(?:delivered|wrote)',
    ]
    
    test_text = '''
    Judge Sarah Michelle Johnson wrote the preliminary ruling.
    Mr. Chief Justice Earl Warren delivered the opinion of the Court.
    Sarah Michelle Johnson, J., concurring in part.
    Earl Warren, C.J., delivered the opinion.
    '''
    
    print(f"📄 Test text:")
    print(f"   {test_text.strip()}")
    
    all_matches = []
    
    for i, pattern in enumerate(corrected_patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, test_text, re.IGNORECASE)
        pattern_matches = []
        
        for match in matches:
            extracted = match.group(1).strip()
            pattern_matches.append(extracted)
            all_matches.append(extracted)
        
        if pattern_matches:
            for match in pattern_matches:
                words = len(match.split())
                print(f"      ✅ '{match}' ({words} words)")
        else:
            print(f"      ❌ No matches")
    
    print(f"\n🎯 SUMMARY:")
    unique_matches = list(set(all_matches))
    print(f"   Total unique full names found: {len(unique_matches)}")
    for match in unique_matches:
        print(f"      - {match}")
    
    expected_names = ['Sarah Michelle Johnson', 'Earl Warren']
    found_all = all(any(expected in match for match in unique_matches) for expected in expected_names)
    
    print(f"   Success: {'✅' if found_all else '❌'} ({'All expected names found' if found_all else 'Missing some names'})")


if __name__ == "__main__":
    print("🧪 SPECIFIC PATTERN TESTING")
    print("=" * 80)
    
    # Test specific failing patterns
    test_specific_failing_patterns()
    
    # Test regex components
    test_regex_components()
    
    # Test working solution
    test_working_solution()
    
    print(f"\n🎯 GOAL: Fix patterns to extract full names!")
    print(f"   Target: Get 'Sarah Michelle Johnson' and 'Earl Warren'")
