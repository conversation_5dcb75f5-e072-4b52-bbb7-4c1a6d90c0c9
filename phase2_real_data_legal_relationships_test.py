#!/usr/bin/env python3
"""
Phase 2: Real Data Legal Relationships Test
Tests optimized legal relationship processing with REAL CourtListener data
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from typing import List
from dotenv import load_dotenv
from supabase import create_client

# Import components
from optimized_legal_relationship_processor import OptimizedLegalRelationshipProcessor
from proper_cross_system_verifier import ProperCrossSystemVerifier
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase2RealDataLegalRelationshipsTest:
    """Phase 2: Test optimized legal relationship processing with REAL CourtListener data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Initialize optimized processor
        self.optimized_processor = OptimizedLegalRelationshipProcessor(
            neo4j_client=self.neo4j_client,
            batch_size=25  # Small batches for testing
        )
        
        # Initialize verifier
        self.verifier = ProperCrossSystemVerifier()
        
        # Test batch ID
        self.test_batch_id = f"phase2_real_legal_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
        if hasattr(self, 'optimized_processor'):
            self.optimized_processor.close()
        if hasattr(self, 'verifier'):
            self.verifier.close()
    
    def fetch_real_cases_with_citations(self, max_cases: int = 5) -> list:
        """Fetch real CourtListener cases that are likely to have citations"""
        
        print(f"\n🌐 FETCHING REAL COURTLISTENER CASES WITH CITATIONS")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        all_cases = []
        
        try:
            # Fetch from multiple circuits to get diverse citation patterns
            courts_to_try = ['ca5', 'ca9', 'ca2']  # Different circuits
            
            for court in courts_to_try:
                if len(all_cases) >= max_cases:
                    break
                    
                print(f"   📡 Fetching from {court.upper()}")
                
                response = requests.get(
                    f"{self.cl_base_url}/opinions/",
                    headers=headers,
                    params={
                        'court': court,
                        'filed_after': '2020-01-01',
                        'ordering': '-date_filed',
                        'page_size': max_cases * 2  # Get extra to filter for quality
                    },
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"   ❌ API request failed for {court}: {response.status_code}")
                    continue
                
                results = response.json().get('results', [])
                
                for case in results:
                    if len(all_cases) >= max_cases:
                        break
                    
                    text = case.get('plain_text', '') or case.get('html', '')
                    
                    # Filter for cases with substantial content and likely citations
                    citation_indicators = [
                        'v.', 'F.2d', 'F.3d', 'U.S.', 'S.Ct.', 
                        'see', 'citing', 'accord', 'compare', 'distinguish'
                    ]
                    
                    if (text and 
                        len(text) > 3000 and  # Substantial content
                        any(indicator in text for indicator in citation_indicators) and
                        ('judge' in text.lower() or 'justice' in text.lower())):
                        
                        # Create processing format
                        case_data = {
                            'id': f"real_cl_{court}_{case.get('id', 'unknown')}",
                            'source': 'courtlistener',
                            'case_name': case.get('case_name', 'Unknown'),
                            'court': case.get('court', ''),
                            'court_name': f"U.S. Court of Appeals, {court.upper()}",
                            'date_filed': case.get('date_filed', ''),
                            'jurisdiction': 'US',
                            'text': text,
                            'precedential_status': case.get('precedential_status', 'Unknown')
                        }
                        
                        all_cases.append(case_data)
                        print(f"   ✅ Added case: {case_data['case_name'][:60]}...")
                        print(f"      Text length: {len(text):,} characters")
                        print(f"      Citation indicators: {sum(1 for ind in citation_indicators if ind in text)}")
                
                time.sleep(1)  # Rate limiting
        
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
        
        print(f"✅ FETCHED {len(all_cases)} REAL COURTLISTENER CASES WITH LIKELY CITATIONS")
        return all_cases
    
    async def test_real_data_legal_relationships(self) -> bool:
        """Test optimized legal relationship processing with real CourtListener data"""
        
        print(f"\n🚀 PHASE 2: REAL DATA LEGAL RELATIONSHIP TEST")
        print("=" * 80)
        print(f"🎯 Testing optimized legal relationships with REAL CourtListener data")
        
        try:
            # Step 1: Fetch real CourtListener cases with citations
            real_cases = self.fetch_real_cases_with_citations(max_cases=4)
            
            if not real_cases:
                print(f"❌ No real cases fetched for testing")
                return False
            
            case_ids = [case['id'] for case in real_cases]
            
            # Step 2: Process real cases through complete pipeline
            print(f"\n🔄 Processing {len(real_cases)} real cases through optimized pipeline...")
            
            processor = SourceAgnosticProcessor(
                self.supabase, 
                self.gcs_client, 
                self.pinecone_client, 
                self.neo4j_client,
                enable_legal_relationships=True,  # Enable optimized legal relationships
                legal_relationship_timeout=180    # 3 minute timeout for real data
            )
            
            start_time = time.time()
            result = await processor.process_coherent_batch(
                raw_cases=real_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            processing_time = time.time() - start_time
            
            print(f"\n📊 REAL DATA PIPELINE PROCESSING RESULT:")
            print(f"   Success: {'✅' if result['success'] else '❌'}")
            print(f"   Cases processed: {result['processed']}")
            print(f"   Total processing time: {processing_time:.2f}s")
            
            if not result['success']:
                print(f"   ❌ Pipeline processing failed: {result}")
                return False
            
            # Step 3: Verify legal relationships were created with real data
            return await self.verify_real_legal_relationships(case_ids, real_cases)
            
        except Exception as e:
            print(f"❌ Real data legal relationship test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_real_legal_relationships(self, case_ids: List[str], real_cases: list) -> bool:
        """Verify that legal relationships were created from real case data"""
        
        print(f"\n🔍 VERIFYING REAL LEGAL RELATIONSHIPS")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Check for citation-based relationships from real data
                result = session.run("""
                    MATCH (c:Case)-[r]->(cited:CitedCase)
                    WHERE c.id IN $case_ids
                    RETURN c.id as case_id, 
                           c.case_name as case_name,
                           type(r) as relationship_type,
                           cited.id as cited_case_id,
                           cited.citation_reference as citation_reference
                    ORDER BY c.id, relationship_type
                """, case_ids=case_ids)
                
                relationships = list(result)
                
                print(f"📊 Found {len(relationships)} citation-based relationships from REAL data:")
                
                relationship_counts = {}
                case_relationship_counts = {}
                
                for record in relationships:
                    case_id = record['case_id']
                    case_name = record['case_name']
                    rel_type = record['relationship_type']
                    cited_case = record['cited_case_id']
                    citation_ref = record['citation_reference']
                    
                    relationship_counts[rel_type] = relationship_counts.get(rel_type, 0) + 1
                    case_relationship_counts[case_id] = case_relationship_counts.get(case_id, 0) + 1
                    
                    print(f"   📄 {case_name[:40]}...")
                    print(f"      -{rel_type}-> {citation_ref}")
                
                print(f"\n📊 Real data relationship type summary:")
                for rel_type, count in relationship_counts.items():
                    print(f"   {rel_type}: {count}")
                
                print(f"\n📊 Relationships per real case:")
                for case_id, count in case_relationship_counts.items():
                    case_name = next((c['case_name'] for c in real_cases if c['id'] == case_id), 'Unknown')
                    print(f"   {case_name[:50]}...: {count} relationships")
                
                # Check for case-to-case relationships
                case_to_case_result = session.run("""
                    MATCH (c1:Case)-[:CITES]->(c2:Case)
                    WHERE c1.id IN $case_ids OR c2.id IN $case_ids
                    RETURN c1.id as citing_case, c1.case_name as citing_name,
                           c2.id as cited_case, c2.case_name as cited_name
                """, case_ids=case_ids)
                
                case_to_case_rels = list(case_to_case_result)
                
                print(f"\n📊 Found {len(case_to_case_rels)} case-to-case relationships:")
                for record in case_to_case_rels:
                    print(f"   {record['citing_name'][:40]}...")
                    print(f"      -CITES-> {record['cited_name'][:40]}...")
                
                # Analyze relationship quality with real data
                total_relationships = len(relationships) + len(case_to_case_rels)
                avg_relationships_per_case = total_relationships / len(case_ids) if case_ids else 0
                
                print(f"\n🎯 REAL DATA LEGAL RELATIONSHIP ANALYSIS:")
                print(f"   Total real cases processed: {len(case_ids)}")
                print(f"   Citation-based relationships: {len(relationships)}")
                print(f"   Case-to-case relationships: {len(case_to_case_rels)}")
                print(f"   Total relationships: {total_relationships}")
                print(f"   Average relationships per case: {avg_relationships_per_case:.1f}")
                
                # Success criteria for real data (more lenient than mock data)
                success = (
                    total_relationships > 0 and  # At least some relationships found
                    len(relationship_counts) > 1 and  # Multiple relationship types
                    avg_relationships_per_case >= 1.0  # At least 1 relationship per case on average
                )
                
                print(f"   Real data verification: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 REAL DATA LEGAL RELATIONSHIPS: SUCCESS!")
                    print(f"✅ Optimized processor working with real CourtListener data")
                    print(f"✅ Multiple relationship types identified from real text")
                    print(f"✅ Reasonable relationship density for real cases")
                    print(f"✅ Performance acceptable for production use")
                else:
                    print(f"\n❌ REAL DATA LEGAL RELATIONSHIPS: INSUFFICIENT!")
                    print(f"❌ Not enough relationships found in real data")
                    print(f"❌ May need citation extraction improvements")
                
                return success
                
        except Exception as e:
            print(f"❌ Error verifying real legal relationships: {e}")
            return False
    
    async def run_complete_phase2_real_test(self) -> bool:
        """Run complete Phase 2 test with real CourtListener data"""
        
        print("🚀 PHASE 2: COMPLETE REAL DATA LEGAL RELATIONSHIP TEST")
        print("=" * 80)
        print("🎯 OBJECTIVES:")
        print("   - Test optimized processing with REAL CourtListener data")
        print("   - Verify legal relationships from real case citations")
        print("   - Confirm performance with complex real legal text")
        print("   - Validate production readiness with real data")
        
        try:
            # Test with real data
            real_data_success = await self.test_real_data_legal_relationships()
            
            print(f"\n🎯 PHASE 2 REAL DATA FINAL ASSESSMENT:")
            print(f"   Real Data Processing: {'✅' if real_data_success else '❌'}")
            print(f"   Overall Success: {'✅' if real_data_success else '❌'}")
            
            if real_data_success:
                print(f"\n🎉 PHASE 2: COMPLETE SUCCESS WITH REAL DATA!")
                print(f"✅ Optimized legal relationship processing working on real cases")
                print(f"✅ Real CourtListener citations processed correctly")
                print(f"✅ Performance improvements confirmed with real data")
                print(f"✅ Ready for production use with real legal documents")
            else:
                print(f"\n❌ PHASE 2: FAILED WITH REAL DATA!")
                print(f"❌ Issues found in real data processing")
                print(f"❌ Further optimization needed for real cases")
            
            return real_data_success
            
        except Exception as e:
            print(f"❌ Complete Phase 2 real test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up Phase 2 real test data"""
        
        print(f"\n🧹 CLEANING UP PHASE 2 REAL DATA TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase real test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                # Clean test cases
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                
                # Clean orphaned cited cases
                session.run('''
                    MATCH (cited:CitedCase)
                    WHERE NOT EXISTS { MATCH (cited)<-[]-() }
                    DELETE cited
                ''')
                
                # Clean orphaned judges
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j real test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run Phase 2 complete real data legal relationship test"""
    
    test = Phase2RealDataLegalRelationshipsTest()
    
    try:
        success = await test.run_complete_phase2_real_test()
        return success
    finally:
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
