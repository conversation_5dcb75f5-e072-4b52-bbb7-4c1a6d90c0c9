#!/usr/bin/env python3
"""
Detailed verification that all chunks/vectors of individual cases can be properly linked
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from pinecone import Pinecone
from supabase import create_client

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DetailedCaseLinkingVerifier:
    """Detailed verification of case-level chunk linking"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.pc = Pinecone(api_key=pinecone_api_key)
        index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
        self.index = self.pc.Index(index_name)
        
        logger.info("✅ Detailed case linking verifier initialized")
    
    async def verify_detailed_case_linking(self):
        """Verify detailed case-level chunk linking"""
        
        print("🔗 DETAILED CASE LINKING VERIFICATION")
        print("=" * 80)
        
        # Get recent test cases from the 50-case test
        cases = self.supabase.table('cases').select('id, case_name').like('batch_id', 'comprehensive_50_%').limit(10).execute()
        
        if not cases.data:
            print("❌ No test cases found")
            return False
        
        print(f"📊 Verifying detailed linking for {len(cases.data)} cases")
        
        all_passed = True
        total_vectors_found = 0
        
        for i, case in enumerate(cases.data):
            case_id = case['id']
            case_name = case.get('case_name', 'Unknown')
            
            print(f"\n{'='*70}")
            print(f"📄 CASE {i+1}: {case_id}")
            print(f"📋 Name: {case_name}")
            print(f"{'='*70}")
            
            case_passed, vector_count = await self._verify_single_case_detailed(case_id)
            total_vectors_found += vector_count
            
            if not case_passed:
                all_passed = False
                print(f"❌ Case {case_id} FAILED detailed verification")
            else:
                print(f"✅ Case {case_id} PASSED detailed verification ({vector_count} vectors)")
        
        # Overall summary
        print(f"\n{'='*80}")
        print("📊 DETAILED LINKING VERIFICATION SUMMARY")
        print("=" * 80)
        print(f"Cases verified: {len(cases.data)}")
        print(f"Total vectors found: {total_vectors_found}")
        print(f"Average vectors per case: {total_vectors_found/len(cases.data):.1f}")
        
        if all_passed:
            print("🎉 ALL CASES PASSED DETAILED LINKING VERIFICATION!")
            print("✅ Every chunk can be linked back to its case")
            print("✅ Case reconstruction is 100% reliable")
        else:
            print("❌ SOME CASES FAILED DETAILED VERIFICATION")
            print("❌ Chunk linking has issues")
        
        return all_passed
    
    async def _verify_single_case_detailed(self, case_id: str) -> tuple[bool, int]:
        """Detailed verification for a single case"""
        
        try:
            # 1. Get all vectors for this case from Pinecone
            stats = self.index.describe_index_stats()
            query_response = self.index.query(
                vector=[0.0] * stats.dimension,
                filter={"case_id": case_id},
                top_k=100,
                include_metadata=True,
                namespace="tx"
            )
            
            vectors = query_response.matches
            print(f"🔍 Found {len(vectors)} vectors in Pinecone")
            
            if len(vectors) == 0:
                print("❌ No vectors found in Pinecone")
                return False, 0
            
            # 2. Verify each vector has proper case linking metadata
            print(f"\n📋 VERIFYING VECTOR METADATA:")
            
            required_fields = ['case_id', 'chunk_index']
            optional_fields = ['case_name', 'batch_id', 'text_length']
            
            for i, vector in enumerate(vectors):
                metadata = vector.metadata
                vector_case_id = metadata.get('case_id')
                chunk_index = metadata.get('chunk_index')
                
                print(f"   Vector {i}: ID={vector.id}")
                print(f"      case_id: {vector_case_id}")
                print(f"      chunk_index: {chunk_index}")
                print(f"      text_length: {metadata.get('text_length', 'N/A')}")
                
                # Verify case_id matches
                if vector_case_id != case_id:
                    print(f"❌ Vector {i}: Wrong case_id {vector_case_id} (expected {case_id})")
                    return False, len(vectors)
                
                # Verify chunk_index is valid
                if chunk_index is None:
                    print(f"❌ Vector {i}: Missing chunk_index")
                    return False, len(vectors)
                
                try:
                    chunk_idx = int(float(chunk_index))
                    if chunk_idx < 0 or chunk_idx >= len(vectors):
                        print(f"❌ Vector {i}: Invalid chunk_index {chunk_idx} (range: 0-{len(vectors)-1})")
                        return False, len(vectors)
                except (ValueError, TypeError):
                    print(f"❌ Vector {i}: Invalid chunk_index format {chunk_index}")
                    return False, len(vectors)
            
            print(f"✅ All {len(vectors)} vectors have valid metadata")
            
            # 3. Verify chunk ordering is complete and sequential
            print(f"\n🔢 VERIFYING CHUNK ORDERING:")
            
            chunk_indices = []
            for vector in vectors:
                chunk_idx = int(float(vector.metadata.get('chunk_index', 0)))
                chunk_indices.append(chunk_idx)
            
            chunk_indices.sort()
            expected_indices = list(range(len(vectors)))
            
            print(f"   Expected indices: {expected_indices}")
            print(f"   Actual indices: {chunk_indices}")
            
            if chunk_indices != expected_indices:
                print(f"❌ Chunk ordering is broken")
                missing = set(expected_indices) - set(chunk_indices)
                extra = set(chunk_indices) - set(expected_indices)
                if missing:
                    print(f"   Missing indices: {missing}")
                if extra:
                    print(f"   Extra indices: {extra}")
                return False, len(vectors)
            
            print(f"✅ Chunk ordering is complete and sequential")
            
            # 4. Test case reconstruction by retrieving chunks in order
            print(f"\n🔄 TESTING CASE RECONSTRUCTION:")
            
            # Sort vectors by chunk_index
            sorted_vectors = sorted(vectors, key=lambda v: int(float(v.metadata.get('chunk_index', 0))))
            
            reconstructed_case = {
                'case_id': case_id,
                'total_chunks': len(sorted_vectors),
                'chunks': []
            }
            
            for vector in sorted_vectors:
                chunk_data = {
                    'vector_id': vector.id,
                    'chunk_index': int(float(vector.metadata.get('chunk_index', 0))),
                    'text_length': vector.metadata.get('text_length', 0),
                    'embedding_dimensions': len(vector.values) if hasattr(vector, 'values') else 'N/A'
                }
                reconstructed_case['chunks'].append(chunk_data)
            
            print(f"   Reconstructed case with {len(reconstructed_case['chunks'])} chunks")
            print(f"   Total text length: {sum(c['text_length'] for c in reconstructed_case['chunks'] if isinstance(c['text_length'], (int, float)))}")
            print(f"   Chunk size range: {min(c['text_length'] for c in reconstructed_case['chunks'] if isinstance(c['text_length'], (int, float)))}-{max(c['text_length'] for c in reconstructed_case['chunks'] if isinstance(c['text_length'], (int, float)))} chars")
            
            # 5. Test retrieval by case_id filter
            print(f"\n🔎 TESTING CASE_ID RETRIEVAL:")
            
            retrieval_response = self.index.query(
                vector=[0.0] * stats.dimension,
                filter={"case_id": case_id},
                top_k=100,
                include_metadata=True,
                namespace="tx"
            )
            
            retrieved_count = len(retrieval_response.matches)
            print(f"   Retrieved {retrieved_count} vectors by case_id filter")
            
            if retrieved_count != len(vectors):
                print(f"❌ Retrieval count mismatch: {retrieved_count} vs {len(vectors)}")
                return False, len(vectors)
            
            print(f"✅ All vectors retrievable by case_id")
            
            # 6. Test partial retrieval (simulate search scenarios)
            print(f"\n🎯 TESTING PARTIAL RETRIEVAL:")
            
            # Get first 3 chunks
            if len(vectors) >= 3:
                partial_response = self.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id},
                    top_k=3,
                    include_metadata=True,
                    namespace="tx"
                )
                
                partial_chunks = sorted(partial_response.matches, 
                                      key=lambda v: int(float(v.metadata.get('chunk_index', 0))))
                
                print(f"   Retrieved first 3 chunks:")
                for chunk in partial_chunks:
                    chunk_idx = int(float(chunk.metadata.get('chunk_index', 0)))
                    print(f"      Chunk {chunk_idx}: {chunk.id}")
                
                # Verify we can get specific chunks by index
                if len(vectors) > 3:
                    specific_response = self.index.query(
                        vector=[0.0] * stats.dimension,
                        filter={"case_id": case_id, "chunk_index": 3},
                        top_k=1,
                        include_metadata=True,
                        namespace="tx"
                    )
                    
                    if len(specific_response.matches) > 0:
                        print(f"   ✅ Successfully retrieved specific chunk (index 3)")
                    else:
                        print(f"   ❌ Could not retrieve specific chunk (index 3)")
                        return False, len(vectors)
            
            # 7. Final case summary
            print(f"\n📊 CASE DETAILED SUMMARY:")
            print(f"   ✅ {len(vectors)} vectors with complete metadata")
            print(f"   ✅ Sequential chunk ordering (0-{len(vectors)-1})")
            print(f"   ✅ Case reconstruction successful")
            print(f"   ✅ Full case retrievable by case_id")
            print(f"   ✅ Partial retrieval working")
            print(f"   ✅ Specific chunk access working")
            
            return True, len(vectors)
            
        except Exception as e:
            print(f"❌ Error in detailed verification: {e}")
            import traceback
            traceback.print_exc()
            return False, 0


async def main():
    """Run detailed case linking verification"""
    
    verifier = DetailedCaseLinkingVerifier()
    
    print("🎯 DETAILED CASE LINKING VERIFICATION")
    print("This verifies every aspect of chunk-to-case linking")
    print()
    
    success = await verifier.verify_detailed_case_linking()
    
    if success:
        print("\n🎉 DETAILED CASE LINKING VERIFICATION PASSED!")
        print("✅ Every chunk can be perfectly linked to its case")
        print("✅ Case reconstruction is 100% reliable")
        return 0
    else:
        print("\n❌ DETAILED CASE LINKING VERIFICATION FAILED!")
        print("❌ Some chunks cannot be properly linked")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
