#!/usr/bin/env python3
"""
Time Window Pipeline Runner
Runs the complete processing pipeline with time window filters and generates delta report.

Time Windows:
- CAP: 1950-12-31 ≤ date_filed ≤ 1993-12-31 (historical)
- CourtListener: 1994-01-01 ≤ date_filed ≤ 2025-12-31 (modern)
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from collections import defaultdict
from dotenv import load_dotenv

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from processing.caselaw_access_processor import CaselawAccessProcessor
from processing.courtlistener_bulk_client import CourtListenerBulkClient
from processing.storage.supabase_connector import SupabaseConnector

load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('time_window_pipeline.log')
    ]
)
logger = logging.getLogger(__name__)

class TimeWindowPipelineRunner:
    """Runs the complete pipeline with time window filters."""
    
    def __init__(self):
        """Initialize pipeline runner."""
        self.supabase = SupabaseConnector()
        self.stats = {
            'cap': {'total_processed': 0, 'ingested': 0, 'skipped': 0},
            'courtlistener': {'total_processed': 0, 'ingested': 0, 'skipped': 0}
        }
        
    async def get_baseline_counts(self):
        """Get baseline counts before processing."""
        logger.info("Getting baseline counts...")
        
        try:
            result = self.supabase.client.table('cases').select('court_id, year_filed, source_window', count='exact').execute()
            
            baseline = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
            
            for case in result.data:
                court_id = case.get('court_id', 'unknown')
                year_filed = case.get('year_filed', 'unknown')
                source_window = case.get('source_window', 'unknown')
                baseline[court_id][year_filed][source_window] += 1
            
            return dict(baseline)
            
        except Exception as e:
            logger.error(f"Error getting baseline counts: {e}")
            return {}
    
    async def run_cap_processing(self):
        """Run CAP processing with historical time window."""
        logger.info("Starting CAP processing (historical: 1950-1993)...")
        
        try:
            processor = CaselawAccessProcessor()
            
            # Find CAP files
            cap_dir = Path("data/caselaw_access_project")
            if not cap_dir.exists():
                logger.warning(f"CAP directory not found: {cap_dir}")
                return
            
            cap_files = list(cap_dir.glob("*.jsonl.gz"))
            logger.info(f"Found {len(cap_files)} CAP files")
            
            for cap_file in cap_files:
                logger.info(f"Processing {cap_file.name}...")
                
                try:
                    # Process file with time window filtering
                    result = await processor.process_file(cap_file)
                    
                    if result:
                        self.stats['cap']['total_processed'] += result.get('total_processed', 0)
                        self.stats['cap']['ingested'] += result.get('stored', 0)
                        self.stats['cap']['skipped'] += result.get('skipped', 0)
                        
                        logger.info(f"  Processed: {result.get('total_processed', 0)}")
                        logger.info(f"  Ingested: {result.get('stored', 0)}")
                        logger.info(f"  Skipped: {result.get('skipped', 0)}")
                
                except Exception as e:
                    logger.error(f"Error processing {cap_file}: {e}")
                    continue
            
            logger.info(f"CAP processing complete: {self.stats['cap']}")
            
        except Exception as e:
            logger.error(f"Error in CAP processing: {e}")
    
    async def run_courtlistener_processing(self):
        """Run CourtListener processing with modern time window."""
        logger.info("Starting CourtListener processing (modern: 1994-2025)...")
        
        try:
            client = CourtListenerBulkClient()
            
            # Process Texas jurisdictions with time window filtering
            jurisdictions = ['tx']  # Start with Texas
            
            for jurisdiction in jurisdictions:
                logger.info(f"Processing jurisdiction: {jurisdiction}")
                
                try:
                    # Run bulk processing with time window filtering
                    result = await client.process_jurisdiction(jurisdiction)
                    
                    if result:
                        self.stats['courtlistener']['total_processed'] += result.get('total_processed', 0)
                        self.stats['courtlistener']['ingested'] += result.get('stored', 0)
                        self.stats['courtlistener']['skipped'] += result.get('skipped', 0)
                        
                        logger.info(f"  Processed: {result.get('total_processed', 0)}")
                        logger.info(f"  Ingested: {result.get('stored', 0)}")
                        logger.info(f"  Skipped: {result.get('skipped', 0)}")
                
                except Exception as e:
                    logger.error(f"Error processing jurisdiction {jurisdiction}: {e}")
                    continue
            
            logger.info(f"CourtListener processing complete: {self.stats['courtlistener']}")
            
        except Exception as e:
            logger.error(f"Error in CourtListener processing: {e}")
    
    async def get_final_counts(self):
        """Get final counts after processing."""
        logger.info("Getting final counts...")
        
        try:
            result = self.supabase.client.table('cases').select('court_id, year_filed, source_window', count='exact').execute()
            
            final = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
            
            for case in result.data:
                court_id = case.get('court_id', 'unknown')
                year_filed = case.get('year_filed', 'unknown')
                source_window = case.get('source_window', 'unknown')
                final[court_id][year_filed][source_window] += 1
            
            return dict(final)
            
        except Exception as e:
            logger.error(f"Error getting final counts: {e}")
            return {}
    
    def generate_delta_report(self, baseline, final):
        """Generate comprehensive delta report."""
        logger.info("Generating delta report...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Calculate deltas
        delta_data = []
        
        # Get all court/year combinations
        all_courts = set()
        all_years = set()
        
        for counts in [baseline, final]:
            for court_id in counts:
                all_courts.add(court_id)
                for year in counts[court_id]:
                    all_years.add(year)
        
        for court_id in sorted(all_courts):
            for year in sorted(all_years):
                baseline_historical = baseline.get(court_id, {}).get(year, {}).get('historical', 0)
                baseline_modern = baseline.get(court_id, {}).get(year, {}).get('modern', 0)
                baseline_total = baseline_historical + baseline_modern
                
                final_historical = final.get(court_id, {}).get(year, {}).get('historical', 0)
                final_modern = final.get(court_id, {}).get(year, {}).get('modern', 0)
                final_total = final_historical + final_modern
                
                delta_historical = final_historical - baseline_historical
                delta_modern = final_modern - baseline_modern
                delta_total = final_total - baseline_total
                
                if delta_total != 0 or baseline_total > 0 or final_total > 0:
                    delta_data.append({
                        'court_id': court_id,
                        'year_filed': year,
                        'baseline_historical': baseline_historical,
                        'baseline_modern': baseline_modern,
                        'baseline_total': baseline_total,
                        'final_historical': final_historical,
                        'final_modern': final_modern,
                        'final_total': final_total,
                        'delta_historical': delta_historical,
                        'delta_modern': delta_modern,
                        'delta_total': delta_total
                    })
        
        # Generate summary
        summary = {
            'timestamp': timestamp,
            'processing_stats': self.stats,
            'time_windows': {
                'historical': '1950-12-31 ≤ date_filed ≤ 1993-12-31 (CAP)',
                'modern': '1994-01-01 ≤ date_filed ≤ 2025-12-31 (CourtListener)'
            },
            'totals': {
                'baseline_total': sum(row['baseline_total'] for row in delta_data),
                'final_total': sum(row['final_total'] for row in delta_data),
                'delta_total': sum(row['delta_total'] for row in delta_data),
                'historical_cases': sum(row['final_historical'] for row in delta_data),
                'modern_cases': sum(row['final_modern'] for row in delta_data)
            }
        }
        
        # Save detailed report
        report = {
            'summary': summary,
            'court_year_deltas': delta_data
        }
        
        report_file = f"time_window_delta_report_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Save CSV
        csv_file = f"time_window_delta_report_{timestamp}.csv"
        with open(csv_file, 'w') as f:
            f.write('court_id,year_filed,baseline_historical,baseline_modern,baseline_total,final_historical,final_modern,final_total,delta_historical,delta_modern,delta_total\n')
            for row in delta_data:
                f.write(f"{row['court_id']},{row['year_filed']},{row['baseline_historical']},{row['baseline_modern']},{row['baseline_total']},{row['final_historical']},{row['final_modern']},{row['final_total']},{row['delta_historical']},{row['delta_modern']},{row['delta_total']}\n")
        
        logger.info(f"Delta report saved: {report_file}, {csv_file}")
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"TIME WINDOW PIPELINE DELTA REPORT")
        print(f"{'='*60}")
        print(f"Timestamp: {timestamp}")
        print(f"\nProcessing Stats:")
        print(f"  CAP (Historical): {self.stats['cap']['ingested']:,} ingested, {self.stats['cap']['skipped']:,} skipped")
        print(f"  CourtListener (Modern): {self.stats['courtlistener']['ingested']:,} ingested, {self.stats['courtlistener']['skipped']:,} skipped")
        print(f"\nFinal Totals:")
        print(f"  Historical cases (1950-1993): {summary['totals']['historical_cases']:,}")
        print(f"  Modern cases (1994-2025): {summary['totals']['modern_cases']:,}")
        print(f"  Total cases: {summary['totals']['final_total']:,}")
        print(f"  Net change: {summary['totals']['delta_total']:,}")
        
        return report

async def main():
    """Main execution function."""
    logger.info("Starting Time Window Pipeline")
    
    runner = TimeWindowPipelineRunner()
    
    try:
        # Get baseline counts
        baseline = await runner.get_baseline_counts()
        
        # Run CAP processing (historical window)
        await runner.run_cap_processing()
        
        # Run CourtListener processing (modern window)
        await runner.run_courtlistener_processing()
        
        # Get final counts
        final = await runner.get_final_counts()
        
        # Generate delta report
        report = runner.generate_delta_report(baseline, final)
        
        logger.info("Time Window Pipeline completed successfully")
        return report
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
