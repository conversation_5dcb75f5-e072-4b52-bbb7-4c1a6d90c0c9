#!/usr/bin/env python3
"""
Comprehensive Texas legal case processor combining Court Listener API and CAP data.
This will process ALL available Texas cases from both sources.
"""

import os
import sys
import logging
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from processing.courtlistener_bulk_client import CourtListenerBulkClient
from processing.caselaw_access_processor import CaselawAccessProcessor
from processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

load_dotenv()

class ComprehensiveTexasProcessor:
    """Comprehensive processor for all Texas legal cases."""
    
    def __init__(self):
        """Initialize the comprehensive processor."""
        self.court_listener_client = CourtListenerBulkClient()
        self.cap_processor = CaselawAccessProcessor()
        self.checkpoint_manager = CheckpointManager()
        
        # Processing configuration
        self.practice_areas = [
            'personal injury', 'criminal defense', 'family law', 
            'estate planning', 'immigration law', 'real estate', 'bankruptcy'
        ]
        
        # Comprehensive targets
        self.court_listener_target = 5000  # 5K cases from Court Listener
        self.cap_target = 10000  # 10K cases from CAP data
        
        logger.info("✅ Comprehensive Texas Processor initialized")
        logger.info(f"   Court Listener target: {self.court_listener_target:,} cases")
        logger.info(f"   CAP target: {self.cap_target:,} cases")
        logger.info(f"   Practice areas: {len(self.practice_areas)}")
    
    async def process_court_listener_cases(self):
        """Process comprehensive Court Listener cases for Texas."""
        logger.info("🚀 PROCESSING COURT LISTENER CASES")
        logger.info("=" * 60)
        
        total_processed = 0
        
        for practice_area in self.practice_areas:
            logger.info(f"📋 Processing {practice_area} cases...")
            
            try:
                # Fetch cases for this practice area
                cases_per_area = self.court_listener_target // len(self.practice_areas)
                cases = self.court_listener_client.fetch_jurisdiction_cases(
                    'tx', 
                    limit=cases_per_area
                )
                
                logger.info(f"   📥 Fetched {len(cases)} {practice_area} cases")
                
                if cases:
                    # Process in batches
                    batch_size = 100
                    for i in range(0, len(cases), batch_size):
                        batch = cases[i:i + batch_size]
                        batch_num = i // batch_size + 1
                        total_batches = (len(cases) + batch_size - 1) // batch_size
                        
                        logger.info(f"   📦 Processing batch {batch_num}/{total_batches} ({len(batch)} cases)")
                        
                        # Process batch
                        processed_count = await self.cap_processor.process_cases_batch(
                            batch, 
                            source='court_listener',
                            jurisdiction='tx',
                            practice_area=practice_area
                        )
                        
                        total_processed += processed_count
                        
                        # Save checkpoint
                        checkpoint_id = f"comprehensive_tx_cl_{practice_area}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        self.checkpoint_manager.save_checkpoint(checkpoint_id, {
                            'source': 'court_listener',
                            'practice_area': practice_area,
                            'batch': batch_num,
                            'processed': processed_count,
                            'total_processed': total_processed
                        })
                
                logger.info(f"✅ Completed {practice_area}: {len(cases)} cases processed")
            
            except Exception as e:
                logger.error(f"❌ Error processing {practice_area}: {e}")
                continue
        
        logger.info(f"🎉 Court Listener processing complete: {total_processed} cases processed")
        return total_processed
    
    async def process_cap_cases(self):
        """Process CAP cases for Texas."""
        logger.info("🚀 PROCESSING CAP CASES")
        logger.info("=" * 60)
        
        try:
            # Process CAP data with Texas filtering
            processed_count = await self.cap_processor.process_cap_data(
                jurisdiction_filter='texas',
                limit=self.cap_target,
                batch_size=100
            )
            
            logger.info(f"🎉 CAP processing complete: {processed_count} cases processed")
            return processed_count
        
        except Exception as e:
            logger.error(f"❌ Error processing CAP data: {e}")
            return 0
    
    async def run_comprehensive_processing(self):
        """Run comprehensive Texas processing."""
        start_time = datetime.now()
        
        logger.info("🌟 STARTING COMPREHENSIVE TEXAS PROCESSING")
        logger.info("=" * 80)
        logger.info(f"Start time: {start_time}")
        logger.info(f"Target: {self.court_listener_target + self.cap_target:,} total cases")
        
        # Phase 1: Court Listener API (recent cases)
        logger.info("\n📡 PHASE 1: COURT LISTENER API PROCESSING")
        cl_processed = await self.process_court_listener_cases()
        
        # Phase 2: CAP Data (historical cases)
        logger.info("\n📚 PHASE 2: CAP DATA PROCESSING")
        cap_processed = await self.process_cap_cases()
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        total_processed = cl_processed + cap_processed
        
        logger.info("\n🎉 COMPREHENSIVE TEXAS PROCESSING COMPLETE!")
        logger.info("=" * 80)
        logger.info(f"Duration: {duration}")
        logger.info(f"Court Listener cases: {cl_processed:,}")
        logger.info(f"CAP cases: {cap_processed:,}")
        logger.info(f"Total processed: {total_processed:,}")
        logger.info(f"Processing rate: {total_processed / duration.total_seconds():.2f} cases/second")
        
        # Database summary
        logger.info("\n📊 DATABASE SUMMARY:")
        try:
            # Get final counts from database
            supabase_count = await self.cap_processor.get_case_count()
            logger.info(f"   Supabase cases: {supabase_count:,}")
            logger.info(f"   Neo4j nodes: Available via graph queries")
            logger.info(f"   Pinecone vectors: Available via vector search")
            logger.info(f"   GCS documents: Available via cloud storage")
        except Exception as e:
            logger.error(f"   Error getting database counts: {e}")
        
        return total_processed

async def main():
    """Main function to run comprehensive Texas processing."""
    processor = ComprehensiveTexasProcessor()
    
    try:
        total_processed = await processor.run_comprehensive_processing()
        
        if total_processed > 0:
            print(f"\n🎯 SUCCESS: Processed {total_processed:,} Texas cases!")
            print("   ✅ Court Listener API: Recent cases from 12 courts")
            print("   ✅ CAP Data: Historical Texas cases")
            print("   ✅ All cases stored in 4 databases with perfect consistency")
        else:
            print("\n⚠️  No cases processed - check logs for errors")
    
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
