#!/usr/bin/env python3
import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

def test_cl_endpoints():
    api_key = os.getenv('COURTLISTENER_API_KEY')
    headers = {
        'Authorization': f'Token {api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    # Test different endpoints and court IDs
    test_courts = ['tex', 'texcrimapp', 'txnd', 'ca5']
    
    results = {}
    
    for court_id in test_courts:
        print(f'Testing {court_id}...')
        
        # Try search API
        try:
            url = 'https://www.courtlistener.com/api/rest/v4/search/'
            params = {
                'q': f'court_id:{court_id}',
                'type': 'o',
                'page_size': 5,
                'format': 'json'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                count = data.get('count', 0)
                results[court_id] = {
                    'endpoint': 'search',
                    'total_count': count,
                    'sample_size': len(data.get('results', [])),
                    'status': 'success'
                }
                print(f'  Search API: {count} total opinions')
            else:
                results[court_id] = {
                    'endpoint': 'search',
                    'status': f'error_{response.status_code}',
                    'error': response.text[:100]
                }
                print(f'  Search API error: {response.status_code}')
                
        except Exception as e:
            results[court_id] = {
                'endpoint': 'search',
                'status': 'exception',
                'error': str(e)
            }
            print(f'  Search API exception: {e}')
    
    return results

if __name__ == '__main__':
    results = test_cl_endpoints()
    
    with open('cl_endpoint_test.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f'\\nTest complete. Results saved to cl_endpoint_test.json')
    
    # Print summary
    for court, result in results.items():
        if result.get('status') == 'success':
            print(f'{court}: {result.get("total_count", 0)} opinions available')
        else:
            print(f'{court}: {result.get("status", "unknown")} - {result.get("error", "")[:50]}')
