#!/usr/bin/env python3
"""
Debug Atomic Pipeline Rollback Issue
"""

import asyncio
import logging
import os
import json
import gzip
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def debug_atomic_pipeline():
    """Debug the atomic pipeline rollback issue"""
    
    print("🔍 ATOMIC PIPELINE ROLLBACK DEBUG")
    print("=" * 60)
    
    load_dotenv()
    
    # Setup clients
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = GCSConnector()
    pinecone_client = RealPineconeClient()
    neo4j_client = RealNeo4jClient()
    
    processor = SourceAgnosticProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client
    )
    
    try:
        # Get minimal test case
        raw_cases = get_test_cases(1)
        
        if not raw_cases:
            print("❌ No test cases found")
            return False
        
        print(f"📊 Testing with {len(raw_cases)} case")
        
        # Process with detailed logging
        batch_id = f"debug_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"\n🔄 Processing batch: {batch_id}")
        
        result = await processor.process_coherent_batch(
            raw_cases=raw_cases,
            source_type='caselaw_access_project',
            batch_id=batch_id
        )
        
        print(f"\n📊 FINAL RESULT:")
        print(f"   Success: {result['success']}")
        print(f"   Processed: {result['processed']}")
        print(f"   Details: {result}")
        
        if result['success']:
            print(f"✅ Pipeline succeeded - no rollback issue")
            
            # Verify data exists in all systems
            print(f"\n🔍 Verifying data in all systems:")
            
            # Supabase
            cases = supabase.table('cases').select('*').eq('batch_id', batch_id).execute()
            print(f"   Supabase: {len(cases.data)} cases")
            
            # GCS
            gcs_paths = [case.get('gcs_path') for case in cases.data if case.get('gcs_path')]
            gcs_files_exist = 0
            for gcs_path in gcs_paths:
                if gcs_path:
                    try:
                        if gcs_path.startswith('gs://'):
                            bucket_path = gcs_path.split('/', 3)[-1]
                        else:
                            bucket_path = gcs_path
                        
                        blob = gcs_client.bucket.blob(bucket_path)
                        if blob.exists():
                            gcs_files_exist += 1
                    except:
                        pass
            print(f"   GCS: {gcs_files_exist} files")
            
            # Neo4j
            with neo4j_client.driver.session() as session:
                result = session.run('MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as count', batch_id=batch_id)
                neo4j_count = result.single()['count']
                print(f"   Neo4j: {neo4j_count} nodes")
            
            return True
        else:
            print(f"❌ Pipeline failed - rollback occurred")
            return False
            
    except Exception as e:
        print(f"💥 Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            supabase.table('cases').delete().like('batch_id', f'debug_test_%').execute()
            print(f"🧹 Cleaned up test data")
        except:
            pass
        
        neo4j_client.close()


def get_test_cases(num_cases: int) -> list:
    """Get test cases from CAP data"""
    
    cap_files = list(Path("data/caselaw_access_project").glob("*.jsonl.gz"))
    raw_cases = []
    
    for cap_file in cap_files:
        if len(raw_cases) >= num_cases:
            break
            
        try:
            with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                for line in f:
                    if len(raw_cases) >= num_cases:
                        break
                        
                    try:
                        raw_case = json.loads(line.strip())
                        text = raw_case.get('text', '')
                        
                        # Filter for substantial cases
                        if len(text) > 1000 and 'texas' in text.lower():
                            raw_cases.append(raw_case)
                            
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            continue
    
    return raw_cases


if __name__ == "__main__":
    success = asyncio.run(debug_atomic_pipeline())
    exit(0 if success else 1)
