#!/usr/bin/env python3
"""
Verify Gemini Jurisdiction Detection on CAP Files
Test if Gemini achieves 90%+ classification rate with perfect sum matching
"""

import os
import gzip
import json
from pathlib import Path
from collections import defaultdict
import time

# Import the Gemini court jurisdiction detector
from gemini_court_jurisdiction_detector import GeminiCourtJurisdictionDetector

def verify_gemini_jurisdiction_on_cap():
    """Verify Gemini jurisdiction detection on actual CAP files."""
    
    print("🧠 VERIFYING GEMINI JURISDICTION DETECTION ON CAP FILES")
    print("Testing if Gemini achieves 90%+ classification rate")
    print("=" * 70)
    
    try:
        detector = GeminiCourtJurisdictionDetector()
    except Exception as e:
        print(f"❌ Error initializing Gemini detector: {e}")
        print("💡 Make sure GEMINI_API_KEY is set in your .env file")
        return
    
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    print(f"📁 Found {len(cap_files)} CAP files")
    print(f"🎯 Testing on SAMPLE of first file (100 cases) for verification")
    print("⏱️  Note: Full file processing would take hours due to API calls")
    print()
    
    # Test on sample from first file
    file_path = cap_files[0]
    print(f"📁 Sampling from: {file_path.name}")
    
    # Load sample cases
    sample_cases = []
    total_cases_in_file = 0
    
    try:
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        case_data = json.loads(line)
                        total_cases_in_file += 1
                        
                        # Take every 400th case for representative sample
                        if line_num % 400 == 0 and len(sample_cases) < 100:
                            sample_cases.append(case_data)
                        
                        # Stop after counting all cases or getting enough samples
                        if len(sample_cases) >= 100:
                            # Continue counting total cases
                            continue
                            
                    except json.JSONDecodeError:
                        continue
                    except Exception as e:
                        continue
    
    except Exception as e:
        print(f"❌ Error reading {file_path.name}: {e}")
        return
    
    print(f"📊 File contains {total_cases_in_file:,} total cases")
    print(f"🎯 Testing on {len(sample_cases)} representative sample cases")
    print()
    
    # Process sample with Gemini
    print("🧠 Processing sample with Gemini...")
    start_time = time.time()
    
    jurisdiction_counts = defaultdict(int)
    confidence_distribution = defaultdict(int)
    sample_results = []
    
    for i, case_data in enumerate(sample_cases, 1):
        print(f"  Processing case {i}/{len(sample_cases)}...", end=" ")
        
        jurisdiction, confidence, reasoning = detector.detect_jurisdiction_with_gemini(case_data)
        jurisdiction_counts[jurisdiction] += 1
        confidence_distribution[confidence] += 1
        
        # Store sample results
        if len(sample_results) < 10:
            sample_results.append({
                'jurisdiction': jurisdiction,
                'confidence': confidence,
                'reasoning': reasoning,
                'text_preview': case_data.get('text', '')[:150]
            })
        
        print(f"✅ {jurisdiction} ({confidence})")
        
        # Small delay to respect API limits
        time.sleep(0.1)
    
    processing_time = time.time() - start_time
    
    # Calculate metrics
    total_sample = len(sample_cases)
    classified_cases = total_sample - jurisdiction_counts.get('unclassified', 0)
    classification_rate = (classified_cases / total_sample) * 100 if total_sample > 0 else 0
    
    # Verify sum matches
    total_assigned = sum(jurisdiction_counts.values())
    perfect_match = total_assigned == total_sample
    
    print(f"\n📊 GEMINI VERIFICATION RESULTS")
    print("=" * 50)
    
    print(f"Sample size: {total_sample}")
    print(f"Total assigned: {total_assigned}")
    print(f"Perfect sum match: {'✅ YES' if perfect_match else '❌ NO'}")
    print(f"Classification rate: {classification_rate:.1f}%")
    print(f"Processing time: {processing_time:.1f} seconds")
    
    print(f"\n🗺️  JURISDICTION DISTRIBUTION:")
    sorted_jurisdictions = sorted(jurisdiction_counts.items(), key=lambda x: x[1], reverse=True)
    for jurisdiction, count in sorted_jurisdictions:
        percentage = (count / total_sample) * 100 if total_sample > 0 else 0
        print(f"  {jurisdiction}: {count} ({percentage:.1f}%)")
    
    print(f"\n🎯 CONFIDENCE DISTRIBUTION:")
    for confidence, count in sorted(confidence_distribution.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_sample) * 100 if total_sample > 0 else 0
        print(f"  {confidence}: {count} ({percentage:.1f}%)")
    
    print(f"\n📝 SAMPLE RESULTS:")
    for i, result in enumerate(sample_results, 1):
        print(f"  {i}. {result['jurisdiction']} ({result['confidence']})")
        print(f"     Reasoning: {result['reasoning']}")
        print(f"     Text: {result['text_preview']}...")
        print()
    
    # Extrapolate to full file
    if classification_rate > 0:
        estimated_classified = int((classification_rate / 100) * total_cases_in_file)
        estimated_texas = int((jurisdiction_counts.get('texas', 0) / total_sample) * total_cases_in_file)
        
        print(f"📈 EXTRAPOLATION TO FULL FILE:")
        print(f"  Estimated classified cases: {estimated_classified:,} ({classification_rate:.1f}%)")
        print(f"  Estimated Texas cases: {estimated_texas:,}")
        print(f"  Estimated unclassified: {total_cases_in_file - estimated_classified:,}")
    
    print(f"\n🎯 ASSESSMENT:")
    if perfect_match:
        print("✅ PERFECT: Sum of jurisdictions = Total cases")
    else:
        print("❌ ERROR: Sum mismatch - need to investigate")
    
    if classification_rate >= 90:
        print("🎉 EXCELLENT: >90% classification rate achieved!")
        print("🚀 Gemini approach is vastly superior to regex (48.2%)")
    elif classification_rate >= 80:
        print("✅ GOOD: >80% classification rate - significant improvement")
    elif classification_rate >= 60:
        print("⚠️ MODERATE: >60% classification rate - better than regex")
    else:
        print("❌ POOR: <60% classification rate - needs improvement")
    
    print(f"\n💡 CONCLUSION:")
    print(f"Gemini achieves {classification_rate:.1f}% classification rate")
    print(f"This is a {classification_rate - 48.2:.1f} percentage point improvement over regex")
    print(f"Ready to process all {total_cases_in_file:,} cases in this file with high confidence")
    
    return {
        'classification_rate': classification_rate,
        'perfect_match': perfect_match,
        'estimated_texas_cases': estimated_texas if 'estimated_texas' in locals() else 0
    }

if __name__ == "__main__":
    results = verify_gemini_jurisdiction_on_cap()
