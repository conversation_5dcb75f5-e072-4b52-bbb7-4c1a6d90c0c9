#!/usr/bin/env python3
"""
CourtListener API Field Analysis
Analyze what structured judge fields are actually available in real API responses
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def analyze_courtlistener_api_fields():
    """Analyze available fields in CourtListener API responses"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🔍 ANALYZING COURTLISTENER API FIELDS")
    print("=" * 60)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # Fetch a few recent opinions to analyze structure
        url = f"{base_url}/opinions/"
        params = {
            'court': 'txnd,txsd',  # Texas federal districts
            'ordering': '-date_created',
            'page_size': 5,
            'format': 'json'
        }
        
        try:
            print(f"📡 Fetching from: {url}")
            response = await client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            opinions = data.get('results', [])
            
            print(f"📊 Analyzing {len(opinions)} opinion records...")
            
            # Analyze field availability
            field_analysis = {}
            judge_related_fields = [
                'author', 'author_str', 'judges', 'panel', 'per_curiam',
                'author_id', 'joined_by', 'type', 'cluster'
            ]
            
            for i, opinion in enumerate(opinions, 1):
                print(f"\n📄 OPINION {i}: ID {opinion.get('id')}")
                print(f"   Case: {opinion.get('case_name', 'Unknown')[:50]}...")
                
                # Check all available fields
                all_fields = list(opinion.keys())
                print(f"   📋 Total fields available: {len(all_fields)}")
                
                # Focus on judge-related fields
                print(f"   👨‍⚖️ JUDGE-RELATED FIELDS:")
                for field in judge_related_fields:
                    value = opinion.get(field)
                    has_value = value is not None and value != '' and value != []
                    
                    # Track field availability
                    if field not in field_analysis:
                        field_analysis[field] = {'total': 0, 'has_value': 0, 'sample_values': []}
                    
                    field_analysis[field]['total'] += 1
                    if has_value:
                        field_analysis[field]['has_value'] += 1
                        if len(field_analysis[field]['sample_values']) < 3:
                            field_analysis[field]['sample_values'].append(str(value)[:100])
                    
                    status = "✅" if has_value else "❌"
                    print(f"      {field}: {status} {value}")
                
                # Check if this opinion has cluster data (might contain more judge info)
                cluster_id = opinion.get('cluster')
                if cluster_id:
                    print(f"   🔗 Has cluster: {cluster_id} - fetching cluster data...")
                    try:
                        cluster_url = f"{base_url}/clusters/{cluster_id}/"
                        cluster_response = await client.get(cluster_url)
                        if cluster_response.status_code == 200:
                            cluster_data = cluster_response.json()
                            cluster_judges = cluster_data.get('judges', [])
                            cluster_panel = cluster_data.get('panel', [])
                            print(f"      Cluster judges: {cluster_judges}")
                            print(f"      Cluster panel: {cluster_panel}")
                    except Exception as e:
                        print(f"      ❌ Error fetching cluster: {e}")
            
            # Summary analysis
            print(f"\n📊 FIELD AVAILABILITY SUMMARY")
            print("=" * 60)
            for field, stats in field_analysis.items():
                availability = (stats['has_value'] / stats['total']) * 100
                print(f"{field:15} | {availability:5.1f}% | {stats['has_value']}/{stats['total']} cases")
                if stats['sample_values']:
                    print(f"                | Samples: {stats['sample_values']}")
                print()
            
        except Exception as e:
            print(f"❌ Error analyzing API: {e}")

if __name__ == "__main__":
    asyncio.run(analyze_courtlistener_api_fields())
