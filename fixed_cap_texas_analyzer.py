#!/usr/bin/env python3
"""
Fixed CAP Texas Cases Analysis
Corrected script that properly detects Texas cases in CAP files
"""

import os
import gzip
import json
import re
from pathlib import Path
from collections import defaultdict

def is_texas_case(case_data):
    """Properly detect if a case is from Texas using the actual CAP structure."""
    
    # Get the case text where court information is embedded
    text = case_data.get('text', '').lower()
    
    # Texas court patterns
    texas_patterns = [
        r'court.*texas',
        r'texas.*court',
        r'supreme court of texas',
        r'court of appeals.*texas',
        r'court of civil appeals.*texas',
        r'district court.*texas',
        r'texas district court',
        r'eastern district of texas',
        r'western district of texas',
        r'northern district of texas',
        r'southern district of texas',
        r'tex\.',
        r'texas',
        r'galveston',  # Texas city
        r'houston',    # Texas city
        r'dallas',     # Texas city
        r'austin',     # Texas city
        r'san antonio', # Texas city
        r'eastland',   # Texas city (seen in sample)
    ]
    
    # Check if any Texas pattern matches
    for pattern in texas_patterns:
        if re.search(pattern, text):
            return True
    
    return False

def analyze_cap_texas_cases():
    """Analyze CAP files with corrected Texas detection."""
    
    print("🤠 FIXED CAP TEXAS CASES ANALYSIS")
    print("=" * 60)
    
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    print(f"📁 Found {len(cap_files)} CAP files")
    
    total_cases = 0
    texas_cases = 0
    sample_texas_cases = []
    file_breakdown = {}
    
    # Analyze each file
    for file_idx, file_path in enumerate(cap_files, 1):
        print(f"\n📁 [{file_idx}/{len(cap_files)}] Processing: {file_path.name}")
        
        file_total = 0
        file_texas = 0
        
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            case_data = json.loads(line)
                            file_total += 1
                            total_cases += 1
                            
                            # Check if this is a Texas case using corrected logic
                            if is_texas_case(case_data):
                                file_texas += 1
                                texas_cases += 1
                                
                                # Extract case info for samples
                                if len(sample_texas_cases) < 10:
                                    text = case_data.get('text', '')
                                    # Extract case name (usually first line)
                                    lines = text.split('\\n')
                                    case_name = lines[0] if lines else 'Unknown Case'
                                    
                                    # Extract court info
                                    court_match = re.search(r'(court.*texas.*|texas.*court.*)', text.lower())
                                    court_info = court_match.group(1) if court_match else 'Unknown Court'
                                    
                                    sample_texas_cases.append({
                                        'name': case_name.strip(),
                                        'court': court_info,
                                        'file': file_path.name,
                                        'id': case_data.get('id', 'Unknown')
                                    })
                            
                            # Progress indicator
                            if line_num % 10000 == 0:
                                print(f"    Processed {line_num:,} cases, found {file_texas} Texas cases")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            continue
        
        except Exception as e:
            print(f"  ❌ Error processing {file_path.name}: {e}")
            continue
        
        file_breakdown[file_path.name] = {
            'total': file_total,
            'texas': file_texas
        }
        
        print(f"  📊 {file_path.name}: {file_total:,} total, {file_texas:,} Texas cases ({(file_texas/file_total*100):.1f}%)")
    
    # Print results
    print(f"\n" + "="*80)
    print("🎉 CORRECTED TEXAS CASES ANALYSIS RESULTS")
    print("="*80)
    
    print(f"📊 Total cases analyzed: {total_cases:,}")
    print(f"🎯 Texas cases found: {texas_cases:,}")
    
    if total_cases > 0:
        texas_percentage = (texas_cases / total_cases) * 100
        print(f"📈 Texas percentage: {texas_percentage:.2f}%")
    
    print(f"\n📋 FILE BREAKDOWN:")
    for file_name, stats in file_breakdown.items():
        if stats['texas'] > 0:
            percentage = (stats['texas'] / stats['total']) * 100 if stats['total'] > 0 else 0
            print(f"  {file_name}: {stats['texas']:,} Texas cases ({percentage:.1f}%)")
    
    print(f"\n📝 SAMPLE TEXAS CASES:")
    for i, case in enumerate(sample_texas_cases, 1):
        print(f"  {i}. {case['name']}")
        print(f"     Court: {case['court']}")
        print(f"     File: {case['file']}")
        print()
    
    print(f"🎯 ASSESSMENT:")
    if texas_cases == 0:
        print("❌ Still no Texas cases found - need further investigation")
    elif texas_cases < 1000:
        print(f"⚠️  Low count ({texas_cases:,}) - may need broader detection patterns")
    elif texas_cases < 10000:
        print(f"✅ Good count ({texas_cases:,}) - reasonable dataset for processing")
    else:
        print(f"🎉 Excellent count ({texas_cases:,}) - large dataset available!")
    
    print(f"\n💡 CONCLUSION: Found {texas_cases:,} Texas cases in CAP files")
    print("🔧 The original filtering logic needs to be updated to use this corrected detection method")
    
    return texas_cases

if __name__ == "__main__":
    texas_count = analyze_cap_texas_cases()
