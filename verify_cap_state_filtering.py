#!/usr/bin/env python3
"""
Verify CAP State Filtering
Universal state detection system with verification that sum of all states = total cases
"""

import os
import gzip
import json
import re
from pathlib import Path
from collections import defaultdict

class UniversalStateDetector:
    """Universal state detection for CAP files."""
    
    def __init__(self):
        """Initialize with comprehensive state patterns."""
        
        # US States with their common patterns
        self.state_patterns = {
            'alabama': ['alabama', 'ala\.', r'\bala\b'],
            'alaska': ['alaska', r'\bak\b'],
            'arizona': ['arizona', 'ariz\.', r'\bariz\b'],
            'arkansas': ['arkansas', 'ark\.', r'\bark\b'],
            'california': ['california', 'cal\.', 'calif\.', r'\bcal\b', r'\bca\b'],
            'colorado': ['colorado', 'colo\.', r'\bcolo\b'],
            'connecticut': ['connecticut', 'conn\.', r'\bconn\b'],
            'delaware': ['delaware', 'del\.', r'\bdel\b'],
            'florida': ['florida', 'fla\.', r'\bfla\b'],
            'georgia': ['georgia', 'ga\.', r'\bga\b'],
            'hawaii': ['hawaii', r'\bhi\b'],
            'idaho': ['idaho', r'\bid\b'],
            'illinois': ['illinois', 'ill\.', r'\bill\b'],
            'indiana': ['indiana', 'ind\.', r'\bind\b'],
            'iowa': ['iowa', r'\bia\b'],
            'kansas': ['kansas', 'kan\.', 'kans\.', r'\bkan\b'],
            'kentucky': ['kentucky', 'ky\.', r'\bky\b'],
            'louisiana': ['louisiana', 'la\.', r'\bla\b'],
            'maine': ['maine', 'me\.', r'\bme\b'],
            'maryland': ['maryland', 'md\.', r'\bmd\b'],
            'massachusetts': ['massachusetts', 'mass\.', r'\bmass\b'],
            'michigan': ['michigan', 'mich\.', r'\bmich\b'],
            'minnesota': ['minnesota', 'minn\.', r'\bminn\b'],
            'mississippi': ['mississippi', 'miss\.', r'\bmiss\b'],
            'missouri': ['missouri', 'mo\.', r'\bmo\b'],
            'montana': ['montana', 'mont\.', r'\bmont\b'],
            'nebraska': ['nebraska', 'neb\.', 'nebr\.', r'\bneb\b'],
            'nevada': ['nevada', 'nev\.', r'\bnev\b'],
            'new_hampshire': ['new hampshire', 'n\.h\.', r'\bn\.?h\b'],
            'new_jersey': ['new jersey', 'n\.j\.', r'\bn\.?j\b'],
            'new_mexico': ['new mexico', 'n\.m\.', 'n\. mex\.', r'\bn\.?m\b'],
            'new_york': ['new york', 'n\.y\.', r'\bn\.?y\b'],
            'north_carolina': ['north carolina', 'n\.c\.', r'\bn\.?c\b'],
            'north_dakota': ['north dakota', 'n\.d\.', r'\bn\.?d\b'],
            'ohio': ['ohio', r'\bohio\b'],
            'oklahoma': ['oklahoma', 'okla\.', r'\bokla\b'],
            'oregon': ['oregon', 'ore\.', 'oreg\.', r'\bore\b'],
            'pennsylvania': ['pennsylvania', 'pa\.', 'penn\.', r'\bpa\b'],
            'rhode_island': ['rhode island', 'r\.i\.', r'\br\.?i\b'],
            'south_carolina': ['south carolina', 's\.c\.', r'\bs\.?c\b'],
            'south_dakota': ['south dakota', 's\.d\.', r'\bs\.?d\b'],
            'tennessee': ['tennessee', 'tenn\.', r'\btenn\b'],
            'texas': ['texas', 'tex\.', r'\btex\b', r'\btx\b'],
            'utah': ['utah', r'\butah\b'],
            'vermont': ['vermont', 'vt\.', r'\bvt\b'],
            'virginia': ['virginia', 'va\.', r'\bva\b'],
            'washington': ['washington', 'wash\.', r'\bwash\b'],
            'west_virginia': ['west virginia', 'w\.va\.', 'w\. va\.', r'\bw\.?va\b'],
            'wisconsin': ['wisconsin', 'wis\.', 'wisc\.', r'\bwis\b'],
            'wyoming': ['wyoming', 'wyo\.', r'\bwyo\b'],
            'federal': ['federal', 'united states', 'u\.s\.', 'supreme court of the united states', 'scotus'],
            'district_of_columbia': ['district of columbia', 'd\.c\.', r'\bd\.?c\b']
        }
        
        # Compile regex patterns for efficiency
        self.compiled_patterns = {}
        for state, patterns in self.state_patterns.items():
            combined_pattern = '|'.join(f'({pattern})' for pattern in patterns)
            self.compiled_patterns[state] = re.compile(combined_pattern, re.IGNORECASE)
    
    def detect_state(self, case_data):
        """Detect which state(s) a case belongs to."""
        
        # Get the case text where court information is embedded
        text = case_data.get('text', '').lower()
        
        # Also check metadata if available
        metadata = case_data.get('metadata', {})
        if isinstance(metadata, dict):
            metadata_text = ' '.join(str(v) for v in metadata.values()).lower()
            text += ' ' + metadata_text
        
        detected_states = []
        
        # Check each state pattern
        for state, pattern in self.compiled_patterns.items():
            if pattern.search(text):
                detected_states.append(state)
        
        return detected_states

def verify_state_filtering():
    """Verify state filtering by checking if sum of all states = total cases."""
    
    print("🔍 VERIFYING CAP STATE FILTERING")
    print("=" * 60)
    
    detector = UniversalStateDetector()
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    print(f"📁 Found {len(cap_files)} CAP files")
    print(f"🎯 Will verify filtering on first 4 files")
    print()
    
    verification_results = []
    
    # Test on first 4 files
    for file_idx, file_path in enumerate(cap_files[:4], 1):
        print(f"📁 [{file_idx}/4] Verifying: {file_path.name}")
        
        total_cases = 0
        state_counts = defaultdict(int)
        unclassified_cases = 0
        multiple_state_cases = 0
        
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            case_data = json.loads(line)
                            total_cases += 1
                            
                            # Detect states for this case
                            detected_states = detector.detect_state(case_data)
                            
                            if not detected_states:
                                unclassified_cases += 1
                            elif len(detected_states) > 1:
                                multiple_state_cases += 1
                                # For multiple states, count in each
                                for state in detected_states:
                                    state_counts[state] += 1
                            else:
                                # Single state
                                state_counts[detected_states[0]] += 1
                            
                            # Progress indicator
                            if line_num % 10000 == 0:
                                print(f"    Processed {line_num:,} cases...")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            continue
        
        except Exception as e:
            print(f"  ❌ Error processing {file_path.name}: {e}")
            continue
        
        # Calculate verification metrics
        total_state_assignments = sum(state_counts.values())
        
        print(f"  📊 Results for {file_path.name}:")
        print(f"    Total cases: {total_cases:,}")
        print(f"    Total state assignments: {total_state_assignments:,}")
        print(f"    Unclassified cases: {unclassified_cases:,}")
        print(f"    Multiple-state cases: {multiple_state_cases:,}")
        
        # Show top 10 states
        print(f"    Top 10 states:")
        sorted_states = sorted(state_counts.items(), key=lambda x: x[1], reverse=True)
        for state, count in sorted_states[:10]:
            percentage = (count / total_cases) * 100 if total_cases > 0 else 0
            print(f"      {state}: {count:,} ({percentage:.1f}%)")
        
        # Verification check
        coverage_rate = ((total_cases - unclassified_cases) / total_cases) * 100 if total_cases > 0 else 0
        
        verification_results.append({
            'file': file_path.name,
            'total_cases': total_cases,
            'classified_cases': total_cases - unclassified_cases,
            'coverage_rate': coverage_rate,
            'top_states': sorted_states[:5]
        })
        
        print(f"    ✅ Coverage rate: {coverage_rate:.1f}% ({total_cases - unclassified_cases:,}/{total_cases:,} cases classified)")
        print()
    
    # Overall verification summary
    print("📊 VERIFICATION SUMMARY")
    print("=" * 40)
    
    total_files_verified = len(verification_results)
    avg_coverage = sum(r['coverage_rate'] for r in verification_results) / total_files_verified if total_files_verified > 0 else 0
    
    print(f"Files verified: {total_files_verified}")
    print(f"Average coverage rate: {avg_coverage:.1f}%")
    
    # Show state distribution across all files
    all_states = defaultdict(int)
    total_all_cases = 0
    
    for result in verification_results:
        total_all_cases += result['total_cases']
        for state, count in result['top_states']:
            all_states[state] += count
    
    print(f"\n🗺️  STATE DISTRIBUTION ACROSS ALL VERIFIED FILES:")
    sorted_all_states = sorted(all_states.items(), key=lambda x: x[1], reverse=True)
    for state, count in sorted_all_states[:15]:
        percentage = (count / total_all_cases) * 100 if total_all_cases > 0 else 0
        print(f"  {state}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n🎯 VERIFICATION ASSESSMENT:")
    if avg_coverage >= 95:
        print("✅ EXCELLENT: >95% coverage - filtering is highly accurate")
    elif avg_coverage >= 90:
        print("✅ GOOD: >90% coverage - filtering is reliable")
    elif avg_coverage >= 80:
        print("⚠️ ACCEPTABLE: >80% coverage - some improvement needed")
    else:
        print("❌ POOR: <80% coverage - filtering needs significant improvement")
    
    print(f"\n💡 CONCLUSION:")
    print(f"State filtering achieves {avg_coverage:.1f}% coverage across {total_files_verified} files")
    print(f"Ready to process {total_all_cases:,} cases across all states")
    
    return verification_results

if __name__ == "__main__":
    results = verify_state_filtering()
