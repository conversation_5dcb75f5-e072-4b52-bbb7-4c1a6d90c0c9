#!/usr/bin/env python3
"""
Enhanced Processing with Full-Text Classification
Integrates Gemini 2.5 Pro classification during full case processing when complete opinion text is available
"""

import os
import sys
import logging
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCaseProcessor:
    """Enhanced case processor with full-text classification during processing."""
    
    def __init__(self):
        """Initialize the enhanced processor."""
        from src.relationships.practice_area_classifier import PracticeAreaClassifier
        
        self.classifier = PracticeAreaClassifier(use_gemini=True)
        
        if not self.classifier.use_gemini:
            logger.warning("Gemini not available - falling back to keyword classification")
        else:
            logger.info("✅ Gemini 2.5 Pro initialized for high-confidence classification")
    
    def classify_with_full_text(self, case_data: Dict, opinion_text: str = None) -> Dict:
        """
        Classify case using full opinion text for maximum confidence.
        
        Args:
            case_data: Basic case information
            opinion_text: Complete opinion text (if available)
            
        Returns:
            Classification results with confidence scores
        """
        
        # Prepare comprehensive document for classification
        text_parts = []
        
        # Add case name and basic info
        case_name = case_data.get('case_name_full') or case_data.get('case_name', '')
        if case_name:
            text_parts.append(f"Case: {case_name}")
        
        # Add court and jurisdiction context
        court = case_data.get('court', '')
        if court:
            text_parts.append(f"Court: {court}")
        
        # Add case type/nature if available
        nature = case_data.get('nature', '') or case_data.get('case_type', '')
        if nature:
            text_parts.append(f"Nature: {nature}")
        
        # Add the most important part - full opinion text
        if opinion_text and len(opinion_text.strip()) > 100:
            # Use substantial portion of opinion text for classification
            # Limit to first 8000 characters to stay within token limits
            opinion_excerpt = opinion_text[:8000]
            text_parts.append(f"Opinion: {opinion_excerpt}")
            
            logger.info(f"Classifying with full opinion text ({len(opinion_text)} chars)")
        else:
            logger.info("Classifying with limited text (no full opinion available)")
        
        # Combine all text
        full_text = "\n\n".join(text_parts)
        
        # Create document for classification
        document = {
            "text": full_text,
            "content": full_text,
            "title": case_name,
            "case_name": case_name,
            "court": court,
            "nature": nature
        }
        
        try:
            # Classify with Gemini
            primary_area, confidence, all_scores = self.classifier.classify_with_confidence(document)
            
            # Determine confidence level
            if confidence >= 0.9:
                confidence_level = "VERY_HIGH"
            elif confidence >= 0.7:
                confidence_level = "HIGH"
            elif confidence >= 0.5:
                confidence_level = "MEDIUM"
            else:
                confidence_level = "LOW"
            
            # Get multiple practice areas if scores are close
            practice_areas = [primary_area]
            if all_scores and len(all_scores) > 1:
                for area, score in all_scores[1:]:
                    if score >= 0.4 and score >= (confidence - 0.3):
                        practice_areas.append(area)
                        if len(practice_areas) >= 3:
                            break
            
            classification_result = {
                'primary_practice_area': primary_area,
                'practice_areas': practice_areas,
                'classification_confidence': confidence,
                'confidence_level': confidence_level,
                'all_scores': dict(all_scores[:5]) if all_scores else {},
                'classification_method': 'gemini_full_text' if opinion_text else 'gemini_basic',
                'text_length': len(full_text)
            }
            
            logger.info(f"Classification: {primary_area} ({confidence_level}, {confidence:.3f})")
            
            return classification_result
            
        except Exception as e:
            logger.error(f"Error in full-text classification: {e}")
            return {
                'primary_practice_area': 'general',
                'practice_areas': ['general'],
                'classification_confidence': 0.0,
                'confidence_level': 'ERROR',
                'classification_method': 'error_fallback',
                'error': str(e)
            }

def integrate_classification_into_processing():
    """
    Integrate enhanced classification into the existing processing pipeline.
    This modifies the case processing to include full-text classification.
    """
    
    print("🔧 INTEGRATING ENHANCED CLASSIFICATION INTO PROCESSING PIPELINE")
    print("=" * 70)
    
    # Check current processing pipeline
    try:
        from src.processing.case_law_processor import CaseLawProcessor
        
        print("✅ Found existing case law processor")
        
        # Create enhanced processor
        enhanced_processor = EnhancedCaseProcessor()
        
        print("✅ Enhanced processor with Gemini classification ready")
        
        # Integration points:
        print("\n📍 INTEGRATION POINTS:")
        print("1. After opinion text extraction")
        print("2. Before storing in Supabase")
        print("3. Before Neo4j node creation")
        print("4. Before Pinecone embedding")
        
        return enhanced_processor
        
    except ImportError as e:
        print(f"❌ Could not import case law processor: {e}")
        return None

def test_enhanced_classification():
    """Test the enhanced classification with sample data."""
    
    print("\n🧪 TESTING ENHANCED CLASSIFICATION")
    print("=" * 40)
    
    processor = EnhancedCaseProcessor()
    
    # Test case with full opinion text
    test_case = {
        'case_name': 'Smith v. ABC Insurance Company',
        'court': 'Texas Supreme Court',
        'nature': 'Civil'
    }
    
    # Sample opinion text (personal injury case)
    opinion_text = """
    This case involves a motor vehicle accident that occurred on Interstate 35 in Austin, Texas. 
    The plaintiff, John Smith, was driving southbound when defendant's insured, driving under the 
    influence of alcohol, ran a red light and collided with plaintiff's vehicle. 
    
    Plaintiff suffered severe injuries including broken ribs, a concussion, and permanent back injury 
    requiring multiple surgeries. Medical expenses exceeded $150,000, and plaintiff was unable to 
    work for eight months, resulting in lost wages of $45,000.
    
    The issue before this court is whether the insurance company acted in bad faith by denying 
    coverage for the accident. Under Texas law, an insurance company has a duty to deal fairly 
    and in good faith with its insureds. When an insurance company breaches this duty, it may be 
    liable for damages beyond the policy limits.
    
    We find that defendant insurance company's denial of coverage was unreasonable given the clear 
    liability of its insured. The evidence shows that the insured was intoxicated at the time of 
    the accident and clearly at fault. The insurance company's investigation was inadequate and 
    its denial was not based on a reasonable interpretation of the policy.
    
    Therefore, we hold that the insurance company is liable for bad faith and award plaintiff 
    damages for medical expenses, lost wages, pain and suffering, and punitive damages.
    """
    
    # Test classification
    result = processor.classify_with_full_text(test_case, opinion_text)
    
    print(f"Test Results:")
    print(f"  Primary Area: {result['primary_practice_area']}")
    print(f"  Confidence: {result['classification_confidence']:.3f} ({result['confidence_level']})")
    print(f"  Practice Areas: {result['practice_areas']}")
    print(f"  Method: {result['classification_method']}")
    print(f"  Text Length: {result['text_length']:,} characters")
    
    if result['all_scores']:
        print("  Top Scores:")
        for area, score in list(result['all_scores'].items())[:3]:
            print(f"    {area}: {score:.3f}")
    
    # This should be classified as personal_injury with very high confidence
    expected_area = 'personal_injury'
    if result['primary_practice_area'] == expected_area and result['classification_confidence'] > 0.8:
        print("✅ Test PASSED - High confidence personal injury classification")
    else:
        print(f"⚠️ Test results unexpected - got {result['primary_practice_area']} with {result['classification_confidence']:.3f} confidence")

def create_processing_integration_plan():
    """Create a plan for integrating enhanced classification."""
    
    print("\n📋 PROCESSING INTEGRATION PLAN")
    print("=" * 40)
    
    plan = """
    PHASE 1: MODIFY EXISTING PROCESSORS
    1. Update case_law_processor.py to use enhanced classification
    2. Modify opinion text extraction to feed into classifier
    3. Store classification results with confidence scores
    
    PHASE 2: REPROCESS EXISTING CASES
    1. Fetch cases that have opinion text but poor classification
    2. Re-classify using full opinion text
    3. Update database with high-confidence classifications
    
    PHASE 3: INTEGRATE INTO PIPELINE
    1. Modify dual_source_coordinator.py
    2. Add classification step after opinion extraction
    3. Ensure Neo4j and Pinecone get classified data
    
    EXPECTED OUTCOMES:
    - 90%+ cases with HIGH or VERY_HIGH confidence classification
    - Much better practice area distribution
    - Accurate classification even for complex cases
    - Foundation for high-quality legal research
    """
    
    print(plan)

def main():
    """Main execution function."""
    
    # Test enhanced classification
    test_enhanced_classification()
    
    # Show integration plan
    create_processing_integration_plan()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Integrate enhanced classification into case processing pipeline")
    print("2. Remove artificial case limits to process more data")
    print("3. Re-run Texas processing with full-text classification")
    print("4. Verify we get 10,000+ cases with high-confidence classification")

if __name__ == "__main__":
    main()
