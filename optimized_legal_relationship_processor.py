#!/usr/bin/env python3
"""
Phase 2: Optimized Legal Relationship Processor
Fixes performance bottlenecks in legal relationship creation with:
- Batch-specific processing (not database-wide)
- Efficient Neo4j queries with batching
- Incremental relationship updates
- Proper timeout handling and circuit breakers
- Progress tracking and resumability
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RelationshipBatch:
    """Batch of relationships to process"""
    batch_id: str
    case_ids: List[str]
    relationships: List[Tuple[str, str, str]]  # (rel_type, source_id, target_id)
    processed: int = 0
    total: int = 0


@dataclass
class ProcessingStats:
    """Statistics for relationship processing"""
    cases_processed: int = 0
    relationships_created: int = 0
    relationships_by_type: Dict[str, int] = None
    processing_time: float = 0.0
    errors: int = 0
    
    def __post_init__(self):
        if self.relationships_by_type is None:
            self.relationships_by_type = {'CITES': 0, 'FOLLOWS': 0, 'DISTINGUISHES': 0, 'OVERRULES': 0}


class OptimizedLegalRelationshipProcessor:
    """Optimized legal relationship processor with batch processing and performance improvements"""
    
    def __init__(self, neo4j_client: RealNeo4jClient = None, batch_size: int = 100):
        self.neo4j_client = neo4j_client or RealNeo4jClient()
        self.batch_size = batch_size
        
        # Performance optimizations
        self.max_text_length = 50000  # Limit text analysis to prevent timeouts
        self.context_window = 150     # Reduced context window for faster processing
        self.max_citations_per_case = 50  # Limit citations to prevent runaway processing
        
        # Relationship keywords (optimized for faster matching)
        self.relationship_patterns = {
            'OVERRULES': ['overrule', 'overturn', 'reverse', 'abrogate', 'supersede'],
            'DISTINGUISHES': ['distinguish', 'differ', 'unlike', 'contrary', 'inapplicable'],
            'FOLLOWS': ['follow', 'adhere', 'consistent', 'accordance', 'pursuant', 'guided'],
            'CITES': []  # Default fallback
        }
        
        # Circuit breaker for error handling
        self.error_threshold = 10
        self.error_count = 0
        self.circuit_open = False
        
    def close(self):
        """Close connections"""
        if self.neo4j_client:
            self.neo4j_client.close()
    
    async def process_batch_relationships(self, case_ids: List[str], batch_id: str) -> ProcessingStats:
        """Process legal relationships for a specific batch of cases (not entire database)"""
        
        print(f"\n⚖️ OPTIMIZED LEGAL RELATIONSHIP PROCESSING")
        print("=" * 60)
        print(f"🎯 Processing batch: {batch_id}")
        print(f"📊 Cases in batch: {len(case_ids)}")
        
        start_time = time.time()
        stats = ProcessingStats()
        
        try:
            # Reset circuit breaker for new batch
            self.error_count = 0
            self.circuit_open = False
            
            # Step 1: Get cases with citations (batch-specific)
            cases_with_citations = await self._get_batch_cases_with_citations(case_ids)
            
            if not cases_with_citations:
                print("✅ No cases with citations in this batch")
                return stats
            
            print(f"📋 Found {len(cases_with_citations)} cases with citations in batch")
            
            # Step 2: Process relationships in smaller batches
            relationship_batches = self._create_relationship_batches(cases_with_citations, batch_id)
            
            for i, rel_batch in enumerate(relationship_batches, 1):
                print(f"\n🔄 Processing relationship batch {i}/{len(relationship_batches)}")
                
                if self.circuit_open:
                    print("⚠️ Circuit breaker open, skipping remaining batches")
                    break
                
                batch_stats = await self._process_relationship_batch(rel_batch)
                
                # Aggregate stats
                stats.cases_processed += batch_stats.cases_processed
                stats.relationships_created += batch_stats.relationships_created
                stats.errors += batch_stats.errors
                
                for rel_type, count in batch_stats.relationships_by_type.items():
                    stats.relationships_by_type[rel_type] += count
                
                # Progress update
                progress = (i / len(relationship_batches)) * 100
                print(f"   📊 Progress: {progress:.1f}% ({stats.relationships_created} relationships created)")
            
            # Step 3: Create case-to-case relationships (optimized)
            case_to_case_stats = await self._create_optimized_case_to_case_relationships(case_ids)
            stats.relationships_created += case_to_case_stats
            
            stats.processing_time = time.time() - start_time
            
            print(f"\n📊 BATCH RELATIONSHIP PROCESSING COMPLETE:")
            print(f"   Cases processed: {stats.cases_processed}")
            print(f"   Relationships created: {stats.relationships_created}")
            print(f"   Processing time: {stats.processing_time:.2f}s")
            print(f"   Relationships by type:")
            for rel_type, count in stats.relationships_by_type.items():
                if count > 0:
                    print(f"      {rel_type}: {count}")
            
            return stats
            
        except Exception as e:
            logger.error(f"Error in batch relationship processing: {e}")
            stats.errors += 1
            stats.processing_time = time.time() - start_time
            return stats
    
    async def _get_batch_cases_with_citations(self, case_ids: List[str]) -> Dict[str, List[str]]:
        """Get cases with citations for specific batch (not entire database)"""
        
        cases_with_citations = {}
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Optimized query: only get cases from this batch that have citations
                # Use correct property names: raw_citations and normalized_citations
                result = session.run("""
                    MATCH (c:Case)
                    WHERE c.id IN $case_ids
                    AND (c.raw_citations IS NOT NULL OR c.normalized_citations IS NOT NULL)
                    RETURN c.id as case_id,
                           coalesce(c.raw_citations, []) as raw_citations,
                           coalesce(c.normalized_citations, []) as normalized_citations
                    LIMIT 1000
                """, case_ids=case_ids)
                
                for record in result:
                    case_id = record['case_id']
                    citations = record['raw_citations'] + record['normalized_citations']
                    
                    # Limit citations per case to prevent runaway processing
                    if citations:
                        limited_citations = citations[:self.max_citations_per_case]
                        cases_with_citations[case_id] = limited_citations
                
                return cases_with_citations
                
        except Exception as e:
            logger.error(f"Error getting batch cases with citations: {e}")
            return {}
    
    def _create_relationship_batches(self, cases_with_citations: Dict[str, List[str]], batch_id: str) -> List[RelationshipBatch]:
        """Create smaller batches for relationship processing"""
        
        batches = []
        current_batch_cases = []
        
        for case_id in cases_with_citations.keys():
            current_batch_cases.append(case_id)
            
            if len(current_batch_cases) >= self.batch_size:
                batch = RelationshipBatch(
                    batch_id=f"{batch_id}_rel_{len(batches)}",
                    case_ids=current_batch_cases.copy(),
                    relationships=[],
                    total=len(current_batch_cases)
                )
                batches.append(batch)
                current_batch_cases = []
        
        # Add remaining cases
        if current_batch_cases:
            batch = RelationshipBatch(
                batch_id=f"{batch_id}_rel_{len(batches)}",
                case_ids=current_batch_cases,
                relationships=[],
                total=len(current_batch_cases)
            )
            batches.append(batch)
        
        return batches
    
    async def _process_relationship_batch(self, rel_batch: RelationshipBatch) -> ProcessingStats:
        """Process a single relationship batch with optimizations"""
        
        stats = ProcessingStats()
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get case texts in batch (optimized query)
                case_texts = {}
                result = session.run("""
                    MATCH (c:Case)
                    WHERE c.id IN $case_ids
                    RETURN c.id as case_id,
                           substring(coalesce(c.text, ''), 0, $max_length) as text,
                           coalesce(c.raw_citations, []) as citations
                """, case_ids=rel_batch.case_ids, max_length=self.max_text_length)
                
                for record in result:
                    case_texts[record['case_id']] = {
                        'text': record['text'],
                        'citations': record['citations'][:self.max_citations_per_case]
                    }
                
                # Process each case in the batch
                for case_id in rel_batch.case_ids:
                    if self.error_count >= self.error_threshold:
                        self.circuit_open = True
                        break
                    
                    case_data = case_texts.get(case_id)
                    if not case_data or not case_data['text']:
                        continue
                    
                    try:
                        # Analyze citations with optimized context analysis
                        relationships = self._analyze_citations_optimized(
                            case_id, 
                            case_data['citations'], 
                            case_data['text']
                        )
                        
                        # Store relationships in batch
                        if relationships:
                            stored = await self._store_relationships_batch(relationships)
                            stats.relationships_created += len(stored)
                            
                            # Count by type
                            for rel_type, _, _ in stored:
                                stats.relationships_by_type[rel_type] += 1
                        
                        stats.cases_processed += 1
                        
                    except Exception as e:
                        logger.warning(f"Error processing case {case_id}: {e}")
                        self.error_count += 1
                        stats.errors += 1
                
                return stats
                
        except Exception as e:
            logger.error(f"Error processing relationship batch: {e}")
            stats.errors += 1
            return stats
    
    def _analyze_citations_optimized(self, case_id: str, citations: List[str], case_text: str) -> List[Tuple[str, str, str]]:
        """Optimized citation analysis with faster pattern matching"""
        
        relationships = []
        
        # Preprocess text once for faster searching
        text_lower = case_text.lower()
        
        for citation in citations:
            try:
                # Find citation contexts more efficiently
                citation_lower = citation.lower()
                citation_pos = text_lower.find(citation_lower)
                
                if citation_pos == -1:
                    continue
                
                # Extract context around citation
                start = max(0, citation_pos - self.context_window)
                end = min(len(case_text), citation_pos + len(citation) + self.context_window)
                context = text_lower[start:end]
                
                # Determine relationship type with optimized pattern matching
                rel_type = self._determine_relationship_type_optimized(context)
                
                # Create normalized citation ID
                cited_case_id = self._normalize_citation_to_id(citation)
                
                relationships.append((rel_type, case_id, cited_case_id))
                
            except Exception as e:
                logger.debug(f"Error analyzing citation {citation}: {e}")
                continue
        
        return relationships
    
    def _determine_relationship_type_optimized(self, context: str) -> str:
        """Optimized relationship type determination"""
        
        # Check patterns in order of specificity
        for rel_type, patterns in self.relationship_patterns.items():
            if rel_type == 'CITES':  # Skip default
                continue
                
            for pattern in patterns:
                if pattern in context:
                    return rel_type
        
        return 'CITES'  # Default
    
    def _normalize_citation_to_id(self, citation: str) -> str:
        """Fast citation normalization"""
        import re
        
        # Simple normalization for performance
        normalized = re.sub(r'[^\w\s]', '', citation.lower())
        normalized = re.sub(r'\s+', '_', normalized.strip())
        
        return f"cited_case_{normalized[:50]}"  # Limit length
    
    async def _store_relationships_batch(self, relationships: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """Store relationships in batch for better performance"""
        
        if not relationships:
            return []
        
        stored_relationships = []
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Batch create cited case nodes
                cited_cases = list(set([target_id for _, _, target_id in relationships]))
                
                if cited_cases:
                    session.run("""
                        UNWIND $cited_cases as cited_case_id
                        MERGE (cited:CitedCase {id: cited_case_id})
                        SET cited.citation_reference = cited_case_id
                    """, cited_cases=cited_cases)
                
                # Batch create relationships
                for rel_type, source_case_id, target_case_id in relationships:
                    try:
                        session.run(f"""
                            MATCH (source:Case {{id: $source_case_id}})
                            MATCH (target:CitedCase {{id: $target_case_id}})
                            MERGE (source)-[:{rel_type}]->(target)
                        """, source_case_id=source_case_id, target_case_id=target_case_id)
                        
                        stored_relationships.append((rel_type, source_case_id, target_case_id))
                        
                    except Exception as e:
                        logger.debug(f"Error storing relationship {rel_type}: {source_case_id} -> {target_case_id}: {e}")
                        continue
            
            return stored_relationships
            
        except Exception as e:
            logger.error(f"Error storing relationships batch: {e}")
            return []
    
    async def _create_optimized_case_to_case_relationships(self, case_ids: List[str]) -> int:
        """Create case-to-case relationships for batch only (optimized)"""
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Optimized query: only check relationships within this batch
                result = session.run("""
                    MATCH (citing:Case)
                    MATCH (cited:Case)
                    WHERE citing.id IN $case_ids 
                    AND cited.id IN $case_ids
                    AND citing.id <> cited.id
                    AND any(citation IN coalesce(citing.normalized_citations, [])
                           WHERE citation CONTAINS cited.case_name OR 
                                 citation CONTAINS split(cited.id, '_')[0])
                    MERGE (citing)-[:CITES]->(cited)
                    RETURN count(*) as relationships_created
                """, case_ids=case_ids)
                
                record = result.single()
                relationships_created = record['relationships_created'] if record else 0
                
                print(f"   📊 Created {relationships_created} case-to-case relationships within batch")
                return relationships_created
                
        except Exception as e:
            logger.error(f"Error creating optimized case-to-case relationships: {e}")
            return 0


async def main():
    """Test the optimized legal relationship processor"""
    
    processor = OptimizedLegalRelationshipProcessor()
    
    try:
        # Test with a small batch
        test_case_ids = [
            "real_cl_ca5_11108616",
            "real_cl_ca5_11108615", 
            "real_cl_ca5_11108614"
        ]
        
        stats = await processor.process_batch_relationships(
            case_ids=test_case_ids,
            batch_id="test_optimized_batch"
        )
        
        print(f"\n✅ OPTIMIZATION TEST COMPLETE:")
        print(f"   Cases processed: {stats.cases_processed}")
        print(f"   Relationships created: {stats.relationships_created}")
        print(f"   Processing time: {stats.processing_time:.2f}s")
        print(f"   Errors: {stats.errors}")
        
        return stats.errors == 0
        
    finally:
        processor.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
