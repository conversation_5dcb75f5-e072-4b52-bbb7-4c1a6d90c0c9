#!/usr/bin/env python3
"""
Test Enhanced Graph Schema

This script tests the enhanced Neo4j graph schema including:
1. Judge modeling and relationships
2. Enhanced case relationship types
3. Authority metrics calculation
4. Graph query capabilities
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List

from enhanced_graph_schema import EnhancedGraphSchema, JudgeInfo, CaseRelationship
from src.processing.storage.neo4j_connector import Neo4jConnector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GraphSchemaValidator:
    """Validates the enhanced graph schema functionality."""
    
    def __init__(self):
        """Initialize the validator."""
        self.enhanced_schema = EnhancedGraphSchema()
        self.neo4j = Neo4jConnector()
        
        # Test data
        self.test_judges = [
            JudgeInfo(
                name="Justice <PERSON>",
                id="judge_roberts",
                court_id="scotus",
                jurisdiction="fed",
                appointment_date="2005-09-29",
                appointing_authority="President <PERSON>",
                judicial_philosophy="Conservative",
                specializations=["Constitutional Law", "Administrative Law"],
                active=True
            ),
            JudgeIn<PERSON>(
                name="Justice <PERSON>",
                id="judge_ginsburg",
                court_id="scotus",
                jurisdiction="fed",
                appointment_date="1993-08-10",
                appointing_authority="President Bill Clinton",
                judicial_philosophy="Liberal",
                specializations=["Gender Equality", "Civil Rights"],
                active=False
            ),
            JudgeInfo(
                name="Judge William Pryor",
                id="judge_pryor",
                court_id="ca11",
                jurisdiction="fed",
                appointment_date="2004-02-20",
                appointing_authority="President George W. Bush",
                judicial_philosophy="Conservative",
                specializations=["Criminal Law", "Constitutional Law"],
                active=True
            )
        ]
        
        self.test_cases = [
            {
                'id': 'test_case_1',
                'name': 'Smith v. Jones',
                'jurisdiction': 'tx',
                'court_id': 'tex_supreme',
                'date_filed': '2023-03-15',
                'practice_area': 'personal_injury',
                'judges': ['Justice Brown', 'Justice Garcia'],
                'author': 'Justice Brown'
            },
            {
                'id': 'test_case_2',
                'name': 'Johnson v. City of Austin',
                'jurisdiction': 'tx',
                'court_id': 'tex_supreme',
                'date_filed': '2023-06-20',
                'practice_area': 'civil_rights',
                'judges': ['Justice Brown', 'Justice Wilson', 'Justice Garcia'],
                'author': 'Justice Wilson'
            },
            {
                'id': 'test_case_3',
                'name': 'Martinez v. State',
                'jurisdiction': 'tx',
                'court_id': 'tex_crim_appeals',
                'date_filed': '2023-08-10',
                'practice_area': 'criminal_defense',
                'judges': ['Judge Thompson', 'Judge Davis'],
                'author': 'Judge Thompson'
            }
        ]
        
        logger.info("✅ Graph Schema Validator initialized")
    
    def test_schema_initialization(self) -> bool:
        """Test schema initialization."""
        
        logger.info("🔧 Testing schema initialization...")
        
        try:
            success = self.enhanced_schema.initialize_enhanced_schema()
            
            if success:
                logger.info("✅ Schema initialization successful")
                return True
            else:
                logger.error("❌ Schema initialization failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Schema initialization error: {e}")
            return False
    
    def test_judge_creation(self) -> bool:
        """Test judge node creation and relationships."""
        
        logger.info("👨‍⚖️ Testing judge creation...")
        
        success_count = 0
        
        for judge_info in self.test_judges:
            try:
                success = self.enhanced_schema.create_or_update_judge(judge_info)
                
                if success:
                    logger.info(f"✅ Created judge: {judge_info.name}")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to create judge: {judge_info.name}")
                    
            except Exception as e:
                logger.error(f"❌ Error creating judge {judge_info.name}: {e}")
        
        success_rate = success_count / len(self.test_judges)
        logger.info(f"Judge creation success rate: {success_count}/{len(self.test_judges)} ({success_rate:.1%})")
        
        return success_rate >= 0.8
    
    def test_case_creation_with_judges(self) -> bool:
        """Test case creation with judge relationships."""
        
        logger.info("⚖️ Testing case creation with judge relationships...")
        
        success_count = 0
        
        for case_data in self.test_cases:
            try:
                # Create case node
                case_properties = {
                    'id': case_data['id'],
                    'name': case_data['name'],
                    'jurisdiction': case_data['jurisdiction'],
                    'court_id': case_data['court_id'],
                    'date_filed': case_data['date_filed'],
                    'practice_area': case_data['practice_area']
                }
                
                case_success = self.neo4j.create_case(case_properties)
                
                if case_success:
                    # Extract and store judges
                    judge_ids = self.enhanced_schema.extract_and_store_judges_from_case(case_data)
                    
                    logger.info(f"✅ Created case {case_data['name']} with {len(judge_ids)} judges")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to create case: {case_data['name']}")
                    
            except Exception as e:
                logger.error(f"❌ Error creating case {case_data['name']}: {e}")
        
        success_rate = success_count / len(self.test_cases)
        logger.info(f"Case creation success rate: {success_count}/{len(self.test_cases)} ({success_rate:.1%})")
        
        return success_rate >= 0.8
    
    def test_enhanced_case_relationships(self) -> bool:
        """Test enhanced case relationship creation."""
        
        logger.info("🔗 Testing enhanced case relationships...")
        
        # Create test relationships between cases
        test_relationships = [
            CaseRelationship(
                citing_case_id='test_case_2',
                cited_case_id='test_case_1',
                relationship_type='FOLLOWS',
                citation_text='Smith v. Jones, 123 S.W.3d 456 (Tex. 2023)',
                context='Following the precedent established in Smith v. Jones...',
                strength='strong',
                confidence_score=0.9
            ),
            CaseRelationship(
                citing_case_id='test_case_3',
                cited_case_id='test_case_1',
                relationship_type='DISTINGUISHES',
                citation_text='Smith v. Jones, 123 S.W.3d 456 (Tex. 2023)',
                context='We distinguish this case from Smith v. Jones because...',
                strength='medium',
                confidence_score=0.8
            ),
            CaseRelationship(
                citing_case_id='test_case_3',
                cited_case_id='test_case_2',
                relationship_type='CITES',
                citation_text='Johnson v. City of Austin, 124 S.W.3d 789 (Tex. 2023)',
                context='As noted in Johnson v. City of Austin...',
                strength='medium',
                confidence_score=0.7
            )
        ]
        
        success_count = 0
        
        for relationship in test_relationships:
            try:
                success = self.enhanced_schema.create_enhanced_case_relationship(relationship)
                
                if success:
                    logger.info(f"✅ Created {relationship.relationship_type} relationship")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to create {relationship.relationship_type} relationship")
                    
            except Exception as e:
                logger.error(f"❌ Error creating relationship: {e}")
        
        success_rate = success_count / len(test_relationships)
        logger.info(f"Relationship creation success rate: {success_count}/{len(test_relationships)} ({success_rate:.1%})")
        
        return success_rate >= 0.8
    
    def test_authority_metrics(self) -> bool:
        """Test authority metrics calculation."""
        
        logger.info("📊 Testing authority metrics calculation...")
        
        try:
            # Calculate authority metrics for test cases
            for case_data in self.test_cases:
                case_id = case_data['id']
                
                metrics = self.enhanced_schema.get_case_authority_metrics(case_id)
                
                logger.info(f"Authority metrics for {case_data['name']}:")
                logger.info(f"  Incoming citations: {metrics['incoming_citations']}")
                logger.info(f"  Outgoing citations: {metrics['outgoing_citations']}")
                logger.info(f"  Judge count: {metrics['judge_count']}")
                logger.info(f"  Authority score: {metrics['authority_score']:.2f}")
                logger.info(f"  Judges: {', '.join(metrics['judges'])}")
            
            logger.info("✅ Authority metrics calculation successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Authority metrics calculation failed: {e}")
            return False
    
    def test_graph_queries(self) -> bool:
        """Test complex graph queries."""
        
        logger.info("🔍 Testing complex graph queries...")
        
        try:
            with self.neo4j.driver.session() as session:
                
                # Query 1: Find cases by judge
                logger.info("Query 1: Cases by judge")
                result = session.run("""
                    MATCH (j:Judge)-[r]->(c:Case)
                    RETURN j.name as judge_name, type(r) as relationship, c.name as case_name
                    LIMIT 10
                """)
                
                for record in result:
                    logger.info(f"  {record['judge_name']} {record['relationship']} {record['case_name']}")
                
                # Query 2: Citation network
                logger.info("Query 2: Citation network")
                result = session.run("""
                    MATCH (c1:Case)-[r]->(c2:Case)
                    WHERE type(r) IN ['CITES', 'FOLLOWS', 'DISTINGUISHES']
                    RETURN c1.name as citing_case, type(r) as relationship, c2.name as cited_case, r.strength as strength
                    LIMIT 10
                """)
                
                for record in result:
                    logger.info(f"  {record['citing_case']} {record['relationship']} {record['cited_case']} ({record['strength']})")
                
                # Query 3: Judge collaboration network
                logger.info("Query 3: Judge collaboration")
                result = session.run("""
                    MATCH (j1:Judge)-[:AUTHORED|JOINED]->(c:Case)<-[:AUTHORED|JOINED]-(j2:Judge)
                    WHERE j1.name < j2.name
                    RETURN j1.name as judge1, j2.name as judge2, c.name as case_name
                    LIMIT 5
                """)
                
                for record in result:
                    logger.info(f"  {record['judge1']} & {record['judge2']} collaborated on {record['case_name']}")
                
                logger.info("✅ Graph queries successful")
                return True
                
        except Exception as e:
            logger.error(f"❌ Graph queries failed: {e}")
            return False
    
    def run_comprehensive_test(self) -> Dict[str, bool]:
        """Run comprehensive test suite."""
        
        logger.info("🚀 STARTING ENHANCED GRAPH SCHEMA TESTS")
        logger.info("=" * 60)
        
        results = {
            'schema_initialization': False,
            'judge_creation': False,
            'case_creation_with_judges': False,
            'enhanced_case_relationships': False,
            'authority_metrics': False,
            'graph_queries': False
        }
        
        # Run all tests
        results['schema_initialization'] = self.test_schema_initialization()
        results['judge_creation'] = self.test_judge_creation()
        results['case_creation_with_judges'] = self.test_case_creation_with_judges()
        results['enhanced_case_relationships'] = self.test_enhanced_case_relationships()
        results['authority_metrics'] = self.test_authority_metrics()
        results['graph_queries'] = self.test_graph_queries()
        
        # Calculate overall success
        passed_tests = sum(1 for result in results.values() if result is True)
        total_tests = len(results)
        overall_success = passed_tests >= 5  # At least 5/6 tests must pass
        
        # Print results
        logger.info("\n🎉 ENHANCED GRAPH SCHEMA TEST RESULTS")
        logger.info("=" * 50)
        
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        logger.info(f"\n📊 OVERALL RESULT: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
        logger.info(f"   Tests passed: {passed_tests}/{total_tests}")
        
        return results


async def main():
    """Main function to run enhanced graph schema tests."""
    
    validator = GraphSchemaValidator()
    results = validator.run_comprehensive_test()
    
    # Return success if most tests passed
    success = sum(1 for result in results.values() if result is True) >= 5
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
