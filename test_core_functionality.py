#!/usr/bin/env python3
"""
Core Functionality Test

This script tests the core functionality of the enhanced processing system
using the correct method names and simplified validation.
"""

import os
import json
import gzip
import logging
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
import sys
sys.path.append('/Users/<USER>/Documents/GitHub/texas-laws-personalinjury')

from src.processing.caselaw_access_processor import CaselawAccessProcessor
from src.relationships.practice_area_classifier import PracticeAreaClassifier

def test_core_functionality():
    """Test the core functionality of the enhanced processing system"""
    
    logger.info("🧪 Testing Core Functionality...")
    
    # Initialize components
    try:
        processor = CaselawAccessProcessor()
        practice_classifier = PracticeAreaClassifier()
        logger.info("✅ Components initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize components: {e}")
        return False
    
    # Get sample data
    data_dir = Path("data/caselaw_access_project")
    if not data_dir.exists():
        logger.error(f"❌ Data directory not found: {data_dir}")
        return False
    
    data_files = list(data_dir.glob("cap_*.jsonl.gz"))
    if not data_files:
        logger.error("❌ No data files found")
        return False
    
    first_file = sorted(data_files)[0]
    logger.info(f"📄 Reading from: {first_file}")
    
    # Test with 5 sample cases
    sample_cases = []
    try:
        with gzip.open(first_file, 'rt', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if len(sample_cases) >= 5:
                    break
                
                try:
                    case_data = json.loads(line.strip())
                    sample_cases.append(case_data)
                except json.JSONDecodeError:
                    continue
    except Exception as e:
        logger.error(f"❌ Error reading data file: {e}")
        return False
    
    if not sample_cases:
        logger.error("❌ No sample cases found")
        return False
    
    logger.info(f"📊 Testing with {len(sample_cases)} sample cases")
    
    # Test each component
    results = {
        'normalize_document': 0,
        'classify_practice_area': 0,
        'assess_quality': 0,
        'generate_namespace': 0,
        'total_cases': len(sample_cases)
    }
    
    for i, case_data in enumerate(sample_cases, 1):
        logger.info(f"🔍 Testing case {i}/{len(sample_cases)}...")
        
        try:
            # Test 1: Document normalization
            doc = processor.normalize_document(case_data)
            results['normalize_document'] += 1
            logger.info(f"  ✅ Normalized: {doc.case_name[:50]}...")
            
            # Test 2: Practice area classification
            practice_area = processor._classify_practice_area(doc)
            results['classify_practice_area'] += 1
            logger.info(f"  ✅ Practice area: {practice_area}")
            
            # Test 3: Quality assessment
            quality = processor._assess_document_quality(doc)
            results['assess_quality'] += 1
            logger.info(f"  ✅ Quality: {quality}")
            
            # Test 4: Namespace generation
            namespace = processor._determine_vector_namespace(doc, practice_area)
            results['generate_namespace'] += 1
            logger.info(f"  ✅ Namespace: {namespace}")
            
            # Test 5: Practice area classifier directly
            try:
                # Test with document format expected by classifier
                practice_result = practice_classifier.classify_with_confidence({"text": doc.text})
                primary_area, confidence, all_areas = practice_result
                logger.info(f"  ✅ Direct classifier: {primary_area} ({confidence:.2f})")
            except Exception as e:
                logger.warning(f"  ⚠️ Direct classifier failed: {e}")
            
        except Exception as e:
            logger.error(f"  ❌ Error processing case {i}: {e}")
    
    # Generate summary
    logger.info("\n" + "="*50)
    logger.info("📋 CORE FUNCTIONALITY TEST SUMMARY")
    logger.info("="*50)
    
    success_rates = {}
    for component, successes in results.items():
        if component != 'total_cases':
            success_rate = (successes / results['total_cases']) * 100
            success_rates[component] = success_rate
            logger.info(f"{component}: {successes}/{results['total_cases']} ({success_rate:.1f}%)")
    
    overall_success = sum(success_rates.values()) / len(success_rates)
    logger.info(f"\nOverall Success Rate: {overall_success:.1f}%")
    
    if overall_success >= 80:
        logger.info("🎉 Core functionality test PASSED!")
        return True
    else:
        logger.error("❌ Core functionality test FAILED!")
        return False

def test_namespace_generation():
    """Test namespace generation logic"""
    
    logger.info("🧪 Testing Namespace Generation...")
    
    try:
        processor = CaselawAccessProcessor()
        
        # Test cases for namespace generation
        test_cases = [
            {"jurisdiction": "tx", "practice_area": "personal_injury", "expected_pattern": "tx-case-personal_injury"},
            {"jurisdiction": "ca", "practice_area": "criminal_law", "expected_pattern": "ca-case-criminal_law"},
            {"jurisdiction": "fed", "practice_area": "business_law", "expected_pattern": "fed-case-business_law"},
            {"jurisdiction": "ny", "practice_area": "general", "expected_pattern": "ny-case"},
            {"jurisdiction": None, "practice_area": "personal_injury", "expected_pattern": "unknown-case"},
        ]
        
        successes = 0
        for i, test_case in enumerate(test_cases, 1):
            # Create mock document
            from src.processing.caselaw_access_processor import CaselawDocument
            from datetime import datetime
            
            doc = CaselawDocument(
                id=f"test_{i}",
                source="test",
                added=datetime.now(),
                created=datetime.now(),
                author="test",
                license="test",
                url="test",
                text="test document",
                jurisdiction=test_case["jurisdiction"]
            )
            
            namespace = processor._determine_vector_namespace(doc, test_case["practice_area"])
            
            if test_case["expected_pattern"] in namespace:
                logger.info(f"✅ Test {i}: {namespace}")
                successes += 1
            else:
                logger.error(f"❌ Test {i}: Expected pattern '{test_case['expected_pattern']}' not in '{namespace}'")
        
        success_rate = (successes / len(test_cases)) * 100
        logger.info(f"Namespace Generation Success Rate: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ Namespace generation test failed: {e}")
        return False

def main():
    """Run all tests"""
    
    logger.info("🚀 Starting Core Functionality Tests...")
    
    # Test 1: Core functionality
    core_test_passed = test_core_functionality()
    
    # Test 2: Namespace generation
    namespace_test_passed = test_namespace_generation()
    
    logger.info("\n" + "="*60)
    logger.info("🎯 FINAL TEST RESULTS")
    logger.info("="*60)
    logger.info(f"Core Functionality: {'✅ PASSED' if core_test_passed else '❌ FAILED'}")
    logger.info(f"Namespace Generation: {'✅ PASSED' if namespace_test_passed else '❌ FAILED'}")
    
    overall_success = core_test_passed and namespace_test_passed
    
    if overall_success:
        logger.info("\n🎉 All tests PASSED! System is ready for processing.")
        logger.info("💡 Next steps:")
        logger.info("  1. Push updated configurations to GitHub")
        logger.info("  2. Begin processing Caselaw Access Project dataset")
        logger.info("  3. Monitor processing and adjust batch sizes as needed")
    else:
        logger.error("\n❌ Some tests FAILED! Please review errors before proceeding.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)