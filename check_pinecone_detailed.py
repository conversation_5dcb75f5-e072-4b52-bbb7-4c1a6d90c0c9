#!/usr/bin/env python3
"""
Detailed script to check Pinecone vector database population.
"""

import asyncio
import os
from pinecone import Pinecone

async def check_pinecone_detailed():
    """Check Pinecone with detailed namespace analysis."""
    try:
        print("🔍 DETAILED PINECONE VECTOR DATABASE ANALYSIS")
        print("=" * 60)
        
        # Initialize Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        
        # Get index stats
        stats = index.describe_index_stats()
        
        print(f"📊 Pinecone Index: texas-laws-voyage3large")
        print(f"   Total vectors: {stats.total_vector_count:,}")
        print(f"   Dimension: {stats.dimension}")
        
        # Detailed namespace analysis
        if hasattr(stats, 'namespaces') and stats.namespaces:
            print(f"\n   📂 Namespace Breakdown:")
            
            total_by_source = {
                'court_listener': 0,
                'caselaw_access': 0,
                'statutes': 0,
                'other': 0
            }
            
            for namespace, info in stats.namespaces.items():
                vector_count = info.vector_count
                print(f"     {namespace}: {vector_count:,} vectors")
                
                # Categorize by source
                if 'cl_' in namespace or 'court_listener' in namespace or namespace.endswith('-case'):
                    total_by_source['court_listener'] += vector_count
                elif 'statute' in namespace:
                    total_by_source['statutes'] += vector_count
                elif namespace == '' or 'cap_' in namespace:
                    total_by_source['caselaw_access'] += vector_count
                else:
                    total_by_source['other'] += vector_count
            
            print(f"\n   📊 Source Summary:")
            for source, count in total_by_source.items():
                if count > 0:
                    print(f"     {source.replace('_', ' ').title()}: {count:,} vectors")
        
        return stats.total_vector_count
        
    except Exception as e:
        print(f"❌ Error checking Pinecone: {e}")
        return 0

async def main():
    """Main function."""
    vector_count = await check_pinecone_detailed()
    
    # Expected vs Actual analysis
    expected_cases = 15284  # From Supabase
    expected_vectors = expected_cases * 3  # Assuming ~3 chunks per case
    
    print(f"\n🎯 CONSISTENCY CHECK:")
    print(f"   Expected cases: {expected_cases:,}")
    print(f"   Expected vectors (~3 per case): {expected_vectors:,}")
    print(f"   Actual vectors: {vector_count:,}")
    
    ratio = vector_count / expected_cases if expected_cases > 0 else 0
    print(f"   Vectors per case ratio: {ratio:.1f}")
    
    if 2.0 <= ratio <= 5.0:
        print(f"   ✅ CONSISTENT: Reasonable vector-to-case ratio")
    else:
        print(f"   ⚠️  INCONSISTENT: Unusual vector-to-case ratio")

if __name__ == "__main__":
    asyncio.run(main())
