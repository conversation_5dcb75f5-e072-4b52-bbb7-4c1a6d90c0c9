#!/usr/bin/env python3
"""
Full Dual-Source Pipeline Runner
Executes complete processing of 92,081 Texas opinions through all storage systems.

Usage:
    python scripts/run_pipeline.py --full
    python scripts/run_pipeline.py --test --limit 100
"""

import os
import sys
import argparse
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from processing.caselaw_access_processor import CaselawAccessProcessor
from processing.courtlistener_bulk_client import CourtListenerBulkClient
from graph.graph_sync import flush_all, get_stats, close_connection

# Set up logging
def setup_logging(log_file):
    """Configure logging for pipeline execution."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )
    return logging.getLogger(__name__)

class FullPipelineRunner:
    """Orchestrates complete dual-source processing pipeline."""
    
    def __init__(self, full_run=True, limit=None):
        """Initialize pipeline runner."""
        self.full_run = full_run
        self.limit = limit
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Set up logging
        log_file = f"logs/full_run_{self.timestamp}.txt"
        os.makedirs('logs', exist_ok=True)
        self.logger = setup_logging(log_file)
        
        self.stats = {
            'start_time': datetime.now(),
            'cap_stats': {},
            'cl_stats': {},
            'total_processed': 0,
            'total_stored': 0,
            'errors': []
        }
        
        self.logger.info(f"Initialized pipeline runner - Full: {full_run}, Limit: {limit}")
    
    def validate_environment(self):
        """Validate required environment variables and connections."""
        self.logger.info("Validating environment configuration...")
        
        required_vars = [
            'SUPABASE_URL', 'SUPABASE_KEY',
            'GCS_BUCKET', 'GOOGLE_APPLICATION_CREDENTIALS',
            'NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD',
            'PINECONE_API_KEY', 'COURTLISTENER_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.logger.error(f"Missing environment variables: {missing_vars}")
            return False
        
        # Validate GCS bucket
        gcs_bucket = os.getenv('GCS_BUCKET')
        if gcs_bucket != 'texas-laws-cases':
            self.logger.warning(f"GCS_BUCKET is '{gcs_bucket}', expected 'texas-laws-cases'")
        
        self.logger.info("✅ Environment validation passed")
        return True
    
    async def process_cap_historical(self):
        """Process CAP historical data (1950-1993)."""
        self.logger.info("Starting CAP historical processing (1950-1993)...")
        
        try:
            processor = CaselawAccessProcessor()
            
            # Find CAP files
            cap_dir = Path("data/caselaw_access_project")
            if not cap_dir.exists():
                self.logger.error(f"CAP directory not found: {cap_dir}")
                return False
            
            cap_files = list(cap_dir.glob("*.jsonl.gz"))
            self.logger.info(f"Found {len(cap_files)} CAP files")
            
            total_processed = 0
            total_stored = 0
            
            for cap_file in cap_files:
                self.logger.info(f"Processing {cap_file.name}...")
                
                try:
                    # Process with time window filtering (1950-1993)
                    result = await processor.process_file(
                        cap_file, 
                        limit=self.limit if not self.full_run else None
                    )
                    
                    if result:
                        file_processed = result.get('total_processed', 0)
                        file_stored = result.get('stored', 0)
                        
                        total_processed += file_processed
                        total_stored += file_stored
                        
                        self.logger.info(f"  {cap_file.name}: {file_processed} processed, {file_stored} stored")
                        
                        # Break if limit reached
                        if not self.full_run and self.limit and total_processed >= self.limit:
                            break
                
                except Exception as e:
                    self.logger.error(f"Error processing {cap_file}: {e}")
                    self.stats['errors'].append(f"CAP file {cap_file.name}: {e}")
                    continue
            
            self.stats['cap_stats'] = {
                'files_processed': len(cap_files),
                'total_processed': total_processed,
                'total_stored': total_stored,
                'source_window': 'historical',
                'date_range': '1950-1993'
            }
            
            self.logger.info(f"✅ CAP processing complete: {total_stored:,} cases stored")
            return True
            
        except Exception as e:
            self.logger.error(f"CAP processing failed: {e}")
            self.stats['errors'].append(f"CAP processing: {e}")
            return False
    
    async def process_courtlistener_modern(self):
        """Process CourtListener modern data (1994-2025)."""
        self.logger.info("Starting CourtListener modern processing (1994-2025)...")
        
        try:
            client = CourtListenerBulkClient()
            
            # Texas jurisdictions for comprehensive coverage
            jurisdictions = ['tx'] if not self.full_run else ['tx']
            
            total_processed = 0
            total_stored = 0
            
            for jurisdiction in jurisdictions:
                self.logger.info(f"Processing jurisdiction: {jurisdiction}")
                
                try:
                    # Process with time window filtering (1994-2025)
                    result = await client.process_jurisdiction(
                        jurisdiction,
                        limit=self.limit if not self.full_run else None
                    )
                    
                    if result:
                        jurisdiction_processed = result.get('total_processed', 0)
                        jurisdiction_stored = result.get('stored', 0)
                        
                        total_processed += jurisdiction_processed
                        total_stored += jurisdiction_stored
                        
                        self.logger.info(f"  {jurisdiction}: {jurisdiction_processed} processed, {jurisdiction_stored} stored")
                
                except Exception as e:
                    self.logger.error(f"Error processing jurisdiction {jurisdiction}: {e}")
                    self.stats['errors'].append(f"CL jurisdiction {jurisdiction}: {e}")
                    continue
            
            self.stats['cl_stats'] = {
                'jurisdictions_processed': len(jurisdictions),
                'total_processed': total_processed,
                'total_stored': total_stored,
                'source_window': 'modern',
                'date_range': '1994-2025'
            }
            
            self.logger.info(f"✅ CourtListener processing complete: {total_stored:,} cases stored")
            return True
            
        except Exception as e:
            self.logger.error(f"CourtListener processing failed: {e}")
            self.stats['errors'].append(f"CourtListener processing: {e}")
            return False
    
    async def finalize_processing(self):
        """Finalize processing and sync all pending operations."""
        self.logger.info("Finalizing processing and syncing all systems...")
        
        try:
            # Flush all pending graph operations
            self.logger.info("Flushing Neo4j graph operations...")
            flush_all()
            
            # Get final graph stats
            graph_stats = get_stats()
            self.logger.info(f"Final graph state: {graph_stats['nodes']['total']:,} nodes, {graph_stats['relationships']['total']:,} relationships")
            
            # Calculate totals
            self.stats['total_processed'] = (
                self.stats['cap_stats'].get('total_processed', 0) + 
                self.stats['cl_stats'].get('total_processed', 0)
            )
            self.stats['total_stored'] = (
                self.stats['cap_stats'].get('total_stored', 0) + 
                self.stats['cl_stats'].get('total_stored', 0)
            )
            
            self.logger.info("✅ Processing finalization complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Finalization failed: {e}")
            self.stats['errors'].append(f"Finalization: {e}")
            return False
    
    def print_final_summary(self):
        """Print comprehensive pipeline execution summary."""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print(f"\n{'='*80}")
        print(f"DUAL-SOURCE PIPELINE EXECUTION SUMMARY")
        print(f"{'='*80}")
        print(f"Execution Mode: {'FULL' if self.full_run else f'TEST (limit: {self.limit})'}")
        print(f"Start Time: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Duration: {duration}")
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"  CAP Historical (1950-1993): {self.stats['cap_stats'].get('total_stored', 0):,} cases")
        print(f"  CourtListener Modern (1994-2025): {self.stats['cl_stats'].get('total_stored', 0):,} cases")
        print(f"  Total Cases Stored: {self.stats['total_stored']:,}")
        
        if duration.total_seconds() > 0:
            rate = self.stats['total_stored'] / (duration.total_seconds() / 60)
            print(f"  Processing Rate: {rate:.1f} cases/minute")
        
        print(f"\n🎯 STORAGE SYSTEMS:")
        print(f"  Supabase: {self.stats['total_stored']:,} records")
        print(f"  GCS: {self.stats['total_stored']:,} JSON documents")
        print(f"  Neo4j: Synced via graph_sync module")
        print(f"  Pinecone: {self.stats['total_stored']:,} vector embeddings")
        
        if self.stats['errors']:
            print(f"\n⚠️  ERRORS ENCOUNTERED ({len(self.stats['errors'])}):")
            for error in self.stats['errors'][:10]:  # Show first 10 errors
                print(f"  - {error}")
            if len(self.stats['errors']) > 10:
                print(f"  ... and {len(self.stats['errors']) - 10} more errors")
        else:
            print(f"\n✅ NO ERRORS - Perfect execution!")
        
        print(f"\n📄 NEXT STEPS:")
        print(f"  1. Review logs: logs/full_run_{self.timestamp}.txt")
        print(f"  2. Generate reports: python scripts/generate_phase5a_reports.py")
        print(f"  3. Validate Neo4j: python scripts/neo4j_delta_validator.py")
        print(f"  4. Proceed to Phase 5b (practice area tagging)")
    
    async def run(self):
        """Execute complete pipeline."""
        self.logger.info(f"Starting Phase 5a pipeline execution - {self.timestamp}")
        
        # Validate environment
        if not self.validate_environment():
            self.logger.error("Environment validation failed - aborting")
            return False
        
        success = True
        
        # Process CAP historical data
        if not await self.process_cap_historical():
            success = False
        
        # Process CourtListener modern data
        if not await self.process_courtlistener_modern():
            success = False
        
        # Finalize processing
        if not await self.finalize_processing():
            success = False
        
        # Close connections
        close_connection()
        
        # Print summary
        self.print_final_summary()
        
        self.logger.info(f"Pipeline execution {'completed successfully' if success else 'completed with errors'}")
        return success

async def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='Run dual-source processing pipeline')
    parser.add_argument('--full', action='store_true', help='Run full pipeline on all 92k cases')
    parser.add_argument('--test', action='store_true', help='Run test mode with limited cases')
    parser.add_argument('--limit', type=int, default=100, help='Limit for test mode (default: 100)')
    
    args = parser.parse_args()
    
    if not args.full and not args.test:
        print("Error: Must specify either --full or --test mode")
        return 1
    
    # Create pipeline runner
    runner = FullPipelineRunner(
        full_run=args.full,
        limit=args.limit if args.test else None
    )
    
    # Execute pipeline
    success = await runner.run()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
