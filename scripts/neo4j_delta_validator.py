#!/usr/bin/env python3
"""
Neo4j Delta Validator
Generates delta reports after nightly sync operations.
Validates graph integrity and reports changes.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from graph.graph_sync import get_graph_sync

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Neo4jDeltaValidator:
    """Validates Neo4j graph changes and generates delta reports."""
    
    def __init__(self):
        """Initialize validator."""
        self.graph_sync = get_graph_sync()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def get_current_stats(self):
        """Get current graph statistics."""
        return self.graph_sync.get_stats()
    
    def validate_constraints(self):
        """Check for constraint violations."""
        if not self.graph_sync.driver:
            self.graph_sync.connect()
        
        violations = []
        
        with self.graph_sync.driver.session() as session:
            # Check for duplicate opinion IDs
            result = session.run("""
                MATCH (o:Opinion)
                WITH o.opinion_id as opinion_id, count(o) as count
                WHERE count > 1
                RETURN opinion_id, count
            """)
            
            for record in result:
                violations.append({
                    'type': 'duplicate_opinion_id',
                    'opinion_id': record['opinion_id'],
                    'count': record['count']
                })
            
            # Check for orphaned opinions (no court relationship)
            result = session.run("""
                MATCH (o:Opinion)
                WHERE NOT (o)<-[:HEARS_BEFORE]-()
                RETURN o.opinion_id as opinion_id
                LIMIT 10
            """)
            
            orphaned_opinions = [record['opinion_id'] for record in result]
            if orphaned_opinions:
                violations.append({
                    'type': 'orphaned_opinions',
                    'count': len(orphaned_opinions),
                    'examples': orphaned_opinions
                })
            
            # Check for opinions without practice areas
            result = session.run("""
                MATCH (o:Opinion)
                WHERE NOT (o)-[:HAS_PA]->()
                RETURN count(o) as count
            """)
            
            no_pa_count = result.single()['count']
            if no_pa_count > 0:
                violations.append({
                    'type': 'opinions_without_practice_areas',
                    'count': no_pa_count
                })
        
        return violations
    
    def get_court_distribution(self):
        """Get distribution of opinions by court."""
        if not self.graph_sync.driver:
            self.graph_sync.connect()
        
        with self.graph_sync.driver.session() as session:
            result = session.run("""
                MATCH (c:Court)-[:HEARS_BEFORE]->(o:Opinion)
                RETURN c.court_id as court_id, c.name as court_name, 
                       count(o) as opinion_count
                ORDER BY opinion_count DESC
            """)
            
            return [
                {
                    'court_id': record['court_id'],
                    'court_name': record['court_name'],
                    'opinion_count': record['opinion_count']
                }
                for record in result
            ]
    
    def get_temporal_distribution(self):
        """Get distribution of opinions by year."""
        if not self.graph_sync.driver:
            self.graph_sync.connect()
        
        with self.graph_sync.driver.session() as session:
            result = session.run("""
                MATCH (o:Opinion)
                WHERE o.year IS NOT NULL
                RETURN o.year as year, count(o) as opinion_count
                ORDER BY year DESC
                LIMIT 20
            """)
            
            return [
                {
                    'year': record['year'],
                    'opinion_count': record['opinion_count']
                }
                for record in result
            ]
    
    def get_practice_area_distribution(self):
        """Get distribution of opinions by practice area."""
        if not self.graph_sync.driver:
            self.graph_sync.connect()
        
        with self.graph_sync.driver.session() as session:
            result = session.run("""
                MATCH (o:Opinion)-[:HAS_PA]->(pa:PA)
                RETURN pa.pa_id as pa_id, pa.name as pa_name, 
                       count(o) as opinion_count
                ORDER BY opinion_count DESC
            """)
            
            return [
                {
                    'pa_id': record['pa_id'],
                    'pa_name': record['pa_name'],
                    'opinion_count': record['opinion_count']
                }
                for record in result
            ]
    
    def get_citation_network_stats(self):
        """Get citation network statistics."""
        if not self.graph_sync.driver:
            self.graph_sync.connect()
        
        with self.graph_sync.driver.session() as session:
            # Total citations
            total_result = session.run("MATCH ()-[r:CITES]->() RETURN count(r) as total")
            total_citations = total_result.single()['total']
            
            # Most cited opinions
            cited_result = session.run("""
                MATCH (cited:Opinion)<-[:CITES]-(citing:Opinion)
                RETURN cited.opinion_id as opinion_id, cited.case_name as case_name,
                       count(citing) as citation_count
                ORDER BY citation_count DESC
                LIMIT 10
            """)
            
            most_cited = [
                {
                    'opinion_id': record['opinion_id'],
                    'case_name': record['case_name'],
                    'citation_count': record['citation_count']
                }
                for record in cited_result
            ]
            
            # Opinions that cite the most
            citing_result = session.run("""
                MATCH (citing:Opinion)-[:CITES]->(cited:Opinion)
                RETURN citing.opinion_id as opinion_id, citing.case_name as case_name,
                       count(cited) as citations_made
                ORDER BY citations_made DESC
                LIMIT 10
            """)
            
            most_citing = [
                {
                    'opinion_id': record['opinion_id'],
                    'case_name': record['case_name'],
                    'citations_made': record['citations_made']
                }
                for record in citing_result
            ]
            
            return {
                'total_citations': total_citations,
                'most_cited_opinions': most_cited,
                'most_citing_opinions': most_citing
            }
    
    def generate_delta_report(self, baseline_stats=None):
        """Generate comprehensive delta report."""
        logger.info("Generating Neo4j delta report...")
        
        # Current statistics
        current_stats = self.get_current_stats()
        
        # Validation checks
        constraint_violations = self.validate_constraints()
        
        # Distribution analysis
        court_distribution = self.get_court_distribution()
        temporal_distribution = self.get_temporal_distribution()
        practice_area_distribution = self.get_practice_area_distribution()
        citation_stats = self.get_citation_network_stats()
        
        # Calculate deltas if baseline provided
        deltas = {}
        if baseline_stats:
            deltas = {
                'nodes': {
                    'courts': current_stats['nodes']['courts'] - baseline_stats['nodes']['courts'],
                    'practice_areas': current_stats['nodes']['practice_areas'] - baseline_stats['nodes']['practice_areas'],
                    'opinions': current_stats['nodes']['opinions'] - baseline_stats['nodes']['opinions'],
                    'total': current_stats['nodes']['total'] - baseline_stats['nodes']['total']
                },
                'relationships': {
                    'hears_before': current_stats['relationships']['hears_before'] - baseline_stats['relationships']['hears_before'],
                    'has_pa': current_stats['relationships']['has_pa'] - baseline_stats['relationships']['has_pa'],
                    'cites': current_stats['relationships']['cites'] - baseline_stats['relationships']['cites'],
                    'total': current_stats['relationships']['total'] - baseline_stats['relationships']['total']
                }
            }
        
        # Compile report
        report = {
            'timestamp': self.timestamp,
            'validation_type': 'nightly_sync_delta',
            'current_stats': current_stats,
            'baseline_stats': baseline_stats,
            'deltas': deltas,
            'constraint_violations': constraint_violations,
            'distributions': {
                'courts': court_distribution,
                'temporal': temporal_distribution,
                'practice_areas': practice_area_distribution
            },
            'citation_network': citation_stats,
            'health_status': {
                'constraint_violations_count': len(constraint_violations),
                'orphaned_opinions': any(v['type'] == 'orphaned_opinions' for v in constraint_violations),
                'duplicate_ids': any(v['type'] == 'duplicate_opinion_id' for v in constraint_violations),
                'overall_health': 'healthy' if len(constraint_violations) == 0 else 'issues_found'
            }
        }
        
        # Save report
        report_file = f"reports/neo4j_delta_{self.timestamp}.json"
        os.makedirs('reports', exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Delta report saved: {report_file}")
        
        # Print summary
        self._print_summary(report)
        
        return report
    
    def _print_summary(self, report):
        """Print delta report summary."""
        print(f"\n{'='*60}")
        print(f"NEO4J DELTA VALIDATION REPORT")
        print(f"{'='*60}")
        print(f"Timestamp: {report['timestamp']}")
        
        # Current stats
        stats = report['current_stats']
        print(f"\nCurrent Graph State:")
        print(f"  Nodes: {stats['nodes']['total']:,} ({stats['nodes']['courts']} courts, {stats['nodes']['practice_areas']} PAs, {stats['nodes']['opinions']:,} opinions)")
        print(f"  Relationships: {stats['relationships']['total']:,}")
        
        # Deltas
        if report['deltas']:
            deltas = report['deltas']
            print(f"\nChanges Since Baseline:")
            print(f"  Nodes: {deltas['nodes']['total']:+,} ({deltas['nodes']['opinions']:+,} opinions)")
            print(f"  Relationships: {deltas['relationships']['total']:+,}")
        
        # Health status
        health = report['health_status']
        status_icon = "✅" if health['overall_health'] == 'healthy' else "⚠️"
        print(f"\nHealth Status: {status_icon} {health['overall_health'].upper()}")
        
        if health['constraint_violations_count'] > 0:
            print(f"  Constraint violations: {health['constraint_violations_count']}")
            for violation in report['constraint_violations']:
                print(f"    - {violation['type']}: {violation.get('count', 'N/A')}")
        
        # Top courts
        print(f"\nTop Courts by Opinion Count:")
        for court in report['distributions']['courts'][:5]:
            print(f"  {court['court_id']}: {court['opinion_count']:,} opinions")

def main():
    """Main execution function."""
    validator = Neo4jDeltaValidator()
    
    # Load baseline if available
    baseline_file = "reports/neo4j_baseline_stats.json"
    baseline_stats = None
    
    if os.path.exists(baseline_file):
        with open(baseline_file, 'r') as f:
            baseline_data = json.load(f)
            baseline_stats = baseline_data.get('current_stats')
    
    # Generate delta report
    report = validator.generate_delta_report(baseline_stats)
    
    # Save current stats as new baseline
    with open(baseline_file, 'w') as f:
        json.dump({
            'timestamp': report['timestamp'],
            'current_stats': report['current_stats']
        }, f, indent=2)
    
    return report

if __name__ == "__main__":
    main()
