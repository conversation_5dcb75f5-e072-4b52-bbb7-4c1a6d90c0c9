#!/usr/bin/env python3
"""
Neo4j Status Checker

Checks the status of the Neo4j deployment without requiring authentication.
"""

import socket
import subprocess
import time
import requests
from datetime import datetime

def test_port_connectivity(host, port, timeout=5):
    """Test if a port is open."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        return False

def check_secret_manager():
    """Check if the Neo4j password secret exists."""
    try:
        result = subprocess.run([
            'gcloud', 'secrets', 'list', '--filter=name:neo4j-prod-pass', '--format=value(name)'
        ], capture_output=True, text=True, check=True)
        return bool(result.stdout.strip())
    except:
        return False

def check_http_endpoint(host, port):
    """Check if Neo4j HTTP endpoint is responding."""
    try:
        response = requests.get(f'http://{host}:{port}/', timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Check Neo4j deployment status."""
    
    print("🔍 NEO4J DEPLOYMENT STATUS CHECK")
    print("=" * 40)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Neo4j server details
    neo4j_host = "**********"
    
    print(f"🖥️  VM Status:")
    print(f"   Host: {neo4j_host}")
    print(f"   Expected: RUNNING (from Terraform)")
    print()
    
    print("🔌 Port Connectivity:")
    http_open = test_port_connectivity(neo4j_host, 7474)
    bolt_open = test_port_connectivity(neo4j_host, 7687)
    
    print(f"   Port 7474 (HTTP): {'✅ Open' if http_open else '❌ Closed'}")
    print(f"   Port 7687 (Bolt): {'✅ Open' if bolt_open else '❌ Closed'}")
    print()
    
    print("🌐 HTTP Endpoint:")
    if http_open:
        http_responding = check_http_endpoint(neo4j_host, 7474)
        print(f"   Neo4j Browser: {'✅ Responding' if http_responding else '❌ Not responding'}")
    else:
        print("   Neo4j Browser: ❌ Port not open")
    print()
    
    print("🔐 Secret Manager:")
    secret_exists = check_secret_manager()
    print(f"   Password secret: {'✅ Created' if secret_exists else '❌ Not created yet'}")
    print()
    
    # Overall status assessment
    print("📊 DEPLOYMENT STATUS:")
    
    if http_open and bolt_open and secret_exists:
        print("   🎉 ✅ FULLY READY - Neo4j is operational!")
        print("   🚀 Ready for data migration and testing")
        status = "READY"
    elif http_open and bolt_open:
        print("   ⏳ 🔄 ALMOST READY - Neo4j running, startup script finishing")
        print("   💡 Secret creation in progress (1-2 minutes)")
        status = "STARTING"
    elif http_open or bolt_open:
        print("   ⏳ 🔄 STARTING UP - Neo4j installation in progress")
        print("   💡 Ports opening, configuration applying (2-5 minutes)")
        status = "INSTALLING"
    else:
        print("   ⏳ 🔄 INITIALIZING - VM running, Neo4j installing")
        print("   💡 Full startup expected in 5-10 minutes")
        status = "INITIALIZING"
    
    print()
    print("🎯 NEXT STEPS:")
    
    if status == "READY":
        print("   1. ✅ Test Neo4j connection with authentication")
        print("   2. ✅ Begin data migration from AuraDB")
        print("   3. ✅ Update application configuration")
    elif status == "STARTING":
        print("   1. ⏳ Wait 1-2 minutes for secret creation")
        print("   2. 🔄 Re-run connection test")
        print("   3. 🚀 Begin data migration once ready")
    else:
        print("   1. ⏳ Wait for Neo4j installation to complete")
        print("   2. 🔄 Check status again in 2-3 minutes")
        print("   3. 📋 Monitor startup progress")
    
    print()
    print("💰 COST SAVINGS REMINDER:")
    print("   AuraDB: $2,080/month → Self-hosted: $150/month")
    print("   Annual savings: $23,160 (93% reduction!)")
    
    return status

if __name__ == "__main__":
    status = main()
    print(f"\nStatus: {status}")
