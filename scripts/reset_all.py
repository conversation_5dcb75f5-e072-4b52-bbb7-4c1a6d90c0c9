#!/usr/bin/env python3
"""
Complete Environment Reset Orchestrator for Texas Laws Personal Injury
WARNING: This will delete ALL data across all systems. Ensure backups are taken before execution.
"""

import os
import sys
import subprocess
import logging
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'reset_all_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class EnvironmentReset:
    """Orchestrate complete environment reset across all systems."""
    
    def __init__(self, confirm_reset=False):
        """Initialize reset orchestrator."""
        self.confirm_reset = confirm_reset
        self.scripts_dir = Path(__file__).parent
        self.project_root = self.scripts_dir.parent
        
        # System configurations
        self.systems = {
            'supabase': {
                'name': 'Supabase Database',
                'script': 'reset_supabase.py',
                'method': 'python'
            },
            'gcs': {
                'name': 'Google Cloud Storage',
                'script': 'reset_gcs.sh',
                'method': 'shell'
            },
            'neo4j': {
                'name': 'Neo4j Graph Database',
                'script': 'reset_neo4j.cypher',
                'method': 'cypher'
            },
            'pinecone': {
                'name': 'Pinecone Vector Database',
                'script': 'reset_pinecone.py',
                'method': 'python'
            }
        }
        
        logger.info("Environment reset orchestrator initialized")
    
    def confirm_reset_action(self):
        """Get user confirmation for destructive reset operation."""
        if self.confirm_reset:
            return True
        
        print("\n" + "="*60)
        print("🚨 DESTRUCTIVE OPERATION WARNING 🚨")
        print("="*60)
        print("This will permanently delete ALL data from:")
        print("  • Supabase: All case records and metadata")
        print("  • Google Cloud Storage: All case documents")
        print("  • Neo4j: All case nodes and relationships")
        print("  • Pinecone: All vector embeddings")
        print("\nBackups will be created where possible, but this action cannot be undone.")
        print("="*60)
        
        response = input("\nType 'RESET' to confirm complete environment reset: ")
        
        if response.strip() == 'RESET':
            logger.info("User confirmed environment reset")
            return True
        else:
            logger.info("Environment reset cancelled by user")
            return False
    
    def check_prerequisites(self):
        """Check that all required tools and credentials are available."""
        logger.info("Checking prerequisites...")
        
        prerequisites = {
            'psql': 'PostgreSQL client for Supabase',
            'gsutil': 'Google Cloud SDK for GCS',
            'cypher-shell': 'Neo4j client for graph database',
            'python': 'Python for Pinecone operations'
        }
        
        missing = []
        
        for tool, description in prerequisites.items():
            if tool == 'cypher-shell':
                # Check if Neo4j is accessible (cypher-shell might not be in PATH)
                neo4j_uri = os.getenv('NEO4J_URI')
                if not neo4j_uri:
                    missing.append(f"{tool} ({description}) - NEO4J_URI not set")
            else:
                try:
                    subprocess.run([tool, '--version'], 
                                 capture_output=True, check=True, timeout=10)
                    logger.info(f"✅ {tool} available")
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    missing.append(f"{tool} ({description})")
        
        # Check environment variables
        required_env_vars = [
            'SUPABASE_URL',
            'SUPABASE_KEY',
            'GOOGLE_CLOUD_PROJECT',
            'NEO4J_URI',
            'NEO4J_USERNAME',
            'NEO4J_PASSWORD',
            'PINECONE_API_KEY'
        ]
        
        for var in required_env_vars:
            if not os.getenv(var):
                missing.append(f"Environment variable: {var}")
        
        if missing:
            logger.error("Missing prerequisites:")
            for item in missing:
                logger.error(f"  ❌ {item}")
            return False
        
        logger.info("✅ All prerequisites satisfied")
        return True
    
    def reset_supabase(self):
        """Reset Supabase database using Python script with MCP tool."""
        logger.info("Resetting Supabase database...")

        try:
            script_file = self.scripts_dir / 'reset_supabase.py'

            # Execute Python script with confirmation flag
            result = subprocess.run([sys.executable, str(script_file), '--confirm'],
                                  capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                logger.info("✅ Supabase reset completed successfully")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"Supabase reset failed: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Supabase reset failed: {e}")
            return False
    
    def reset_gcs(self):
        """Reset Google Cloud Storage."""
        logger.info("Resetting Google Cloud Storage...")
        
        try:
            script_file = self.scripts_dir / 'reset_gcs.sh'
            
            # Make script executable
            os.chmod(script_file, 0o755)
            
            # Execute shell script
            result = subprocess.run([str(script_file)], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ GCS reset completed successfully")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"GCS reset failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"GCS reset failed: {e}")
            return False
    
    def reset_neo4j(self):
        """Reset Neo4j graph database."""
        logger.info("Resetting Neo4j graph database...")
        
        try:
            cypher_file = self.scripts_dir / 'reset_neo4j.cypher'
            
            neo4j_uri = os.getenv('NEO4J_URI')
            neo4j_user = os.getenv('NEO4J_USERNAME')
            neo4j_password = os.getenv('NEO4J_PASSWORD')
            
            # Use cypher-shell if available
            cmd = [
                'cypher-shell',
                '-a', neo4j_uri,
                '-u', neo4j_user,
                '-p', neo4j_password,
                '-f', str(cypher_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ Neo4j reset completed successfully")
                return True
            else:
                logger.error(f"Neo4j reset failed: {result.stderr}")
                # Fallback: log instructions for manual execution
                logger.warning(f"Manual execution required: cypher-shell -f {cypher_file}")
                return False
                
        except Exception as e:
            logger.error(f"Neo4j reset failed: {e}")
            return False
    
    def reset_pinecone(self):
        """Reset Pinecone vector database."""
        logger.info("Resetting Pinecone vector database...")
        
        try:
            script_file = self.scripts_dir / 'reset_pinecone.py'
            
            # Execute Python script
            result = subprocess.run([sys.executable, str(script_file)], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ Pinecone reset completed successfully")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"Pinecone reset failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Pinecone reset failed: {e}")
            return False
    
    def execute_reset(self):
        """Execute complete environment reset."""
        logger.info("Starting complete environment reset...")
        
        # Confirmation check
        if not self.confirm_reset_action():
            logger.info("Environment reset cancelled")
            return False
        
        # Prerequisites check
        if not self.check_prerequisites():
            logger.error("Prerequisites not met - aborting reset")
            return False
        
        # Execute resets in order
        reset_methods = {
            'pinecone': self.reset_pinecone,
            'neo4j': self.reset_neo4j,
            'gcs': self.reset_gcs,
            'supabase': self.reset_supabase
        }
        
        results = {}
        
        for system, method in reset_methods.items():
            logger.info(f"Resetting {self.systems[system]['name']}...")
            
            try:
                success = method()
                results[system] = success
                
                if success:
                    logger.info(f"✅ {self.systems[system]['name']} reset successful")
                else:
                    logger.error(f"❌ {self.systems[system]['name']} reset failed")
                
                # Brief pause between systems
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ {self.systems[system]['name']} reset failed: {e}")
                results[system] = False
        
        # Summary
        successful = sum(results.values())
        total = len(results)
        
        logger.info(f"\n{'='*60}")
        logger.info(f"ENVIRONMENT RESET SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Successful: {successful}/{total} systems")
        
        for system, success in results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"  {self.systems[system]['name']}: {status}")
        
        if successful == total:
            logger.info("🎉 Complete environment reset successful!")
            logger.info("All systems are now clean and ready for fresh data ingestion.")
            return True
        else:
            logger.warning(f"⚠️  Partial reset completed ({successful}/{total} systems)")
            logger.warning("Manual intervention may be required for failed systems.")
            return False

def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Reset all Texas Laws Personal Injury systems')
    parser.add_argument('--confirm', action='store_true', 
                        help='Skip confirmation prompt (use with caution)')
    
    args = parser.parse_args()
    
    reset_orchestrator = EnvironmentReset(confirm_reset=args.confirm)
    success = reset_orchestrator.execute_reset()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
