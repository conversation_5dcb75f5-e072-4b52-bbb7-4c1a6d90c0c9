#!/usr/bin/env python3
"""
CourtListener Texas Opinions Refresh Script
Retrieves all Texas judicial opinions from CourtListener API and maps them to precise court IDs.

Usage:
    python scripts/refresh_cl_opinions.py [--output_dir OUTPUT_DIR]

Options:
    --output_dir OUTPUT_DIR    Directory to save output files (default: data/courtlistener)
"""

import os
import sys
import argparse
import requests
import json
import time
import logging
from pathlib import Path
from collections import defaultdict
from datetime import datetime
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cl_refresh.log')
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Court ID mapping table
COURT_ID_MAPPING = {
    # State courts
    'tex': 'TXSC',                    # Texas Supreme Court
    'texcrimapp': 'TCCA',             # Texas Court of Criminal Appeals
    
    # Texas Courts of Appeals
    'texapp1st': 'TXCOA01',           # Houston (1st)
    'texapp2nd': 'TXCOA02',           # Fort Worth (2nd)
    'texapp3rd': 'TXCOA03',           # Austin (3rd)
    'texapp4th': 'TXCOA04',           # San Antonio (4th)
    'texapp5th': 'TXCOA05',           # Dallas (5th)
    'texapp6th': 'TXCOA06',           # Texarkana (6th)
    'texapp7th': 'TXCOA07',           # Amarillo (7th)
    'texapp8th': 'TXCOA08',           # El Paso (8th)
    'texapp9th': 'TXCOA09',           # Beaumont (9th)
    'texapp10th': 'TXCOA10',          # Waco (10th)
    'texapp11th': 'TXCOA11',          # Eastland (11th)
    'texapp12th': 'TXCOA12',          # Tyler (12th)
    'texapp13th': 'TXCOA13',          # Corpus Christi (13th)
    'texapp14th': 'TXCOA14',          # Houston (14th)
    
    # Federal courts
    'txnd': 'TXND',                   # Northern District of Texas
    'txsd': 'TXSD',                   # Southern District of Texas
    'txed': 'TXED',                   # Eastern District of Texas
    'txwd': 'TXWD',                   # Western District of Texas
    'ca5': 'CA5'                      # Fifth Circuit Court of Appeals
}

def refresh_courtlistener_opinions(output_dir):
    """
    Refresh all Texas judicial opinions from CourtListener.
    
    Args:
        output_dir: Directory to save output files
    
    Returns:
        dict: Results summary
    """
    api_key = os.getenv('COURTLISTENER_API_KEY')
    if not api_key:
        logger.error("COURTLISTENER_API_KEY not found in environment")
        sys.exit(1)
    
    headers = {
        'Authorization': f'Token {api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    all_opinions = []
    court_totals = {}
    court_year_counts = defaultdict(lambda: defaultdict(int))
    
    # Process each court
    for court_code, mapped_id in COURT_ID_MAPPING.items():
        logger.info(f"Crawling {court_code} → {mapped_id}...")
        court_total = 0
        page = 1
        consecutive_empty = 0
        
        while consecutive_empty < 3:  # Stop after 3 consecutive empty pages
            try:
                # Use search API with opinion filter
                url = 'https://www.courtlistener.com/api/rest/v4/search/'
                params = {
                    'q': f'court_id:{court_code}',
                    'type': 'o',  # opinions only
                    'page_size': 100,
                    'page': page,
                    'format': 'json'
                }
                
                response = requests.get(url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    opinions = data.get('results', [])
                    total_available = data.get('count', 0)
                    
                    if not opinions:
                        consecutive_empty += 1
                        logger.info(f"  Page {page}: Empty (consecutive: {consecutive_empty})")
                        page += 1
                        continue
                    else:
                        consecutive_empty = 0
                    
                    for opinion in opinions:
                        # Extract year from dateFiled
                        date_filed = opinion.get('dateFiled', '')
                        year_filed = None
                        if date_filed:
                            try:
                                year_filed = int(date_filed[:4])
                            except (ValueError, TypeError):
                                pass
                        
                        opinion_record = {
                            'id': opinion.get('id'),
                            'court_id': mapped_id,
                            'year_filed': year_filed,
                            'date_filed': date_filed,
                            'case_name': opinion.get('caseName', ''),
                            'original_court': court_code,
                            'document_type': 'opinion'  # All are opinions due to type=o filter
                        }
                        
                        all_opinions.append(opinion_record)
                        court_year_counts[mapped_id][year_filed] += 1
                    
                    court_total += len(opinions)
                    logger.info(f"  Page {page}: {len(opinions)} opinions (total: {court_total}/{total_available})")
                    page += 1
                    
                    # Rate limiting
                    time.sleep(0.2)
                    
                elif response.status_code == 404:
                    logger.warning(f"  Court {court_code} not found (404)")
                    break
                elif response.status_code == 429:
                    logger.warning(f"  Rate limited, waiting 60s...")
                    time.sleep(60)
                    continue
                else:
                    logger.error(f"  Error {response.status_code}: {response.text[:100]}")
                    consecutive_empty += 1
                    page += 1
                    
            except Exception as e:
                logger.error(f"  Error: {e}")
                consecutive_empty += 1
                page += 1
                time.sleep(1)
        
        court_totals[mapped_id] = court_total
        logger.info(f"  {court_code} → {mapped_id} FINAL: {court_total} opinions")
    
    # Generate report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save all opinions
    opinions_file = output_path / f"cl_opinions_{timestamp}.json"
    with open(opinions_file, 'w') as f:
        json.dump(all_opinions, f, indent=2)
    
    # Generate court×year matrix
    court_year_matrix = []
    for court_id, years in court_year_counts.items():
        for year, count in years.items():
            court_year_matrix.append({
                'court_id': court_id,
                'year_filed': year,
                'case_count': count
            })
    
    # Sort by court and year
    court_year_matrix.sort(key=lambda x: (x['court_id'], x['year_filed'] or 0))
    
    # Save court×year counts
    counts_file = output_path / f"cl_court_year_counts_{timestamp}.json"
    with open(counts_file, 'w') as f:
        json.dump(court_year_matrix, f, indent=2)
    
    # Save as CSV
    csv_file = output_path / f"cl_court_year_counts_{timestamp}.csv"
    with open(csv_file, 'w') as f:
        f.write('court_id,year_filed,case_count\n')
        for entry in court_year_matrix:
            year = entry['year_filed'] if entry['year_filed'] is not None else 'NULL'
            f.write(f"{entry['court_id']},{year},{entry['case_count']}\n")
    
    # Calculate missing data
    missing_year = sum(1 for op in all_opinions if op['year_filed'] is None)
    
    # Generate summary
    summary = {
        'timestamp': timestamp,
        'total_opinions': len(all_opinions),
        'court_totals': court_totals,
        'missing_year_filed': missing_year,
        'files_generated': [
            str(opinions_file),
            str(counts_file),
            str(csv_file)
        ]
    }
    
    # Save summary
    summary_file = output_path / f"cl_refresh_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    logger.info(f"Refresh complete! {len(all_opinions)} opinions retrieved")
    logger.info(f"Files saved to {output_path}")
    
    return summary

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Refresh CourtListener Texas opinions')
    parser.add_argument('--output_dir', default='data/courtlistener', 
                        help='Directory to save output files')
    
    args = parser.parse_args()
    
    logger.info("Starting CourtListener Texas opinions refresh")
    start_time = time.time()
    
    summary = refresh_courtlistener_opinions(args.output_dir)
    
    elapsed_time = time.time() - start_time
    logger.info(f"Refresh completed in {elapsed_time:.2f} seconds")
    logger.info(f"Retrieved {summary['total_opinions']} opinions")
    
    # Print court totals
    logger.info("Court totals:")
    for court_id, total in sorted(summary['court_totals'].items()):
        logger.info(f"  {court_id}: {total}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
