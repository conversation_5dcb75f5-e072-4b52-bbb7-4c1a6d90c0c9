// Reset Neo4j Database for Texas Laws Personal Injury
// WARNING: This will delete ALL case nodes and relationships. Ensure backups are taken before execution.

// Get current database statistics
CALL db.stats.retrieve('GRAPH COUNTS') YIELD data
RETURN 'Before Reset - Nodes: ' + toString(data.nodes) + ', Relationships: ' + toString(data.relationships) AS stats;

// Create backup export (optional safety measure)
// Note: This requires APOC plugin to be installed
CALL apoc.export.cypher.all("backup_pre_reset_" + toString(datetime().epochSeconds) + ".cypher", {
    format: "cypher-shell",
    useOptimizations: {type: "UNWIND_BATCH", unwindBatchSize: 20}
})
YIELD file, batches, source, format, nodes, relationships, properties, time, rows, batchSize, batches, done, data
RETURN file, nodes, relationships, time;

// Delete all case-related relationships first (to avoid constraint violations)
MATCH ()-[r:CITES]->()
DELETE r;

MATCH ()-[r:SIMILAR_TO]->()
DELETE r;

MATCH ()-[r:DECIDED_BY]->()
DELETE r;

MATCH ()-[r:APPEALS_FROM]->()
DELETE r;

MATCH ()-[r:RELATED_TO]->()
DELETE r;

MATCH ()-[r:REFERENCES]->()
DELETE r;

// Delete all case nodes
MATCH (c:Case)
DELETE c;

// Delete court nodes (will be recreated)
MATCH (court:Court)
DELETE court;

// Delete practice area nodes (will be recreated)
MATCH (pa:PracticeArea)
DELETE pa;

// Delete citation nodes
MATCH (cite:Citation)
DELETE cite;

// Delete any remaining orphaned nodes
MATCH (n)
WHERE NOT EXISTS((n)--())
DELETE n;

// Recreate essential court nodes
CREATE (:Court {id: 'TXSC', name: 'Texas Supreme Court', type: 'state_supreme', jurisdiction: 'Texas'});
CREATE (:Court {id: 'TCCA', name: 'Texas Court of Criminal Appeals', type: 'state_supreme', jurisdiction: 'Texas'});

// Texas Courts of Appeals
CREATE (:Court {id: 'TXCOA01', name: 'Texas Court of Appeals, First District', type: 'state_appellate', jurisdiction: 'Houston'});
CREATE (:Court {id: 'TXCOA02', name: 'Texas Court of Appeals, Second District', type: 'state_appellate', jurisdiction: 'Fort Worth'});
CREATE (:Court {id: 'TXCOA03', name: 'Texas Court of Appeals, Third District', type: 'state_appellate', jurisdiction: 'Austin'});
CREATE (:Court {id: 'TXCOA04', name: 'Texas Court of Appeals, Fourth District', type: 'state_appellate', jurisdiction: 'San Antonio'});
CREATE (:Court {id: 'TXCOA05', name: 'Texas Court of Appeals, Fifth District', type: 'state_appellate', jurisdiction: 'Dallas'});
CREATE (:Court {id: 'TXCOA06', name: 'Texas Court of Appeals, Sixth District', type: 'state_appellate', jurisdiction: 'Texarkana'});
CREATE (:Court {id: 'TXCOA07', name: 'Texas Court of Appeals, Seventh District', type: 'state_appellate', jurisdiction: 'Amarillo'});
CREATE (:Court {id: 'TXCOA08', name: 'Texas Court of Appeals, Eighth District', type: 'state_appellate', jurisdiction: 'El Paso'});
CREATE (:Court {id: 'TXCOA09', name: 'Texas Court of Appeals, Ninth District', type: 'state_appellate', jurisdiction: 'Beaumont'});
CREATE (:Court {id: 'TXCOA10', name: 'Texas Court of Appeals, Tenth District', type: 'state_appellate', jurisdiction: 'Waco'});
CREATE (:Court {id: 'TXCOA11', name: 'Texas Court of Appeals, Eleventh District', type: 'state_appellate', jurisdiction: 'Eastland'});
CREATE (:Court {id: 'TXCOA12', name: 'Texas Court of Appeals, Twelfth District', type: 'state_appellate', jurisdiction: 'Tyler'});
CREATE (:Court {id: 'TXCOA13', name: 'Texas Court of Appeals, Thirteenth District', type: 'state_appellate', jurisdiction: 'Corpus Christi'});
CREATE (:Court {id: 'TXCOA14', name: 'Texas Court of Appeals, Fourteenth District', type: 'state_appellate', jurisdiction: 'Houston'});

// Federal Courts
CREATE (:Court {id: 'TXND', name: 'U.S. District Court for the Northern District of Texas', type: 'federal_district', jurisdiction: 'Northern Texas'});
CREATE (:Court {id: 'TXSD', name: 'U.S. District Court for the Southern District of Texas', type: 'federal_district', jurisdiction: 'Southern Texas'});
CREATE (:Court {id: 'TXED', name: 'U.S. District Court for the Eastern District of Texas', type: 'federal_district', jurisdiction: 'Eastern Texas'});
CREATE (:Court {id: 'TXWD', name: 'U.S. District Court for the Western District of Texas', type: 'federal_district', jurisdiction: 'Western Texas'});
CREATE (:Court {id: 'CA5', name: 'U.S. Court of Appeals for the Fifth Circuit', type: 'federal_appellate', jurisdiction: 'Fifth Circuit'});

// Recreate practice area nodes
CREATE (:PracticeArea {id: 'personal_injury', name: 'Personal Injury', description: 'Cases involving bodily harm, negligence, and tort claims'});
CREATE (:PracticeArea {id: 'criminal_defense', name: 'Criminal Defense', description: 'Criminal law cases and defense matters'});
CREATE (:PracticeArea {id: 'family_law', name: 'Family Law', description: 'Divorce, custody, adoption, and family matters'});
CREATE (:PracticeArea {id: 'estate_planning', name: 'Estate Planning & Probate', description: 'Wills, trusts, probate, and estate administration'});
CREATE (:PracticeArea {id: 'immigration_law', name: 'Immigration Law', description: 'Immigration, naturalization, and deportation cases'});
CREATE (:PracticeArea {id: 'real_estate', name: 'Real Estate', description: 'Property law, transactions, and landlord-tenant matters'});
CREATE (:PracticeArea {id: 'bankruptcy', name: 'Bankruptcy', description: 'Bankruptcy proceedings and debt relief matters'});

// Create indexes for performance
CREATE INDEX case_id_index IF NOT EXISTS FOR (c:Case) ON (c.id);
CREATE INDEX case_court_index IF NOT EXISTS FOR (c:Case) ON (c.court_id);
CREATE INDEX case_year_index IF NOT EXISTS FOR (c:Case) ON (c.year_filed);
CREATE INDEX case_practice_area_index IF NOT EXISTS FOR (c:Case) ON (c.practice_area);
CREATE INDEX court_id_index IF NOT EXISTS FOR (court:Court) ON (court.id);
CREATE INDEX practice_area_id_index IF NOT EXISTS FOR (pa:PracticeArea) ON (pa.id);

// Create constraints
CREATE CONSTRAINT case_id_unique IF NOT EXISTS FOR (c:Case) REQUIRE c.id IS UNIQUE;
CREATE CONSTRAINT court_id_unique IF NOT EXISTS FOR (court:Court) REQUIRE court.id IS UNIQUE;
CREATE CONSTRAINT practice_area_id_unique IF NOT EXISTS FOR (pa:PracticeArea) REQUIRE pa.id IS UNIQUE;

// Get final database statistics
CALL db.stats.retrieve('GRAPH COUNTS') YIELD data
RETURN 'After Reset - Nodes: ' + toString(data.nodes) + ', Relationships: ' + toString(data.relationships) AS stats;

// Verify court and practice area nodes were created
MATCH (court:Court)
RETURN 'Courts created: ' + toString(count(court)) AS court_count;

MATCH (pa:PracticeArea)
RETURN 'Practice areas created: ' + toString(count(pa)) AS practice_area_count;

// Log completion
RETURN 'Neo4j reset completed at ' + toString(datetime()) AS completion_time;
