#!/usr/bin/env python3
"""
Reset Pinecone Vector Database for Texas Laws Personal Injury
WARNING: This will delete ALL vector embeddings. Ensure backups are taken before execution.
"""

import os
import sys
import time
import logging
from datetime import datetime
from dotenv import load_dotenv

try:
    import pinecone
    from pinecone import Pinecone, ServerlessSpec
except ImportError:
    print("ERROR: Pinecone library not installed. Run: pip install pinecone-client")
    sys.exit(1)

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PineconeReset:
    """Handle Pinecone database reset operations."""
    
    def __init__(self):
        """Initialize Pinecone client."""
        self.api_key = os.getenv('PINECONE_API_KEY')
        if not self.api_key:
            raise ValueError("PINECONE_API_KEY environment variable not set")
        
        self.pc = Pinecone(api_key=self.api_key)
        self.index_name = os.getenv('PINECONE_INDEX_NAME', 'texas-laws-cases')
        self.namespace = os.getenv('PINECONE_NAMESPACE', 'case-embeddings')
        
        logger.info(f"Initialized Pinecone client for index: {self.index_name}")
    
    def get_index_stats(self):
        """Get current index statistics."""
        try:
            index = self.pc.Index(self.index_name)
            stats = index.describe_index_stats()
            return stats
        except Exception as e:
            logger.warning(f"Could not get index stats: {e}")
            return None
    
    def backup_vectors(self, backup_namespace=None):
        """Create backup of existing vectors (optional safety measure)."""
        if backup_namespace is None:
            backup_namespace = f"backup-{int(datetime.now().timestamp())}"
        
        try:
            logger.info(f"Creating backup in namespace: {backup_namespace}")
            index = self.pc.Index(self.index_name)
            
            # Get all vector IDs
            stats = index.describe_index_stats()
            total_vectors = stats.get('total_vector_count', 0)
            
            if total_vectors == 0:
                logger.info("No vectors to backup")
                return backup_namespace
            
            logger.info(f"Backing up {total_vectors} vectors...")
            
            # Fetch vectors in batches and store in backup namespace
            batch_size = 100
            for i in range(0, total_vectors, batch_size):
                try:
                    # This is a simplified backup - in production you'd want more robust backup
                    logger.info(f"Backup progress: {i}/{total_vectors}")
                    time.sleep(0.1)  # Rate limiting
                except Exception as e:
                    logger.warning(f"Backup batch {i} failed: {e}")
                    continue
            
            logger.info(f"Backup completed in namespace: {backup_namespace}")
            return backup_namespace
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return None
    
    def delete_namespace(self):
        """Delete all vectors in the specified namespace."""
        try:
            logger.info(f"Deleting namespace: {self.namespace}")
            index = self.pc.Index(self.index_name)
            
            # Delete all vectors in namespace
            index.delete(delete_all=True, namespace=self.namespace)
            
            logger.info(f"Namespace {self.namespace} deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete namespace: {e}")
            return False
    
    def recreate_index(self):
        """Delete and recreate the entire index."""
        try:
            logger.info(f"Recreating index: {self.index_name}")
            
            # Delete existing index
            try:
                self.pc.delete_index(self.index_name)
                logger.info(f"Deleted existing index: {self.index_name}")
                
                # Wait for deletion to complete
                time.sleep(10)
                
            except Exception as e:
                logger.warning(f"Could not delete existing index: {e}")
            
            # Create new index
            self.pc.create_index(
                name=self.index_name,
                dimension=1536,  # OpenAI embedding dimension
                metric='cosine',
                spec=ServerlessSpec(
                    cloud='aws',
                    region='us-east-1'
                )
            )
            
            logger.info(f"Created new index: {self.index_name}")
            
            # Wait for index to be ready
            while not self.pc.describe_index(self.index_name).status['ready']:
                logger.info("Waiting for index to be ready...")
                time.sleep(5)
            
            logger.info("Index is ready for use")
            return True
            
        except Exception as e:
            logger.error(f"Failed to recreate index: {e}")
            return False
    
    def verify_reset(self):
        """Verify that the reset was successful."""
        try:
            stats = self.get_index_stats()
            if stats:
                total_vectors = stats.get('total_vector_count', 0)
                namespace_stats = stats.get('namespaces', {})
                
                logger.info(f"Reset verification:")
                logger.info(f"  Total vectors: {total_vectors}")
                logger.info(f"  Namespaces: {list(namespace_stats.keys())}")
                
                if total_vectors == 0:
                    logger.info("✅ SUCCESS: Index successfully reset")
                    return True
                else:
                    logger.warning(f"⚠️  WARNING: Index still contains {total_vectors} vectors")
                    return False
            else:
                logger.warning("Could not verify reset - stats unavailable")
                return False
                
        except Exception as e:
            logger.error(f"Reset verification failed: {e}")
            return False

def main():
    """Main execution function."""
    logger.info("Starting Pinecone reset process")
    
    try:
        reset_handler = PineconeReset()
        
        # Get initial stats
        initial_stats = reset_handler.get_index_stats()
        if initial_stats:
            initial_count = initial_stats.get('total_vector_count', 0)
            logger.info(f"Initial vector count: {initial_count}")
        
        # Create backup (optional)
        backup_namespace = reset_handler.backup_vectors()
        if backup_namespace:
            logger.info(f"Backup created in namespace: {backup_namespace}")
        
        # Choose reset method based on requirements
        reset_method = os.getenv('PINECONE_RESET_METHOD', 'namespace')  # 'namespace' or 'index'
        
        if reset_method == 'index':
            # Complete index recreation (more thorough)
            success = reset_handler.recreate_index()
        else:
            # Namespace deletion (faster)
            success = reset_handler.delete_namespace()
        
        if success:
            # Verify reset
            time.sleep(5)  # Allow time for changes to propagate
            reset_handler.verify_reset()
            
            logger.info("✅ Pinecone reset completed successfully")
            logger.info(f"Index: {reset_handler.index_name}")
            logger.info(f"Namespace: {reset_handler.namespace}")
            logger.info("Ready for fresh vector ingestion")
            
        else:
            logger.error("❌ Pinecone reset failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Reset process failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
