#!/usr/bin/env python3
"""
Phase 1: Comprehensive Cross-System Verification Test
Uses the proper cross-system verifier to actually check data integrity across all 4 systems
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import the proper verifier
from proper_cross_system_verifier import ProperCrossSystemVerifier

# Import storage clients for test data creation
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase1ComprehensiveVerificationTest:
    """Phase 1: Comprehensive verification test with proper cross-system checking"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Initialize proper verifier
        self.verifier = ProperCrossSystemVerifier()
        
        # Test batch ID
        self.test_batch_id = f"phase1_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
        if hasattr(self, 'verifier'):
            self.verifier.close()
    
    def get_existing_case_ids(self, limit: int = 10) -> list:
        """Get existing case IDs from recent CourtListener processing for verification"""

        try:
            # Look for recent CourtListener cases first (they should have complete cross-system storage)
            response = self.supabase.table('cases').select('id, source, created_at').eq('source', 'courtlistener').order('created_at', desc=True).limit(limit).execute()

            if response.data:
                case_ids = [case['id'] for case in response.data]
                print(f"✅ Found {len(case_ids)} CourtListener cases for verification")
                for case in response.data[:3]:  # Show first 3
                    print(f"   - {case['id']} (created: {case.get('created_at', 'unknown')})")
                return case_ids
            else:
                print("❌ No CourtListener cases found, trying any recent cases...")

                # Fallback to any recent cases
                response = self.supabase.table('cases').select('id, source, created_at').order('created_at', desc=True).limit(limit).execute()

                if response.data:
                    # Filter for cases that might have complete storage
                    recent_cases = []
                    for case in response.data:
                        case_id = case['id']
                        # Skip old CAP cases that might not have complete storage
                        if not case_id.startswith('sw2d_') and not case_id.startswith('tex_'):
                            recent_cases.append(case_id)

                    if recent_cases:
                        print(f"✅ Found {len(recent_cases)} recent cases for verification")
                        return recent_cases[:limit]

                print("❌ No suitable existing cases found")
                return []

        except Exception as e:
            print(f"❌ Error getting existing case IDs: {e}")
            return []
    
    async def create_test_data_if_needed(self) -> list:
        """Create small test dataset if no existing data found"""
        
        print(f"\n🔄 CREATING SMALL TEST DATASET")
        print("=" * 60)
        
        # Create a realistic test case with judge names for enhanced features testing
        test_cases = [{
            'id': f'phase1_test_{int(datetime.now().timestamp())}',
            'source': 'test_data',
            'case_name': 'Phase 1 Verification Test Case v. Enhanced Features',
            'court': 'ca5',
            'court_name': 'U.S. Court of Appeals, Fifth Circuit',
            'date_filed': '2024-01-01',
            'jurisdiction': 'US',
            'text': '''This is a comprehensive test case for Phase 1 verification of cross-system data integrity.

OPINION

The Honorable Judge Miranda M. Du delivered the opinion of the court.

This case involves testing the enhanced judge disambiguation system with proper cross-system verification. The court finds that all storage systems must maintain data integrity across Supabase, GCS, Pinecone, and Neo4j.

Judge Smith participated in this decision. Justice Roberts authored the concurring opinion.

The enhanced features being tested include:
1. Confidence scoring for judge identification
2. Career progression detection
3. External validation against reference sources
4. Geographic and temporal disambiguation

This text is substantial enough to generate multiple vector embeddings and test the complete pipeline with all enhanced features enabled.

CONCLUSION

The court concludes that proper cross-system verification is essential for production readiness.

''' * 3,  # Repeat for substantial content
            'precedential_status': 'Published'
        }]
        
        try:
            # Process through the pipeline with all features enabled
            processor = SourceAgnosticProcessor(
                self.supabase, 
                self.gcs_client, 
                self.pinecone_client, 
                self.neo4j_client,
                enable_legal_relationships=True,  # Enable all features for proper testing
                legal_relationship_timeout=60     # Short timeout for test
            )
            
            print(f"📊 Processing {len(test_cases)} test cases with ALL FEATURES ENABLED")
            print(f"   ⚖️ Legal relationships: ENABLED")
            print(f"   🔧 Enhanced features: ENABLED")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='test_data',
                batch_id=self.test_batch_id
            )
            
            if result['success']:
                print(f"✅ Test data created successfully")
                return [case['id'] for case in test_cases]
            else:
                print(f"❌ Test data creation failed: {result}")
                return []
                
        except Exception as e:
            print(f"❌ Error creating test data: {e}")
            return []
    
    async def run_comprehensive_verification(self) -> bool:
        """Run comprehensive cross-system verification"""
        
        print("🔍 PHASE 1: COMPREHENSIVE CROSS-SYSTEM VERIFICATION")
        print("=" * 80)
        print("🎯 GOAL: Verify data integrity across all 4 systems")
        print("📊 SYSTEMS: Supabase + GCS + Pinecone + Neo4j")
        print("🔧 FEATURES: All enhanced features enabled")
        
        try:
            # Step 1: Always create fresh test data to ensure complete cross-system storage
            print(f"\n🔄 Creating fresh test data for proper verification...")
            case_ids = await self.create_test_data_if_needed()

            if not case_ids:
                print(f"❌ Failed to create test data for verification")
                return False
            
            print(f"\n📋 VERIFYING {len(case_ids)} CASES:")
            for i, case_id in enumerate(case_ids, 1):
                print(f"   {i}. {case_id}")
            
            # Step 2: Run basic cross-system verification
            print(f"\n🔍 STEP 1: BASIC CROSS-SYSTEM VERIFICATION")
            basic_report = await self.verifier.verify_batch(case_ids)
            
            # Step 3: Run detailed data integrity verification
            print(f"\n🔍 STEP 2: DETAILED DATA INTEGRITY VERIFICATION")
            integrity_report = await self.verifier.verify_data_integrity(case_ids)
            
            # Step 4: Generate comprehensive assessment
            return self._assess_verification_results(basic_report, integrity_report)
            
        except Exception as e:
            print(f"❌ Comprehensive verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _assess_verification_results(self, basic_report: dict, integrity_report: dict) -> bool:
        """Assess verification results and generate final report"""
        
        print(f"\n📊 PHASE 1 VERIFICATION RESULTS")
        print("=" * 80)
        
        # Basic verification results
        basic_success = basic_report.get('overall_success', False)
        success_rate = basic_report.get('verification_summary', {}).get('success_rate', 0)
        avg_consistency = basic_report.get('verification_summary', {}).get('average_consistency_score', 0)
        
        print(f"🔍 BASIC CROSS-SYSTEM VERIFICATION:")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Consistency: {avg_consistency:.1f}%")
        print(f"   Overall Success: {'✅' if basic_success else '❌'}")
        
        # System coverage
        system_coverage = basic_report.get('system_coverage', {})
        system_success_rates = basic_report.get('system_success_rates', {})
        
        print(f"\n📊 SYSTEM COVERAGE:")
        for system, count in system_coverage.items():
            success_rate = system_success_rates.get(system, 0)
            print(f"   {system.upper()}: {count} cases ({success_rate:.1f}%)")
        
        # Data integrity results
        integrity_success = integrity_report.get('overall_integrity', False)
        integrity_summary = integrity_report.get('integrity_summary', {})
        
        print(f"\n🔍 DATA INTEGRITY VERIFICATION:")
        print(f"   Overall Integrity: {'✅' if integrity_success else '❌'}")
        
        for check_type, results in integrity_summary.items():
            success_rate = results.get('success_rate', 0)
            issues = results.get('issues', [])
            print(f"   {check_type.replace('_', ' ').title()}: {success_rate:.1f}%")
            
            if issues:
                for issue in issues[:3]:  # Show first 3 issues
                    if issue:
                        print(f"      - {issue}")
        
        # Issue analysis
        issue_analysis = basic_report.get('issue_analysis', {})
        total_issues = issue_analysis.get('total_issues', 0)
        
        if total_issues > 0:
            print(f"\n⚠️ ISSUES IDENTIFIED:")
            print(f"   Total Issues: {total_issues}")
            
            issue_breakdown = issue_analysis.get('issue_breakdown', {})
            for issue, count in list(issue_breakdown.items())[:5]:  # Top 5 issues
                print(f"   - {issue}: {count} cases")
        
        # Final assessment
        overall_success = basic_success and integrity_success and success_rate >= 90
        
        print(f"\n🎯 PHASE 1 FINAL ASSESSMENT:")
        print(f"   Basic Verification: {'✅' if basic_success else '❌'}")
        print(f"   Data Integrity: {'✅' if integrity_success else '❌'}")
        print(f"   Success Rate: {'✅' if success_rate >= 90 else '❌'} ({success_rate:.1f}%)")
        print(f"   Overall Success: {'✅' if overall_success else '❌'}")
        
        if overall_success:
            print(f"\n🎉 PHASE 1: SUCCESS!")
            print(f"✅ All 4 systems verified with proper data integrity")
            print(f"✅ Cross-system consistency confirmed")
            print(f"✅ Enhanced features working correctly")
            print(f"✅ Ready to proceed to Phase 2 (Legal Relationship Fixes)")
        else:
            print(f"\n❌ PHASE 1: FAILED!")
            print(f"❌ Issues found in cross-system verification")
            print(f"❌ Must fix issues before proceeding to Phase 2")
            
            # Provide specific recommendations
            print(f"\n🔧 RECOMMENDED FIXES:")
            if not basic_success:
                print(f"   - Fix basic cross-system data storage issues")
            if not integrity_success:
                print(f"   - Fix data integrity issues across systems")
            if success_rate < 90:
                print(f"   - Improve success rate from {success_rate:.1f}% to >90%")
        
        return overall_success
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP PHASE 1 TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run Phase 1 comprehensive verification test"""
    
    test = Phase1ComprehensiveVerificationTest()
    
    try:
        success = await test.run_comprehensive_verification()
        return success
    finally:
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
