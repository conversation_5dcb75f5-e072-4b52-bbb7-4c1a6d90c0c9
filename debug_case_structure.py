#!/usr/bin/env python3
"""
Debug the case data structure to understand the format.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from processing.courtlistener_bulk_client import CourtListenerBulkClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

def debug_case_structure():
    """Debug the structure of cases returned by the API."""
    
    print("🔍 DEBUGGING CASE DATA STRUCTURE")
    print("=" * 50)
    
    # Initialize client
    client = CourtListenerBulkClient()
    
    # Fetch just 2 cases to examine structure
    print("\n📋 Fetching 2 Texas cases to examine structure...")
    
    try:
        texas_cases = client.fetch_jurisdiction_cases('tx', limit=2)
        
        print(f"✅ Fetched {len(texas_cases)} cases")
        print(f"📊 Type of cases list: {type(texas_cases)}")
        
        if texas_cases:
            print(f"\n🔍 EXAMINING FIRST CASE:")
            first_case = texas_cases[0]
            print(f"   Type: {type(first_case)}")
            
            if isinstance(first_case, dict):
                print(f"   Keys: {list(first_case.keys())}")
                
                # Check specific fields
                print(f"\n   📋 CASE DETAILS:")
                print(f"      case_name: {first_case.get('case_name', 'N/A')}")
                print(f"      date_filed: {first_case.get('date_filed', 'N/A')}")
                print(f"      id: {first_case.get('id', 'N/A')}")
                
                # Check docket structure
                docket = first_case.get('docket')
                print(f"\n   🏛️ DOCKET STRUCTURE:")
                print(f"      docket type: {type(docket)}")
                if isinstance(docket, dict):
                    print(f"      docket keys: {list(docket.keys())}")
                    print(f"      court: {docket.get('court', 'N/A')}")
                elif isinstance(docket, str):
                    print(f"      docket (string): {docket}")
                else:
                    print(f"      docket: {docket}")
            else:
                print(f"   Raw data: {first_case}")
        
        if len(texas_cases) > 1:
            print(f"\n🔍 EXAMINING SECOND CASE:")
            second_case = texas_cases[1]
            print(f"   Type: {type(second_case)}")
            
            if isinstance(second_case, dict):
                print(f"   case_name: {second_case.get('case_name', 'N/A')}")
                docket = second_case.get('docket')
                print(f"   docket type: {type(docket)}")
                if isinstance(docket, str):
                    print(f"   docket (string): {docket}")
                elif isinstance(docket, dict):
                    print(f"   court: {docket.get('court', 'N/A')}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_case_structure()
