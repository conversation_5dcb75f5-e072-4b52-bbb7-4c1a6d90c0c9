#!/usr/bin/env python3
"""
Comprehensive test of enhanced Neo4j pipeline with all improvements
"""

import asyncio
import logging
import os
import json
import gzip
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client

# Import components
from test_cap_enhanced_tracking import MockGCSClient
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_enhanced_neo4j_pipeline():
    """Test the complete enhanced Neo4j pipeline"""
    
    print("🚀 ENHANCED NEO4J PIPELINE TEST")
    print("=" * 80)
    print("🎯 Testing all enhancements: cleanup, metadata, citations, relationships")
    
    load_dotenv()
    
    # Setup clients
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = RealPineconeClient()
    neo4j_client = RealNeo4jClient()
    
    processor = SourceAgnosticProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client
    )
    
    try:
        # Clean existing test data
        logger.info("🧹 Cleaning existing test data...")
        supabase.table('cases').delete().like('batch_id', 'enhanced_test_%').execute()
        
        # Get test cases
        raw_cases = get_test_cases(5)
        
        if len(raw_cases) < 5:
            print(f"⚠️ Only found {len(raw_cases)} cases")
        
        # Process batch with enhancements
        batch_id = f"enhanced_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"\n📦 PROCESSING ENHANCED BATCH")
        print(f"   Cases: {len(raw_cases)}")
        print(f"   Batch ID: {batch_id}")
        
        result = await processor.process_coherent_batch(
            raw_cases=raw_cases,
            source_type='caselaw_access_project',
            batch_id=batch_id
        )
        
        if not result['success']:
            print(f"❌ Processing failed: {result}")
            return False
        
        print(f"✅ Processing successful")
        
        # Comprehensive verification
        print(f"\n🔍 COMPREHENSIVE VERIFICATION")
        success = await verify_enhanced_results(supabase, neo4j_client, batch_id)
        
        if success:
            print(f"\n🎉 ENHANCED NEO4J PIPELINE TEST: SUCCESS!")
            print(f"✅ All enhancements working correctly")
            return True
        else:
            print(f"\n❌ ENHANCED NEO4J PIPELINE TEST: FAILED!")
            return False
            
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        neo4j_client.close()


def get_test_cases(num_cases: int) -> list:
    """Get test cases from CAP data"""
    
    cap_files = list(Path("data/caselaw_access_project").glob("*.jsonl.gz"))
    raw_cases = []
    
    for cap_file in cap_files:
        if len(raw_cases) >= num_cases:
            break
            
        try:
            with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                for line in f:
                    if len(raw_cases) >= num_cases:
                        break
                        
                    try:
                        raw_case = json.loads(line.strip())
                        text = raw_case.get('text', '')
                        
                        # Filter for substantial cases with citations
                        if (len(text) > 1000 and 
                            'texas' in text.lower() and
                            ('tex.' in text.lower() or 's.w.' in text.lower())):
                            raw_cases.append(raw_case)
                            
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            continue
    
    return raw_cases


async def verify_enhanced_results(supabase, neo4j_client, batch_id: str) -> bool:
    """Comprehensive verification of all enhancements"""
    
    print("=" * 80)
    
    # 1. Basic storage verification
    print("📊 1. BASIC STORAGE VERIFICATION")
    
    cases = supabase.table('cases').select('id, case_name').eq('batch_id', batch_id).execute()
    
    if not cases.data:
        print("❌ No cases found in Supabase")
        return False
    
    print(f"   ✅ Supabase: {len(cases.data)} cases")
    
    # 2. Neo4j storage verification
    print("📊 2. NEO4J STORAGE VERIFICATION")
    
    with neo4j_client.driver.session() as session:
        result = session.run('''
            MATCH (c:Case {batch_id: $batch_id})
            RETURN count(c) as count
        ''', batch_id=batch_id)
        
        neo4j_count = result.single()['count']
        print(f"   ✅ Neo4j: {neo4j_count} cases")
        
        if neo4j_count != len(cases.data):
            print(f"❌ Count mismatch: Supabase {len(cases.data)} vs Neo4j {neo4j_count}")
            return False
    
    # 3. Metadata enrichment verification
    print("📊 3. METADATA ENRICHMENT VERIFICATION")
    
    with neo4j_client.driver.session() as session:
        result = session.run('''
            MATCH (c:Case {batch_id: $batch_id})
            WHERE c.case_name IS NOT NULL AND c.case_name <> ""
            RETURN count(c) as enriched_count, collect(c.case_name)[0..3] as sample_names
        ''', batch_id=batch_id)
        
        record = result.single()
        enriched_count = record['enriched_count']
        sample_names = record['sample_names']
        
        print(f"   ✅ Enriched cases: {enriched_count}/{neo4j_count}")
        print(f"   📄 Sample names: {sample_names}")
    
    # 4. Citation parsing verification
    print("📊 4. CITATION PARSING VERIFICATION")
    
    with neo4j_client.driver.session() as session:
        result = session.run('''
            MATCH (c:Case {batch_id: $batch_id})
            WHERE c.citation_count > 0
            RETURN count(c) as cases_with_citations, 
                   sum(c.citation_count) as total_citations,
                   collect(c.raw_citations[0])[0..3] as sample_citations
        ''', batch_id=batch_id)
        
        record = result.single()
        cases_with_citations = record['cases_with_citations']
        total_citations = record['total_citations'] or 0
        sample_citations = record['sample_citations']
        
        print(f"   ✅ Cases with citations: {cases_with_citations}/{neo4j_count}")
        print(f"   📚 Total citations: {total_citations}")
        print(f"   📄 Sample citations: {sample_citations}")
    
    # 5. Legal relationships verification
    print("📊 5. LEGAL RELATIONSHIPS VERIFICATION")
    
    with neo4j_client.driver.session() as session:
        # Count CITES relationships
        result = session.run('''
            MATCH (c1:Case)-[:CITES]->(c2:Case)
            WHERE c1.batch_id = $batch_id OR c2.batch_id = $batch_id
            RETURN count(*) as cites_count
        ''', batch_id=batch_id)
        
        cites_count = result.single()['cites_count']
        print(f"   ✅ CITES relationships: {cites_count}")
        
        # Sample relationships
        result = session.run('''
            MATCH (c1:Case)-[:CITES]->(c2:Case)
            WHERE c1.batch_id = $batch_id
            RETURN c1.id, c2.id, c1.case_name, c2.case_name
            LIMIT 3
        ''', batch_id=batch_id)
        
        relationships = list(result)
        print(f"   📄 Sample relationships:")
        for rel in relationships:
            citing_name = rel['c1.case_name'] or 'N/A'
            cited_name = rel['c2.case_name'] or 'N/A'
            print(f"      {citing_name} -> CITES -> {cited_name}")
    
    # 6. Overall graph statistics
    print("📊 6. OVERALL GRAPH STATISTICS")
    
    with neo4j_client.driver.session() as session:
        result = session.run('MATCH (n) RETURN count(n) as total_nodes')
        total_nodes = result.single()['total_nodes']
        
        result = session.run('MATCH ()-[r]->() RETURN count(r) as total_rels')
        total_rels = result.single()['total_rels']
        
        result = session.run('MATCH (c:Case) RETURN count(c) as case_count')
        case_count = result.single()['case_count']
        
        print(f"   📊 Total nodes: {total_nodes}")
        print(f"   📊 Total relationships: {total_rels}")
        print(f"   📊 Case nodes: {case_count}")
    
    # 7. Enhancement success criteria
    print("📊 7. ENHANCEMENT SUCCESS CRITERIA")
    
    success_criteria = {
        'Basic storage': neo4j_count == len(cases.data),
        'Metadata enrichment': enriched_count > 0,
        'Citation parsing': total_citations > 0,
        'Legal relationships': cites_count > 0,
        'Graph growth': total_rels > 30  # Should have grown from initial state
    }
    
    all_passed = True
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status}: {criterion}")
        if not passed:
            all_passed = False
    
    return all_passed


if __name__ == "__main__":
    success = asyncio.run(test_enhanced_neo4j_pipeline())
    exit(0 if success else 1)
