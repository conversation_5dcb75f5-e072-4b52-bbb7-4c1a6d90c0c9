# Texas Phase 1 Multi-Cloud Processor Docker Image
# Optimized for Criminal Defense + Personal Injury & Medical Malpractice processing

FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY texas_phase1_filter.py .
COPY texas_phase1_serverless_pipeline.py .

# Copy processing modules
COPY processing/ ./processing/

# Create necessary directories
RUN mkdir -p /app/data /app/logs /app/temp

# Set environment variables
ENV PYTHONPATH=/app:/app/src
ENV PYTHONUNBUFFERED=1
ENV LOG_LEVEL=INFO
ENV PROCESSING_TIMEOUT=3000
ENV MAX_RETRIES=3
ENV BATCH_SIZE=50

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash texas-processor
RUN chown -R texas-processor:texas-processor /app
USER texas-processor

# Expose port for health checks
EXPOSE 8080

# Default command
CMD ["python", "-m", "src.texas_phase1_processor"]
