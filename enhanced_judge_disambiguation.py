#!/usr/bin/env python3
"""
Enhanced Judge Disambiguation System
Smart system that handles judge career transitions and provides confidence scoring
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

class ConfidenceLevel(Enum):
    """Confidence levels for judge identification"""
    VERY_HIGH = "very_high"  # Full name + court + era + external validation
    HIGH = "high"           # Full name + court + era
    MEDIUM = "medium"       # Partial name + court + era
    LOW = "low"            # Partial name + limited context
    VERY_LOW = "very_low"  # Minimal information

@dataclass
class JudgeIdentity:
    """Enhanced judge identity with confidence and career tracking"""
    name: str
    full_name: Optional[str]
    court: str
    court_type: str  # 'district', 'circuit', 'supreme', 'state'
    geographic_region: str  # '5th_circuit', 'texas', 'federal'
    era_start: int
    era_end: Optional[int]
    confidence_score: float
    confidence_level: ConfidenceLevel
    practice_areas: List[str]
    career_positions: List[Dict[str, Any]]
    external_validated: bool = False

class EnhancedJudgeDisambiguator:
    """Enhanced judge disambiguation with career tracking and confidence scoring"""
    
    def __init__(self):
        # Court hierarchy and geographic mapping
        self.court_hierarchy = {
            'scotus': {'type': 'supreme', 'level': 4, 'region': 'federal'},
            'ca1': {'type': 'circuit', 'level': 3, 'region': '1st_circuit'},
            'ca2': {'type': 'circuit', 'level': 3, 'region': '2nd_circuit'},
            'ca5': {'type': 'circuit', 'level': 3, 'region': '5th_circuit'},
            'ca9': {'type': 'circuit', 'level': 3, 'region': '9th_circuit'},
            'txnd': {'type': 'district', 'level': 2, 'region': 'texas_north'},
            'txsd': {'type': 'district', 'level': 2, 'region': 'texas_south'},
            'nysd': {'type': 'district', 'level': 2, 'region': 'new_york_south'},
        }
        
        # Career progression patterns
        self.typical_progressions = [
            ['district', 'circuit', 'supreme'],  # Federal progression
            ['state_trial', 'state_appellate', 'state_supreme'],  # State progression
            ['district', 'circuit'],  # Common federal progression
        ]
        
        # Practice area keywords
        self.practice_area_keywords = {
            'criminal': ['criminal', 'prosecution', 'defendant', 'guilty', 'sentence'],
            'civil': ['civil', 'contract', 'tort', 'damages', 'plaintiff'],
            'constitutional': ['constitutional', 'amendment', 'rights', 'due process'],
            'corporate': ['corporate', 'securities', 'merger', 'business'],
            'immigration': ['immigration', 'deportation', 'asylum', 'visa'],
            'tax': ['tax', 'irs', 'revenue', 'deduction'],
        }
    
    def create_enhanced_judge_identity(self, 
                                     name: str, 
                                     court: str, 
                                     case_date: str,
                                     case_text: str = "",
                                     full_name: Optional[str] = None) -> JudgeIdentity:
        """Create enhanced judge identity with confidence scoring"""
        
        # Extract court information
        court_info = self.court_hierarchy.get(court, {
            'type': 'unknown', 'level': 1, 'region': 'unknown'
        })
        
        # Calculate era with decade granularity
        era_start = self._calculate_decade_era(case_date)
        
        # Determine practice areas from case text
        practice_areas = self._extract_practice_areas(case_text)
        
        # Calculate confidence score
        confidence_score, confidence_level = self._calculate_confidence(
            name, full_name, court, case_date, case_text
        )
        
        # Create career position entry
        career_position = {
            'court': court,
            'court_type': court_info['type'],
            'era_start': era_start,
            'era_end': None,  # Will be updated when we see them in different courts
            'practice_areas': practice_areas
        }
        
        return JudgeIdentity(
            name=name,
            full_name=full_name or name,
            court=court,
            court_type=court_info['type'],
            geographic_region=court_info['region'],
            era_start=era_start,
            era_end=None,
            confidence_score=confidence_score,
            confidence_level=confidence_level,
            practice_areas=practice_areas,
            career_positions=[career_position],
            external_validated=False
        )
    
    def _calculate_decade_era(self, case_date: str) -> int:
        """Calculate decade-level era from case date"""
        try:
            if case_date:
                year = int(case_date[:4])
                return (year // 10) * 10  # Round down to decade
            return 2020  # Default to current decade
        except:
            return 2020
    
    def _extract_practice_areas(self, case_text: str) -> List[str]:
        """Extract practice areas from case text"""
        if not case_text:
            return []
        
        text_lower = case_text.lower()
        found_areas = []
        
        for area, keywords in self.practice_area_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                found_areas.append(area)
        
        return found_areas
    
    def _calculate_confidence(self, 
                            name: str, 
                            full_name: Optional[str], 
                            court: str, 
                            case_date: str,
                            case_text: str) -> Tuple[float, ConfidenceLevel]:
        """Calculate confidence score for judge identification"""
        
        score = 0.0
        
        # Name quality (40% of score)
        if full_name and len(full_name.split()) >= 3:
            score += 40  # Full name with middle initial
        elif full_name and len(full_name.split()) == 2:
            score += 30  # First and last name
        elif len(name.split()) > 1:
            score += 20  # Multi-word name
        else:
            score += 10  # Single name only
        
        # Court context (25% of score)
        if court in self.court_hierarchy:
            score += 25  # Known court
        else:
            score += 10  # Unknown court
        
        # Temporal context (15% of score)
        if case_date and len(case_date) >= 4:
            score += 15  # Good date info
        else:
            score += 5   # Limited date info
        
        # Text context (10% of score)
        if case_text and len(case_text) > 1000:
            score += 10  # Substantial text
        elif case_text:
            score += 5   # Some text
        
        # Pattern quality (10% of score)
        if 'delivered' in case_text.lower() or 'authored' in case_text.lower():
            score += 10  # Strong judge action pattern
        elif 'judge' in case_text.lower() or 'justice' in case_text.lower():
            score += 5   # Basic judge pattern
        
        # Determine confidence level
        if score >= 85:
            level = ConfidenceLevel.VERY_HIGH
        elif score >= 70:
            level = ConfidenceLevel.HIGH
        elif score >= 50:
            level = ConfidenceLevel.MEDIUM
        elif score >= 30:
            level = ConfidenceLevel.LOW
        else:
            level = ConfidenceLevel.VERY_LOW
        
        return score, level
    
    def smart_judge_matching(self, 
                           new_identity: JudgeIdentity, 
                           existing_judges: List[JudgeIdentity]) -> Optional[JudgeIdentity]:
        """Smart matching that handles career transitions"""
        
        best_match = None
        best_score = 0.0
        
        for existing in existing_judges:
            match_score = self._calculate_match_score(new_identity, existing)
            
            if match_score > best_score and match_score > 0.7:  # 70% threshold
                best_score = match_score
                best_match = existing
        
        return best_match
    
    def _calculate_match_score(self, 
                             identity1: JudgeIdentity, 
                             identity2: JudgeIdentity) -> float:
        """Calculate how likely two identities represent the same judge"""
        
        score = 0.0
        
        # Name similarity (40% weight)
        name_score = self._calculate_name_similarity(identity1.full_name, identity2.full_name)
        score += name_score * 0.4
        
        # Career progression logic (30% weight)
        career_score = self._calculate_career_compatibility(identity1, identity2)
        score += career_score * 0.3
        
        # Geographic compatibility (20% weight)
        geo_score = self._calculate_geographic_compatibility(identity1, identity2)
        score += geo_score * 0.2
        
        # Temporal compatibility (10% weight)
        temporal_score = self._calculate_temporal_compatibility(identity1, identity2)
        score += temporal_score * 0.1
        
        return score
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate name similarity with smart partial matching"""
        
        if not name1 or not name2:
            return 0.0
        
        words1 = set(name1.lower().split())
        words2 = set(name2.lower().split())
        
        # Exact match
        if name1.lower() == name2.lower():
            return 1.0
        
        # One name is subset of another (e.g., "Smith" vs "John Smith")
        if words1.issubset(words2) or words2.issubset(words1):
            return 0.9
        
        # Significant overlap
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if len(intersection) > 0:
            return len(intersection) / len(union)
        
        return 0.0
    
    def _calculate_career_compatibility(self, 
                                      identity1: JudgeIdentity, 
                                      identity2: JudgeIdentity) -> float:
        """Check if career progression makes sense"""
        
        # Same court = likely same judge
        if identity1.court == identity2.court:
            return 1.0
        
        # Check if progression follows typical patterns
        court1_level = self.court_hierarchy.get(identity1.court, {}).get('level', 0)
        court2_level = self.court_hierarchy.get(identity2.court, {}).get('level', 0)
        
        # Promotion (district → circuit → supreme)
        if court2_level > court1_level and identity2.era_start > identity1.era_start:
            return 0.8
        
        # Same level, different jurisdiction (possible transfer)
        if court1_level == court2_level:
            return 0.6
        
        # Demotion (unusual but possible)
        if court2_level < court1_level:
            return 0.3
        
        return 0.1
    
    def _calculate_geographic_compatibility(self, 
                                          identity1: JudgeIdentity, 
                                          identity2: JudgeIdentity) -> float:
        """Check geographic compatibility"""
        
        # Same region
        if identity1.geographic_region == identity2.geographic_region:
            return 1.0
        
        # Related regions (e.g., Texas districts)
        if 'texas' in identity1.geographic_region and 'texas' in identity2.geographic_region:
            return 0.8
        
        # Same circuit family
        if 'circuit' in identity1.geographic_region and 'circuit' in identity2.geographic_region:
            return 0.4
        
        # Federal courts (compatible)
        if identity1.geographic_region == 'federal' or identity2.geographic_region == 'federal':
            return 0.6
        
        return 0.2
    
    def _calculate_temporal_compatibility(self, 
                                        identity1: JudgeIdentity, 
                                        identity2: JudgeIdentity) -> float:
        """Check temporal compatibility"""
        
        era_diff = abs(identity1.era_start - identity2.era_start)
        
        # Same decade
        if era_diff == 0:
            return 1.0
        
        # Adjacent decades (career transition)
        if era_diff == 10:
            return 0.8
        
        # Two decades apart (possible long career)
        if era_diff == 20:
            return 0.6
        
        # Three decades apart (possible but less likely)
        if era_diff == 30:
            return 0.3
        
        # More than 30 years apart (unlikely same person)
        return 0.1
    
    def merge_judge_identities(self, 
                             primary: JudgeIdentity, 
                             secondary: JudgeIdentity) -> JudgeIdentity:
        """Merge two judge identities representing the same person"""
        
        # Use the identity with higher confidence as primary
        if secondary.confidence_score > primary.confidence_score:
            primary, secondary = secondary, primary
        
        # Merge career positions
        merged_positions = primary.career_positions + secondary.career_positions
        
        # Sort by era
        merged_positions.sort(key=lambda x: x['era_start'])
        
        # Update era end dates
        for i in range(len(merged_positions) - 1):
            merged_positions[i]['era_end'] = merged_positions[i + 1]['era_start']
        
        # Merge practice areas
        merged_practice_areas = list(set(primary.practice_areas + secondary.practice_areas))
        
        # Use best available full name
        best_full_name = primary.full_name
        if secondary.full_name and len(secondary.full_name.split()) > len(primary.full_name.split()):
            best_full_name = secondary.full_name
        
        # Calculate new confidence score
        new_confidence = min(100.0, (primary.confidence_score + secondary.confidence_score) / 2 + 10)
        new_level = ConfidenceLevel.VERY_HIGH if new_confidence >= 85 else ConfidenceLevel.HIGH
        
        return JudgeIdentity(
            name=primary.name,
            full_name=best_full_name,
            court=primary.court,  # Use most recent court
            court_type=primary.court_type,
            geographic_region=primary.geographic_region,
            era_start=min(primary.era_start, secondary.era_start),
            era_end=max(primary.era_start, secondary.era_start) if primary.era_start != secondary.era_start else None,
            confidence_score=new_confidence,
            confidence_level=new_level,
            practice_areas=merged_practice_areas,
            career_positions=merged_positions,
            external_validated=primary.external_validated or secondary.external_validated
        )
    
    def generate_smart_judge_id(self, identity: JudgeIdentity) -> str:
        """Generate smart judge ID that handles career transitions"""
        
        # Use full name if available, otherwise use name
        name_part = identity.full_name.lower().replace(' ', '_').replace('.', '')
        
        # For judges with multiple positions, use a career-based ID
        if len(identity.career_positions) > 1:
            # Use primary region and era span
            era_span = f"{identity.era_start}s"
            if identity.era_end:
                era_span += f"_{identity.era_end}s"
            
            return f"judge_{name_part}_{identity.geographic_region}_{era_span}"
        else:
            # Single position - use traditional format
            return f"judge_{name_part}_{identity.court}_{identity.era_start}s"


def demonstrate_enhanced_disambiguation():
    """Demonstrate the enhanced disambiguation system"""
    
    print("🧪 ENHANCED JUDGE DISAMBIGUATION DEMONSTRATION")
    print("=" * 80)
    
    disambiguator = EnhancedJudgeDisambiguator()
    
    # Simulate judge career progression
    scenarios = [
        {
            'name': 'Career Progression',
            'judges': [
                {'name': 'Smith', 'full_name': 'John Smith', 'court': 'txnd', 'date': '1995-01-01', 'text': 'Judge John Smith delivered the opinion in this civil case.'},
                {'name': 'Smith', 'full_name': 'John Smith', 'court': 'ca5', 'date': '2005-01-01', 'text': 'Circuit Judge John Smith authored the majority opinion.'},
                {'name': 'Smith', 'full_name': 'John Smith', 'court': 'scotus', 'date': '2015-01-01', 'text': 'Justice John Smith delivered the opinion of the Court.'},
            ]
        },
        {
            'name': 'Different Judges Same Name',
            'judges': [
                {'name': 'Johnson', 'full_name': 'Mary Johnson', 'court': 'ca2', 'date': '2020-01-01', 'text': 'Circuit Judge Mary Johnson wrote the opinion.'},
                {'name': 'Johnson', 'full_name': 'Robert Johnson', 'court': 'ca5', 'date': '2020-01-01', 'text': 'Circuit Judge Robert Johnson dissented.'},
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 SCENARIO: {scenario['name']}")
        print("-" * 40)
        
        identities = []
        for judge_data in scenario['judges']:
            identity = disambiguator.create_enhanced_judge_identity(
                name=judge_data['name'],
                court=judge_data['court'],
                case_date=judge_data['date'],
                case_text=judge_data['text'],
                full_name=judge_data['full_name']
            )
            
            print(f"\n   Judge: {identity.full_name}")
            print(f"   Court: {identity.court} ({identity.court_type})")
            print(f"   Era: {identity.era_start}s")
            print(f"   Confidence: {identity.confidence_score:.1f} ({identity.confidence_level.value})")
            print(f"   ID: {disambiguator.generate_smart_judge_id(identity)}")
            
            # Check for matches with existing identities
            match = disambiguator.smart_judge_matching(identity, identities)
            if match:
                print(f"   🔗 MATCHES: {match.full_name} (career progression)")
                merged = disambiguator.merge_judge_identities(match, identity)
                print(f"   📈 MERGED ID: {disambiguator.generate_smart_judge_id(merged)}")
                print(f"   📈 Career span: {merged.era_start}s - {merged.era_end or 'present'}")
                # Replace the match with merged identity
                identities = [i for i in identities if i != match]
                identities.append(merged)
            else:
                print(f"   ✨ NEW JUDGE: Unique identity")
                identities.append(identity)


if __name__ == "__main__":
    demonstrate_enhanced_disambiguation()
