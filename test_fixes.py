#!/usr/bin/env python3
"""
Test the fixes for checkpoint saving and source field
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import test components
from test_cap_enhanced_tracking import MockGCSClient, MockPineconeClient, MockNeo4jClient
from production_cap_processor import ProductionCAPProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_fixes():
    """Test the checkpoint and source fixes"""
    
    load_dotenv()
    
    # Setup clients
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    # Clean existing test data
    logger.info("🧹 Cleaning existing test data...")
    supabase.table('cases').delete().like('batch_id', 'test_fixes_%').execute()
    
    # Initialize processor
    processor = ProductionCAPProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client,
        batch_size=10,  # Very small for quick test
        chunk_size=20   # Very small for quick test
    )
    
    logger.info("🧪 Testing fixes...")
    
    # Process with fixes
    results = await processor.process_cap_production(
        jurisdiction='tx',
        max_files=1,  # Just one file
        resume=False,  # Fresh start
        test_mode=True
    )
    
    logger.info(f"📊 Processing Results: {results}")
    
    # Check if source field is correct now
    logger.info("🔍 Checking source field...")
    result = supabase.table('cases').select('source, count').like('batch_id', 'cap_tx_%').execute()
    
    if result.data:
        for row in result.data:
            logger.info(f"   Source: {row.get('source', 'unknown')}")
    
    # Check specific cases
    cases = supabase.table('cases').select('id, source, batch_id').like('batch_id', 'cap_tx_%').limit(3).execute()
    
    logger.info("📋 Sample cases:")
    for case in cases.data:
        logger.info(f"   ID: {case['id']}, Source: {case['source']}, Batch: {case['batch_id']}")
    
    # Test checkpoint functionality
    logger.info("💾 Testing checkpoint save...")
    try:
        await processor._save_checkpoint('tx', 'test_file.jsonl.gz', {'test': 'data'})
        logger.info("✅ Checkpoint save: SUCCESS")
    except Exception as e:
        logger.error(f"❌ Checkpoint save: FAILED - {e}")
    
    # Test checkpoint load
    logger.info("📋 Testing checkpoint load...")
    try:
        checkpoint = await processor._load_checkpoint('tx')
        if checkpoint:
            logger.info(f"✅ Checkpoint load: SUCCESS - {checkpoint.get('last_file', 'unknown')}")
        else:
            logger.info("ℹ️ Checkpoint load: No checkpoint found (expected for fresh test)")
    except Exception as e:
        logger.error(f"❌ Checkpoint load: FAILED - {e}")
    
    return results


if __name__ == "__main__":
    asyncio.run(test_fixes())
