#!/usr/bin/env python3
"""
Test CAP data processing with a small batch to verify consistency.
Process 1,000 records and verify all databases stay perfectly in sync.
"""

import asyncio
import json
import gzip
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from processing.caselaw_access_processor import CaselawAccessProcessor

async def test_cap_batch_consistency():
    """Test CAP processing with 1,000 records to verify consistency."""
    
    print("🧪 TESTING CAP BATCH PROCESSING CONSISTENCY")
    print("=" * 70)
    print(f"Processing 1,000 CAP records to verify database synchronization")
    print("=" * 70)
    
    try:
        # Get baseline database counts
        print(f"📊 BASELINE DATABASE COUNTS:")
        baseline_counts = await get_database_counts()
        print(f"   Supabase: {baseline_counts['supabase']:,}")
        print(f"   Neo4j: {baseline_counts['neo4j']:,}")
        print(f"   Pinecone: {baseline_counts['pinecone']:,}")
        
        # Initialize CAP processor
        print(f"\n🔧 Initializing CAP Processor...")
        processor = CaselawAccessProcessor()
        
        # Read 1,000 records from first CAP file
        cap_file = "data/caselaw_access_project/cap_00000.jsonl.gz"
        print(f"📂 Reading from: {cap_file}")
        
        test_records = []
        with gzip.open(cap_file, 'rt') as f:
            for i, line in enumerate(f):
                if i >= 1000:  # Process first 1,000 records
                    break
                test_records.append(json.loads(line))
        
        print(f"📥 Loaded {len(test_records):,} test records")
        
        # Process records and track results
        print(f"\n🔄 PROCESSING TEST RECORDS:")
        processed_count = 0
        duplicate_count = 0
        error_count = 0
        
        start_time = datetime.now()
        
        for i, record in enumerate(test_records):
            try:
                # Convert JSON record to CaselawDocument format
                doc = convert_cap_record_to_document(record)
                
                # Process through the pipeline
                success = await processor.process_document(doc)
                
                if success:
                    processed_count += 1
                    if (processed_count % 100) == 0:
                        print(f"   ✅ Processed {processed_count:,}/1,000 ({processed_count/10:.1f}%)")
                else:
                    duplicate_count += 1
                
            except Exception as e:
                error_count += 1
                if error_count <= 5:  # Show first 5 errors
                    print(f"   ❌ Error processing record {i+1}: {e}")
        
        end_time = datetime.now()
        processing_time = end_time - start_time
        
        # Get final database counts
        print(f"\n📊 FINAL DATABASE COUNTS:")
        final_counts = await get_database_counts()
        print(f"   Supabase: {final_counts['supabase']:,} (+{final_counts['supabase'] - baseline_counts['supabase']:,})")
        print(f"   Neo4j: {final_counts['neo4j']:,} (+{final_counts['neo4j'] - baseline_counts['neo4j']:,})")
        print(f"   Pinecone: {final_counts['pinecone']:,} (+{final_counts['pinecone'] - baseline_counts['pinecone']:,})")
        
        # Calculate increases
        supabase_increase = final_counts['supabase'] - baseline_counts['supabase']
        neo4j_increase = final_counts['neo4j'] - baseline_counts['neo4j']
        pinecone_increase = final_counts['pinecone'] - baseline_counts['pinecone']
        
        # Verify consistency
        print(f"\n🎯 CONSISTENCY VERIFICATION:")
        print(f"   Records processed: {processed_count:,}")
        print(f"   Duplicates detected: {duplicate_count:,}")
        print(f"   Errors: {error_count:,}")
        print(f"   Processing time: {processing_time}")
        print(f"   Rate: {processed_count / processing_time.total_seconds():.1f} records/second")
        
        print(f"\n   Database Increases:")
        print(f"   Supabase: +{supabase_increase:,}")
        print(f"   Neo4j: +{neo4j_increase:,}")
        print(f"   Pinecone: +{pinecone_increase:,}")
        
        # Check perfect consistency
        if supabase_increase == neo4j_increase == processed_count:
            print(f"\n   ✅ PERFECT CONSISTENCY ACHIEVED!")
            print(f"   ✅ All {processed_count:,} records in all databases")
            print(f"   ✅ Supabase ↔ Neo4j: 100% synchronized")
            consistency_rate = 100.0
        elif supabase_increase == neo4j_increase:
            print(f"\n   ✅ EXCELLENT CONSISTENCY!")
            print(f"   ✅ Supabase ↔ Neo4j: 100% synchronized")
            print(f"   ⚠️  Some records may have been filtered/skipped")
            consistency_rate = 99.0
        else:
            print(f"\n   ❌ CONSISTENCY ISSUE DETECTED!")
            print(f"   ❌ Supabase vs Neo4j mismatch")
            print(f"   🔧 Need to investigate before scaling")
            consistency_rate = 0.0
        
        # Pinecone consistency (vectors take time to propagate)
        expected_vectors = processed_count * 3  # ~3 chunks per case
        if pinecone_increase >= expected_vectors * 0.8:  # 80% threshold (propagation delay)
            print(f"   ✅ Pinecone: Good vector coverage ({pinecone_increase:,} vectors)")
        else:
            print(f"   ⚠️  Pinecone: Low vector count (may be propagation delay)")
        
        return consistency_rate >= 99.0, processed_count, error_count
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 1

def convert_cap_record_to_document(record):
    """Convert CAP JSON record to CaselawDocument format."""
    
    from processing.caselaw_access_processor import CaselawDocument
    from datetime import datetime
    
    # Extract metadata
    metadata = record.get('metadata', {})
    
    # Create CaselawDocument
    doc = CaselawDocument(
        # Required fields
        id=record['id'],
        source=record.get('source', 'Caselaw Access Project'),
        added=datetime.fromisoformat(record.get('added', '2024-01-01T00:00:00').replace('Z', '+00:00')),
        created=datetime.fromisoformat(record.get('created', '2024-01-01T00:00:00').replace('Z', '+00:00')),
        author=metadata.get('author', 'Unknown'),
        license=metadata.get('license', 'Public Domain'),
        url=metadata.get('url', ''),
        text=record.get('text', ''),
        
        # Optional fields - will be extracted from text
        case_name='',  # Will be extracted
        docket_number='',  # Will be extracted
        date_filed=None,  # Will be extracted
        court='',  # Will be extracted
        jurisdiction='',  # Will be extracted
        practice_area='',  # Will be classified
        precedential_status='',
        citation_count=0,
        judges=[],
        parties=[]
    )
    
    return doc

async def get_database_counts():
    """Get current counts from all databases."""
    
    counts = {'supabase': 0, 'neo4j': 0, 'pinecone': 0}
    
    try:
        # Supabase count
        from processing.storage.supabase_connector import SupabaseConnector
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        counts['supabase'] = response.count
        
        # Neo4j count
        from processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            counts['neo4j'] = result.single()['count']
        neo4j.close()
        
        # Pinecone count
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        counts['pinecone'] = stats.total_vector_count
        
    except Exception as e:
        print(f"⚠️  Error getting database counts: {e}")
    
    return counts

async def main():
    """Main test function."""
    
    print("🧪 CAP BATCH CONSISTENCY TEST")
    print("=" * 80)
    print("Testing 1,000 CAP records to verify database synchronization")
    print("This will determine if we can safely scale to 640,000 records")
    print("=" * 80)
    
    # Run the test
    success, processed, errors = await test_cap_batch_consistency()
    
    print(f"\n🎯 TEST RESULTS:")
    if success:
        print(f"   ✅ CAP PROCESSING CONSISTENCY VERIFIED!")
        print(f"   ✅ Processed {processed:,} records successfully")
        print(f"   ✅ All databases perfectly synchronized")
        print(f"   🚀 READY FOR FULL-SCALE CAP PROCESSING")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Process full CAP dataset (640,000 records)")
        print(f"   2. Expect perfect consistency across all databases")
        print(f"   3. Monitor processing for any issues")
        print(f"   4. Achieve massive database population")
        
        # Estimate full processing
        if processed > 0:
            processing_rate = processed / 60  # rough estimate per minute
            full_time_hours = 640000 / (processing_rate * 60)
            print(f"\n📊 FULL PROCESSING ESTIMATES:")
            print(f"   Total records: 640,000")
            print(f"   Estimated time: {full_time_hours:.1f} hours")
            print(f"   Expected final database size: ~640,100 cases")
    else:
        print(f"   ❌ CONSISTENCY ISSUES DETECTED")
        print(f"   ❌ Processed: {processed:,}, Errors: {errors:,}")
        print(f"   🔧 MUST FIX BEFORE SCALING")
        
        print(f"\n📋 REQUIRED ACTIONS:")
        print(f"   1. Debug consistency issues")
        print(f"   2. Fix CAP processing pipeline")
        print(f"   3. Re-test before full processing")

if __name__ == "__main__":
    asyncio.run(main())
