#!/usr/bin/env python3
"""
Test script to verify the Court Listener processing fixes.
"""

import asyncio
import logging
from datetime import datetime
from enhanced_court_listener_processor import EnhancedCourtListenerProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_court_listener_fixes():
    """Test the Court Listener processing fixes."""
    
    print("🧪 TESTING COURT LISTENER PROCESSING FIXES")
    print("=" * 60)
    
    try:
        # Initialize processor
        print("🔧 Initializing Enhanced Court Listener Processor...")
        processor = EnhancedCourtListenerProcessor()
        
        # Test with a small batch from Texas Personal Injury
        print("\n📋 Testing with Texas Personal Injury cases...")
        print("   Fetching 3 cases for validation...")
        
        # Fetch a small batch
        cases = processor.court_listener.fetch_jurisdiction_cases(
            jurisdiction='tx',
            limit=3,
            practice_areas=['personal injury']
        )
        
        print(f"   ✅ Fetched {len(cases)} cases successfully")
        
        if not cases:
            print("   ⚠️  No cases fetched - cannot test fixes")
            return False
        
        # Process the cases one by one to see the actual IDs
        print(f"\n🔍 Processing cases individually to verify fixes:")
        
        for i, case in enumerate(cases[:2]):  # Test first 2 cases
            print(f"\n   Case {i+1}:")
            print(f"     Court Listener ID: {case.get('id')}")
            print(f"     Case Name: {case.get('case_name', 'Unknown')[:50]}...")
            
            # Transform to CaselawDocument
            case_doc = processor._transform_court_listener_case(case, 'tx', 'personal injury')
            print(f"     Generated Document ID: {case_doc.id}")
            print(f"     Source: {case_doc.source}")
            
            # Check what the storage ID would be
            storage_id = processor.caselaw_processor._generate_case_id(case_doc)
            print(f"     Storage ID: {storage_id}")
            
            # Process the document
            try:
                success = await processor.caselaw_processor.process_document(case_doc)
                if success:
                    print(f"     ✅ Successfully processed and stored")
                else:
                    print(f"     ⚠️  Detected as duplicate (expected if already processed)")
            except Exception as e:
                print(f"     ❌ Error processing: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def verify_database_after_fixes():
    """Verify the database state after applying fixes."""
    
    print("\n🔍 VERIFYING DATABASE STATE AFTER FIXES")
    print("=" * 60)
    
    # Check Supabase for new Court Listener cases
    from supabase import create_client
    import os
    
    try:
        supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_KEY')
        )
        
        # Check for cases with proper cl_ IDs (not cap_cl_)
        response = supabase.table('cases').select('id, case_name, source').eq('source', 'court_listener').like('id', 'cl_%').not_.like('id', 'cap_cl_%').limit(10).execute()
        
        proper_cl_cases = len(response.data)
        print(f"📊 Cases with proper Court Listener IDs (cl_*): {proper_cl_cases}")
        
        if proper_cl_cases > 0:
            print("   ✅ SUCCESS: Court Listener cases now have correct IDs!")
            for case in response.data[:3]:
                print(f"     {case['id']}: {case['case_name'][:50]}...")
        else:
            print("   ⚠️  No cases with proper cl_ IDs found yet")
        
        # Check total Court Listener cases
        response = supabase.table('cases').select('id', count='exact').eq('source', 'court_listener').execute()
        total_cl_cases = response.count
        print(f"📊 Total Court Listener cases: {total_cl_cases}")
        
        return proper_cl_cases > 0
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

async def main():
    """Main test execution."""
    
    print("🧪 COURT LISTENER PROCESSING FIXES VALIDATION")
    print("=" * 70)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("=" * 70)
    
    # Test 1: Process a few cases with fixes
    processing_test_passed = await test_court_listener_fixes()
    
    if processing_test_passed:
        # Test 2: Verify database state
        database_test_passed = await verify_database_after_fixes()
        
        if database_test_passed:
            print("\n🎉 ALL FIXES VALIDATED!")
            print("✅ Court Listener processing now working correctly")
            print("✅ Cases getting proper IDs and storage paths")
            print("✅ Ready for full-scale processing")
        else:
            print("\n⚠️  PARTIAL SUCCESS")
            print("✅ Processing logic fixed")
            print("❌ Need to process more cases to see database changes")
    else:
        print("\n❌ FIXES NEED MORE WORK")
        print("❌ Processing still has issues")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
