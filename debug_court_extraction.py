#!/usr/bin/env python3
"""
Debug Court Extraction
Figure out why court extraction is failing
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def debug_court_extraction():
    """Debug court extraction to understand data structure"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🔍 DEBUGGING COURT EXTRACTION")
    print("=" * 50)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # Get a single case and examine its structure
        print("\n📄 1. EXAMINING CASE DATA STRUCTURE")
        print("-" * 40)
        
        try:
            # Get a SCOTUS case
            url = f"{base_url}/opinions/"
            params = {
                'cluster__docket__court': 'scotus',
                'page_size': 1,
                'format': 'json',
                'author__isnull': False
            }
            
            response = await client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            cases = data.get('results', [])
            
            if not cases:
                print("❌ No cases found")
                return
            
            case = cases[0]
            print(f"📄 Case ID: {case.get('id')}")
            print(f"📊 Available fields: {list(case.keys())}")
            
            # Check for court-related fields
            court_fields = ['court', 'docket', 'cluster']
            print(f"\n🏛️ COURT-RELATED FIELDS:")
            for field in court_fields:
                value = case.get(field)
                print(f"   {field}: {value} (type: {type(value).__name__})")
            
            # Get cluster data to see docket structure
            cluster_url = case.get('cluster')
            if cluster_url:
                print(f"\n📊 2. EXAMINING CLUSTER DATA")
                print("-" * 40)
                
                cluster_response = await client.get(cluster_url)
                if cluster_response.status_code == 200:
                    cluster_data = cluster_response.json()
                    print(f"📊 Cluster fields: {list(cluster_data.keys())}")
                    
                    # Check docket field
                    docket = cluster_data.get('docket')
                    print(f"\n🏛️ DOCKET FIELD:")
                    print(f"   docket: {docket} (type: {type(docket).__name__})")
                    
                    if isinstance(docket, str) and docket.startswith('http'):
                        print(f"\n📊 3. FETCHING DOCKET DATA")
                        print("-" * 40)
                        
                        # Fetch docket data
                        docket_response = await client.get(docket)
                        if docket_response.status_code == 200:
                            docket_data = docket_response.json()
                            print(f"📊 Docket fields: {list(docket_data.keys())}")
                            
                            # Check court field in docket
                            docket_court = docket_data.get('court')
                            print(f"\n🏛️ DOCKET COURT FIELD:")
                            print(f"   court: {docket_court} (type: {type(docket_court).__name__})")
                            
                            if isinstance(docket_court, str) and docket_court.startswith('http'):
                                print(f"\n📊 4. FETCHING COURT DATA")
                                print("-" * 40)
                                
                                # Fetch court data
                                court_response = await client.get(docket_court)
                                if court_response.status_code == 200:
                                    court_data = court_response.json()
                                    print(f"📊 Court fields: {list(court_data.keys())}")
                                    
                                    # Show key court info
                                    key_fields = ['id', 'short_name', 'full_name', 'jurisdiction']
                                    print(f"\n🏛️ COURT INFORMATION:")
                                    for field in key_fields:
                                        value = court_data.get(field)
                                        print(f"   {field}: {value}")
                                else:
                                    print(f"❌ Failed to fetch court data: {court_response.status_code}")
                            elif isinstance(docket_court, str):
                                # It might be a court ID directly
                                print(f"   Court appears to be ID: {docket_court}")
                        else:
                            print(f"❌ Failed to fetch docket data: {docket_response.status_code}")
                    elif isinstance(docket, dict):
                        print(f"   Docket is embedded dict with fields: {list(docket.keys())}")
                        docket_court = docket.get('court')
                        print(f"   Embedded court: {docket_court}")
                else:
                    print(f"❌ Failed to fetch cluster data: {cluster_response.status_code}")
        
        except Exception as e:
            print(f"❌ Error in debugging: {e}")
        
        # Test our current extraction logic
        print(f"\n🔧 5. TESTING CURRENT EXTRACTION LOGIC")
        print("-" * 40)
        
        # Simulate what our extraction function does
        print("Testing extraction paths:")
        
        # Path 1: Direct docket field
        docket = case.get('docket')
        print(f"Path 1 - case.get('docket'): {docket}")
        if docket and isinstance(docket, dict):
            court_id = docket.get('court')
            print(f"   docket.get('court'): {court_id}")
        
        # Path 2: Cluster docket
        cluster_data = case.get('cluster_data', {})  # This would be populated by our enrichment
        print(f"Path 2 - case.get('cluster_data'): {bool(cluster_data)}")
        if cluster_data:
            cluster_docket = cluster_data.get('docket')
            print(f"   cluster_data.get('docket'): {cluster_docket}")
            if isinstance(cluster_docket, dict):
                court_id = cluster_docket.get('court')
                print(f"   cluster_docket.get('court'): {court_id}")
        
        # Path 3: Direct court field
        court_field = case.get('court')
        print(f"Path 3 - case.get('court'): {court_field}")

if __name__ == "__main__":
    asyncio.run(debug_court_extraction())
