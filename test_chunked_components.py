#!/usr/bin/env python3
"""
Test Chunked Processing Components
Tests the new enhanced components for robust processing
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv

# Import our new components
from src.processing.court_id_resolver import CourtIDResolver
from src.processing.time_window_processor import TimeWindowProcessor
from src.processing.atomic_checkpoint_manager import AtomicCheckpointManager
from src.processing.retry_manager import RetryManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_court_id_resolver():
    """Test the CourtIDResolver component"""
    print("\n🔍 Testing CourtIDResolver...")
    
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        print("❌ COURTLISTENER_API_KEY not found in environment")
        return False
    
    try:
        async with CourtIDResolver(api_key) as resolver:
            # Test fetching Texas court IDs
            court_ids = await resolver.fetch_texas_court_ids()
            print(f"✅ Found {len(court_ids)} Texas courts")
            print(f"   Sample courts: {court_ids[:5]}...")
            
            # Test validation of first 3 courts
            if len(court_ids) >= 3:
                validation = await resolver.validate_court_ids(court_ids[:3])
                valid_count = sum(validation.values())
                print(f"✅ Validated {valid_count}/{len(validation)} sample courts")
            
            return True
            
    except Exception as e:
        print(f"❌ CourtIDResolver test failed: {e}")
        return False


async def test_time_window_processor():
    """Test the TimeWindowProcessor component"""
    print("\n📅 Testing TimeWindowProcessor...")
    
    try:
        # Create processor (no session needed for window generation)
        processor = TimeWindowProcessor(None, "")
        
        # Test window generation
        windows = processor.generate_time_windows(2024, 2024, 1, 3)  # Jan-Mar 2024
        print(f"✅ Generated {len(windows)} time windows")
        print(f"   First window: {windows[0]}")
        print(f"   Last window: {windows[-1]}")
        
        # Test window splitting
        if windows:
            sub_windows = processor.split_window_by_date(windows[0], 4)
            print(f"✅ Split window into {len(sub_windows)} sub-windows")
        
        # Test stats
        stats = processor.get_window_stats(windows)
        print(f"✅ Window stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ TimeWindowProcessor test failed: {e}")
        return False


async def test_atomic_checkpoint_manager():
    """Test the AtomicCheckpointManager component"""
    print("\n💾 Testing AtomicCheckpointManager...")
    
    try:
        manager = AtomicCheckpointManager("test_checkpoints")
        
        # Test saving a checkpoint
        test_data = {
            "checkpoint_id": f"test_tx_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "phase": "fetch",
            "window": {"start": "2024-01-01", "end": "2024-01-31"},
            "cursor": "cD0xMTA4ODY2OQ==",
            "fetched": 1500,
            "persisted": 1450,
            "court_ids": ["tex", "texcrimapp", "texapp1"],
            "metadata": {"jurisdiction": "tx", "test": True}
        }
        
        success = await manager.save_checkpoint(test_data)
        print(f"✅ Checkpoint saved: {success}")
        
        # Test loading the checkpoint
        loaded = manager.load_checkpoint(test_data["checkpoint_id"])
        if loaded:
            print(f"✅ Checkpoint loaded: {loaded.checkpoint_id}")
            print(f"   Progress: {loaded.persisted}/{loaded.fetched}")
        
        # Test progress summary
        summary = manager.get_progress_summary(test_data["checkpoint_id"])
        if summary:
            print(f"✅ Progress summary: {summary['success_rate']:.1%} success rate")
        
        # Test listing checkpoints
        checkpoints = manager.list_checkpoints()
        print(f"✅ Found {len(checkpoints)} total checkpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ AtomicCheckpointManager test failed: {e}")
        return False


async def test_retry_manager():
    """Test the RetryManager component"""
    print("\n🔄 Testing RetryManager...")
    
    try:
        retry_manager = RetryManager(max_retries=3, base_delay=0.1)
        
        # Test successful operation
        async def success_operation():
            return "success"
        
        result = await retry_manager.retry_with_backoff(success_operation)
        print(f"✅ Success operation result: {result}")
        
        # Test operation that fails then succeeds
        attempt_count = 0
        async def flaky_operation():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise Exception("Temporary failure")
            return "success after retries"
        
        result = await retry_manager.retry_with_backoff(
            flaky_operation,
            item_id="test_flaky",
            context={"test": True}
        )
        print(f"✅ Flaky operation result: {result}")
        
        # Check stats
        stats = retry_manager.get_retry_stats()
        print(f"✅ Retry stats: {stats['successful_retries']} successful retries")
        
        return True
        
    except Exception as e:
        print(f"❌ RetryManager test failed: {e}")
        return False


async def test_integration():
    """Test integration of multiple components"""
    print("\n🔗 Testing Component Integration...")
    
    try:
        api_key = os.getenv("COURTLISTENER_API_KEY")
        if not api_key:
            print("❌ COURTLISTENER_API_KEY not found - skipping integration test")
            return True
        
        # Test court ID resolution + time windows
        async with CourtIDResolver(api_key) as resolver:
            court_ids = await resolver.fetch_texas_court_ids()
            
            processor = TimeWindowProcessor(None, "")
            windows = processor.generate_time_windows(2024, 2024, 1, 1)  # Just January 2024
            
            print(f"✅ Integration: {len(court_ids)} courts × {len(windows)} windows")
        
        # Test checkpoint + retry manager
        checkpoint_manager = AtomicCheckpointManager("integration_test")
        retry_manager = RetryManager(max_retries=2, base_delay=0.1)
        
        async def checkpoint_operation():
            return await checkpoint_manager.save_checkpoint({
                "checkpoint_id": f"integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "phase": "test",
                "fetched": 100,
                "persisted": 95,
                "court_ids": court_ids[:5] if court_ids else [],
                "metadata": {"integration_test": True}
            })
        
        result = await retry_manager.retry_with_backoff(
            checkpoint_operation,
            item_id="integration_checkpoint"
        )
        print(f"✅ Integration checkpoint result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Processing Component Tests")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Run all tests
    tests = [
        ("CourtIDResolver", test_court_id_resolver),
        ("TimeWindowProcessor", test_time_window_processor),
        ("AtomicCheckpointManager", test_atomic_checkpoint_manager),
        ("RetryManager", test_retry_manager),
        ("Integration", test_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced processing system is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
