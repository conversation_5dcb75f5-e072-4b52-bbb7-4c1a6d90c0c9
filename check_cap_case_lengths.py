#!/usr/bin/env python3
"""
Check CAP case lengths to understand vector chunking
"""

import json
import gzip
from pathlib import Path

def check_cap_case_lengths():
    """Check lengths of CAP cases to understand chunking"""
    
    cap_file = Path("data/caselaw_access_project/cap_00000.jsonl.gz")
    
    print("🔍 ANALYZING CAP CASE LENGTHS")
    print("=" * 50)
    
    case_lengths = []
    texas_cases = []
    
    with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 100:  # Check first 100 cases
                break
                
            try:
                case = json.loads(line.strip())
                text = case.get('text', '')
                word_count = len(text.split()) if text else 0
                case_lengths.append(word_count)
                
                # Check if it's a Texas case
                if 'texas' in text.lower() or 'tex' in text.lower():
                    texas_cases.append({
                        'id': case.get('id', 'unknown'),
                        'word_count': word_count,
                        'text_preview': text[:200] + '...' if len(text) > 200 else text
                    })
                    
            except Exception as e:
                print(f"Error parsing case {i}: {e}")
    
    # Statistics
    if case_lengths:
        avg_length = sum(case_lengths) / len(case_lengths)
        min_length = min(case_lengths)
        max_length = max(case_lengths)
        
        print(f"📊 CASE LENGTH STATISTICS (first 100 cases):")
        print(f"   Average: {avg_length:.0f} words")
        print(f"   Minimum: {min_length} words")
        print(f"   Maximum: {max_length} words")
        print(f"   Cases > 1000 words: {sum(1 for x in case_lengths if x > 1000)}")
        print(f"   Cases > 2000 words: {sum(1 for x in case_lengths if x > 2000)}")
        print(f"   Cases > 5000 words: {sum(1 for x in case_lengths if x > 5000)}")
        
        # Length distribution
        print(f"\n📈 LENGTH DISTRIBUTION:")
        ranges = [
            (0, 500, "Very Short"),
            (500, 1000, "Short"), 
            (1000, 2000, "Medium"),
            (2000, 5000, "Long"),
            (5000, float('inf'), "Very Long")
        ]
        
        for min_len, max_len, label in ranges:
            count = sum(1 for x in case_lengths if min_len <= x < max_len)
            pct = count / len(case_lengths) * 100
            print(f"   {label} ({min_len}-{max_len if max_len != float('inf') else '∞'}): {count} cases ({pct:.1f}%)")
    
    # Texas cases
    print(f"\n🎯 TEXAS CASES FOUND: {len(texas_cases)}")
    for i, case in enumerate(texas_cases[:5]):  # Show first 5
        print(f"\n   Case {i+1}: {case['id']}")
        print(f"   Words: {case['word_count']}")
        print(f"   Preview: {case['text_preview']}")
    
    # Chunking analysis
    print(f"\n🔄 CHUNKING ANALYSIS:")
    print(f"   Max chunk size: 1000 words")
    
    chunks_needed = []
    for length in case_lengths:
        if length == 0:
            chunks = 1  # Empty cases get 1 default chunk
        else:
            chunks = max(1, (length + 999) // 1000)  # Ceiling division
        chunks_needed.append(chunks)
    
    if chunks_needed:
        avg_chunks = sum(chunks_needed) / len(chunks_needed)
        total_chunks = sum(chunks_needed)
        
        print(f"   Average chunks per case: {avg_chunks:.2f}")
        print(f"   Total chunks for 100 cases: {total_chunks}")
        print(f"   Cases needing 1 chunk: {sum(1 for x in chunks_needed if x == 1)}")
        print(f"   Cases needing 2+ chunks: {sum(1 for x in chunks_needed if x > 1)}")
        print(f"   Cases needing 5+ chunks: {sum(1 for x in chunks_needed if x >= 5)}")

if __name__ == "__main__":
    check_cap_case_lengths()
