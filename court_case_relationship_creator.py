#!/usr/bin/env python3
"""
Court-Case Relationship Creator - Create missing Court ↔ Case relationships
"""

import logging
from typing import Dict, List, Any
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CourtCaseRelationshipCreator:
    """Create Court ↔ Case relationships"""
    
    def __init__(self):
        self.neo4j_client = RealNeo4jClient()
        
        # Court ID mapping for Texas courts
        self.court_mapping = {
            'Supreme Court of Texas': 'tex',
            'Texas Supreme Court': 'tex',
            'Court of Appeals': 'texapp',
            'Texas Court of Appeals': 'texapp',
            'Supreme Court of the United States': 'scotus',
            'U.S. Supreme Court': 'scotus'
        }
    
    def close(self):
        """Close connections"""
        if self.neo4j_client:
            self.neo4j_client.close()
    
    def create_court_case_relationships(self) -> bool:
        """Create bidirectional Court ↔ Case relationships"""
        
        print("🏛️ COURT ↔ CASE RELATIONSHIP CREATION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # 1. Create Case -[FILED_IN]-> Court relationships
                print("📊 1. CREATING CASE -[FILED_IN]-> COURT RELATIONSHIPS")
                
                # Get cases that need court relationships
                result = session.run('''
                    MATCH (c:Case)
                    WHERE c.jurisdiction IS NOT NULL
                    RETURN c.id, c.jurisdiction, c.court_name, c.case_name
                ''')
                
                cases_needing_courts = list(result)
                print(f"   Found {len(cases_needing_courts)} cases needing court relationships")
                
                filed_in_count = 0
                for case in cases_needing_courts:
                    case_id = case['c.id']
                    jurisdiction = case['c.jurisdiction']
                    court_name = case['c.court_name']
                    case_name = case['c.case_name'] or 'N/A'
                    
                    # Determine court ID
                    court_id = self._determine_court_id(jurisdiction, court_name)
                    
                    if court_id:
                        # Create court node and relationship
                        session.run('''
                            MATCH (c:Case {id: $case_id})
                            MERGE (court:Court {id: $court_id})
                            SET court.name = COALESCE(court.name, $court_name),
                                court.jurisdiction = $jurisdiction
                            MERGE (c)-[:FILED_IN]->(court)
                        ''', 
                        case_id=case_id, 
                        court_id=court_id,
                        court_name=court_name or self._get_court_name(court_id),
                        jurisdiction=jurisdiction
                        )
                        
                        filed_in_count += 1
                        logger.debug(f"Created FILED_IN: {case_name} -> {court_id}")
                
                print(f"   ✅ Created {filed_in_count} FILED_IN relationships")
                
                # 2. Create Court -[ISSUED]-> Case relationships
                print("\\n📊 2. CREATING COURT -[ISSUED]-> CASE RELATIONSHIPS")
                
                # Create the reverse relationship (institutional authority)
                result = session.run('''
                    MATCH (c:Case)-[:FILED_IN]->(court:Court)
                    MERGE (court)-[:ISSUED]->(c)
                    RETURN count(*) as issued_count
                ''')
                
                issued_count = result.single()['issued_count']
                print(f"   ✅ Created {issued_count} ISSUED relationships")
                
                # 3. Verify relationships
                print("\\n📊 3. VERIFICATION")
                
                # Count Case -> Court relationships
                result = session.run('''
                    MATCH (c:Case)-[:FILED_IN]->(court:Court)
                    RETURN count(*) as filed_in_count
                ''')
                total_filed_in = result.single()['filed_in_count']
                
                # Count Court -> Case relationships
                result = session.run('''
                    MATCH (court:Court)-[:ISSUED]->(c:Case)
                    RETURN count(*) as issued_count
                ''')
                total_issued = result.single()['issued_count']
                
                print(f"   Case -[FILED_IN]-> Court: {total_filed_in}")
                print(f"   Court -[ISSUED]-> Case: {total_issued}")
                
                # Sample relationships
                result = session.run('''
                    MATCH (c:Case)-[:FILED_IN]->(court:Court)
                    RETURN c.case_name, court.name, court.id
                    LIMIT 3
                ''')
                
                print(f"\\n📄 Sample relationships:")
                for record in result:
                    case_name = record['c.case_name'] or 'N/A'
                    court_name = record['court.name']
                    court_id = record['court.id']
                    print(f"   {case_name} -[FILED_IN]-> {court_name} ({court_id})")
                
                if total_filed_in > 0 and total_issued > 0:
                    print(f"\\n🎉 COURT ↔ CASE RELATIONSHIPS CREATED!")
                    print(f"✅ Bidirectional relationships established")
                    return True
                else:
                    print(f"\\n❌ No relationships created")
                    return False
                    
        except Exception as e:
            print(f"❌ Relationship creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _determine_court_id(self, jurisdiction: str, court_name: str) -> str:
        """Determine court ID from jurisdiction and court name"""
        
        if not jurisdiction:
            return None
        
        jurisdiction = jurisdiction.upper()
        
        # Texas courts
        if jurisdiction == 'TX':
            if court_name:
                court_name_lower = court_name.lower()
                if 'supreme' in court_name_lower:
                    return 'tex'
                elif 'appeal' in court_name_lower:
                    return 'texapp1'  # Default to first district
            return 'tex'  # Default to Texas Supreme Court
        
        # Federal courts
        elif jurisdiction == 'US' or jurisdiction == 'FEDERAL':
            return 'scotus'
        
        # Other states
        elif jurisdiction == 'CA':
            return 'cal'
        elif jurisdiction == 'NY':
            return 'ny'
        elif jurisdiction == 'OH':
            return 'ohio'
        
        # Default: create jurisdiction-based court ID
        return jurisdiction.lower()
    
    def _get_court_name(self, court_id: str) -> str:
        """Get court name from court ID"""
        
        court_names = {
            'tex': 'Supreme Court of Texas',
            'texapp1': 'Court of Appeals for the First District of Texas',
            'scotus': 'Supreme Court of the United States',
            'cal': 'Supreme Court of California',
            'ny': 'New York Court of Appeals',
            'ohio': 'Supreme Court of Ohio'
        }
        
        return court_names.get(court_id, f'Court {court_id.upper()}')
    
    def analyze_court_case_relationships(self) -> Dict[str, Any]:
        """Analyze current Court ↔ Case relationship state"""
        
        print("\\n🔍 COURT ↔ CASE RELATIONSHIP ANALYSIS")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                analysis = {}
                
                # 1. Count relationships by type
                result = session.run('''
                    MATCH ()-[r]->()
                    WHERE type(r) IN ['FILED_IN', 'ISSUED']
                    RETURN type(r) as rel_type, count(r) as count
                ''')
                
                for record in result:
                    rel_type = record['rel_type']
                    count = record['count']
                    analysis[rel_type] = count
                    print(f"   {rel_type}: {count} relationships")
                
                # 2. Court coverage
                result = session.run('''
                    MATCH (court:Court)
                    OPTIONAL MATCH (court)-[:ISSUED]->(c:Case)
                    RETURN court.id, court.name, count(c) as case_count
                    ORDER BY case_count DESC
                ''')
                
                print(f"\\n📊 Court coverage:")
                courts_with_cases = 0
                for record in result:
                    court_id = record['court.id']
                    court_name = record['court.name']
                    case_count = record['case_count']
                    
                    if case_count > 0:
                        courts_with_cases += 1
                        print(f"   {court_id}: {case_count} cases ({court_name})")
                
                analysis['courts_with_cases'] = courts_with_cases
                
                # 3. Case coverage
                result = session.run('''
                    MATCH (c:Case)
                    OPTIONAL MATCH (c)-[:FILED_IN]->(court:Court)
                    RETURN count(c) as total_cases, 
                           sum(CASE WHEN court IS NOT NULL THEN 1 ELSE 0 END) as cases_with_courts
                ''')
                
                record = result.single()
                total_cases = record['total_cases']
                cases_with_courts = record['cases_with_courts']
                coverage_pct = (cases_with_courts / total_cases * 100) if total_cases > 0 else 0
                
                print(f"\\n📊 Case coverage:")
                print(f"   Total cases: {total_cases}")
                print(f"   Cases with court relationships: {cases_with_courts}")
                print(f"   Coverage: {coverage_pct:.1f}%")
                
                analysis['total_cases'] = total_cases
                analysis['cases_with_courts'] = cases_with_courts
                analysis['coverage_percentage'] = coverage_pct
                
                return analysis
                
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {}


def main():
    """Run court-case relationship creation"""
    
    creator = CourtCaseRelationshipCreator()
    
    try:
        # Create relationships
        success = creator.create_court_case_relationships()
        
        # Analyze results
        analysis = creator.analyze_court_case_relationships()
        
        if success:
            print("\\n✅ Court-Case relationship creation successful!")
            return 0
        else:
            print("\\n❌ Court-Case relationship creation failed!")
            return 1
            
    finally:
        creator.close()


if __name__ == "__main__":
    exit(main())
