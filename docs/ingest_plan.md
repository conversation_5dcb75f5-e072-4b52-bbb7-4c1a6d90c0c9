# Texas Laws Personal Injury - Complete Ingest Execution Plan

## 🎯 Overview
Complete reset and re-ingestion of Texas judicial opinions with dual-source processing, practice area classification, and multi-storage deployment.

**Target**: 92,081 cases → ~20,000 practice-area filtered cases across 4 storage systems

## 📋 Task Breakdown

### Phase 1: Execution Planning ⏱️ 30 minutes
**Status**: ✅ COMPLETE
- [x] Document complete execution plan
- [x] Define success criteria and validation metrics
- [x] Estimate costs and runtime for each phase

### Phase 2: Environment Reset Scripts ⏱️ 45 minutes | 💰 $0
**Deliverables**:
- `scripts/reset_supabase.sql` - Truncate cases table, reset sequences
- `scripts/reset_gcs.sh` - Purge case documents folder structure  
- `scripts/reset_neo4j.cypher` - Delete all case nodes and relationships
- `scripts/reset_pinecone.py` - Delete namespace and recreate index
- `scripts/reset_all.py` - Orchestrator script for complete environment reset

**Risk**: Data loss (by design) - requires confirmation before execution

### Phase 3: Dry-Run Validation ⏱️ 2 hours | 💰 $15
**Sample Size**: 100 CAP + 100 CourtListener cases
**Processing Pipeline**:
1. Document type filtering (opinions only)
2. Time window validation (CAP ≤1993, CL ≥1994)
3. Court ID mapping and validation
4. Content deduplication (citation + hash)
5. Practice area classification (LLM)
6. Multi-storage deployment (Supabase + GCS + Neo4j + Pinecone)

**Deliverables**:
- `scripts/dry_run_pipeline.py` - Mini dual-processor test
- `reports/dry_run_YYYYMMDD_HHMMSS.json` - Comprehensive metrics report
- `reports/dry_run_YYYYMMDD_HHMMSS.csv` - Tabular validation data

**Success Criteria**:
- ✅ Zero temporal overlap (CAP ≤1993, CL ≥1994)
- ✅ Zero null court_id or year_filed
- ✅ Duplicate rate <1%
- ✅ Practice area tags on 100% of documents
- ✅ All 4 storage systems populated correctly

### Phase 4: Full Environment Reset ⏱️ 30 minutes | 💰 $0
**Execution**: Only after dry-run approval
- Execute reset scripts from Phase 2
- Verify clean state across all systems
- Prepare for full ingestion

### Phase 5: Full Dual-Source Ingestion ⏱️ 8-12 hours | 💰 $200-300
**Data Sources**:
- **CAP Historical**: 1950-1993 (~2,847 TXSC cases)
- **CourtListener Modern**: 1994-2025 (~89,234 cases, 21 courts)

**Processing Pipeline**:
1. **Document Retrieval**: CAP files + CourtListener API crawl
2. **Document Type Filtering**: Judicial opinions only
3. **Time Window Enforcement**: Clean temporal boundaries
4. **Court ID Mapping**: Standardize to 21 Texas court IDs
5. **Content Deduplication**: Citation + docket + content hash
6. **Practice Area Classification**: LLM-based 7-category tagging
7. **Multi-Storage Deployment**: Parallel writes to all systems

**Deliverables**:
- `scripts/run_full_pipeline.py --full` - Complete ingestion orchestrator
- `reports/full_ingest_YYYYMMDD_HHMMSS.json` - Final metrics report
- Updated database with ~20,000 practice-area filtered cases

**Cost Breakdown**:
- CourtListener API: $50 (rate limits + processing time)
- LLM Classification: $150-200 (Gemini 2.5 Pro for 92k cases)
- Cloud Storage: $10-20 (GCS + compute)
- Neo4j/Pinecone: $20-30 (vector operations)

### Phase 6: Post-Ingest Optimization ⏱️ 3 hours | 💰 $50
**Multi-Label Practice Area Tagging**:
- Secondary classification for edge cases
- Cross-practice area case identification
- Confidence scoring and manual review queue

**Vector Re-indexing**:
- Optimize embeddings for practice area queries
- Update similarity thresholds
- Performance tuning for search operations

**Deliverables**:
- `scripts/tag_practice_areas.py` - Multi-label classification
- `scripts/reindex_vectors.py` - Vector optimization
- Updated README with new data statistics

### Phase 7: Nightly Sync Automation ⏱️ 2 hours | 💰 $0
**GitHub Actions Workflow**:
- Daily CourtListener incremental sync
- Practice area classification for new cases
- Failure alerting and rollback procedures

**Deliverables**:
- `.github/workflows/nightly_cl_sync.yml` - Automated sync
- `scripts/incremental_sync.py` - Delta processing
- Monitoring dashboard integration

## 📊 Expected Outcomes

### Data Volume Projections:
- **Raw Retrieval**: 92,081 judicial opinions
- **Practice Area Filtering**: ~20,000 cases (7 target areas)
- **Storage Distribution**:
  - Supabase: 20,000 case records
  - GCS: 20,000 full-text documents  
  - Neo4j: 20,000 case nodes + ~50,000 relationship nodes
  - Pinecone: 20,000 vector embeddings

### Performance Targets:
- **Ingestion Rate**: 150-200 cases/minute
- **Classification Accuracy**: >95% for practice areas
- **Deduplication Effectiveness**: <1% duplicate rate
- **Search Response Time**: <500ms for vector queries

## 💰 Total Cost Estimate: $265-385

| Phase | Time | Cost | Risk Level |
|-------|------|------|------------|
| Planning | 30m | $0 | Low |
| Reset Scripts | 45m | $0 | Medium |
| Dry-Run | 2h | $15 | Low |
| Full Reset | 30m | $0 | High |
| Full Ingest | 8-12h | $200-300 | Medium |
| Optimization | 3h | $50 | Low |
| Automation | 2h | $0 | Low |
| **TOTAL** | **16-20h** | **$265-385** | **Medium** |

## 🚨 Risk Mitigation

### Data Loss Prevention:
- Database backups before reset
- Staged rollout with checkpoints
- Rollback procedures documented

### Cost Control:
- API rate limiting to prevent overages
- LLM batch processing for efficiency
- Resource monitoring and alerts

### Quality Assurance:
- Dry-run validation before full processing
- Automated testing at each pipeline stage
- Manual spot-checks on final dataset

## ✅ Success Metrics

### Quantitative:
- 20,000 ± 2,000 final cases (practice area filtered)
- <1% duplicate rate across all sources
- >95% practice area classification accuracy
- 100% court ID mapping success
- <500ms average search response time

### Qualitative:
- Clean temporal boundaries (no CAP/CL overlap)
- Comprehensive court coverage (all 21 Texas courts)
- Robust practice area distribution
- Reliable nightly sync operations
- Production-ready monitoring and alerting

## 🎯 Next Steps

1. **Immediate**: Create environment reset scripts (Phase 2)
2. **Day 1**: Execute dry-run validation (Phase 3)
3. **Day 2**: Review dry-run results and approve full ingest
4. **Day 3-4**: Execute full pipeline (Phase 5)
5. **Day 5**: Optimization and automation setup (Phases 6-7)

**Estimated Completion**: 5 business days from approval
