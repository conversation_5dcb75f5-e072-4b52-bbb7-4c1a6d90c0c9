# Neo4j Ingestion Plan for 92,081 Texas Opinions

## 🎯 Goal
Ingest 92,081 Texas judicial opinions into Neo4j following a lean node/relationship model that:
- Keeps storage well under 200 MB
- Is idempotent and rerunnable
- Provides comprehensive validation metrics
- Supports efficient legal research queries

## 🗺️ Target Graph Model

### Node Types
| Label | Count | Properties | Storage Est. |
|-------|-------|------------|--------------|
| `(:Court)` | 21 | `court_id` (PK), `name`, `level`, `state='TX'` | ~2 KB |
| `(:PA)` | 7 | `pa_id` (PK), `name` | ~1 KB |
| `(:Opinion)` | 92,081 | `opinion_id` (PK), `court_id`, `year`, `practice_areas[]`, `citation`, `docket`, `gcs_uri` | ~45 MB |

### Relationship Types
| Relationship | Count | Properties | Storage Est. |
|--------------|-------|------------|--------------|
| `(:Court)-[:HEARS_BEFORE]->(:Opinion)` | 92,081 | None | ~7 MB |
| `(:Opinion)-[:HAS_PA]->(:PA)` | ~184,162 (avg 2 PAs per opinion) | None | ~14 MB |
| `(:Opinion)-[:CITES]->(:Opinion)` | ~250,000 | None | ~20 MB |

**Total Estimated Storage: ~86 MB** (well under 200 MB target)

## 📄 CSV Schema Design

### 1. Courts (`courts.csv`)
```csv
court_id,name,level,state
TXSC,Texas Supreme Court,state_supreme,TX
TCCA,Texas Court of Criminal Appeals,state_supreme,TX
TXCOA01,Texas Court of Appeals First District,state_appellate,TX
TXCOA02,Texas Court of Appeals Second District,state_appellate,TX
TXCOA03,Texas Court of Appeals Third District,state_appellate,TX
TXCOA04,Texas Court of Appeals Fourth District,state_appellate,TX
TXCOA05,Texas Court of Appeals Fifth District,state_appellate,TX
TXCOA06,Texas Court of Appeals Sixth District,state_appellate,TX
TXCOA07,Texas Court of Appeals Seventh District,state_appellate,TX
TXCOA08,Texas Court of Appeals Eighth District,state_appellate,TX
TXCOA09,Texas Court of Appeals Ninth District,state_appellate,TX
TXCOA10,Texas Court of Appeals Tenth District,state_appellate,TX
TXCOA11,Texas Court of Appeals Eleventh District,state_appellate,TX
TXCOA12,Texas Court of Appeals Twelfth District,state_appellate,TX
TXCOA13,Texas Court of Appeals Thirteenth District,state_appellate,TX
TXCOA14,Texas Court of Appeals Fourteenth District,state_appellate,TX
TXND,U.S. District Court Northern District of Texas,federal_district,TX
TXSD,U.S. District Court Southern District of Texas,federal_district,TX
TXED,U.S. District Court Eastern District of Texas,federal_district,TX
TXWD,U.S. District Court Western District of Texas,federal_district,TX
CA5,U.S. Court of Appeals Fifth Circuit,federal_appellate,TX
```

### 2. Practice Areas (`practice_areas.csv`)
```csv
pa_id,name
personal_injury,Personal Injury
criminal_defense,Criminal Defense
family_law,Family Law
estate_planning,Estate Planning & Probate
immigration_law,Immigration Law
real_estate,Real Estate
bankruptcy,Bankruptcy
```

### 3. Opinions (`opinions.csv`)
```csv
opinion_id,court_id,year,practice_areas,citation,docket,gcs_uri
cap_tx_1985_001,TXSC,1985,"personal_injury",123 S.W.2d 456,85-1234,gs://texas-laws-cases/TXSC/1985/cap_tx_1985_001.json
cl_tx_2020_001,TXND,2020,"personal_injury;criminal_defense",456 F.3d 789,4:20-cv-01234,gs://texas-laws-cases/TXND/2020/cl_tx_2020_001.json
cap_tx_1991_003,TXSC,1991,"family_law",789 S.W.2d 123,91-5678,gs://texas-laws-cases/TXSC/1991/cap_tx_1991_003.json
```

### 4. Court-Opinion Relationships (`court_hears.csv`)
```csv
:START_ID(Court),opinion_id,:END_ID(Opinion)
TXSC,cap_tx_1985_001,cap_tx_1985_001
TXND,cl_tx_2020_001,cl_tx_2020_001
TXSC,cap_tx_1991_003,cap_tx_1991_003
```

### 5. Opinion-Practice Area Relationships (`opinion_pa.csv`)
```csv
:START_ID(Opinion),:END_ID(PA)
cap_tx_1985_001,personal_injury
cl_tx_2020_001,personal_injury
cl_tx_2020_001,criminal_defense
cap_tx_1991_003,family_law
```

### 6. Citation Relationships (`citations.csv`)
```csv
:START_ID(Opinion),:END_ID(Opinion)
cl_tx_2020_001,cap_tx_1985_001
cap_tx_1991_003,cap_tx_1985_001
```

## 🛠️ Import Method: Option B - Batched LOAD CSV

**Selected Method**: Batched `LOAD CSV` with `USING PERIODIC COMMIT 10000`

**Rationale**:
- **Flexibility**: Can run against live AuraDB instance
- **Idempotency**: Easy to implement `MERGE` operations for reruns
- **Monitoring**: Real-time progress tracking and error handling
- **Incremental**: Can process in stages if needed
- **Size Appropriate**: 92k records manageable with batched approach

**Alternative Rejected**: `neo4j-admin import` requires offline database and full rebuild

## 📁 Staging Folder Layout

```
data/neo4j/csv/
├── nodes/
│   ├── courts.csv
│   ├── practice_areas.csv
│   └── opinions.csv
├── relationships/
│   ├── court_hears.csv
│   ├── opinion_pa.csv
│   └── citations.csv
└── scripts/
    ├── 01_create_constraints.cypher
    ├── 02_import_nodes.cypher
    ├── 03_import_relationships.cypher
    └── 04_validate_import.cypher
```

## 🔒 Constraints & Indexes

### Constraints (Uniqueness)
```cypher
CREATE CONSTRAINT court_id_unique FOR (c:Court) REQUIRE c.court_id IS UNIQUE;
CREATE CONSTRAINT pa_id_unique FOR (pa:PA) REQUIRE pa.pa_id IS UNIQUE;
CREATE CONSTRAINT opinion_id_unique FOR (o:Opinion) REQUIRE o.opinion_id IS UNIQUE;
```

### Indexes (Performance)
```cypher
CREATE INDEX court_level_idx FOR (c:Court) ON (c.level);
CREATE INDEX opinion_court_idx FOR (o:Opinion) ON (o.court_id);
CREATE INDEX opinion_year_idx FOR (o:Opinion) ON (o.year);
CREATE INDEX opinion_pa_gin FOR (o:Opinion) ON (o.practice_areas);
CREATE INDEX opinion_citation_idx FOR (o:Opinion) ON (o.citation);
```

## 📊 Validation Queries

### 1. Node Counts
```cypher
// Expected: 21 courts
MATCH (c:Court) RETURN count(c) as court_count;

// Expected: 7 practice areas  
MATCH (pa:PA) RETURN count(pa) as pa_count;

// Expected: 92,081 opinions
MATCH (o:Opinion) RETURN count(o) as opinion_count;
```

### 2. Relationship Counts
```cypher
// Expected: 92,081 court-opinion relationships
MATCH ()-[r:HEARS_BEFORE]->() RETURN count(r) as hears_count;

// Expected: ~184,162 opinion-PA relationships
MATCH ()-[r:HAS_PA]->() RETURN count(r) as pa_rel_count;

// Expected: ~250,000 citation relationships
MATCH ()-[r:CITES]->() RETURN count(r) as citation_count;
```

### 3. Data Quality Checks
```cypher
// Top courts by opinion count
MATCH (c:Court)-[:HEARS_BEFORE]->(o:Opinion)
RETURN c.court_id, c.name, count(o) as opinion_count
ORDER BY opinion_count DESC LIMIT 5;

// Practice area distribution
MATCH (o:Opinion)-[:HAS_PA]->(pa:PA)
RETURN pa.name, count(o) as opinion_count
ORDER BY opinion_count DESC;

// Year distribution
MATCH (o:Opinion)
RETURN o.year, count(o) as opinion_count
ORDER BY o.year DESC LIMIT 10;

// Orphaned nodes check (should be 0)
MATCH (o:Opinion) WHERE NOT (o)<-[:HEARS_BEFORE]-()
RETURN count(o) as orphaned_opinions;
```

### 4. Storage Validation
```cypher
// Database size and statistics
CALL db.stats.retrieve('GRAPH COUNTS') YIELD data
RETURN data.nodes as total_nodes, data.relationships as total_relationships;

// Index status
SHOW INDEXES YIELD name, state, populationPercent
WHERE state <> 'ONLINE' OR populationPercent < 100.0;
```

## 💾 Disk Footprint Estimate

### Storage Breakdown
- **Nodes**: ~45 MB (92k opinions + metadata)
- **Relationships**: ~41 MB (426k total relationships)
- **Indexes**: ~15 MB (5 indexes on key properties)
- **Overhead**: ~10 MB (Neo4j metadata, logs)

**Total Estimated**: ~111 MB (well under 200 MB target)

### Memory Requirements
- **Heap**: 2GB recommended for import process
- **Page Cache**: 1GB for optimal query performance
- **Import Buffer**: 500MB for batch processing

## 🔄 Rollback Instructions

### Complete Database Reset
```cypher
// 1. Delete all relationships
MATCH ()-[r]-() DELETE r;

// 2. Delete all nodes
MATCH (n) DELETE n;

// 3. Drop constraints
DROP CONSTRAINT court_id_unique IF EXISTS;
DROP CONSTRAINT pa_id_unique IF EXISTS;
DROP CONSTRAINT opinion_id_unique IF EXISTS;

// 4. Drop indexes
DROP INDEX court_level_idx IF EXISTS;
DROP INDEX opinion_court_idx IF EXISTS;
DROP INDEX opinion_year_idx IF EXISTS;
DROP INDEX opinion_pa_gin IF EXISTS;
DROP INDEX opinion_citation_idx IF EXISTS;
```

### Selective Rollback (Opinions Only)
```cypher
// Remove opinion-related data while preserving courts/PAs
MATCH (o:Opinion) DETACH DELETE o;
```

## 🚀 Import Execution Plan

### Phase 1: Preparation (5 minutes)
1. Generate CSV files from Supabase data
2. Create staging directory structure
3. Validate CSV format and row counts

### Phase 2: Schema Setup (2 minutes)
1. Create constraints for data integrity
2. Create indexes for query performance
3. Verify schema readiness

### Phase 3: Node Import (15 minutes)
1. Import courts (21 records)
2. Import practice areas (7 records)  
3. Import opinions (92,081 records)

### Phase 4: Relationship Import (25 minutes)
1. Import court-opinion relationships (92,081)
2. Import opinion-PA relationships (~184,162)
3. Import citation relationships (~250,000)

### Phase 5: Validation (5 minutes)
1. Run all validation queries
2. Verify counts and data quality
3. Generate import success report

**Total Estimated Time**: 52 minutes

## ✅ Success Criteria

### Quantitative Metrics
- **Node Count**: 92,109 total nodes (21 + 7 + 92,081)
- **Relationship Count**: ~426,243 total relationships
- **Storage Size**: <200 MB total database size
- **Import Speed**: >1,500 opinions/minute
- **Data Integrity**: 0 orphaned nodes, 100% constraint compliance

### Qualitative Metrics
- **Query Performance**: <100ms for basic court/year queries
- **Index Coverage**: 100% index population
- **Idempotency**: Successful rerun without duplicates
- **Validation**: All validation queries return expected results

## 🎯 Ready for Approval

This plan provides:
- ✅ Lean node/relationship model (3 node types, 3 relationship types)
- ✅ Comprehensive CSV schema with sample data
- ✅ Idempotent import process using MERGE operations
- ✅ Storage estimate well under 200 MB target
- ✅ Detailed validation queries for sign-off
- ✅ Complete rollback procedures

**Awaiting approval to proceed with CSV generation and import execution.**
