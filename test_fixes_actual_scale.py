#!/usr/bin/env python3
"""
Test fixes on actual scale CAP data
Verify checkpoint system and source field work with real production volumes
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import test components
from test_cap_enhanced_tracking import MockGCSClient, MockPineconeClient, MockNeo4jClient
from production_cap_processor import ProductionCAPProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'test_fixes_scale_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


async def test_fixes_actual_scale():
    """Test fixes on actual scale CAP data"""
    
    logger.info("🧪 TESTING FIXES ON ACTUAL SCALE CAP DATA")
    logger.info("=" * 80)
    
    load_dotenv()
    
    # Setup clients
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    # Clean existing test data
    logger.info("🧹 Cleaning existing test data...")
    try:
        supabase.table('cases').delete().like('batch_id', 'scale_test_%').execute()
        logger.info("✅ Existing test data cleaned")
    except Exception as e:
        logger.warning(f"Warning cleaning test data: {e}")
    
    # Initialize processor with production-like settings
    processor = ProductionCAPProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client,
        batch_size=1000,  # Production batch size
        chunk_size=5000   # Reasonable chunk size for testing
    )
    
    logger.info("🎯 Testing with production-like settings:")
    logger.info(f"   📦 Batch Size: 1000 cases")
    logger.info(f"   🔄 Chunk Size: 5000 cases")
    logger.info(f"   📁 Max Files: 3 files")
    
    # Test 1: Initial processing (no resume)
    logger.info("\n" + "="*60)
    logger.info("📊 TEST 1: INITIAL PROCESSING (NO RESUME)")
    logger.info("="*60)
    
    start_time = datetime.now()
    
    try:
        results_1 = await processor.process_cap_production(
            jurisdiction='tx',
            max_files=3,  # Process 3 files for substantial test
            resume=False,  # Fresh start
            test_mode=False  # Full processing
        )
        
        end_time = datetime.now()
        duration_1 = end_time - start_time
        
        logger.info(f"📊 Test 1 Results: {results_1}")
        logger.info(f"⏱️ Duration: {duration_1}")
        
        if not results_1['success']:
            logger.error("❌ Test 1 failed - aborting")
            return False
        
        # Verify source field correctness
        logger.info("\n🔍 VERIFYING SOURCE FIELD CORRECTNESS:")
        source_check = supabase.table('cases').select('source', count='exact').like('batch_id', 'cap_tx_%').execute()
        
        cap_cases = supabase.table('cases').select('source', count='exact').eq('source', 'caselaw_access_project').like('batch_id', 'cap_tx_%').execute()
        cl_cases = supabase.table('cases').select('source', count='exact').eq('source', 'courtlistener').like('batch_id', 'cap_tx_%').execute()
        
        total_cases = source_check.count or 0
        cap_count = cap_cases.count or 0
        cl_count = cl_cases.count or 0
        
        logger.info(f"   Total CAP test cases: {total_cases}")
        logger.info(f"   With correct source (caselaw_access_project): {cap_count}")
        logger.info(f"   With incorrect source (courtlistener): {cl_count}")
        
        if cap_count == total_cases and cl_count == 0:
            logger.info("✅ Source field: CORRECT")
        else:
            logger.error("❌ Source field: INCORRECT")
            return False
        
        # Verify cross-system tracking
        logger.info("\n🔗 VERIFYING CROSS-SYSTEM TRACKING:")
        tracking_check = supabase.table('cases').select(
            'id, gcs_path, pinecone_id, neo4j_node_id, word_count', count='exact'
        ).like('batch_id', 'cap_tx_%').execute()
        
        cases_with_gcs = len([c for c in tracking_check.data if c.get('gcs_path')])
        cases_with_pinecone = len([c for c in tracking_check.data if c.get('pinecone_id')])
        cases_with_neo4j = len([c for c in tracking_check.data if c.get('neo4j_node_id')])
        cases_with_vectors = len([c for c in tracking_check.data if c.get('word_count', 0) > 0])
        
        logger.info(f"   Cases with GCS paths: {cases_with_gcs}/{total_cases} ({cases_with_gcs/max(total_cases,1):.1%})")
        logger.info(f"   Cases with Pinecone IDs: {cases_with_pinecone}/{total_cases} ({cases_with_pinecone/max(total_cases,1):.1%})")
        logger.info(f"   Cases with Neo4j nodes: {cases_with_neo4j}/{total_cases} ({cases_with_neo4j/max(total_cases,1):.1%})")
        logger.info(f"   Cases with vectors: {cases_with_vectors}/{total_cases} ({cases_with_vectors/max(total_cases,1):.1%})")
        
        cross_system_perfect = (cases_with_gcs == cases_with_pinecone == cases_with_neo4j == cases_with_vectors == total_cases)
        
        if cross_system_perfect:
            logger.info("✅ Cross-system tracking: PERFECT")
        else:
            logger.error("❌ Cross-system tracking: INCONSISTENT")
            return False
        
        # Test 2: Resume capability
        logger.info("\n" + "="*60)
        logger.info("📊 TEST 2: RESUME CAPABILITY")
        logger.info("="*60)
        
        # Simulate interruption by processing more files with resume=True
        logger.info("🔄 Testing resume from checkpoint...")
        
        results_2 = await processor.process_cap_production(
            jurisdiction='tx',
            max_files=5,  # Process 2 more files
            resume=True,  # Resume from checkpoint
            test_mode=False
        )
        
        logger.info(f"📊 Test 2 Results: {results_2}")
        
        if results_2['success']:
            logger.info("✅ Resume capability: WORKING")
        else:
            logger.error("❌ Resume capability: FAILED")
            return False
        
        # Final verification
        logger.info("\n" + "="*60)
        logger.info("📊 FINAL VERIFICATION")
        logger.info("="*60)
        
        final_check = supabase.table('cases').select('source', count='exact').like('batch_id', 'cap_tx_%').execute()
        final_total = final_check.count or 0
        
        final_cap_cases = supabase.table('cases').select('source', count='exact').eq('source', 'caselaw_access_project').like('batch_id', 'cap_tx_%').execute()
        final_cap_count = final_cap_cases.count or 0
        
        logger.info(f"📊 Final Results:")
        logger.info(f"   Total cases processed: {final_total}")
        logger.info(f"   Cases with correct source: {final_cap_count}")
        logger.info(f"   Files processed: {results_1['files_processed'] + results_2['files_processed']}")
        logger.info(f"   Batches completed: {results_1['batches_completed'] + results_2['batches_completed']}")
        logger.info(f"   Total errors: {results_1['errors'] + results_2['errors']}")
        
        # Success criteria
        success_criteria = {
            'source_field_correct': final_cap_count == final_total,
            'cross_system_tracking': cross_system_perfect,
            'resume_capability': results_2['success'],
            'no_errors': (results_1['errors'] + results_2['errors']) == 0,
            'substantial_scale': final_total >= 1000  # At least 1000 cases processed
        }
        
        logger.info(f"\n🎯 SUCCESS CRITERIA:")
        for criterion, passed in success_criteria.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"   {criterion}: {status}")
        
        all_passed = all(success_criteria.values())
        
        if all_passed:
            logger.info("\n🎉 FIXES VERIFIED ON ACTUAL SCALE DATA!")
            logger.info("✅ Source field fix: Working at scale")
            logger.info("✅ Checkpoint system: Working at scale")
            logger.info("✅ Cross-system tracking: Perfect at scale")
            logger.info("✅ Resume capability: Working at scale")
            logger.info("✅ Production ready: All systems go!")
        else:
            logger.error("\n❌ FIXES FAILED AT SCALE")
            logger.error("❌ Issues found - review logs for details")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"💥 Scale test failed with error: {e}")
        return False


async def main():
    """Main test execution"""
    try:
        success = await test_fixes_actual_scale()
        
        if success:
            print("\n🎉 FIXES VERIFIED ON ACTUAL SCALE DATA!")
            print("✅ Ready for full production processing")
            return 0
        else:
            print("\n❌ FIXES FAILED AT SCALE")
            print("❌ Review logs for issues")
            return 1
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
