#!/usr/bin/env python3
"""
Production CourtListener Pipeline Runner
Executes the real ChunkedCourtListenerProcessor with production databases for 10 cases
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import production components
from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'production_courtlistener_run_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def setup_production_clients():
    """Setup all production database clients"""
    load_dotenv()
    
    # Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    if not supabase_url or not supabase_key:
        raise ValueError("Missing Supabase credentials")
    supabase = create_client(supabase_url, supabase_key)
    logger.info("✅ Supabase client initialized")
    
    # GCS client
    gcs_client = GCSConnector()
    logger.info("✅ GCS client initialized")
    
    # Pinecone client
    pinecone_client = RealPineconeClient()
    logger.info("✅ Pinecone client initialized")
    
    # Neo4j client
    neo4j_client = RealNeo4jClient()
    logger.info("✅ Neo4j client initialized")
    
    return supabase, gcs_client, pinecone_client, neo4j_client

async def run_production_pipeline():
    """Run the production CourtListener pipeline for 10 cases"""
    logger.info("🚀 STARTING PRODUCTION COURTLISTENER PIPELINE")
    logger.info("=" * 80)
    
    # Get API key
    load_dotenv()
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        raise ValueError("COURTLISTENER_API_KEY not found in environment")
    
    # Setup production clients
    logger.info("🔧 Setting up production database clients...")
    supabase, gcs_client, pinecone_client, neo4j_client = await setup_production_clients()
    
    # Run the production pipeline
    logger.info("⚡ Executing ChunkedCourtListenerProcessor with REAL DATA...")
    logger.info("📊 Target: 10 real CourtListener cases with People API integration")
    
    async with ChunkedCourtListenerProcessor(
        api_key=api_key,
        supabase_client=supabase,
        gcs_client=gcs_client,
        pinecone_client=pinecone_client,
        neo4j_client=neo4j_client,
        chunk_size=30,  # Process exactly 30 cases
        batch_size=10   # Process in batches of 10
    ) as processor:
        
        logger.info("🎯 ChunkedCourtListenerProcessor initialized with production clients")
        
        # Process Texas jurisdiction for 2024 (recent cases more likely to have author URLs)
        results = await processor.process_jurisdiction_chunked(
            jurisdiction='tx',
            start_year=2024,
            end_year=2024,
            resume=False  # Fresh run
        )
        
        logger.info("✅ Production pipeline completed!")
        logger.info(f"📊 Results: {results}")
        
        return results

async def validate_production_results():
    """Validate that the production run worked correctly"""
    logger.info("🔍 VALIDATING PRODUCTION RESULTS")
    logger.info("=" * 50)
    
    # Check Supabase
    load_dotenv()
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    # Count cases in Supabase
    response = supabase.table('cases').select('*', count='exact').execute()
    case_count = response.count
    logger.info(f"📊 Supabase cases stored: {case_count}")
    
    # Check for judge data
    judge_response = supabase.table('cases').select('judge_name, judge_metadata').not_.is_('judge_name', 'null').execute()
    judges_found = len(judge_response.data)
    logger.info(f"👨‍⚖️ Cases with judge data: {judges_found}")
    
    # Check Pinecone
    pinecone_client = RealPineconeClient()
    stats = pinecone_client.index.describe_index_stats()
    vector_count = stats.total_vector_count
    logger.info(f"🔢 Pinecone vectors stored: {vector_count}")
    
    # Check Neo4j
    neo4j_client = RealNeo4jClient()
    with neo4j_client.driver.session() as session:
        result = session.run("MATCH (n) RETURN count(n) as node_count")
        node_count = result.single()["node_count"]
        logger.info(f"🕸️ Neo4j nodes created: {node_count}")
    
    logger.info("✅ Production validation completed!")
    
    return {
        'supabase_cases': case_count,
        'cases_with_judges': judges_found,
        'pinecone_vectors': vector_count,
        'neo4j_nodes': node_count
    }

async def main():
    """Main execution function"""
    try:
        # Run production pipeline
        pipeline_results = await run_production_pipeline()
        
        # Validate results
        validation_results = await validate_production_results()
        
        # Final summary
        logger.info("🎉 PRODUCTION PIPELINE SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Pipeline Results: {pipeline_results}")
        logger.info(f"Validation Results: {validation_results}")
        
        # Check if People API was used
        if validation_results['cases_with_judges'] > 0:
            logger.info("✅ SUCCESS: People API integration working - judges found!")
        else:
            logger.info("⚠️ WARNING: No judges found - may need cases with author URLs")
        
        logger.info("🚀 PRODUCTION PIPELINE COMPLETED SUCCESSFULLY!")
        
    except Exception as e:
        logger.error(f"❌ Production pipeline failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
