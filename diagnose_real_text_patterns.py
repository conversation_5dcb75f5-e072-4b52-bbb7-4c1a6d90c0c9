#!/usr/bin/env python3
"""
Diagnose Real Text Patterns
Analyze why full name patterns aren't matching real data
"""

import requests
import re
from dotenv import load_dotenv
import os

def analyze_real_courtlistener_text():
    """Analyze real CourtListener text to see why patterns aren't matching"""
    
    print("🔍 ANALYZING REAL COURTLISTENER TEXT PATTERNS")
    print("=" * 60)
    
    load_dotenv()
    cl_api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not cl_api_key:
        print("❌ No CourtListener API key found")
        return
    
    headers = {
        'Authorization': f'Token {cl_api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    try:
        # Get a recent case
        response = requests.get(
            "https://www.courtlistener.com/api/rest/v4/opinions/",
            headers=headers,
            params={
                'court': 'ca5,ca2,scotus',
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 3
            },
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return
        
        results = response.json().get('results', [])
        
        for i, case in enumerate(results[:1], 1):  # Just analyze first case
            case_name = case.get('case_name', 'Unknown')
            text = case.get('plain_text', '') or case.get('html', '')
            
            if not text:
                continue
            
            print(f"\n📄 CASE {i}: {case_name}")
            print(f"   Text length: {len(text):,} characters")
            print(f"   Court: {case.get('court', 'Unknown')}")
            
            # Show first 2000 characters to see the structure
            print(f"\n📝 TEXT SAMPLE (first 2000 chars):")
            print("=" * 40)
            print(text[:2000])
            print("=" * 40)
            
            # Look for judge-related text
            print(f"\n🔍 SEARCHING FOR JUDGE PATTERNS:")
            
            # Search for common judge patterns
            judge_patterns_to_test = [
                r'Judge\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*',
                r'Justice\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*',
                r'Circuit\s+Judge\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*',
                r'District\s+Judge\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*',
                r'[A-Z][a-z]+,\s+(?:Circuit|District)\s+Judge',
                r'[A-Z][a-z]+,\s+J\.',
                r'Before\s+[^.]*Judge',
                r'delivered\s+(?:the\s+)?opinion',
                r'wrote\s+(?:the\s+)?(?:majority|dissenting|concurring)',
            ]
            
            for pattern in judge_patterns_to_test:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    print(f"   ✅ Pattern '{pattern}' found {len(matches)} matches:")
                    for match in matches[:3]:  # Show first 3
                        print(f"      - {match}")
                else:
                    print(f"   ❌ Pattern '{pattern}' - no matches")
            
            # Look for specific judge name patterns
            print(f"\n🔍 SEARCHING FOR SPECIFIC JUDGE NAMES:")
            
            # Look for names that might be judges
            name_patterns = [
                r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*)',
                r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*)',
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*),\s+(?:Circuit|District)\s+Judge',
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*),\s+J\.',
            ]
            
            all_found_names = []
            
            for pattern in name_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    print(f"   ✅ Name pattern found {len(matches)} matches:")
                    for match in matches[:3]:
                        name = match if isinstance(match, str) else match[0] if match else ""
                        if name:
                            all_found_names.append(name)
                            words = len(name.split())
                            word_status = "✅ Full" if words > 1 else "❌ Partial"
                            print(f"      - '{name}' ({word_status}, {words} words)")
            
            if not all_found_names:
                print(f"   ❌ No judge names found with standard patterns")
                
                # Try broader search
                print(f"\n🔍 BROADER SEARCH FOR JUDGE INDICATORS:")
                
                # Look for any mention of judge/justice
                judge_mentions = re.findall(r'.{0,50}(?:judge|justice).{0,50}', text, re.IGNORECASE)
                
                if judge_mentions:
                    print(f"   Found {len(judge_mentions)} judge/justice mentions:")
                    for mention in judge_mentions[:5]:
                        clean_mention = mention.replace('\n', ' ').strip()
                        print(f"      - ...{clean_mention}...")
                else:
                    print(f"   ❌ No judge/justice mentions found")
            
            break  # Only analyze first case
            
    except Exception as e:
        print(f"❌ Error: {e}")


def test_our_patterns_on_real_text():
    """Test our current patterns on real text"""
    
    print(f"\n🧪 TESTING OUR PATTERNS ON REAL TEXT")
    print("=" * 60)
    
    # Sample real text that we know has judges but our patterns don't catch
    # This would be from the actual CourtListener response
    sample_real_text = """
    No. 23-40582
    
    UNITED STATES COURT OF APPEALS
    FOR THE FIFTH CIRCUIT
    
    HARRIS v. UNITED STATES
    
    Appeal from the United States District Court
    for the Southern District of Texas
    
    Before DIAZ, HARRIS, and WILSON, Circuit Judges.
    
    PER CURIAM:
    
    Appellant challenges the district court's denial of his motion.
    The district court did not err. We AFFIRM.
    """
    
    print(f"📝 SAMPLE REAL TEXT:")
    print("=" * 40)
    print(sample_real_text)
    print("=" * 40)
    
    # Our current patterns
    our_patterns = [
        r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored)',
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored|presiding)',
        r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+),\s+(?:Circuit|District)\s+Judge\s*:',
        r'Before[^.]*?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)[^,]*,\s+(?:Circuit\s+)?Judge',
        r'([A-Z][a-z]+),\s+(?:Circuit|District)\s+Judge\s*:',  # Single name fallback
    ]
    
    print(f"\n🔍 TESTING OUR PATTERNS:")
    
    for i, pattern in enumerate(our_patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.findall(pattern, sample_real_text, re.IGNORECASE)
        
        if matches:
            print(f"      ✅ Found {len(matches)} matches:")
            for match in matches:
                name = match if isinstance(match, str) else match[0] if match else ""
                if name:
                    words = len(name.split())
                    word_status = "✅ Full" if words > 1 else "❌ Partial"
                    print(f"         - '{name}' ({word_status}, {words} words)")
        else:
            print(f"      ❌ No matches")
    
    print(f"\n💡 ANALYSIS:")
    print(f"   The real text has 'Before DIAZ, HARRIS, and WILSON, Circuit Judges.'")
    print(f"   But our patterns expect action words like 'delivered', 'wrote', 'authored'")
    print(f"   Real federal court opinions often just list judges without action words")
    
    print(f"\n🔧 SUGGESTED IMPROVEMENTS:")
    print(f"   1. Add pattern for 'Before [NAMES], Circuit Judges' format")
    print(f"   2. Add pattern for 'PER CURIAM' opinions")
    print(f"   3. Make patterns more flexible for real court document formats")


def main():
    """Run diagnostic analysis"""
    
    print("🧪 REAL TEXT PATTERN DIAGNOSIS")
    print("=" * 80)
    print("🎯 Analyzing why full name patterns don't match real data")
    
    # Analyze real CourtListener text
    analyze_real_courtlistener_text()
    
    # Test our patterns on known real text
    test_our_patterns_on_real_text()
    
    print(f"\n🎯 CONCLUSION:")
    print(f"   Real court documents have different formats than our mock data")
    print(f"   Need to add patterns for actual federal court opinion formats")
    print(f"   Focus on 'Before [JUDGES], Circuit Judges' and 'PER CURIAM' patterns")


if __name__ == "__main__":
    main()
