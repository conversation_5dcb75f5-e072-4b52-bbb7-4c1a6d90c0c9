#!/usr/bin/env python3
"""
Test Pipeline Fix
Test the fixed pipeline that retrieves text from GCS when not in Neo4j
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PipelineFixTest:
    """Test the fixed pipeline for text retrieval"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"pipeline_fix_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_single_courtlistener_case(self) -> dict:
        """Fetch a single CourtListener case for testing"""
        
        print(f"🌐 FETCHING SINGLE COURTLISTENER CASE FOR PIPELINE TEST")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return None
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        try:
            # Get a single case
            response = requests.get(
                f"{self.cl_base_url}/opinions/",
                headers=headers,
                params={
                    'court': 'ca5',
                    'filed_after': '2020-01-01',
                    'ordering': '-date_filed',
                    'page_size': 1
                },
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ API request failed: {response.status_code}")
                return None
            
            results = response.json().get('results', [])
            
            if not results:
                print("❌ No results from API")
                return None
            
            case = results[0]
            text = case.get('plain_text', '') or case.get('html', '')
            
            if not text:
                print("❌ No text content in API response")
                return None
            
            # Create processing format
            case_data = {
                'id': f"pipeline_fix_{case.get('id', 'unknown')}",
                'source': 'courtlistener',
                'case_name': case.get('case_name', 'Unknown'),
                'court': case.get('court', ''),
                'court_name': f"Court {case.get('court', '')}",
                'date_filed': case.get('date_filed', ''),
                'jurisdiction': 'US',
                'text': text,
                'precedential_status': case.get('precedential_status', 'Unknown')
            }
            
            print(f"✅ FETCHED CASE: {case_data['case_name']}")
            print(f"   Court: {case_data['court']}")
            print(f"   Text length: {len(text):,} characters")
            print(f"   Has judge indicators: {'judge' in text.lower() or 'justice' in text.lower()}")
            
            return case_data
            
        except Exception as e:
            print(f"❌ Error fetching case: {e}")
            return None
    
    async def test_pipeline_fix(self) -> bool:
        """Test the pipeline fix on a single real case"""
        
        print(f"\n🔄 TESTING PIPELINE FIX")
        print("=" * 60)
        
        try:
            # Fetch a single real case
            test_case = self.fetch_single_courtlistener_case()
            
            if not test_case:
                print(f"❌ No test case available")
                return False
            
            print(f"\n📊 Processing 1 real case through fixed pipeline...")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            result = await processor.process_coherent_batch(
                raw_cases=[test_case],
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Now test judge extraction specifically
            return await self.test_judge_extraction_fix(test_case['id'])
            
        except Exception as e:
            print(f"❌ Pipeline fix test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_judge_extraction_fix(self, case_id: str) -> bool:
        """Test judge extraction with the fixed pipeline"""
        
        print(f"\n🧪 TESTING JUDGE EXTRACTION FIX")
        print("=" * 60)
        
        try:
            from judge_relationship_enhancer import JudgeRelationshipEnhancer
            
            # Test judge extraction directly
            enhancer = JudgeRelationshipEnhancer()
            
            try:
                print(f"📄 Testing judge extraction for case: {case_id}")
                
                # This should now use the fixed _get_case_data method
                success = enhancer.enhance_judge_relationships([case_id])
                
                print(f"\n📊 JUDGE EXTRACTION RESULT:")
                print(f"   Success: {success}")
                
                if success:
                    # Check what judges were found
                    with self.neo4j_client.driver.session() as session:
                        result = session.run('''
                            MATCH (j:Judge)<-[r]-(c:Case {id: $case_id})
                            RETURN j.name as judge_name, type(r) as relationship_type
                        ''', case_id=case_id)
                        
                        judges = list(result)
                        
                        print(f"   Judges found: {len(judges)}")
                        for judge in judges:
                            print(f"      - {judge['judge_name']} ({judge['relationship_type']})")
                        
                        return len(judges) > 0
                else:
                    print(f"   ❌ Judge extraction failed")
                    return False
                
            finally:
                enhancer.close()
                
        except Exception as e:
            print(f"❌ Judge extraction test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP PIPELINE FIX TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run pipeline fix test"""
    
    print("🔧 PIPELINE FIX TEST")
    print("=" * 80)
    print("🎯 Testing fixed pipeline that retrieves text from GCS when not in Neo4j")
    
    test = PipelineFixTest()
    
    try:
        # Run the test
        success = await test.test_pipeline_fix()
        
        if success:
            print(f"\n🎉 PIPELINE FIX: SUCCESS!")
            print(f"✅ Text retrieval from GCS working")
            print(f"✅ Judge extraction working on real CourtListener data")
            print(f"✅ Ready for complete enhanced validation")
            return True
        else:
            print(f"\n❌ PIPELINE FIX: FAILED!")
            print(f"❌ Pipeline still not working correctly")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
