#!/usr/bin/env python3
"""
Unified Enhanced Processor
Processes both CourtListener and CAP data with consistent cross-system tracking
"""

import asyncio
import logging
import os
import sys
import json
import gzip
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor
from src.processing.atomic_storage_pipeline import AtomicStoragePipeline
from src.processing.cross_system_validator import CrossSystemValidator

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'unified_enhanced_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class UnifiedCaseDocument:
    """
    Unified case document format for both CourtListener and CAP data
    Ensures consistent metadata and processing philosophy
    """
    
    def __init__(
        self,
        # Core identifiers (consistent across sources)
        id: str,
        source: str,  # 'courtlistener' or 'caselaw_access_project'
        
        # Case metadata (unified format)
        case_name: str = '',
        court_id: str = '',
        court_name: str = '',
        jurisdiction: str = 'TX',
        date_filed: Optional[datetime] = None,
        docket_number: str = '',
        
        # Content
        text_content: str = '',
        
        # Source-specific IDs (for traceability)
        source_id: str = '',
        cluster_id: str = '',
        
        # Enhanced metadata (consistent philosophy)
        practice_area: str = 'general',
        practice_areas: List[str] = None,
        historical_era: str = '',
        document_type: str = 'opinion',
        
        # Quality metrics (unified)
        word_count: int = 0,
        completeness_score: float = 0.0,
        authority_score: float = 0.0,
        
        # Processing metadata
        batch_id: str = '',
        created_at: Optional[datetime] = None
    ):
        self.id = id
        self.source = source
        self.case_name = case_name
        self.court_id = court_id
        self.court_name = court_name
        self.jurisdiction = jurisdiction
        self.date_filed = date_filed
        self.docket_number = docket_number
        self.text_content = text_content
        self.source_id = source_id
        self.cluster_id = cluster_id
        self.practice_area = practice_area
        self.practice_areas = practice_areas or [practice_area]
        self.historical_era = historical_era
        self.document_type = document_type
        self.word_count = word_count
        self.completeness_score = completeness_score
        self.authority_score = authority_score
        self.batch_id = batch_id
        self.created_at = created_at or datetime.now()
    
    def to_supabase_dict(self) -> Dict[str, Any]:
        """Convert to Supabase-compatible dictionary with consistent schema"""
        return {
            'id': self.id,
            'case_name': self.case_name,
            'court_id': self.court_id,
            'court': self.court_name,
            'jurisdiction': self.jurisdiction,
            'date_filed': self.date_filed.date() if self.date_filed else None,
            'year_filed': self.date_filed.year if self.date_filed else None,
            'docket_number': self.docket_number,
            'source': self.source,
            'source_id': self.source_id,
            'cluster_id': self.cluster_id,
            'source_type': self.source,
            'primary_practice_area': self.practice_area,
            'practice_areas': self.practice_areas,
            'historical_era': self.historical_era,
            'document_type': self.document_type,
            'word_count': self.word_count,
            'completeness_score': self.completeness_score,
            'authority_score': self.authority_score,
            'batch_id': self.batch_id,
            'created_at': self.created_at.isoformat(),
            'source_window': self._determine_source_window(),
            'content_hash': self._calculate_content_hash()
        }
    
    def _determine_source_window(self) -> str:
        """Determine source window based on date and source"""
        if not self.date_filed:
            return 'unknown'
        
        year = self.date_filed.year
        
        if self.source == 'caselaw_access_project':
            return 'historical'  # CAP covers 1950-1993
        elif self.source == 'courtlistener':
            if year >= 1994:
                return 'modern'  # CourtListener covers 1994+
            else:
                return 'historical'
        
        return 'unknown'
    
    def _calculate_content_hash(self) -> str:
        """Calculate consistent content hash"""
        import hashlib
        content = f"{self.case_name}|{self.text_content}|{self.date_filed}"
        return hashlib.sha256(content.encode()).hexdigest()[:16]


class UnifiedEnhancedProcessor:
    """
    Unified processor for both CourtListener and CAP data
    Ensures consistent cross-system tracking and metadata philosophy
    """
    
    def __init__(
        self,
        supabase_client,
        gcs_client,
        pinecone_client,
        neo4j_client,
        batch_size: int = 1000
    ):
        self.supabase = supabase_client
        self.storage_pipeline = AtomicStoragePipeline(
            supabase_client, gcs_client, pinecone_client, neo4j_client, batch_size
        )
        self.validator = CrossSystemValidator(
            supabase_client, gcs_client, pinecone_client, neo4j_client
        )
        self.batch_size = batch_size
        
        logger.info("✅ Unified Enhanced Processor initialized")
    
    async def process_courtlistener_cases(
        self,
        api_key: str,
        jurisdiction: str = 'tx',
        start_year: int = 1994,
        end_year: int = 2025
    ) -> Dict[str, Any]:
        """Process CourtListener cases with unified format"""
        
        logger.info("📡 Processing CourtListener cases with unified format")
        
        # Use existing enhanced processor but transform to unified format
        async with ChunkedCourtListenerProcessor(
            api_key=api_key,
            supabase_client=self.supabase,
            gcs_client=self.storage_pipeline.gcs,
            pinecone_client=self.storage_pipeline.pinecone,
            neo4j_client=self.storage_pipeline.neo4j,
            chunk_size=20000,
            batch_size=self.batch_size
        ) as processor:
            
            # Override the processor to use unified format
            original_transform = processor._transform_case_for_storage
            processor._transform_case_for_storage = self._transform_courtlistener_case
            
            results = await processor.process_jurisdiction_chunked(
                jurisdiction=jurisdiction,
                start_year=start_year,
                end_year=end_year,
                resume=True
            )
            
            return results
    
    async def process_cap_cases(
        self,
        cap_data_dir: str = "data/caselaw_access_project",
        jurisdiction: str = 'tx',
        max_cases: int = 50000
    ) -> Dict[str, Any]:
        """Process CAP cases with unified format and enhanced cross-system tracking"""
        
        logger.info("📚 Processing CAP cases with unified format and enhanced tracking")
        
        cap_files = list(Path(cap_data_dir).glob("*.jsonl.gz"))
        if not cap_files:
            logger.warning(f"No CAP files found in {cap_data_dir}")
            return {'processed_cases': 0, 'errors': 0}
        
        total_processed = 0
        total_errors = 0
        
        for cap_file in cap_files:
            logger.info(f"📄 Processing CAP file: {cap_file.name}")
            
            try:
                # Parse cases from file
                cases = []
                with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if len(cases) >= max_cases:
                            break
                        
                        try:
                            raw_case = json.loads(line.strip())
                            unified_case = self._transform_cap_case(raw_case, jurisdiction)
                            
                            if unified_case and self._should_include_case(unified_case, jurisdiction):
                                cases.append(unified_case)
                                
                        except Exception as e:
                            logger.warning(f"Error parsing line {line_num} in {cap_file.name}: {e}")
                            total_errors += 1
                
                if not cases:
                    logger.info(f"   No relevant cases found in {cap_file.name}")
                    continue
                
                # Process cases in batches with enhanced pipeline
                for i in range(0, len(cases), self.batch_size):
                    batch = cases[i:i + self.batch_size]
                    batch_id = f"cap_{jurisdiction}_{cap_file.stem}_{i//self.batch_size}"
                    
                    logger.info(f"   📦 Processing batch {i//self.batch_size + 1} ({len(batch)} cases)")
                    
                    # Convert to format expected by storage pipeline
                    storage_cases = [case.to_supabase_dict() for case in batch]
                    
                    # Process through enhanced atomic storage pipeline
                    result = await self.storage_pipeline.store_batch_atomic(storage_cases, batch_id)
                    
                    if result.success:
                        total_processed += len(batch)
                        logger.info(f"   ✅ Batch processed successfully: {len(batch)} cases")
                    else:
                        total_errors += len(batch)
                        logger.error(f"   ❌ Batch failed: {result.error}")
                
            except Exception as e:
                logger.error(f"Error processing CAP file {cap_file.name}: {e}")
                total_errors += 1
        
        logger.info(f"📚 CAP processing complete: {total_processed} cases processed, {total_errors} errors")
        
        return {
            'processed_cases': total_processed,
            'errors': total_errors,
            'files_processed': len(cap_files)
        }
    
    def _transform_courtlistener_case(self, case_data: Dict[str, Any]) -> UnifiedCaseDocument:
        """Transform CourtListener case to unified format"""
        
        # Parse date
        date_filed = None
        if case_data.get('date_filed'):
            try:
                date_filed = datetime.fromisoformat(case_data['date_filed'].replace('Z', '+00:00'))
            except:
                pass
        
        # Determine historical era
        historical_era = ''
        if date_filed:
            year = date_filed.year
            if year >= 2000:
                historical_era = 'modern'
            elif year >= 1990:
                historical_era = 'recent'
            elif year >= 1970:
                historical_era = 'contemporary'
            else:
                historical_era = 'historical'
        
        return UnifiedCaseDocument(
            id=str(case_data.get('id') or case_data.get('opinion_id')),
            source='courtlistener',
            case_name=case_data.get('case_name', ''),
            court_id=case_data.get('court', ''),
            court_name=case_data.get('court_name', ''),
            jurisdiction=case_data.get('jurisdiction', 'TX'),
            date_filed=date_filed,
            docket_number=case_data.get('docket_number', ''),
            text_content=case_data.get('plain_text', '') or case_data.get('html', ''),
            source_id=str(case_data.get('id')),
            cluster_id=str(case_data.get('cluster_id', '')),
            practice_area=case_data.get('practice_area', 'general'),
            historical_era=historical_era,
            document_type='opinion',
            word_count=len((case_data.get('plain_text', '') or '').split()),
            batch_id=case_data.get('batch_id', '')
        )
    
    def _transform_cap_case(self, raw_case: Dict[str, Any], jurisdiction: str) -> Optional[UnifiedCaseDocument]:
        """Transform CAP case to unified format"""
        
        try:
            # Extract metadata
            metadata = raw_case.get('metadata', {})
            
            # Parse date
            date_filed = None
            if raw_case.get('decision_date'):
                try:
                    date_filed = datetime.fromisoformat(raw_case['decision_date'].replace('Z', '+00:00'))
                except:
                    pass
            
            # Extract court info
            court_info = raw_case.get('court', {})
            court_name = court_info.get('name', '')
            
            # Generate consistent ID (remove cap_ prefix for consistency)
            case_id = str(raw_case.get('id', '')).replace('/', '_').replace('.html', '')
            
            return UnifiedCaseDocument(
                id=case_id,  # Consistent ID format
                source='caselaw_access_project',
                case_name=raw_case.get('name', ''),
                court_id=court_info.get('id', ''),
                court_name=court_name,
                jurisdiction=jurisdiction.upper(),
                date_filed=date_filed,
                docket_number=raw_case.get('docket_number', ''),
                text_content=raw_case.get('text', ''),
                source_id=str(raw_case.get('id')),
                cluster_id=case_id,  # Use same as ID for CAP
                practice_area='general',  # Will be classified later
                historical_era='historical',  # CAP is historical data
                document_type='opinion',
                word_count=len((raw_case.get('text', '') or '').split()),
                batch_id=f"cap_{jurisdiction}_{datetime.now().strftime('%Y%m%d')}"
            )
            
        except Exception as e:
            logger.error(f"Error transforming CAP case {raw_case.get('id', 'unknown')}: {e}")
            return None
    
    def _should_include_case(self, case: UnifiedCaseDocument, target_jurisdiction: str) -> bool:
        """Determine if case should be included based on jurisdiction and quality"""
        
        # Check jurisdiction
        if case.jurisdiction.lower() != target_jurisdiction.lower():
            return False
        
        # Check date range for CAP (historical window)
        if case.source == 'caselaw_access_project' and case.date_filed:
            year = case.date_filed.year
            if year < 1950 or year > 1993:
                return False
        
        # Check content quality
        if len(case.text_content.strip()) < 100:  # Minimum content length
            return False
        
        return True
    
    async def validate_cross_source_consistency(self, jurisdiction: str) -> Dict[str, Any]:
        """Validate consistency between CourtListener and CAP data"""
        
        logger.info("🔍 Validating cross-source consistency with unified format")
        
        # Use the existing validator but with unified expectations
        validation_report = await self.validator.validate_batch_consistency(
            batch_id=f"unified_{jurisdiction}",
            expected_cases=0,  # Will be calculated dynamically
            time_range=None
        )
        
        return {
            'validation_report': validation_report,
            'consistency_score': validation_report.overall_score,
            'issues': validation_report.issues,
            'passed': validation_report.passed
        }


# Convenience functions for testing
async def test_unified_processing():
    """Test the unified processing system"""
    
    load_dotenv()
    
    # Setup clients (using mocks for testing)
    from test_500_cases_comprehensive import MockGCSClient, MockPineconeClient, MockNeo4jClient
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    # Initialize unified processor
    processor = UnifiedEnhancedProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client
    )
    
    logger.info("🧪 Testing unified processing system")
    
    # Test CAP processing with small sample
    cap_results = await processor.process_cap_cases(
        jurisdiction='tx',
        max_cases=100  # Small test
    )
    
    logger.info(f"📊 CAP Test Results: {cap_results}")
    
    # Validate consistency
    validation = await processor.validate_cross_source_consistency('tx')
    
    logger.info(f"✅ Validation Results: {validation}")
    
    return cap_results, validation


if __name__ == "__main__":
    asyncio.run(test_unified_processing())
