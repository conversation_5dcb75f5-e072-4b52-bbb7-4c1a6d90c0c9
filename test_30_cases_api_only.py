#!/usr/bin/env python3
"""
Test script to fetch exactly 30 Texas court cases from CourtListener API
Direct API calls with precise control - stops at exactly 30 cases
"""

import os
import asyncio
import httpx
import logging
from datetime import datetime
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fetch_exactly_30_texas_cases():
    """Fetch exactly 30 Texas court cases using direct CourtListener API calls"""

    load_dotenv()

    # Get API key
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        logger.error("❌ COURTLISTENER_API_KEY not found in .env file")
        return False

    logger.info("🚀 Starting EXACT 30-case Texas CourtListener API test")
    logger.info("=" * 60)

    base_url = "https://www.courtlistener.com/api/rest/v4"
    headers = {"Authorization": f"Token {api_key}"}

    # All 21 Texas courts (from full_courtlistener_crawl.py)
    texas_courts = [
        'tex', 'texcrimapp',  # State supreme courts
        'texapp1st', 'texapp2nd', 'texapp3rd', 'texapp4th', 'texapp5th',
        'texapp6th', 'texapp7th', 'texapp8th', 'texapp9th', 'texapp10th',
        'texapp11th', 'texapp12th', 'texapp13th', 'texapp14th',  # State appellate
        'txnd', 'txsd', 'txed', 'txwd',  # Federal district
        'ca5'  # Federal circuit (covers Texas)
    ]

    start_time = datetime.now()
    all_cases = []

    try:
        async with httpx.AsyncClient(headers=headers, timeout=30.0) as client:

            logger.info(f"🔍 Fetching from Texas courts: {texas_courts}")

            # Fetch cases from opinions endpoint with Texas courts
            # Note: CourtListener API has max page_size of 20, so we need multiple requests for 30 cases
            url = f"{base_url}/opinions/"
            base_params = {
                'court': ','.join(texas_courts),  # All Texas courts
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'ordering': '-date_created',  # Most recent first
                'format': 'json'
            }

            logger.info(f"📡 Making API requests to: {url}")
            logger.info(f"📋 Base parameters: {base_params}")

            all_cases = []

            # First request: Get 20 cases
            params1 = {**base_params, 'page_size': 20}
            logger.info(f"📥 Request 1: Fetching first 20 cases...")
            response1 = await client.get(url, params=params1)

            if response1.status_code == 200:
                data1 = response1.json()
                cases1 = data1.get('results', [])
                total_available = data1.get('count', 0)
                all_cases.extend(cases1)
                logger.info(f"✅ Got {len(cases1)} cases from first request")

                # Second request: Get next 10 cases (page 2)
                if len(all_cases) < 30 and data1.get('next'):
                    params2 = {**base_params, 'page_size': 20, 'page': 2}
                    logger.info(f"📥 Request 2: Fetching next 20 cases...")
                    response2 = await client.get(url, params=params2)

                    if response2.status_code == 200:
                        data2 = response2.json()
                        cases2 = data2.get('results', [])
                        all_cases.extend(cases2)
                        logger.info(f"✅ Got {len(cases2)} cases from second request")

                # Take exactly 30 cases (or fewer if less available)
                cases_to_process = all_cases[:30]
                actual_count = len(cases_to_process)

                logger.info(f"✅ API Response successful!")
                logger.info(f"📊 Total available cases: {total_available}")
                logger.info(f"📥 Cases fetched: {actual_count}")

                # Display sample cases
                logger.info(f"\n📄 SAMPLE CASES FETCHED:")
                for i, case in enumerate(cases_to_process[:5], 1):
                    case_name = case.get('case_name', 'Unknown')[:50]
                    court_url = case.get('court', 'Unknown')
                    # Extract court ID from URL if it's a URL
                    if isinstance(court_url, str) and 'courts/' in court_url:
                        court_id = court_url.split('/')[-2] if court_url.endswith('/') else court_url.split('/')[-1]
                    else:
                        court_id = court_url
                    date_filed = case.get('date_filed', 'Unknown')
                    logger.info(f"   {i}. {case_name}... (Court: {court_id}, Filed: {date_filed})")

                if actual_count > 5:
                    logger.info(f"   ... and {actual_count - 5} more cases")

                # Calculate duration
                duration = datetime.now() - start_time

                logger.info(f"\n✅ FETCH COMPLETED SUCCESSFULLY!")
                logger.info("=" * 60)
                logger.info(f"📊 FINAL RESULTS:")
                logger.info(f"   Target cases: 30")
                logger.info(f"   Actual cases fetched: {actual_count}")
                logger.info(f"   Success: {'✅' if actual_count > 0 else '❌'}")
                logger.info(f"   Duration: {duration.total_seconds():.2f} seconds")
                logger.info(f"   API endpoint: {url}")
                logger.info(f"   Texas courts queried: {len(texas_courts)}")

                return actual_count > 0

            else:
                logger.error(f"❌ First API request failed: HTTP {response1.status_code}")
                logger.error(f"Response: {response1.text[:200]}")
                return False

    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fetch_exactly_30_texas_cases())

    if success:
        print("\n🎉 SUCCESS: Texas court cases fetched successfully from CourtListener API!")
        print("🔍 Check the log output above for exact case count and details.")
    else:
        print("\n❌ FAILED: Could not fetch cases. Check logs for details.")
        exit(1)
