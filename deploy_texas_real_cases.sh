#!/bin/bash

# Texas Real Cases - Google Cloud Run Deployment Script

set -e

echo "🚀 Deploying Texas Real Cases to Google Cloud Run..."

# Configuration
PROJECT_ID="texas-laws-personalinjury"
REGION="us-central1"
SERVICE_NAME="texas-phase1-processor"
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

# Build and push Docker image
echo "📦 Building Docker image..."
docker build -f Dockerfile.texas-real-cases -t $IMAGE_NAME .

echo "📤 Pushing to Google Container Registry..."
docker push $IMAGE_NAME

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --memory 32Gi \
    --cpu 8 \
    --max-instances 100 \
    --concurrency 10 \
    --timeout 3600s \
    --allow-unauthenticated \
    --set-env-vars="ENVIRONMENT=production" \
    --project $PROJECT_ID

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')

echo "✅ Deployment complete!"
echo "🌐 Service URL: $SERVICE_URL"
echo "🔍 Health check: $SERVICE_URL/health"

# Test the deployment
echo "🧪 Testing deployment..."
curl -s "$SERVICE_URL/health" | jq .

echo "🎉 Texas Real Cases deployment successful!"
