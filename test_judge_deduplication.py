#!/usr/bin/env python3
"""
Test Judge Deduplication
Test the improved judge extraction with deduplication logic
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JudgeDeduplicationTest:
    """Test judge deduplication with real CourtListener data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"judge_dedup_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_courtlistener_case_for_dedup_test(self) -> list:
        """Fetch a single CourtListener case for deduplication testing"""
        
        print(f"🌐 FETCHING COURTLISTENER CASE FOR DEDUPLICATION TEST")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        try:
            # Search for recent federal cases
            search_params = {
                'court': 'ca5,ca2,txnd',  # Federal courts
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 10
            }
            
            print(f"   🔍 Searching CourtListener API V4...")
            
            response = requests.get(
                f"{self.cl_base_url}/opinions/",
                headers=headers,
                params=search_params,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ API request failed: {response.status_code}")
                return []
            
            search_results = response.json()
            results = search_results.get('results', [])
            
            print(f"   📊 Found {len(results)} search results")
            
            # Get the first case with substantial text
            for opinion_data in results[:3]:
                try:
                    print(f"   📄 Processing: {opinion_data.get('case_name', 'Unknown')[:50]}...")
                    
                    time.sleep(0.2)  # Rate limit
                    
                    # Check text content
                    plain_text = opinion_data.get('plain_text', '')
                    html_content = opinion_data.get('html', '')
                    text_content = plain_text or html_content
                    
                    if not text_content or len(text_content) < 1000:
                        continue
                    
                    # Look for judge patterns that might cause duplicates
                    text_lower = text_content.lower()
                    if 'judge' in text_lower and ('before' in text_lower or 'delivered' in text_lower):
                        # Create processing format
                        case_data = {
                            'id': f"cl_dedup_{opinion_data.get('id', 'unknown')}",
                            'source': 'courtlistener',
                            'case_name': opinion_data.get('case_name', 'Unknown'),
                            'court': opinion_data.get('court', ''),
                            'court_name': self._get_court_name(opinion_data.get('court', '')),
                            'date_filed': opinion_data.get('date_filed', ''),
                            'jurisdiction': 'US',
                            'text': text_content,
                            'precedential_status': opinion_data.get('precedential_status', 'Unknown')
                        }
                        
                        print(f"      ✅ Selected case: {case_data['case_name'][:50]}...")
                        print(f"         Court: {case_data['court_name']}")
                        print(f"         Text length: {len(text_content):,} characters")
                        
                        return [case_data]
                        
                except Exception as e:
                    print(f"      ❌ Error processing case: {e}")
                    continue
            
            print(f"   ❌ No suitable cases found for deduplication test")
            return []
            
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
            return []
    
    def _get_court_name(self, court_id: str) -> str:
        """Get full court name from court ID"""
        court_names = {
            'ca5': 'U.S. Court of Appeals, Fifth Circuit',
            'ca2': 'U.S. Court of Appeals, Second Circuit',
            'txnd': 'U.S. District Court, Northern District of Texas',
        }
        return court_names.get(court_id, f'Court {court_id}')
    
    async def test_judge_deduplication(self) -> bool:
        """Test judge deduplication with real CourtListener data"""
        
        print(f"\n🔄 JUDGE DEDUPLICATION TEST")
        print("=" * 60)
        
        try:
            # Fetch a real case
            real_cases = self.fetch_courtlistener_case_for_dedup_test()
            
            if not real_cases:
                print(f"❌ No real CourtListener cases fetched")
                return False
            
            print(f"\n📊 PROCESSING 1 REAL CASE FOR DEDUPLICATION TEST")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing with improved deduplication...")
            
            result = await processor.process_coherent_batch(
                raw_cases=real_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify deduplication worked
            return await self.verify_judge_deduplication(real_cases)
            
        except Exception as e:
            print(f"❌ Judge deduplication test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_judge_deduplication(self, original_cases: list) -> bool:
        """Verify judge deduplication worked correctly"""
        
        print(f"\n🔍 VERIFYING JUDGE DEDUPLICATION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get judges extracted
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case {batch_id: $batch_id})
                        WHERE type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_id=self.test_batch_id)
                
                judges = list(result)
                
                print(f"📊 JUDGES EXTRACTED AFTER DEDUPLICATION:")
                print(f"   Total judges found: {len(judges)}")
                
                for judge in judges:
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']}, Cases: {judge['case_count']})")
                
                # Check for potential duplicates
                judge_names = [j['judge_name'] for j in judges]
                
                print(f"\n🔍 DUPLICATE ANALYSIS:")
                potential_duplicates = []
                
                for i, name1 in enumerate(judge_names):
                    for j, name2 in enumerate(judge_names[i+1:], i+1):
                        name1_lower = name1.lower()
                        name2_lower = name2.lower()
                        
                        # Check if one name is contained in another
                        if name1_lower in name2_lower or name2_lower in name1_lower:
                            potential_duplicates.append((name1, name2))
                
                if potential_duplicates:
                    print(f"   ❌ Potential duplicates found:")
                    for dup in potential_duplicates:
                        print(f"      - '{dup[0]}' vs '{dup[1]}'")
                else:
                    print(f"   ✅ No duplicate names found")
                
                # Show sample text for context
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})
                    RETURN c.case_name as case_name, substring(c.text, 0, 500) as text_sample
                    LIMIT 1
                ''', batch_id=self.test_batch_id)
                
                text_samples = list(result)
                if text_samples:
                    print(f"\n📄 SAMPLE TEXT PROCESSED:")
                    sample = text_samples[0]
                    print(f"   Case: {sample['case_name']}")
                    print(f"   Text: {sample['text_sample']}...")
                
                # Success criteria
                success = len(judges) > 0 and len(potential_duplicates) == 0
                
                print(f"\n🎯 DEDUPLICATION VERIFICATION:")
                print(f"   Judges extracted: {'✅' if len(judges) > 0 else '❌'} ({len(judges)})")
                print(f"   No duplicates: {'✅' if len(potential_duplicates) == 0 else '❌'}")
                print(f"   Overall success: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 JUDGE DEDUPLICATION: SUCCESS!")
                    print(f"✅ No duplicate judge names found")
                    print(f"✅ Clean extraction with {len(judges)} unique judges")
                else:
                    print(f"\n⚠️ JUDGE DEDUPLICATION: NEEDS IMPROVEMENT!")
                    if potential_duplicates:
                        print(f"❌ Found potential duplicates that should be merged")
                
                return success
                
        except Exception as e:
            print(f"❌ Judge deduplication verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP DEDUPLICATION TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run judge deduplication test"""
    
    print("🧪 JUDGE DEDUPLICATION TEST")
    print("=" * 80)
    print("🎯 Testing improved judge extraction with deduplication")
    
    test = JudgeDeduplicationTest()
    
    try:
        # Run the test
        success = await test.test_judge_deduplication()
        
        if success:
            print(f"\n🎉 JUDGE DEDUPLICATION: SUCCESS!")
            print(f"✅ No duplicate judges found")
            return True
        else:
            print(f"\n❌ JUDGE DEDUPLICATION: FAILED!")
            print(f"❌ Still finding duplicate judge names")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
