#!/usr/bin/env python3
"""
Texas Cloud Run Processor

Processes real Texas cases in Google Cloud Run environment.
"""

import asyncio
import json
import logging
import os
import sys
from flask import Flask, request, jsonify
from concurrent.futures import ThreadPoolExecutor
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from texas_phase1_filter import TexasPhase1Filter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Initialize filter engine
filter_engine = TexasPhase1Filter()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'texas-phase1-processor'})

@app.route('/process', methods=['POST'])
def process_cases():
    """Process a batch of Texas cases."""
    
    try:
        # Get cases from request
        data = request.get_json()
        cases = data.get('cases', [])
        
        if not cases:
            return jsonify({'error': 'No cases provided'}), 400
        
        logger.info(f"Processing {len(cases)} cases...")
        
        # Process cases
        start_time = time.time()
        
        # Filter through Phase 1 criteria
        filtered_cases, filter_stats = filter_engine.batch_filter_documents(cases)
        
        processing_time = time.time() - start_time
        
        # Simulate database storage (replace with actual storage)
        stored_count = len(filtered_cases)
        
        result = {
            'processed': len(cases),
            'filtered': len(filtered_cases),
            'stored': stored_count,
            'processing_time_seconds': processing_time,
            'filter_stats': filter_stats,
            'success': True
        }
        
        logger.info(f"✅ Processed {len(cases)} cases in {processing_time:.2f}s")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Processing error: {e}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/batch-process', methods=['POST'])
def batch_process():
    """Process multiple batches of cases."""
    
    try:
        data = request.get_json()
        batches = data.get('batches', [])
        
        if not batches:
            return jsonify({'error': 'No batches provided'}), 400
        
        logger.info(f"Processing {len(batches)} batches...")
        
        total_processed = 0
        total_filtered = 0
        total_stored = 0
        batch_results = []
        
        start_time = time.time()
        
        # Process each batch
        for i, batch in enumerate(batches):
            batch_start = time.time()
            
            # Filter cases
            filtered_cases, filter_stats = filter_engine.batch_filter_documents(batch)
            
            # Simulate storage
            stored_count = len(filtered_cases)
            
            batch_time = time.time() - batch_start
            
            batch_result = {
                'batch_id': i,
                'processed': len(batch),
                'filtered': len(filtered_cases),
                'stored': stored_count,
                'processing_time_seconds': batch_time,
                'filter_stats': filter_stats
            }
            
            batch_results.append(batch_result)
            
            total_processed += len(batch)
            total_filtered += len(filtered_cases)
            total_stored += stored_count
            
            logger.info(f"Batch {i+1}/{len(batches)}: {len(batch)} → {len(filtered_cases)} cases")
        
        total_time = time.time() - start_time
        
        result = {
            'total_batches': len(batches),
            'total_processed': total_processed,
            'total_filtered': total_filtered,
            'total_stored': total_stored,
            'total_time_seconds': total_time,
            'throughput_cases_per_second': total_processed / total_time if total_time > 0 else 0,
            'batch_results': batch_results,
            'success': True
        }
        
        logger.info(f"✅ Batch processing complete: {total_processed} cases in {total_time:.2f}s")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Batch processing error: {e}")
        return jsonify({'error': str(e), 'success': False}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
