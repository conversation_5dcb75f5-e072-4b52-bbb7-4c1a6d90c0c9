#!/usr/bin/env python3
"""
Find exactly which cases are in Supabase but missing from Neo4j.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector
from processing.storage.supabase_connector import SupabaseConnector

async def find_missing_cases():
    """Find cases that exist in Supabase but are missing from Neo4j."""
    
    try:
        print("🔍 FINDING MISSING CASES BETWEEN SUPABASE AND NEO4J")
        print("=" * 70)
        
        # Initialize connections
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()

        # Get all case IDs from Supabase
        print("📥 Fetching all case IDs from Supabase...")
        response = supabase.client.table('cases').select('id, case_name, source').execute()
        supabase_cases = {case['id']: case for case in response.data}
        supabase_count = len(supabase_cases)
        
        print(f"   Found {supabase_count:,} cases in Supabase")
        
        # Get all Document node IDs from Neo4j
        print("📥 Fetching all Document node IDs from Neo4j...")
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN d.id as id")
            neo4j_ids = set()
            null_id_count = 0
            
            for record in result:
                doc_id = record['id']
                if doc_id:
                    neo4j_ids.add(doc_id)
                else:
                    null_id_count += 1
        
        neo4j_count = len(neo4j_ids)
        print(f"   Found {neo4j_count:,} Document nodes with valid IDs in Neo4j")
        print(f"   Found {null_id_count} Document nodes with null IDs")
        
        # Find missing cases
        missing_cases = []
        for case_id in supabase_cases.keys():
            if case_id not in neo4j_ids:
                missing_cases.append(supabase_cases[case_id])
        
        print(f"\n📊 CONSISTENCY ANALYSIS:")
        print(f"   Supabase cases: {supabase_count:,}")
        print(f"   Neo4j Document nodes: {neo4j_count:,}")
        print(f"   Missing from Neo4j: {len(missing_cases):,}")
        print(f"   Consistency rate: {((supabase_count - len(missing_cases)) / supabase_count * 100):.2f}%")
        
        if missing_cases:
            print(f"\n❌ MISSING CASES (first 20):")
            for i, case in enumerate(missing_cases[:20]):
                print(f"   {i+1:2d}. {case['id']} - {case['case_name'][:60]}... (source: {case['source']})")
            
            if len(missing_cases) > 20:
                print(f"   ... and {len(missing_cases) - 20} more")
            
            # Analyze missing cases by source
            missing_by_source = {}
            for case in missing_cases:
                source = case['source']
                missing_by_source[source] = missing_by_source.get(source, 0) + 1
            
            print(f"\n📊 MISSING CASES BY SOURCE:")
            for source, count in sorted(missing_by_source.items(), key=lambda x: x[1], reverse=True):
                print(f"   {source}: {count:,} missing")
        
        # Check for extra cases in Neo4j (shouldn't happen but let's verify)
        extra_in_neo4j = []
        for neo4j_id in neo4j_ids:
            if neo4j_id not in supabase_cases:
                extra_in_neo4j.append(neo4j_id)
        
        if extra_in_neo4j:
            print(f"\n⚠️  EXTRA CASES IN NEO4J (not in Supabase): {len(extra_in_neo4j)}")
            for i, case_id in enumerate(extra_in_neo4j[:10]):
                print(f"   {i+1:2d}. {case_id}")
        
        neo4j.close()
        
        return len(missing_cases), missing_by_source
        
    except Exception as e:
        print(f"❌ Error finding missing cases: {e}")
        import traceback
        traceback.print_exc()
        return 0, {}

async def main():
    """Main function."""
    missing_count, missing_by_source = await find_missing_cases()
    
    print(f"\n🎯 SUMMARY:")
    if missing_count == 0:
        print(f"   ✅ PERFECT CONSISTENCY: All Supabase cases exist in Neo4j!")
    else:
        print(f"   ❌ INCONSISTENCY FOUND: {missing_count:,} cases missing from Neo4j")
        print(f"   🔧 ACTION REQUIRED: Fix Neo4j storage pipeline")
        
        if missing_by_source:
            most_affected_source = max(missing_by_source.items(), key=lambda x: x[1])
            print(f"   📊 Most affected source: {most_affected_source[0]} ({most_affected_source[1]} missing)")

if __name__ == "__main__":
    asyncio.run(main())
