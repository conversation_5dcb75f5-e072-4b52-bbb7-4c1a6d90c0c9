#!/usr/bin/env python3
"""
Test Full Name Extraction
Test the improved patterns that prioritize full names
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FullNameExtractionTest:
    """Test full name extraction with improved patterns"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"full_name_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def create_full_name_test_cases(self) -> list:
        """Create test cases designed to extract full names"""
        
        print(f"👨‍⚖️ CREATING FULL NAME TEST CASES")
        print("=" * 60)
        
        test_cases = [
            # Case 1: Circuit court with full names and actions
            {
                'id': 'full_name_test_1',
                'source': 'courtlistener',
                'case_name': 'United States v. Technology Corp',
                'court': 'ca5',
                'court_name': 'U.S. Court of Appeals, Fifth Circuit',
                'date_filed': '2021-08-22',
                'jurisdiction': 'US',
                'text': '''
                UNITED STATES v. TECHNOLOGY CORP
                
                Before SMITH, JONES, and WILLIAMS, Circuit Judges.
                
                Circuit Judge Jennifer L. Smith delivered the opinion of the court.
                
                This matter comes before us on appeal. Circuit Judge Michael R. Jones 
                wrote a separate concurring opinion. Circuit Judge Patricia Williams 
                authored the dissenting opinion.
                
                Jennifer L. Smith, Circuit Judge:
                
                We hold that the district court erred...
                
                Michael R. Jones, J., concurring:
                I agree with the majority but write separately.
                
                Patricia Williams, Circuit Judge, dissenting:
                I respectfully dissent from the majority opinion.
                ''',
                'expected_full_names': [
                    'Jennifer L. Smith',
                    'Michael R. Jones', 
                    'Patricia Williams'
                ]
            },
            
            # Case 2: Supreme Court with formal titles
            {
                'id': 'full_name_test_2',
                'source': 'courtlistener',
                'case_name': 'Brown v. Board of Education',
                'court': 'scotus',
                'court_name': 'Supreme Court of the United States',
                'date_filed': '1954-05-17',
                'jurisdiction': 'US',
                'text': '''
                BROWN v. BOARD OF EDUCATION
                
                Mr. Chief Justice Earl Warren delivered the opinion of the Court.
                
                We conclude that, in the field of public education, the doctrine 
                of "separate but equal" has no place.
                
                Justice Hugo L. Black, with whom Justice William O. Douglas joins, concurring.
                
                Justice Felix Frankfurter wrote separately.
                
                Earl Warren, C.J., delivered the opinion.
                Hugo L. Black, J., concurring.
                ''',
                'expected_full_names': [
                    'Earl Warren',
                    'Hugo L. Black',
                    'William O. Douglas',
                    'Felix Frankfurter'
                ]
            },
            
            # Case 3: District court with mixed formats
            {
                'id': 'full_name_test_3',
                'source': 'courtlistener',
                'case_name': 'Warren v. State of Texas',
                'court': 'txnd',
                'court_name': 'U.S. District Court, Northern District of Texas',
                'date_filed': '2020-03-15',
                'jurisdiction': 'US',
                'text': '''
                WARREN v. STATE OF TEXAS
                
                MEMORANDUM OPINION AND ORDER
                
                District Judge Rick A. Warren delivered this opinion.
                
                This matter comes before the Court on Plaintiff's Motion for Summary Judgment.
                
                Judge Sarah Michelle Johnson wrote the preliminary ruling.
                
                Rick A. Warren, District Judge:
                
                After careful consideration, the motion is GRANTED.
                
                Sarah Michelle Johnson, J., concurring in part.
                ''',
                'expected_full_names': [
                    'Rick A. Warren',
                    'Sarah Michelle Johnson'
                ]
            }
        ]
        
        print(f"   ✅ Created {len(test_cases)} full name test cases")
        
        total_expected = sum(len(case['expected_full_names']) for case in test_cases)
        print(f"   📊 Total expected full names: {total_expected}")
        
        for i, case in enumerate(test_cases, 1):
            expected = case['expected_full_names']
            print(f"\n   {i}. {case['case_name']} ({case['court']}):")
            for name in expected:
                words = len(name.split())
                print(f"      - {name} ({words} words)")
        
        return test_cases
    
    async def test_full_name_extraction(self) -> bool:
        """Test full name extraction with improved patterns"""
        
        print(f"\n🔄 FULL NAME EXTRACTION TEST")
        print("=" * 60)
        
        try:
            # Create full name test cases
            test_cases = self.create_full_name_test_cases()
            
            print(f"\n📊 Processing {len(test_cases)} full name test cases")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing through improved full name extraction...")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify full name extraction
            return await self.verify_full_name_extraction(test_cases)
            
        except Exception as e:
            print(f"❌ Full name extraction test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_full_name_extraction(self, test_cases: list) -> bool:
        """Verify full name extraction worked correctly"""
        
        print(f"\n🔍 VERIFYING FULL NAME EXTRACTION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get all judges extracted
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case {batch_id: $batch_id})
                        WHERE type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_id=self.test_batch_id)
                
                extracted_judges = list(result)
                
                print(f"📊 1. JUDGES EXTRACTED:")
                print(f"   Total judges found: {len(extracted_judges)}")
                
                for judge in extracted_judges:
                    words = len(judge['judge_name'].split())
                    print(f"      - {judge['judge_name']} ({words} words, ID: {judge['judge_id']})")
                
                # Analyze full name vs partial name extraction
                full_name_judges = [j for j in extracted_judges if len(j['judge_name'].split()) > 1]
                single_name_judges = [j for j in extracted_judges if len(j['judge_name'].split()) == 1]
                
                print(f"\n📊 2. FULL NAME ANALYSIS:")
                print(f"   Full names (2+ words): {len(full_name_judges)}")
                for judge in full_name_judges:
                    print(f"      ✅ {judge['judge_name']}")
                
                print(f"   Single names (1 word): {len(single_name_judges)}")
                for judge in single_name_judges:
                    print(f"      ❌ {judge['judge_name']}")
                
                # Check against expected full names
                all_expected_names = []
                for case in test_cases:
                    all_expected_names.extend(case['expected_full_names'])
                
                extracted_names = [j['judge_name'] for j in extracted_judges]
                
                print(f"\n📊 3. EXPECTED VS EXTRACTED:")
                print(f"   Expected full names: {len(all_expected_names)}")
                print(f"   Extracted names: {len(extracted_names)}")
                
                # Check matches
                matches = 0
                for expected in all_expected_names:
                    found = any(expected in extracted or extracted in expected 
                              for extracted in extracted_names)
                    status = "✅" if found else "❌"
                    print(f"      {status} {expected}")
                    if found:
                        matches += 1
                
                match_rate = matches / len(all_expected_names) * 100 if all_expected_names else 0
                
                print(f"\n📊 4. FULL NAME EXTRACTION METRICS:")
                print(f"   Expected names: {len(all_expected_names)}")
                print(f"   Names found: {matches}")
                print(f"   Match rate: {match_rate:.1f}%")
                print(f"   Full name ratio: {len(full_name_judges)}/{len(extracted_judges)} ({len(full_name_judges)/len(extracted_judges)*100:.1f}%)")
                
                # Success criteria
                success_criteria = [
                    len(extracted_judges) > 0,  # At least some judges found
                    len(full_name_judges) > len(single_name_judges),  # More full names than single names
                    match_rate >= 70,  # At least 70% of expected names found
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 FULL NAME EXTRACTION VERIFICATION:")
                print(f"   Judges extracted: {'✅' if len(extracted_judges) > 0 else '❌'} ({len(extracted_judges)})")
                print(f"   Full names preferred: {'✅' if len(full_name_judges) > len(single_name_judges) else '❌'} ({len(full_name_judges)} vs {len(single_name_judges)})")
                print(f"   Match rate: {'✅' if match_rate >= 70 else '❌'} ({match_rate:.1f}%)")
                print(f"   Overall success: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 FULL NAME EXTRACTION: SUCCESS!")
                    print(f"✅ Full names successfully extracted")
                    print(f"✅ {len(full_name_judges)} judges with full names")
                    print(f"✅ {match_rate:.1f}% match rate with expected names")
                else:
                    print(f"\n⚠️ FULL NAME EXTRACTION: NEEDS IMPROVEMENT!")
                    if len(full_name_judges) <= len(single_name_judges):
                        print(f"❌ Still extracting too many partial names")
                    if match_rate < 70:
                        print(f"❌ Low match rate with expected full names")
                
                return success
                
        except Exception as e:
            print(f"❌ Full name extraction verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP FULL NAME TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run full name extraction test"""
    
    print("🧪 FULL NAME EXTRACTION TEST")
    print("=" * 80)
    print("🎯 Testing improved patterns that prioritize full names")
    
    test = FullNameExtractionTest()
    
    try:
        # Run the test
        success = await test.test_full_name_extraction()
        
        if success:
            print(f"\n🎉 FULL NAME EXTRACTION: SUCCESS!")
            print(f"✅ Full names successfully extracted")
            return True
        else:
            print(f"\n❌ FULL NAME EXTRACTION: FAILED!")
            print(f"❌ Still extracting partial names instead of full names")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
