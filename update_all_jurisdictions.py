#!/usr/bin/env python3
"""
Update Jurisdiction Configurations for All 57 US Jurisdictions

This script updates both the jurisdiction config and harvesting config to support
all 57 US jurisdictions (1 federal + 50 states + 6 territories/DC).
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Complete list of all 57 US jurisdictions
ALL_JURISDICTIONS = {
    # Federal
    "fed": {
        "name": "Federal",
        "full_name": "United States Federal",
        "type": "federal",
        "abbreviation": "US"
    },
    # States (50)
    "al": {"name": "Alabama", "full_name": "State of Alabama", "type": "state", "abbreviation": "AL"},
    "ak": {"name": "Alaska", "full_name": "State of Alaska", "type": "state", "abbreviation": "AK"},
    "az": {"name": "Arizona", "full_name": "State of Arizona", "type": "state", "abbreviation": "AZ"},
    "ar": {"name": "Arkansas", "full_name": "State of Arkansas", "type": "state", "abbreviation": "AR"},
    "ca": {"name": "California", "full_name": "State of California", "type": "state", "abbreviation": "CA"},
    "co": {"name": "Colorado", "full_name": "State of Colorado", "type": "state", "abbreviation": "CO"},
    "ct": {"name": "Connecticut", "full_name": "State of Connecticut", "type": "state", "abbreviation": "CT"},
    "de": {"name": "Delaware", "full_name": "State of Delaware", "type": "state", "abbreviation": "DE"},
    "fl": {"name": "Florida", "full_name": "State of Florida", "type": "state", "abbreviation": "FL"},
    "ga": {"name": "Georgia", "full_name": "State of Georgia", "type": "state", "abbreviation": "GA"},
    "hi": {"name": "Hawaii", "full_name": "State of Hawaii", "type": "state", "abbreviation": "HI"},
    "id": {"name": "Idaho", "full_name": "State of Idaho", "type": "state", "abbreviation": "ID"},
    "il": {"name": "Illinois", "full_name": "State of Illinois", "type": "state", "abbreviation": "IL"},
    "in": {"name": "Indiana", "full_name": "State of Indiana", "type": "state", "abbreviation": "IN"},
    "ia": {"name": "Iowa", "full_name": "State of Iowa", "type": "state", "abbreviation": "IA"},
    "ks": {"name": "Kansas", "full_name": "State of Kansas", "type": "state", "abbreviation": "KS"},
    "ky": {"name": "Kentucky", "full_name": "Commonwealth of Kentucky", "type": "state", "abbreviation": "KY"},
    "la": {"name": "Louisiana", "full_name": "State of Louisiana", "type": "state", "abbreviation": "LA"},
    "me": {"name": "Maine", "full_name": "State of Maine", "type": "state", "abbreviation": "ME"},
    "md": {"name": "Maryland", "full_name": "State of Maryland", "type": "state", "abbreviation": "MD"},
    "ma": {"name": "Massachusetts", "full_name": "Commonwealth of Massachusetts", "type": "state", "abbreviation": "MA"},
    "mi": {"name": "Michigan", "full_name": "State of Michigan", "type": "state", "abbreviation": "MI"},
    "mn": {"name": "Minnesota", "full_name": "State of Minnesota", "type": "state", "abbreviation": "MN"},
    "ms": {"name": "Mississippi", "full_name": "State of Mississippi", "type": "state", "abbreviation": "MS"},
    "mo": {"name": "Missouri", "full_name": "State of Missouri", "type": "state", "abbreviation": "MO"},
    "mt": {"name": "Montana", "full_name": "State of Montana", "type": "state", "abbreviation": "MT"},
    "ne": {"name": "Nebraska", "full_name": "State of Nebraska", "type": "state", "abbreviation": "NE"},
    "nv": {"name": "Nevada", "full_name": "State of Nevada", "type": "state", "abbreviation": "NV"},
    "nh": {"name": "New Hampshire", "full_name": "State of New Hampshire", "type": "state", "abbreviation": "NH"},
    "nj": {"name": "New Jersey", "full_name": "State of New Jersey", "type": "state", "abbreviation": "NJ"},
    "nm": {"name": "New Mexico", "full_name": "State of New Mexico", "type": "state", "abbreviation": "NM"},
    "ny": {"name": "New York", "full_name": "State of New York", "type": "state", "abbreviation": "NY"},
    "nc": {"name": "North Carolina", "full_name": "State of North Carolina", "type": "state", "abbreviation": "NC"},
    "nd": {"name": "North Dakota", "full_name": "State of North Dakota", "type": "state", "abbreviation": "ND"},
    "oh": {"name": "Ohio", "full_name": "State of Ohio", "type": "state", "abbreviation": "OH"},
    "ok": {"name": "Oklahoma", "full_name": "State of Oklahoma", "type": "state", "abbreviation": "OK"},
    "or": {"name": "Oregon", "full_name": "State of Oregon", "type": "state", "abbreviation": "OR"},
    "pa": {"name": "Pennsylvania", "full_name": "Commonwealth of Pennsylvania", "type": "state", "abbreviation": "PA"},
    "ri": {"name": "Rhode Island", "full_name": "State of Rhode Island", "type": "state", "abbreviation": "RI"},
    "sc": {"name": "South Carolina", "full_name": "State of South Carolina", "type": "state", "abbreviation": "SC"},
    "sd": {"name": "South Dakota", "full_name": "State of South Dakota", "type": "state", "abbreviation": "SD"},
    "tn": {"name": "Tennessee", "full_name": "State of Tennessee", "type": "state", "abbreviation": "TN"},
    "tx": {"name": "Texas", "full_name": "State of Texas", "type": "state", "abbreviation": "TX"},
    "ut": {"name": "Utah", "full_name": "State of Utah", "type": "state", "abbreviation": "UT"},
    "vt": {"name": "Vermont", "full_name": "State of Vermont", "type": "state", "abbreviation": "VT"},
    "va": {"name": "Virginia", "full_name": "Commonwealth of Virginia", "type": "state", "abbreviation": "VA"},
    "wa": {"name": "Washington", "full_name": "State of Washington", "type": "state", "abbreviation": "WA"},
    "wv": {"name": "West Virginia", "full_name": "State of West Virginia", "type": "state", "abbreviation": "WV"},
    "wi": {"name": "Wisconsin", "full_name": "State of Wisconsin", "type": "state", "abbreviation": "WI"},
    "wy": {"name": "Wyoming", "full_name": "State of Wyoming", "type": "state", "abbreviation": "WY"},
    # Territories and DC (6)
    "dc": {"name": "District of Columbia", "full_name": "District of Columbia", "type": "district", "abbreviation": "DC"},
    "pr": {"name": "Puerto Rico", "full_name": "Commonwealth of Puerto Rico", "type": "territory", "abbreviation": "PR"},
    "vi": {"name": "U.S. Virgin Islands", "full_name": "U.S. Virgin Islands", "type": "territory", "abbreviation": "VI"},
    "gu": {"name": "Guam", "full_name": "Territory of Guam", "type": "territory", "abbreviation": "GU"},
    "as": {"name": "American Samoa", "full_name": "Territory of American Samoa", "type": "territory", "abbreviation": "AS"},
    "mp": {"name": "Northern Mariana Islands", "full_name": "Commonwealth of the Northern Mariana Islands", "type": "territory", "abbreviation": "MP"}
}

# Priority jurisdictions (existing ones that have been tested)
PRIORITY_JURISDICTIONS = ["tx", "ca", "ny", "fl", "oh", "fed"]

# Practice areas for all jurisdictions
STANDARD_PRACTICE_AREAS = [
    "personal_injury",
    "criminal_law", 
    "business_law",
    "family_law",
    "employment_law",
    "real_estate",
    "constitutional_law",
    "tax_law",
    "civil_rights",
    "environmental_law"
]

def create_jurisdiction_config(jurisdiction_code: str, jurisdiction_info: Dict[str, Any]) -> Dict[str, Any]:
    """Create a jurisdiction configuration entry"""
    
    # Determine priority
    priority = "high" if jurisdiction_code in PRIORITY_JURISDICTIONS else "medium"
    
    # Enable by default for priority jurisdictions, others can be enabled gradually
    enabled = jurisdiction_code in PRIORITY_JURISDICTIONS
    
    # Generate court list (simplified for now)
    courts = []
    if jurisdiction_code == "fed":
        courts = ["scotus", "ca1", "ca2", "ca3", "ca4", "ca5", "ca6", "ca7", "ca8", "ca9", "ca10", "ca11", "cadc", "cafc"]
    else:
        # State courts (simplified - can be expanded later)
        courts = [f"{jurisdiction_code}"]
    
    # Create schedule based on priority
    schedule = "0 1 * * *" if priority == "high" else "0 3 * * *"
    
    return {
        "name": jurisdiction_info["name"],
        "enabled": enabled,
        "priority": priority,
        "schedule": schedule,
        "courts": courts,
        "practice_areas": STANDARD_PRACTICE_AREAS,
        "search_queries": {
            "personal_injury": [
                "negligence", "tort liability", "personal injury", "medical malpractice",
                "product liability", "premises liability", "motor vehicle accident", "wrongful death"
            ],
            "criminal_law": [
                "criminal procedure", "constitutional rights", "search and seizure", "due process"
            ],
            "business_law": [
                "contract", "commercial law", "business dispute", "corporate law"
            ],
            "family_law": [
                "divorce", "custody", "child support", "adoption", "domestic relations"
            ],
            "employment_law": [
                "employment discrimination", "wage and hour", "workers compensation", "labor law"
            ],
            "real_estate": [
                "property law", "real estate transaction", "landlord tenant", "zoning"
            ],
            "constitutional_law": [
                "constitutional rights", "civil liberties", "due process", "equal protection"
            ],
            "tax_law": [
                "tax assessment", "tax appeal", "property tax", "state tax"
            ]
        },
        "date_range": {
            "start_date": "2020-01-01",
            "incremental_days": 30
        },
        "max_cases_per_run": 100 if priority == "high" else 50
    }

def update_harvesting_config():
    """Update the harvesting configuration to include all 57 jurisdictions"""
    
    config_path = Path("config/harvesting_config.json")
    
    # Read existing config
    if config_path.exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {}
    
    # Ensure base structure exists
    if "harvesting" not in config:
        config["harvesting"] = {
            "enabled": True,
            "default_schedule": "0 2 * * *",
            "max_concurrent_jobs": 10,  # Increased for more jurisdictions
            "retry_attempts": 3,
            "retry_delay_seconds": 300,
            "batch_size": 50,
            "rate_limit_delay": 0.5
        }
    
    # Update jurisdictions
    config["jurisdictions"] = {}
    for jurisdiction_code, jurisdiction_info in ALL_JURISDICTIONS.items():
        config["jurisdictions"][jurisdiction_code] = create_jurisdiction_config(jurisdiction_code, jurisdiction_info)
    
    # Add additional configuration sections
    config["storage"] = {
        "gcs_bucket": "texas-laws-personalinjury",
        "pinecone_index": "texas-laws-voyage3large",
        "neo4j_database": "neo4j",
        "supabase_project": "texas-laws-personalinjury"
    }
    
    config["monitoring"] = {
        "enabled": True,
        "slack_webhook": None,
        "email_alerts": True,
        "prometheus_metrics": True
    }
    
    config["quality_thresholds"] = {
        "min_document_length": 100,
        "min_confidence_score": 0.3,
        "max_duplicate_similarity": 0.95,
        "required_fields": ["case_name", "court", "date_filed"]
    }
    
    # Write updated config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"Updated harvesting config with {len(ALL_JURISDICTIONS)} jurisdictions")
    
    # Show summary
    enabled_count = sum(1 for j in config["jurisdictions"].values() if j["enabled"])
    logger.info(f"Total jurisdictions: {len(ALL_JURISDICTIONS)}")
    logger.info(f"Enabled jurisdictions: {enabled_count}")
    logger.info(f"Priority jurisdictions: {len(PRIORITY_JURISDICTIONS)}")

def create_enhanced_jurisdiction_config():
    """Create an enhanced jurisdiction configuration with all 57 jurisdictions"""
    
    config_path = Path("src/config/complete_jurisdiction_config.json")
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create comprehensive jurisdiction config
    config = {
        "jurisdictions": {},
        "global_settings": {
            "default_jurisdiction": "tx",
            "supported_document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"],
            "cross_jurisdictional_citations": True,
            "jurisdiction_hierarchy_enabled": True,
            "storage_organization": {
                "gcs_structure": "{jurisdiction}/{doc_type}/{year}/{document_id}",
                "pinecone_namespace_pattern": "{jurisdiction}_{doc_type}",
                "neo4j_label_pattern": ["Document", "{Jurisdiction}", "{DocType}"]
            }
        }
    }
    
    # Add each jurisdiction with comprehensive details
    for jurisdiction_code, jurisdiction_info in ALL_JURISDICTIONS.items():
        
        # Create comprehensive jurisdiction entry
        jurisdiction_entry = {
            "code": jurisdiction_code,
            "name": jurisdiction_info["name"],
            "full_name": jurisdiction_info["full_name"],
            "level": jurisdiction_info["type"],
            "parent_jurisdiction": "fed" if jurisdiction_info["type"] != "federal" else None,
            "abbreviation": jurisdiction_info["abbreviation"],
            "court_hierarchy": {
                "levels": []
            },
            "citation_formats": {
                "statute": [f"{jurisdiction_info['abbreviation']}\\. [A-Z][a-z]+ Code § \\d+"],
                "regulation": [f"{jurisdiction_info['abbreviation']}\\. Admin\\. Code § \\d+"],
                "case": {},
                "constitution": [f"{jurisdiction_info['abbreviation']}\\. Const\\. art\\. [IVX]+"]
            },
            "document_types": ["statute", "regulation", "case", "constitution", "administrative_ruling"],
            "storage_config": {
                "gcs_prefix": f"legal/{jurisdiction_code}",
                "pinecone_namespace": jurisdiction_code,
                "neo4j_labels": ["Document", jurisdiction_info["name"].replace(" ", ""), jurisdiction_info["type"].title()]
            }
        }
        
        # Add federal-specific details
        if jurisdiction_code == "fed":
            jurisdiction_entry["court_hierarchy"]["levels"] = [
                {"level": 1, "name": "Supreme Court of the United States", "abbreviation": "SCOTUS", "court_id": "scotus"},
                {"level": 2, "name": "United States Courts of Appeals", "abbreviation": "Circuit Courts", "court_id": "circuit"},
                {"level": 3, "name": "United States District Courts", "abbreviation": "District Courts", "court_id": "district"},
                {"level": 4, "name": "United States Bankruptcy Courts", "abbreviation": "Bankruptcy Courts", "court_id": "bankruptcy"}
            ]
            jurisdiction_entry["citation_formats"]["case"] = {
                "scotus": ["\\d+ U\\.S\\. \\d+", "\\d+ S\\.Ct\\. \\d+"],
                "circuit": ["\\d+ F\\.\\d+d \\d+", "\\d+ F\\. App'x \\d+"],
                "district": ["\\d+ F\\. Supp\\. \\d+d \\d+"],
                "bankruptcy": ["\\d+ B\\.R\\. \\d+"]
            }
        else:
            # State/territory court hierarchy (simplified)
            jurisdiction_entry["court_hierarchy"]["levels"] = [
                {"level": 1, "name": f"Supreme Court of {jurisdiction_info['name']}", "abbreviation": f"{jurisdiction_info['abbreviation']} Sup. Ct.", "court_id": f"{jurisdiction_code}_supreme"},
                {"level": 2, "name": f"{jurisdiction_info['name']} Court of Appeals", "abbreviation": f"{jurisdiction_info['abbreviation']} App.", "court_id": f"{jurisdiction_code}_appeals"},
                {"level": 3, "name": f"{jurisdiction_info['name']} District Court", "abbreviation": f"{jurisdiction_info['abbreviation']} Dist. Ct.", "court_id": f"{jurisdiction_code}_district"}
            ]
            jurisdiction_entry["citation_formats"]["case"] = {
                f"{jurisdiction_code}_supreme": [f"\\d+ {jurisdiction_info['abbreviation']}\\. \\d+d \\d+"],
                f"{jurisdiction_code}_appeals": [f"\\d+ {jurisdiction_info['abbreviation']}\\. App\\. \\d+d \\d+"],
                f"{jurisdiction_code}_district": [f"\\d+ {jurisdiction_info['abbreviation']}\\. Dist\\. \\d+d \\d+"]
            }
        
        config["jurisdictions"][jurisdiction_code] = jurisdiction_entry
    
    # Write the comprehensive config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"Created comprehensive jurisdiction config with {len(ALL_JURISDICTIONS)} jurisdictions")
    logger.info(f"Config saved to: {config_path}")

def main():
    """Main function to update all jurisdiction configurations"""
    
    logger.info("Updating jurisdiction configurations for all 57 US jurisdictions...")
    
    # Update harvesting config
    update_harvesting_config()
    
    # Create enhanced jurisdiction config
    create_enhanced_jurisdiction_config()
    
    logger.info("✅ Successfully updated all jurisdiction configurations!")
    logger.info("\nSummary:")
    logger.info(f"- Total jurisdictions supported: {len(ALL_JURISDICTIONS)}")
    logger.info(f"- Priority jurisdictions (enabled): {len(PRIORITY_JURISDICTIONS)}")
    logger.info(f"- Standard practice areas: {len(STANDARD_PRACTICE_AREAS)}")
    logger.info("\nFiles updated:")
    logger.info("- config/harvesting_config.json")
    logger.info("- src/config/complete_jurisdiction_config.json")
    logger.info("\nNext steps:")
    logger.info("1. Test with priority jurisdictions first")
    logger.info("2. Gradually enable additional jurisdictions")
    logger.info("3. Monitor processing performance and adjust batch sizes")

if __name__ == "__main__":
    main()