# Multi-Practice Area Legal Database System

A comprehensive legal database system supporting 9 practice areas across 6 jurisdictions, featuring automated case harvesting, intelligent document classification, cross-document relationship analysis, and advanced search capabilities.

## 📚 Complete Documentation

### 🚀 Getting Started
- **[User Guide](documentation/USER_GUIDE.md)** - Comprehensive user manual with tutorials and examples
- **[Installation Guide](scripts/README.md)** - Step-by-step installation and setup
- **[Quick Start Tutorial](documentation/USER_GUIDE.md#getting-started)** - Get up and running in 15 minutes
- **[Comprehensive Processing Guide](documentation/COMPREHENSIVE_PROCESSING_GUIDE.md)** - Complete guide for state-by-state legal case processing

### 🏗️ Implementation Documentation
- **[Week 1: Jurisdiction Infrastructure](documentation/WEEK1_IMPLEMENTATION.md)** - Foundation and jurisdiction tagging
- **[Week 2: Automated Harvesting](documentation/WEEK2_IMPLEMENTATION.md)** - Multi-jurisdictional case collection
- **[Week 3: Cross-Document Relationships](documentation/WEEK3_IMPLEMENTATION.md)** - Multi-practice area integration
- **[Week 3 Summary](documentation/WEEK3_SUMMARY.md)** - Feature overview and test results

### 🔧 Technical Reference
- **[API Reference](documentation/API_REFERENCE.md)** - Comprehensive API documentation
- **[Database Schema](documentation/WEEK3_IMPLEMENTATION.md#database-schema)** - Complete database structure
- **[Performance Guide](documentation/PERFORMANCE.md)** - Optimization and scalability
- **[Troubleshooting](documentation/TROUBLESHOOTING.md)** - Common issues and solutions

### 🚀 Operations
- **[Deployment Guide](documentation/DEPLOYMENT.md)** - Production deployment procedures
- **[Monitoring Setup](documentation/DEPLOYMENT.md#monitoring-and-logging)** - System monitoring and alerting
- **[Backup & Recovery](documentation/DEPLOYMENT.md#backup-and-recovery)** - Data protection procedures
- **[Security Configuration](documentation/DEPLOYMENT.md#security-configuration)** - Security best practices
- **[Graph Sync](#graph-sync)** - Neo4j graph database synchronization

### 📊 System Information
- **[Changelog](documentation/CHANGELOG.md)** - Version history and release notes
- **[Performance Benchmarks](documentation/PERFORMANCE.md#system-performance-benchmarks)** - System performance metrics
- **[Architecture Overview](documentation/WEEK3_IMPLEMENTATION.md#architecture)** - System architecture and design

## Graph Sync

The system includes automatic Neo4j graph synchronization that ensures every processed opinion is reflected in the graph database with proper relationships.

### Features

- **Automatic Sync**: Every opinion processed through the dual-source pipeline is automatically synced to Neo4j
- **Idempotent Operations**: Uses `MERGE` operations to prevent duplicates on reruns
- **Batch Processing**: Processes 1,000 operations per transaction for optimal performance
- **Relationship Management**: Automatically creates court, practice area, and citation relationships
- **Delta Validation**: Nightly reports track changes and validate graph integrity

### Configuration

Add Neo4j connection details to your `.env` file:

```bash
# Neo4j Database Configuration
NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_password_here
```

### Usage

Graph sync is automatically integrated into the processing pipeline:

```python
from graph.graph_sync import write_opinion, write_citation, flush_all

# Write opinion to graph (automatically called by processors)
write_opinion(opinion_data)

# Write citation relationship
write_citation(citing_opinion_id, cited_opinion_id)

# Flush all pending operations
flush_all()
```

### Validation Reports

Generate nightly delta reports to monitor graph health:

```bash
python scripts/neo4j_delta_validator.py
```

Reports include:
- Node and relationship counts
- Constraint violation checks
- Court and practice area distributions
- Citation network statistics
- Performance metrics

### Graph Schema

The graph follows a lean model optimized for legal research:

- **(:Court)** - 21 Texas courts with jurisdiction metadata
- **(:PA)** - 7 practice areas (Personal Injury, Criminal Defense, etc.)
- **(:Opinion)** - 92k+ judicial opinions with full metadata
- **(:Court)-[:HEARS_BEFORE]->(:Opinion)** - Court jurisdiction relationships
- **(:Opinion)-[:HAS_PA]->(:PA)** - Practice area classifications
- **(:Opinion)-[:CITES]->(:Opinion)** - Citation network (250k+ relationships)

## Repository Structure

The repository has been organized into the following directory structure for better maintainability and clarity:

```
texas-laws-personalinjury/
├── src/                           # Source code
│   ├── extractors/                # Citation extraction modules
│   ├── validators/                # Validation logic
│   ├── database/                  # Database interactions
│   ├── processing/                # Processing pipelines
│   └── utils/                     # Common utilities
├── apps/                          # Applications
│   └── document_explorer/         # Web app for exploring documents 
├── tests/                         # Testing code
├── scripts/                       # One-off scripts
├── analysis/                      # Analysis scripts
├── logs/                          # Log directory
├── data/                          # Data files
└── documentation/                 # Documentation files
```

## 🚀 Quick Processing Command

**Process any U.S. state's legal cases:**
```bash
python execute_comprehensive_dual_source_processing.py --jurisdiction [state_code]
```

**Examples:**
- Texas: `--jurisdiction tx`
- New York: `--jurisdiction ny`
- Florida: `--jurisdiction fl`

**Features:** Court Listener API + CAP historical data, 7 practice areas, 4-database storage, comprehensive deduplication.

## Current Status

✅ **Week 1 Complete**: Foundation improvements with jurisdiction tagging, document classification, and storage organization
✅ **Week 2 Complete**: Data acquisition and automated case harvesting
✅ **Week 3 Complete**: Multi-practice area integration and cross-document relationships
🚀 **Ready for Week 4**: API development and user interface

### Week 3 Achievements
- **9 Practice Areas Supported**: Personal Injury, Criminal Law, Business Law, Family Law, Employment Law, Real Estate, Tax Law, Constitutional Law, Immigration Law
- **Cross-Document Relationships**: Advanced relationship detection across practice areas
- **Citation Network Analysis**: Authority scoring and influence mapping
- **94.2% Classification Accuracy**: High-precision practice area classification
- **100% Test Coverage**: Comprehensive test suite with all tests passing
- **Production-Ready**: Complete documentation and deployment guides

## Getting Started

### Quick Start (Week 3 - Multi-Practice Area System)

1. Install all dependencies:
   ```bash
   python scripts/install_week1_dependencies.py
   python scripts/install_week2_dependencies.py
   python scripts/install_week3_dependencies.py
   ```

2. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration:
   # SUPABASE_URL=your_supabase_url
   # SUPABASE_KEY=your_supabase_key
   # GOOGLE_CLOUD_PROJECT=your_gcp_project
   # PINECONE_API_KEY=your_pinecone_key
   # COURTLISTENER_API_KEY=your_courtlistener_key
   ```

3. Apply database migrations:
   ```bash
   python scripts/apply_week1_migration.py
   python scripts/apply_week2_migration.py
   python scripts/apply_week3_migration.py
   ```

4. Test the complete system:
   ```bash
   python scripts/test_week3_implementation.py
   ```

5. Start using the system:
   ```bash
   # Classify documents into practice areas
   python -c "
   from src.relationships.practice_area_classifier import PracticeAreaClassifier
   classifier = PracticeAreaClassifier()
   result = classifier.classify_document('Your legal document text here')
   print(f'Practice Area: {result.primary_practice_area}')
   "

   # Start automated harvesting
   python scripts/start_harvester.py
   ```

### Legacy Setup (Week 1)

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. Process documents:
   ```
   python -m src.processing.batch_processor
   ```

4. Load citations into Neo4j:
   ```
   python -m src.database.neo4j_citation_loader
   ```

## Key Components

### Week 3 Features (Multi-Practice Area Integration)

- **Multi-Practice Area Classification**: Automatic classification into 9 practice areas with 94.2% accuracy
- **Cross-Document Relationship Detection**: Advanced relationship analysis across practice areas
- **Citation Network Analysis**: Authority scoring using PageRank algorithm and network centrality
- **Cross-Jurisdictional Support**: Analysis across Federal, State, and Local jurisdictions
- **Relationship Validation**: Comprehensive validation framework ensuring data quality
- **Practice Area Influence Mapping**: Visual representation of practice area interconnections

### Week 2 Features (Automated Harvesting)

- **Automated Case Harvesting**: Scheduled harvesting from Court Listener API with cron-based scheduling
- **Enhanced Opinion Processing**: Advanced opinion classification, quality assessment, and metadata extraction
- **Case Metadata Enhancement**: Comprehensive party extraction, judge identification, and subject matter classification
- **Multi-Jurisdictional Support**: Configurable harvesting for different jurisdictions with custom parameters
- **Quality Filtering**: Intelligent filtering based on citation count, opinion length, and metadata completeness
- **Rate Limiting**: Respectful API usage with configurable delays and batch processing
- **Monitoring & Logging**: Comprehensive status reporting and error tracking

### Week 1 Features (Foundation)

- **Jurisdiction Infrastructure**: Comprehensive multi-jurisdictional support with hierarchical organization
- **Document Type Classification**: Advanced taxonomy for legal document types
- **Enhanced Storage Integration**: Jurisdiction-aware storage across GCS, Pinecone, and Supabase
- **Tenant-Based Access Control**: Multi-tenant architecture with jurisdiction-level access

## Court Listener Integration

### Overview
This project includes a robust multi-system data processing pipeline for integrating Court Listener API data. The pipeline is designed to:

- Fetch case data from Court Listener
- Process and transform case information
- Store data across four different databases:
  1. Supabase: Case metadata and basic information
  2. Neo4j: Case relationships and graph-based insights
  3. Google Cloud Storage: Full-text case documents
  4. Pinecone: Vector embeddings for semantic search

### Key Components

#### Data Processing Pipeline
- `debug_case_processing.py`: End-to-end testing script for case processing
- Connectors for each storage system with enhanced error handling
- Dynamic schema compatibility across storage platforms

#### Storage Systems
- **Supabase**: 
  - Stores case metadata
  - Implements row-level security
  - Uses correct column names (`status`, `updated_at`)

- **Neo4j**: 
  - Creates relationships between cases
  - Enables advanced graph-based queries

- **Google Cloud Storage**: 
  - Stores full-text case documents
  - Generates unique paths and URLs for each document

- **Pinecone**: 
  - Stores vector embeddings of case texts
  - Enables semantic search capabilities

### Workflow
1. Fetch cases from Court Listener
2. Process and transform data
3. Validate and clean case information
4. Store data across multiple databases
5. Prepare for retrieval by separate application

### Performance Considerations
- Batch processing with error recovery
- Minimal real-time API dependencies
- Optimized for fast query responses

### Future Improvements
- Comprehensive testing across jurisdictions
- Advanced AI analysis features
- Scalable deployment infrastructure

### Dependencies
- Python packages for API interactions
- Environment variable management
- Secure API key handling

### Usage
Refer to individual connector scripts in `src/processing/storage/` for detailed implementation.

## Data Refresh Scripts

### CourtListener Opinions Refresh

To refresh all Texas judicial opinions from CourtListener:

```bash
# Refresh with default output directory (data/courtlistener)
python scripts/refresh_cl_opinions.py

# Refresh with custom output directory
python scripts/refresh_cl_opinions.py --output_dir /path/to/output
```

**Requirements:**
- `COURTLISTENER_API_KEY` environment variable must be set
- Internet connection for API access
- Approximately 30-60 minutes for full refresh

**Output Files:**
- `cl_opinions_YYYYMMDD_HHMMSS.json` - All opinion records
- `cl_court_year_counts_YYYYMMDD_HHMMSS.json` - Court×year matrix
- `cl_court_year_counts_YYYYMMDD_HHMMSS.csv` - Court×year matrix (CSV)
- `cl_refresh_summary_YYYYMMDD_HHMMSS.json` - Refresh summary

**Court Coverage:**
- **State Courts**: TXSC, TCCA, TXCOA01-TXCOA14 (21 total courts)
- **Federal Courts**: TXND, TXSD, TXED, TXWD, CA5
- **Expected Volume**: 55,000-100,000+ opinions
- **Date Range**: 1950-present

See `scripts/court_id_mapping.json` for complete court ID mappings.

## Documentation

For detailed documentation, see the `documentation/` directory and READMEs within each module directory.
