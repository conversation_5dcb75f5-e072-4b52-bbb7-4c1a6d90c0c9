#!/usr/bin/env python3
"""
Production CAP Processor
Production-ready CAP processing with same features as CourtListener:
- Checkpoint/resume capability
- Batch processing with QA
- Connection resilience
- Cross-system tracking
- Actual CAP data processing
"""

import asyncio
import logging
import os
import sys
import json
import gzip
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.atomic_storage_pipeline import AtomicStoragePipeline
from src.processing.cross_system_validator import CrossSystemValidator
from src.processing.atomic_checkpoint_manager import AtomicCheckpointManager
from src.processing.retry_manager import RetryManager
from src.ui.enhanced_progress_ui import EnhancedProgressUI
from source_agnostic_processor import CoherentCase, SourceAgnosticProcessor

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'production_cap_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ProductionCAPProcessor:
    """
    Production CAP processor with same features as enhanced CourtListener processor
    """
    
    def __init__(
        self,
        supabase_client,
        gcs_client,
        pinecone_client,
        neo4j_client,
        cap_data_dir: str = "data/caselaw_access_project",
        batch_size: int = 1000,
        chunk_size: int = 20000
    ):
        self.supabase = supabase_client
        self.cap_data_dir = Path(cap_data_dir)
        self.batch_size = batch_size
        self.chunk_size = chunk_size
        
        # Enhanced processing components (identical to CourtListener)
        self.storage_pipeline = AtomicStoragePipeline(
            supabase_client, gcs_client, pinecone_client, neo4j_client, batch_size
        )
        self.checkpoint_manager = AtomicCheckpointManager()
        self.retry_manager = RetryManager()
        self.validator = CrossSystemValidator(
            supabase_client, gcs_client, pinecone_client, neo4j_client
        )
        self.progress_ui = EnhancedProgressUI()
        self.source_processor = SourceAgnosticProcessor(
            supabase_client, gcs_client, pinecone_client, neo4j_client
        )
        
        # Processing state
        self.stats = {
            'files_processed': 0,
            'cases_processed': 0,
            'batches_completed': 0,
            'errors': 0,
            'start_time': None
        }
        
        logger.info("✅ Production CAP Processor initialized")
        logger.info(f"📁 CAP Data Directory: {self.cap_data_dir}")
        logger.info(f"📦 Batch Size: {batch_size}")
        logger.info(f"🔄 Chunk Size: {chunk_size}")
    
    async def process_cap_production(
        self,
        jurisdiction: str = 'tx',
        max_files: Optional[int] = None,
        resume: bool = True,
        test_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Process CAP data with production features
        """
        
        logger.info("🚀 PRODUCTION CAP PROCESSING STARTED")
        logger.info("=" * 80)
        logger.info(f"📍 Jurisdiction: {jurisdiction.upper()}")
        logger.info(f"📁 Data Directory: {self.cap_data_dir}")
        logger.info(f"🔄 Resume Mode: {resume}")
        logger.info(f"🧪 Test Mode: {test_mode}")
        
        self.stats['start_time'] = datetime.now()
        
        try:
            # Initialize progress UI
            # self.progress_ui.initialize("Production CAP Processing")  # Method doesn't exist
            
            # Load checkpoint if resuming
            checkpoint = None
            if resume:
                checkpoint = await self._load_checkpoint(jurisdiction)
                if checkpoint:
                    logger.info(f"📋 Resuming from checkpoint: {checkpoint.get('last_file', 'unknown')}")
            
            # Get CAP files to process
            cap_files = self._get_cap_files(max_files)
            if not cap_files:
                logger.error("❌ No CAP files found")
                return {'success': False, 'error': 'No CAP files found'}
            
            logger.info(f"📚 Found {len(cap_files)} CAP files to process")
            
            # Filter files based on checkpoint
            if checkpoint:
                start_index = self._find_file_index(cap_files, checkpoint.get('last_file', ''))
                cap_files = cap_files[start_index:]
                logger.info(f"📋 Resuming from file index {start_index}")
            
            # Process files with enhanced features
            for file_index, cap_file in enumerate(cap_files):
                logger.info(f"📄 Processing file {file_index + 1}/{len(cap_files)}: {cap_file.name}")
                
                try:
                    # Process single file with batching and QA
                    file_result = await self._process_cap_file_production(
                        cap_file, jurisdiction, file_index, test_mode
                    )
                    
                    if file_result['success']:
                        self.stats['files_processed'] += 1
                        self.stats['cases_processed'] += file_result['cases_processed']
                        self.stats['batches_completed'] += file_result['batches_completed']
                        
                        # Save checkpoint after each file (resilience)
                        await self._save_checkpoint(jurisdiction, cap_file.name, self.stats)
                        
                        logger.info(f"✅ File processed: {file_result['cases_processed']} cases")
                    else:
                        self.stats['errors'] += 1
                        logger.error(f"❌ File processing failed: {file_result.get('error', 'Unknown error')}")
                
                except Exception as e:
                    self.stats['errors'] += 1
                    logger.error(f"💥 Error processing file {cap_file.name}: {e}")
                    
                    # Continue with next file (connection resilience)
                    continue
                
                # Break early in test mode
                if test_mode and file_index >= 1:  # Process max 2 files in test
                    logger.info("🧪 Test mode: stopping after 2 files")
                    break
            
            # Final processing summary
            end_time = datetime.now()
            duration = end_time - self.stats['start_time']
            
            results = {
                'success': True,
                'files_processed': self.stats['files_processed'],
                'cases_processed': self.stats['cases_processed'],
                'batches_completed': self.stats['batches_completed'],
                'errors': self.stats['errors'],
                'duration': str(duration),
                'jurisdiction': jurisdiction.upper()
            }
            
            logger.info("🎉 PRODUCTION CAP PROCESSING COMPLETE")
            logger.info(f"📊 Results: {results}")
            
            return results
            
        except Exception as e:
            logger.error(f"💥 Production CAP processing failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _process_cap_file_production(
        self,
        cap_file: Path,
        jurisdiction: str,
        file_index: int,
        test_mode: bool = False
    ) -> Dict[str, Any]:
        """Process single CAP file with production features"""
        
        logger.info(f"🔄 Processing CAP file: {cap_file.name}")
        
        try:
            # Read and parse cases from file
            raw_cases = []
            line_count = 0
            
            with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line_count += 1
                    
                    try:
                        raw_case = json.loads(line.strip())
                        
                        # Filter for target jurisdiction
                        if self._should_include_case(raw_case, jurisdiction):
                            raw_cases.append(raw_case)
                            
                        # Limit for test mode or chunk size
                        max_cases = 100 if test_mode else self.chunk_size
                        if len(raw_cases) >= max_cases:
                            break
                            
                    except Exception as e:
                        logger.warning(f"Error parsing line {line_num} in {cap_file.name}: {e}")
            
            logger.info(f"   📊 Scanned {line_count} lines, found {len(raw_cases)} relevant cases")
            
            if not raw_cases:
                logger.info(f"   No relevant cases found in {cap_file.name}")
                return {'success': True, 'cases_processed': 0, 'batches_completed': 0}
            
            # Process cases in batches with QA (same pattern as CourtListener)
            cases_processed = 0
            batches_completed = 0
            
            for batch_index in range(0, len(raw_cases), self.batch_size):
                batch = raw_cases[batch_index:batch_index + self.batch_size]
                batch_id = f"cap_{jurisdiction}_{cap_file.stem}_{batch_index//self.batch_size}"
                
                logger.info(f"   📦 Processing batch {batch_index//self.batch_size + 1} ({len(batch)} cases)")
                
                # Process batch with enhanced pipeline and QA
                batch_result = await self._process_batch_with_qa(batch, batch_id, jurisdiction)
                
                if batch_result['success']:
                    cases_processed += len(batch)
                    batches_completed += 1
                    
                    # Update progress UI (same as CourtListener)
                    self.progress_ui.update_stats({
                        'cases_processed': cases_processed,
                        'batches_completed': batches_completed,
                        'current_file': cap_file.name,
                        'success_rate': cases_processed / max(cases_processed, 1)
                    })
                    
                    logger.info(f"   ✅ Batch completed: {len(batch)} cases")
                else:
                    logger.error(f"   ❌ Batch failed: {batch_result.get('error', 'Unknown error')}")
            
            return {
                'success': True,
                'cases_processed': cases_processed,
                'batches_completed': batches_completed
            }
            
        except Exception as e:
            logger.error(f"Error processing CAP file {cap_file.name}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _process_batch_with_qa(
        self,
        raw_cases: List[Dict[str, Any]],
        batch_id: str,
        jurisdiction: str
    ) -> Dict[str, Any]:
        """Process batch with QA validation (identical to CourtListener pattern)"""
        
        try:
            # Process through source-agnostic processor (coherent output)
            result = await self.source_processor.process_coherent_batch(
                raw_cases=raw_cases,
                source_type='caselaw_access_project',
                batch_id=batch_id
            )
            
            if not result['success']:
                return result
            
            # Quality assurance validation (same as CourtListener)
            await self._validate_batch_quality(batch_id, len(raw_cases), jurisdiction)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing batch {batch_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _validate_batch_quality(
        self,
        batch_id: str,
        expected_count: int,
        jurisdiction: str
    ):
        """Validate batch quality (identical to CourtListener)"""
        
        logger.info(f"🔍 Validating quality for batch {batch_id}")
        
        # Use cross-system validator (same as CourtListener)
        validation_report = await self.validator.validate_batch_consistency(
            batch_id=batch_id,
            expected_cases=expected_count
        )
        
        # Update UI with validation results (same as CourtListener)
        for system, count in validation_report.actual_counts.to_dict().items():
            consistency = validation_report.consistency_scores[system]
            status = "✅" if consistency > 0.98 else "⚠️" if consistency > 0.95 else "❌"
            self.progress_ui.update_storage_status(system, count, consistency, status)
        
        # Log validation results
        validation_status = "✅ PASSED" if validation_report.passed else "❌ FAILED"
        logger.info(f"✅ Quality validation complete: {validation_report.overall_score:.1%} ({validation_status})")
        
        return validation_report
    
    def _get_cap_files(self, max_files: Optional[int] = None) -> List[Path]:
        """Get CAP files to process"""
        cap_files = list(self.cap_data_dir.glob("*.jsonl.gz"))
        cap_files.sort()  # Process in order
        
        if max_files:
            cap_files = cap_files[:max_files]
        
        return cap_files
    
    def _should_include_case(self, raw_case: Dict[str, Any], target_jurisdiction: str) -> bool:
        """Determine if case should be included (enhanced filtering)"""

        # Get case text for content-based filtering
        case_text = raw_case.get('text', '').lower()

        # Check jurisdiction in multiple ways
        court_info = raw_case.get('court', {})
        if isinstance(court_info, dict):
            court_name = court_info.get('name', '').lower()
            court_id = court_info.get('id', '').lower()
        else:
            court_name = str(court_info).lower()
            court_id = ''

        # Texas jurisdiction check (enhanced with content analysis)
        if target_jurisdiction.lower() == 'tx':
            texas_indicators = ['texas', 'tex', 'tx']

            # Check court name/id
            court_match = any(indicator in court_name or indicator in court_id
                            for indicator in texas_indicators)

            # Check case text for Texas references (for federal cases with TX jurisdiction)
            text_match = any(f'{indicator} ' in case_text or f', {indicator}' in case_text
                           for indicator in texas_indicators)

            # Include if either court or text indicates Texas jurisdiction
            return court_match or text_match

        # Add other jurisdictions as needed
        elif target_jurisdiction.lower() == 'ny':
            ny_indicators = ['new york', 'ny', 'n.y.']
            court_match = any(indicator in court_name or indicator in court_id
                            for indicator in ny_indicators)
            text_match = any(indicator in case_text for indicator in ny_indicators)
            return court_match or text_match

        return False
    
    def _find_file_index(self, cap_files: List[Path], last_file: str) -> int:
        """Find index of last processed file"""
        for i, cap_file in enumerate(cap_files):
            if cap_file.name == last_file:
                return i + 1  # Start from next file
        return 0  # Start from beginning if not found
    
    async def _load_checkpoint(self, jurisdiction: str) -> Optional[Dict[str, Any]]:
        """Load checkpoint for resuming (same as CourtListener)"""
        try:
            # The checkpoint manager loads the most recent checkpoint
            checkpoint = await self.checkpoint_manager.load_checkpoint()

            # Filter for our jurisdiction if checkpoint exists
            if checkpoint and checkpoint.get('jurisdiction') == jurisdiction:
                return checkpoint
            return None

        except Exception as e:
            logger.warning(f"Could not load checkpoint: {e}")
            return None
    
    async def _save_checkpoint(
        self,
        jurisdiction: str,
        last_file: str,
        stats: Dict[str, Any]
    ):
        """Save checkpoint for resuming (same as CourtListener)"""
        try:
            checkpoint_data = {
                'checkpoint_id': f"cap_{jurisdiction}_checkpoint",  # Required by checkpoint manager
                'jurisdiction': jurisdiction,
                'last_file': last_file,
                'timestamp': datetime.now().isoformat(),
                'stats': stats
            }

            # The checkpoint manager handles the file path internally
            await self.checkpoint_manager.save_checkpoint(checkpoint_data)

        except Exception as e:
            logger.warning(f"Could not save checkpoint: {e}")


# Test function for actual CAP data
async def test_production_cap_on_actual_data():
    """Test production CAP processing on actual CAP data files"""
    
    load_dotenv()
    
    # Setup clients (using mocks for testing)
    from test_cap_enhanced_tracking import MockGCSClient, MockPineconeClient, MockNeo4jClient
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    # Initialize production CAP processor
    processor = ProductionCAPProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client,
        batch_size=50,  # Small batches for testing
        chunk_size=500  # Small chunks for testing
    )
    
    logger.info("🧪 Testing Production CAP Processing on Actual CAP Data")
    
    # Process with production features
    results = await processor.process_cap_production(
        jurisdiction='tx',
        max_files=2,  # Test on first 2 files
        resume=False,  # Fresh start for testing
        test_mode=True  # Enable test mode
    )
    
    logger.info(f"📊 Test Results: {results}")
    
    return results


if __name__ == "__main__":
    asyncio.run(test_production_cap_on_actual_data())
