#!/usr/bin/env python3
"""
Test Text Storage Fix - Verify text is being stored in Neo4j
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_text_storage():
    """Test that text is being stored in Neo4j"""
    
    print("💾 TESTING TEXT STORAGE IN NEO4J")
    print("=" * 60)
    
    load_dotenv()
    
    # Initialize storage clients
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = GCSConnector()
    pinecone_client = RealPineconeClient()
    neo4j_client = RealNeo4jClient()
    
    test_batch_id = f"text_storage_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Create a simple test case with text
        test_case = {
            'id': 'text_test_case_1',
            'source': 'test',
            'case_name': 'Test Case for Text Storage',
            'court': 'test_court',
            'court_name': 'Test Court',
            'date_filed': '2024-01-01',
            'jurisdiction': 'US',
            'text': '''
            This is a test case with judge information.
            
            Chief Justice WARREN delivered the opinion of the Court.
            
            Justice BLACK, with whom Justice HARLAN joins, dissenting.
            
            This text should be stored in Neo4j for judge extraction.
            ''',
            'plain_text': '''
            This is a test case with judge information.
            
            Chief Justice WARREN delivered the opinion of the Court.
            
            Justice BLACK, with whom Justice HARLAN joins, dissenting.
            
            This text should be stored in Neo4j for judge extraction.
            ''',
            'precedential_status': 'Published'
        }
        
        print(f"📄 Test case: {test_case['case_name']}")
        print(f"   Text length: {len(test_case['text'])} characters")
        print(f"   Expected judges: WARREN, BLACK, HARLAN")
        
        # Process through pipeline
        processor = SourceAgnosticProcessor(
            supabase, gcs_client, pinecone_client, neo4j_client
        )
        
        print(f"\n🔄 Processing through pipeline...")
        
        result = await processor.process_coherent_batch(
            raw_cases=[test_case],
            source_type='test',
            batch_id=test_batch_id
        )
        
        print(f"\n📊 PROCESSING RESULT:")
        print(f"   Success: {result['success']}")
        print(f"   Processed: {result['processed']}")
        
        if not result['success']:
            print(f"   ❌ Processing failed: {result}")
            return False
        
        # Check if text was stored in Neo4j
        print(f"\n🔍 VERIFYING TEXT STORAGE IN NEO4J:")
        
        with neo4j_client.driver.session() as session:
            # Check if case exists with text
            result = session.run('''
                MATCH (c:Case {id: $case_id})
                RETURN c.id as case_id, c.case_name as case_name,
                       c.text IS NOT NULL as has_text,
                       size(c.text) as text_length,
                       substring(c.text, 0, 100) as text_sample
            ''', case_id='text_test_case_1')
            
            record = result.single()
            
            if record:
                print(f"   ✅ Case found: {record['case_id']}")
                print(f"   ✅ Case name: {record['case_name']}")
                print(f"   ✅ Has text: {record['has_text']}")
                print(f"   ✅ Text length: {record['text_length']} characters")
                print(f"   ✅ Text sample: {record['text_sample']}")
                
                if record['has_text'] and record['text_length'] > 0:
                    print(f"\n🎉 TEXT STORAGE: SUCCESS!")
                    print(f"✅ Text is being stored in Neo4j correctly")
                    
                    # Test judge extraction on stored text
                    print(f"\n🔍 TESTING JUDGE EXTRACTION ON STORED TEXT:")
                    
                    full_text_result = session.run('''
                        MATCH (c:Case {id: $case_id})
                        RETURN c.text as text
                    ''', case_id='text_test_case_1')
                    
                    text_record = full_text_result.single()
                    if text_record:
                        stored_text = text_record['text']
                        
                        # Test judge patterns
                        import re
                        judge_patterns = [
                            r'Chief\s+Justice\s+([A-Z][a-z]+)',
                            r'Justice\s+([A-Z][a-z]+)',
                        ]
                        
                        found_judges = []
                        for pattern in judge_patterns:
                            matches = re.finditer(pattern, stored_text, re.IGNORECASE)
                            for match in matches:
                                judge_name = match.group(1).strip()
                                found_judges.append(judge_name)
                        
                        print(f"   Judges found in stored text: {found_judges}")
                        
                        if found_judges:
                            print(f"   ✅ Judge extraction working on stored text!")
                            return True
                        else:
                            print(f"   ❌ No judges found in stored text")
                            return False
                    else:
                        print(f"   ❌ Could not retrieve full text")
                        return False
                else:
                    print(f"\n❌ TEXT STORAGE: FAILED!")
                    print(f"❌ Text is not being stored in Neo4j")
                    return False
            else:
                print(f"   ❌ Case not found in Neo4j")
                return False
        
    except Exception as e:
        print(f"❌ Text storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            supabase.table('cases').delete().eq('batch_id', test_batch_id).execute()
            with neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=test_batch_id)
            print(f"\n🧹 Cleaned up test data")
        except:
            pass
        
        neo4j_client.close()


if __name__ == "__main__":
    success = asyncio.run(test_text_storage())
    exit(0 if success else 1)
