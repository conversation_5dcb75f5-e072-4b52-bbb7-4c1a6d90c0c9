#!/usr/bin/env python3
"""
Debug Full Name Extraction
Analyze why we're getting partial names instead of full names
"""

import re
from judge_relationship_enhancer import JudgeRelationshipEnhancer

def test_full_name_patterns():
    """Test full name extraction patterns"""
    
    print("🔍 DEBUGGING FULL NAME EXTRACTION")
    print("=" * 60)
    
    # Sample texts that should extract full names
    test_texts = [
        # Should extract "Ryan <PERSON>"
        """
        Before RYAN, HARRIS, and QUATTLEBAUM, Circuit Judges.
        
        HARRIS, Circuit Judge:
        
        This case involves... Circuit Judge <PERSON> delivered the opinion.
        """,
        
        # Should extract "<PERSON>"  
        """
        Circuit Judge <PERSON> delivered the opinion of the court.
        
        Judge <PERSON> wrote the majority opinion.
        """,
        
        # Should extract "<PERSON>"
        """
        Mr. Chief Justice <PERSON> delivered the opinion of the Court.
        
        <PERSON>, with whom <PERSON> joins, concurring.
        """
    ]
    
    enhancer = JudgeRelationshipEnhancer()
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📄 TEST CASE {i}:")
        print(f"   Text: {text[:100].replace(chr(10), ' ')}...")
        
        # Extract judges
        judges = enhancer._extract_judges_from_text(text)
        
        print(f"   📊 Extracted judges: {len(judges)}")
        for judge in judges:
            print(f"      - '{judge['name']}' (role: {judge['role']})")
        
        # Analyze what should have been extracted
        expected_full_names = []
        if i == 1:
            expected_full_names = ["Ryan M. <PERSON>"]
        elif i == 2:
            expected_full_names = ["<PERSON> <PERSON>. <PERSON>"]
        elif i == 3:
            expected_full_names = ["<PERSON> <PERSON>"]
        
        print(f"   🎯 Expected full names: {expected_full_names}")
        
        # Check if we got full names
        extracted_names = [j['name'] for j in judges]
        full_names_found = [name for name in extracted_names if len(name.split()) > 1]
        
        print(f"   ✅ Full names found: {full_names_found}")
        print(f"   ❌ Partial names found: {[name for name in extracted_names if len(name.split()) == 1]}")
    
    enhancer.close()


def analyze_pattern_issues():
    """Analyze why patterns aren't capturing full names"""
    
    print(f"\n🔬 ANALYZING PATTERN ISSUES")
    print("=" * 60)
    
    # The problematic text from our test
    sample_text = """
    Before RYAN, HARRIS, and QUATTLEBAUM, Circuit Judges.
    
    HARRIS, Circuit Judge:
    
    This matter comes before the court on appeal. Circuit Judge Ryan M. Harris 
    delivered the opinion of the court.
    """
    
    print(f"📄 SAMPLE TEXT:")
    print(f"   {sample_text}")
    
    # Current patterns from JudgeRelationshipEnhancer
    patterns = [
        r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|concurred|dissented)',
        r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s*[,:]\s*(?:delivered|wrote|authored)',
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|presiding)',
        r'Before[^.]*?([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Circuit\s+)?Judge',
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Circuit\s+)?Judge\s*[,:]',
    ]
    
    print(f"\n🔍 TESTING INDIVIDUAL PATTERNS:")
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, sample_text, re.IGNORECASE)
        found_matches = []
        
        for match in matches:
            extracted = match.group(1).strip()
            context_start = max(0, match.start() - 30)
            context_end = min(len(sample_text), match.end() + 30)
            context = sample_text[context_start:context_end].replace('\n', ' ').strip()
            
            found_matches.append({
                'extracted': extracted,
                'context': context
            })
        
        if found_matches:
            for j, match in enumerate(found_matches, 1):
                print(f"      Match {j}: '{match['extracted']}'")
                print(f"         Context: ...{match['context']}...")
                
                # Check if this is a full name
                words = match['extracted'].split()
                is_full_name = len(words) > 1
                print(f"         Full name: {'✅' if is_full_name else '❌'} ({len(words)} words)")
        else:
            print(f"      No matches")
    
    print(f"\n💡 ISSUES IDENTIFIED:")
    print(f"   1. Panel pattern extracts 'HARRIS' from 'Before RYAN, HARRIS, and QUATTLEBAUM'")
    print(f"   2. Full name pattern should extract 'Ryan M. Harris' from 'Circuit Judge Ryan M. Harris delivered'")
    print(f"   3. Need better regex to capture middle initials and full names")


def suggest_improved_patterns():
    """Suggest improved patterns for full name extraction"""
    
    print(f"\n💡 IMPROVED PATTERN SUGGESTIONS")
    print("=" * 60)
    
    # Improved patterns that should capture full names better
    improved_patterns = [
        # Full name with action (most reliable)
        r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s+(?:delivered|wrote|authored)',
        r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s+(?:delivered|wrote|authored)',
        
        # Full name with title and colon (common format)
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*),\s+(?:Circuit|District)\s+Judge\s*:',
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*),\s+(?:Chief\s+)?Justice\s*:',
        
        # Mr./Ms. formal patterns
        r'Mr\.\s+(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)',
        r'Ms\.\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)',
        
        # Opinion attribution patterns
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',
        
        # Panel patterns (but only if we can get full names)
        r'Before[^.]*?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)[^,]*,\s+(?:Circuit\s+)?Judge',
    ]
    
    print(f"📊 TESTING IMPROVED PATTERNS:")
    
    test_text = """
    Circuit Judge Ryan M. Harris delivered the opinion.
    
    Jennifer L. Smith, Circuit Judge:
    
    Mr. Chief Justice Earl Warren delivered the opinion.
    
    Before WILLIAMS, HARRIS, and QUATTLEBAUM, Circuit Judges.
    
    Ryan M. Harris, J., concurring.
    """
    
    for i, pattern in enumerate(improved_patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        if matches:
            for match in matches:
                words = match.split()
                is_full_name = len(words) > 1
                print(f"      → '{match}' ({'✅ Full name' if is_full_name else '❌ Partial'})")
        else:
            print(f"      → No matches")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"   1. Prioritize patterns that require action words (delivered, wrote, authored)")
    print(f"   2. Use patterns that capture title + colon format")
    print(f"   3. Avoid panel patterns unless they can capture full names")
    print(f"   4. Prefer longer matches over shorter ones in deduplication")


if __name__ == "__main__":
    print("🧪 FULL NAME EXTRACTION DEBUGGING")
    print("=" * 80)
    
    # Test current patterns
    test_full_name_patterns()
    
    # Analyze pattern issues
    analyze_pattern_issues()
    
    # Suggest improvements
    suggest_improved_patterns()
    
    print(f"\n🎯 CONCLUSION:")
    print(f"   Current patterns are extracting partial names from panel listings")
    print(f"   Need to prioritize patterns that capture full names with context")
    print(f"   Should prefer 'Circuit Judge Ryan M. Harris delivered' over 'HARRIS, Circuit Judge'")
