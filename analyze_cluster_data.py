#!/usr/bin/env python3
"""
CourtListener Cluster Data Analysis
Check what judge information is available in cluster endpoints
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def analyze_cluster_data():
    """Analyze cluster data for judge information"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🔍 ANALYZING COURTLISTENER CLUSTER DATA")
    print("=" * 60)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # First get some opinions to get cluster IDs
        opinions_url = f"{base_url}/opinions/"
        params = {
            'court': 'txnd,txsd',
            'ordering': '-date_created',
            'page_size': 3,
            'format': 'json'
        }
        
        try:
            print("📡 Fetching opinions to get cluster IDs...")
            response = await client.get(opinions_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            opinions = data.get('results', [])
            
            for i, opinion in enumerate(opinions, 1):
                cluster_url = opinion.get('cluster')
                if cluster_url:
                    print(f"\n📄 OPINION {i}: ID {opinion.get('id')}")
                    print(f"   Cluster URL: {cluster_url}")
                    
                    try:
                        # Fetch cluster data
                        cluster_response = await client.get(cluster_url)
                        cluster_response.raise_for_status()
                        
                        cluster_data = cluster_response.json()
                        
                        print(f"   📋 Cluster fields: {list(cluster_data.keys())}")
                        
                        # Check for judge-related fields in cluster
                        judge_fields = ['judges', 'panel', 'author', 'author_str']
                        for field in judge_fields:
                            value = cluster_data.get(field)
                            if value:
                                print(f"   ✅ {field}: {value}")
                            else:
                                print(f"   ❌ {field}: {value}")
                        
                        # Check if cluster has sub_opinions with judge data
                        sub_opinions = cluster_data.get('sub_opinions', [])
                        if sub_opinions:
                            print(f"   📄 Sub-opinions: {len(sub_opinions)}")
                            for j, sub_op in enumerate(sub_opinions[:2]):  # Check first 2
                                if isinstance(sub_op, str):
                                    # It's a URL, fetch it
                                    print(f"      Sub-opinion {j+1}: {sub_op}")
                                    try:
                                        sub_response = await client.get(sub_op)
                                        if sub_response.status_code == 200:
                                            sub_data = sub_response.json()
                                            sub_author = sub_data.get('author_str', '')
                                            sub_judges = sub_data.get('judges', [])
                                            print(f"         Author: {sub_author}")
                                            print(f"         Judges: {sub_judges}")
                                    except Exception as e:
                                        print(f"         ❌ Error: {e}")
                                else:
                                    print(f"      Sub-opinion {j+1}: {sub_op}")
                        
                        # Check case_name for potential judge info
                        case_name = cluster_data.get('case_name', '')
                        if case_name:
                            print(f"   📝 Case name: {case_name[:100]}...")
                        
                    except Exception as e:
                        print(f"   ❌ Error fetching cluster: {e}")
            
            # Also try the People/Judges endpoint to see what's available
            print(f"\n🔍 CHECKING PEOPLE/JUDGES ENDPOINT")
            print("=" * 60)
            
            people_url = f"{base_url}/people/"
            people_params = {
                'page_size': 3,
                'format': 'json'
            }
            
            try:
                people_response = await client.get(people_url, params=people_params)
                people_response.raise_for_status()
                
                people_data = people_response.json()
                people = people_data.get('results', [])
                
                print(f"📊 Found {len(people)} people records")
                for person in people[:2]:
                    print(f"   👤 {person.get('name_full', 'Unknown')}")
                    print(f"      ID: {person.get('id')}")
                    print(f"      Positions: {person.get('positions', [])}")
                    
            except Exception as e:
                print(f"❌ Error checking people endpoint: {e}")
                
        except Exception as e:
            print(f"❌ Error analyzing clusters: {e}")

if __name__ == "__main__":
    asyncio.run(analyze_cluster_data())
