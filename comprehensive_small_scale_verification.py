#!/usr/bin/env python3
"""
Comprehensive Small-Scale Verification
Verify all enhanced processes work correctly on small datasets before any large-scale testing
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client
from pathlib import Path
import json

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ComprehensiveSmallScaleVerification:
    """Comprehensive verification of all enhanced processes on small datasets"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # CAP data directory
        self.cap_data_dir = Path("data/caselaw_access_project")
        
        # Test batch ID
        self.test_batch_id = f"small_scale_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Verification results
        self.verification_results = {
            'courtlistener_texas': {'processed': 0, 'success': False, 'details': {}},
            'courtlistener_newyork': {'processed': 0, 'success': False, 'details': {}},
            'cap_data': {'processed': 0, 'success': False, 'details': {}},
            'cross_system_tracking': {'success': False, 'details': {}},
            'enhanced_features': {'success': False, 'details': {}}
        }
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_courtlistener_cases(self, jurisdiction: str, max_cases: int = 25) -> list:
        """Fetch small set of CourtListener cases for verification"""
        
        print(f"\n🌐 FETCHING COURTLISTENER CASES: {jurisdiction.upper()}")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        # Map jurisdictions to court codes
        court_mapping = {
            'texas': ['txnd', 'txsd', 'txed', 'txwd'],  # Texas federal district courts
            'newyork': ['nynd', 'nysd', 'nyed', 'nywd']  # New York federal district courts
        }
        
        courts = court_mapping.get(jurisdiction, [])
        if not courts:
            print(f"❌ Unknown jurisdiction: {jurisdiction}")
            return []
        
        all_cases = []
        
        try:
            for court in courts:
                if len(all_cases) >= max_cases:
                    break
                
                print(f"   📡 Fetching from court: {court}")
                
                response = requests.get(
                    f"{self.cl_base_url}/opinions/",
                    headers=headers,
                    params={
                        'court': court,
                        'filed_after': '2020-01-01',
                        'ordering': '-date_filed',
                        'page_size': min(10, max_cases - len(all_cases))
                    },
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"   ⚠️ API request failed for {court}: {response.status_code}")
                    continue
                
                results = response.json().get('results', [])
                
                for case in results:
                    if len(all_cases) >= max_cases:
                        break
                    
                    text = case.get('plain_text', '') or case.get('html', '')
                    
                    if not text or len(text) < 500:
                        continue  # Skip cases without substantial text
                    
                    # Create processing format with unique ID including court
                    case_data = {
                        'id': f"cl_{jurisdiction}_{court}_{case.get('id', 'unknown')}",
                        'source': 'courtlistener',
                        'case_name': case.get('case_name', 'Unknown'),
                        'court': case.get('court', ''),
                        'court_name': f"Court {case.get('court', '')}",
                        'date_filed': case.get('date_filed', ''),
                        'jurisdiction': jurisdiction.upper(),
                        'text': text,
                        'precedential_status': case.get('precedential_status', 'Unknown')
                    }
                    
                    all_cases.append(case_data)
                    print(f"   ✅ Added case: {case_data['case_name'][:50]}...")
                
                time.sleep(0.5)  # Rate limiting
        
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
        
        print(f"✅ FETCHED {len(all_cases)} COURTLISTENER CASES FOR {jurisdiction.upper()}")
        return all_cases
    
    def fetch_cap_cases(self, max_cases: int = 10) -> list:
        """Fetch small set of CAP cases for verification"""
        
        print(f"\n📚 FETCHING CAP CASES")
        print("=" * 60)
        
        if not self.cap_data_dir.exists():
            print(f"❌ CAP data directory not found: {self.cap_data_dir}")
            return []
        
        # Look for Texas CAP files
        tx_files = list(self.cap_data_dir.glob("**/texas/**/*.json"))
        
        if not tx_files:
            print(f"❌ No Texas CAP files found")
            return []
        
        all_cases = []
        
        try:
            for file_path in tx_files[:3]:  # Process first 3 files only
                if len(all_cases) >= max_cases:
                    break
                
                print(f"   📄 Processing file: {file_path.name}")
                
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                cases = data.get('cases', [])
                
                for case in cases:
                    if len(all_cases) >= max_cases:
                        break
                    
                    # Extract case text
                    opinions = case.get('casebody', {}).get('opinions', [])
                    if not opinions:
                        continue
                    
                    text = opinions[0].get('text', '')
                    if not text or len(text) < 500:
                        continue
                    
                    # Create processing format with unique ID including court and timestamp
                    import time
                    unique_suffix = f"{int(time.time() * 1000)}"  # Millisecond timestamp
                    case_data = {
                        'id': f"cap_tx_{case.get('court', {}).get('slug', 'unknown')}_{case.get('id', unique_suffix)}",
                        'source': 'caselaw_access_project',
                        'case_name': case.get('name', 'Unknown'),
                        'court': case.get('court', {}).get('slug', ''),
                        'court_name': case.get('court', {}).get('name', ''),
                        'date_filed': case.get('decision_date', ''),
                        'jurisdiction': 'TX',
                        'text': text,
                        'precedential_status': 'Published'
                    }
                    
                    all_cases.append(case_data)
                    print(f"   ✅ Added case: {case_data['case_name'][:50]}...")
        
        except Exception as e:
            print(f"❌ Error fetching CAP cases: {e}")
        
        print(f"✅ FETCHED {len(all_cases)} CAP CASES")
        return all_cases
    
    async def verify_courtlistener_processing(self) -> bool:
        """Verify CourtListener processing for Texas and New York"""
        
        print(f"\n🔄 VERIFYING COURTLISTENER PROCESSING")
        print("=" * 60)
        
        try:
            # Test Texas
            print(f"\n📍 TESTING TEXAS COURTLISTENER DATA")
            tx_cases = self.fetch_courtlistener_cases('texas', max_cases=25)
            
            if tx_cases:
                tx_result = await self._process_and_verify(
                    tx_cases, 'courtlistener_texas', 'courtlistener'
                )
                self.verification_results['courtlistener_texas'] = tx_result
            else:
                print(f"❌ No Texas cases to process")
                self.verification_results['courtlistener_texas']['success'] = False
            
            # Test New York
            print(f"\n📍 TESTING NEW YORK COURTLISTENER DATA")
            ny_cases = self.fetch_courtlistener_cases('newyork', max_cases=25)
            
            if ny_cases:
                ny_result = await self._process_and_verify(
                    ny_cases, 'courtlistener_newyork', 'courtlistener'
                )
                self.verification_results['courtlistener_newyork'] = ny_result
            else:
                print(f"❌ No New York cases to process")
                self.verification_results['courtlistener_newyork']['success'] = False
            
            # Overall CourtListener success
            tx_success = self.verification_results['courtlistener_texas']['success']
            ny_success = self.verification_results['courtlistener_newyork']['success']
            
            overall_success = tx_success and ny_success
            
            print(f"\n📊 COURTLISTENER VERIFICATION SUMMARY:")
            print(f"   Texas: {'✅' if tx_success else '❌'} ({self.verification_results['courtlistener_texas']['processed']} cases)")
            print(f"   New York: {'✅' if ny_success else '❌'} ({self.verification_results['courtlistener_newyork']['processed']} cases)")
            print(f"   Overall: {'✅' if overall_success else '❌'}")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ CourtListener verification failed: {e}")
            return False
    
    async def verify_cap_processing(self) -> bool:
        """Verify CAP data processing"""
        
        print(f"\n🔄 VERIFYING CAP PROCESSING")
        print("=" * 60)
        
        try:
            cap_cases = self.fetch_cap_cases(max_cases=10)
            
            if not cap_cases:
                print(f"❌ No CAP cases to process")
                self.verification_results['cap_data']['success'] = False
                return False
            
            cap_result = await self._process_and_verify(
                cap_cases, 'cap_data', 'caselaw_access_project'
            )
            self.verification_results['cap_data'] = cap_result
            
            print(f"\n📊 CAP VERIFICATION SUMMARY:")
            print(f"   Processed: {cap_result['processed']} cases")
            print(f"   Success: {'✅' if cap_result['success'] else '❌'}")
            
            return cap_result['success']
            
        except Exception as e:
            print(f"❌ CAP verification failed: {e}")
            return False
    
    async def _process_and_verify(self, cases: list, test_name: str, source_type: str) -> dict:
        """Process cases and verify all systems"""
        
        batch_id = f"{self.test_batch_id}_{test_name}"
        
        print(f"\n📊 Processing {len(cases)} cases through enhanced pipeline...")
        
        try:
            # Process through enhanced pipeline with optimized settings for small-scale testing
            processor = SourceAgnosticProcessor(
                self.supabase,
                self.gcs_client,
                self.pinecone_client,
                self.neo4j_client,
                enable_legal_relationships=False,  # Disable for small-scale testing
                legal_relationship_timeout=60      # Short timeout if enabled
            )

            print(f"   ⚖️ Legal relationships: DISABLED (optimized for small-scale testing)")
            print(f"   🔧 Enhanced text retrieval: ENABLED")
            print(f"   📊 Enhanced judge features: ENABLED")
            
            result = await processor.process_coherent_batch(
                raw_cases=cases,
                source_type=source_type,
                batch_id=batch_id
            )
            
            print(f"   Processing result: {'✅' if result['success'] else '❌'}")
            print(f"   Cases processed: {result['processed']}")
            
            if not result['success']:
                return {
                    'processed': 0,
                    'success': False,
                    'details': {'error': 'Processing failed', 'result': result}
                }
            
            # Verify cross-system tracking
            verification_details = await self._verify_cross_system_tracking(
                [case['id'] for case in cases], batch_id
            )
            
            return {
                'processed': result['processed'],
                'success': verification_details['success'],
                'details': verification_details
            }
            
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            return {
                'processed': 0,
                'success': False,
                'details': {'error': str(e)}
            }
    
    async def _verify_cross_system_tracking(self, case_ids: list, batch_id: str) -> dict:
        """Verify cross-system tracking and enhanced features"""
        
        print(f"\n🔍 VERIFYING CROSS-SYSTEM TRACKING")
        
        verification = {
            'success': False,
            'supabase_count': 0,
            'gcs_count': 0,
            'pinecone_count': 0,
            'neo4j_count': 0,
            'judges_count': 0,
            'enhanced_features': {},
            'consistency_check': False
        }
        
        try:
            # Check Supabase
            supabase_response = self.supabase.table('cases').select('*').eq('batch_id', batch_id).execute()
            verification['supabase_count'] = len(supabase_response.data)
            print(f"   Supabase: {verification['supabase_count']} cases")
            
            # Check Neo4j
            with self.neo4j_client.driver.session() as session:
                # Count cases
                case_result = session.run('MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as count', batch_id=batch_id)
                verification['neo4j_count'] = case_result.single()['count']
                print(f"   Neo4j Cases: {verification['neo4j_count']} cases")
                
                # Count judges with enhanced features
                judge_result = session.run('''
                    MATCH (j:Judge)<-[r]-(c:Case {batch_id: $batch_id})
                    RETURN count(DISTINCT j) as judge_count,
                           avg(j.confidence_score) as avg_confidence,
                           count(CASE WHEN j.external_validated = true THEN 1 END) as validated_count
                ''', batch_id=batch_id)
                
                judge_data = judge_result.single()
                verification['judges_count'] = judge_data['judge_count'] or 0
                verification['enhanced_features'] = {
                    'avg_confidence': judge_data['avg_confidence'] or 0,
                    'validated_judges': judge_data['validated_count'] or 0
                }
                
                print(f"   Neo4j Judges: {verification['judges_count']} judges")
                print(f"   Avg Confidence: {verification['enhanced_features']['avg_confidence']:.1f}")
                print(f"   Validated Judges: {verification['enhanced_features']['validated_judges']}")
            
            # Check consistency
            consistency = (
                verification['supabase_count'] > 0 and
                verification['neo4j_count'] > 0 and
                verification['supabase_count'] == verification['neo4j_count']
            )
            verification['consistency_check'] = consistency
            
            # Overall success
            verification['success'] = (
                consistency and
                verification['judges_count'] > 0 and
                verification['enhanced_features']['avg_confidence'] > 0
            )
            
            print(f"   Consistency: {'✅' if consistency else '❌'}")
            print(f"   Enhanced Features: {'✅' if verification['enhanced_features']['avg_confidence'] > 0 else '❌'}")
            
        except Exception as e:
            print(f"❌ Cross-system verification failed: {e}")
            verification['error'] = str(e)
        
        return verification
    
    async def run_comprehensive_verification(self) -> bool:
        """Run complete small-scale verification"""
        
        print("🔍 COMPREHENSIVE SMALL-SCALE VERIFICATION (OPTIMIZED)")
        print("=" * 80)
        print("🎯 Verifying all enhanced processes on small datasets")
        print("📊 Max 50 CourtListener cases (25 TX + 25 NY) + 10 CAP cases")
        print("🔧 FIXES APPLIED:")
        print("   ✅ Enhanced text retrieval with multiple fallback strategies")
        print("   ✅ Legal relationship processing disabled for speed")
        print("   ✅ Timeout protection for long-running operations")
        
        try:
            # 1. Verify CourtListener processing
            cl_success = await self.verify_courtlistener_processing()
            
            # 2. Verify CAP processing
            cap_success = await self.verify_cap_processing()
            
            # 3. Overall assessment
            overall_success = cl_success and cap_success
            
            print(f"\n📊 COMPREHENSIVE VERIFICATION RESULTS:")
            print("=" * 60)
            print(f"   CourtListener (TX + NY): {'✅' if cl_success else '❌'}")
            print(f"   CAP Data: {'✅' if cap_success else '❌'}")
            print(f"   Overall Success: {'✅' if overall_success else '❌'}")
            
            if overall_success:
                print(f"\n🎉 COMPREHENSIVE VERIFICATION: SUCCESS!")
                print(f"✅ All enhanced processes working correctly")
                print(f"✅ Cross-system tracking verified")
                print(f"✅ Enhanced features (confidence, career, validation) active")
                print(f"✅ Ready for larger-scale testing")
            else:
                print(f"\n❌ COMPREHENSIVE VERIFICATION: FAILED!")
                print(f"❌ Some processes not working correctly")
                print(f"❌ Need to fix issues before larger-scale testing")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ Comprehensive verification failed: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Clean up all test data"""
        
        print(f"\n🧹 CLEANING UP VERIFICATION TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().like('batch_id', f'{self.test_batch_id}%').execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case) WHERE c.batch_id STARTS WITH $batch_prefix DETACH DELETE c', 
                          batch_prefix=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run comprehensive small-scale verification"""
    
    verification = ComprehensiveSmallScaleVerification()
    
    try:
        success = await verification.run_comprehensive_verification()
        return success
    finally:
        await verification.cleanup_test_data()
        verification.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
