#!/usr/bin/env python3
"""
Clean up all databases for a fresh start with consistent data.
KEEPS Supabase as the source of truth, cleans everything else.
"""

import asyncio
import sys
import os
from pinecone import Pinecone

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector
from processing.storage.gcs_connector import GCSConnector

async def cleanup_neo4j():
    """Clean up Neo4j Document nodes and related data."""
    
    try:
        print("🗑️  CLEANING UP NEO4J")
        print("=" * 50)
        
        neo4j = Neo4jConnector()
        
        with neo4j.driver.session() as session:
            # Count current nodes
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            doc_count = result.single()['count']
            print(f"   Current Document nodes: {doc_count:,}")
            
            # Delete all Document nodes and their relationships
            print("   🗑️  Deleting all Document nodes...")
            result = session.run("MATCH (d:Document) DETACH DELETE d")
            print("   ✅ Document nodes deleted")
            
            # Delete Citation nodes (they reference documents)
            result = session.run("MATCH (c:Citation) RETURN count(c) as count")
            citation_count = result.single()['count']
            print(f"   Current Citation nodes: {citation_count:,}")
            
            print("   🗑️  Deleting all Citation nodes...")
            result = session.run("MATCH (c:Citation) DETACH DELETE c")
            print("   ✅ Citation nodes deleted")
            
            # Keep Courts, Jurisdictions, and other structural nodes
            print("   ✅ Keeping structural nodes (Courts, Jurisdictions, etc.)")
            
            # Verify cleanup
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            remaining_docs = result.single()['count']
            
            result = session.run("MATCH (c:Citation) RETURN count(c) as count")
            remaining_citations = result.single()['count']
            
            print(f"   📊 Cleanup results:")
            print(f"     Document nodes remaining: {remaining_docs}")
            print(f"     Citation nodes remaining: {remaining_citations}")
        
        neo4j.close()
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Neo4j: {e}")
        return False

async def cleanup_pinecone():
    """Clean up Pinecone vectors."""
    
    try:
        print("\n🗑️  CLEANING UP PINECONE")
        print("=" * 50)
        
        # Initialize Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        
        # Get current stats
        stats = index.describe_index_stats()
        print(f"   Current vectors: {stats.total_vector_count:,}")
        
        # Delete all vectors
        print("   🗑️  Deleting all vectors...")
        index.delete(delete_all=True)
        print("   ✅ All vectors deleted")
        
        # Verify cleanup
        import time
        time.sleep(5)  # Wait for deletion to propagate
        stats = index.describe_index_stats()
        print(f"   📊 Vectors remaining: {stats.total_vector_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Pinecone: {e}")
        return False

async def cleanup_gcs():
    """Clean up Google Cloud Storage files."""
    
    try:
        print("\n🗑️  CLEANING UP GOOGLE CLOUD STORAGE")
        print("=" * 50)
        
        gcs = GCSConnector()
        bucket = gcs.client.bucket(gcs.bucket_name)
        
        # Count current files
        all_blobs = list(bucket.list_blobs())
        print(f"   Current files: {len(all_blobs):,}")
        
        # Delete files by category
        categories = {
            'caselaw_access/': 'Caselaw Access Project files',
            'court_listener/': 'Court Listener files',
            'cases/': 'Case files'
        }
        
        total_deleted = 0
        for prefix, description in categories.items():
            blobs = list(bucket.list_blobs(prefix=prefix))
            if blobs:
                print(f"   🗑️  Deleting {len(blobs)} {description}...")
                for blob in blobs:
                    blob.delete()
                total_deleted += len(blobs)
                print(f"   ✅ Deleted {len(blobs)} files")
        
        # Keep statute files and other non-case files
        print("   ✅ Keeping statute files and other structural data")
        
        print(f"   📊 Total files deleted: {total_deleted:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning GCS: {e}")
        return False

async def verify_supabase_intact():
    """Verify Supabase data is intact (our source of truth)."""
    
    try:
        print("\n✅ VERIFYING SUPABASE DATA INTACT")
        print("=" * 50)
        
        from processing.storage.supabase_connector import SupabaseConnector
        supabase = SupabaseConnector()
        
        # Count cases
        response = supabase.client.table('cases').select('id', count='exact').execute()
        case_count = response.count
        
        print(f"   📊 Supabase cases: {case_count:,}")
        print("   ✅ Supabase data intact - will be our source of truth")
        
        return case_count
        
    except Exception as e:
        print(f"❌ Error verifying Supabase: {e}")
        return 0

async def main():
    """Main cleanup function."""
    
    print("🧹 FRESH START DATABASE CLEANUP")
    print("=" * 70)
    print("This will clean all databases EXCEPT Supabase (source of truth)")
    print("=" * 70)
    
    # Verify Supabase first
    case_count = await verify_supabase_intact()
    if case_count == 0:
        print("❌ Cannot proceed - Supabase verification failed")
        return
    
    # Perform cleanup
    neo4j_success = await cleanup_neo4j()
    pinecone_success = await cleanup_pinecone()
    gcs_success = await cleanup_gcs()
    
    print(f"\n🎯 CLEANUP SUMMARY:")
    print(f"   Neo4j cleanup: {'✅ Success' if neo4j_success else '❌ Failed'}")
    print(f"   Pinecone cleanup: {'✅ Success' if pinecone_success else '❌ Failed'}")
    print(f"   GCS cleanup: {'✅ Success' if gcs_success else '❌ Failed'}")
    print(f"   Supabase: ✅ Intact ({case_count:,} cases)")
    
    if all([neo4j_success, pinecone_success, gcs_success]):
        print(f"\n🎉 FRESH START READY!")
        print(f"   ✅ All databases cleaned")
        print(f"   ✅ Supabase preserved as source of truth")
        print(f"   🚀 Ready to reprocess {case_count:,} cases with perfect consistency")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Run the Court Listener processor")
        print(f"   2. All cases will be processed with consistent IDs")
        print(f"   3. Perfect 100% consistency across all databases")
    else:
        print(f"\n⚠️  PARTIAL CLEANUP")
        print(f"   Some cleanup operations failed - check logs above")

if __name__ == "__main__":
    # Confirm before proceeding
    print("⚠️  WARNING: This will delete all data except Supabase!")
    print("   - Neo4j: All Document and Citation nodes")
    print("   - Pinecone: All vectors") 
    print("   - GCS: All case files")
    print("   - Supabase: PRESERVED (source of truth)")
    
    confirm = input("\nType 'YES' to proceed with cleanup: ")
    if confirm == 'YES':
        asyncio.run(main())
    else:
        print("❌ Cleanup cancelled")
