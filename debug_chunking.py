#!/usr/bin/env python3
"""
Debug the chunking process to see why we're only getting 1 vector per case
"""

import json
import gzip
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from source_agnostic_processor import CoherentCase

def debug_chunking():
    """Debug the chunking process"""

    print("🔍 DEBUGGING CAP CHUNKING PROCESS")
    print("=" * 60)

    cap_file = Path("data/caselaw_access_project/cap_00000.jsonl.gz")

    # Test a few cases
    texas_cases_found = 0
    with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 50:  # Check more cases to find Texas ones
                break

            try:
                raw_case = json.loads(line.strip())

                # Check if it's a Texas case
                text = raw_case.get('text', '')
                if not ('texas' in text.lower() or 'tex' in text.lower()):
                    continue

                texas_cases_found += 1
                if texas_cases_found > 3:  # Only test first 3 Texas cases
                    break

                print(f"\n📄 TEXAS CASE {texas_cases_found}: {raw_case.get('id', 'unknown')}")
                print(f"   Raw text length: {len(text)} chars")
                print(f"   Raw word count: {len(text.split())} words")

                # Create CoherentCase
                coherent_case = CoherentCase(raw_case, 'caselaw_access_project')

                print(f"   CoherentCase word_count: {coherent_case.word_count}")
                print(f"   CoherentCase text length: {len(coherent_case.text_content)} chars")

                # Test chunking directly
                chunks = coherent_case._chunk_text(coherent_case.text_content)
                print(f"   Number of chunks created: {len(chunks)}")

                for j, chunk in enumerate(chunks):
                    chunk_words = len(chunk.split())
                    print(f"      Chunk {j+1}: {chunk_words} words")
                    if j == 0:  # Show preview of first chunk
                        preview = chunk[:100] + "..." if len(chunk) > 100 else chunk
                        print(f"      Preview: {preview}")

                # Test vector creation
                vectors = coherent_case.to_pinecone_vectors()
                print(f"   Number of vectors created: {len(vectors)}")

                for j, vector in enumerate(vectors):
                    print(f"      Vector {j+1} ID: {vector['id']}")
                    print(f"      Vector {j+1} chunk_index: {vector['metadata'].get('chunk_index', 'unknown')}")

                print("-" * 40)

            except Exception as e:
                print(f"Error processing case {i}: {e}")
                import traceback
                traceback.print_exc()

    if texas_cases_found == 0:
        print("❌ No Texas cases found in first 50 cases")

if __name__ == "__main__":
    debug_chunking()
