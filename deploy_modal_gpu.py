#!/usr/bin/env python3
"""
Deploy Texas Phase 1 to Modal GPU

This script deploys the GPU-accelerated embedding generation to Modal,
which is the easiest and most powerful component to deploy first.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_modal_auth():
    """Check if Modal is authenticated."""
    try:
        result = subprocess.run(['modal', 'token', 'current'], 
                              capture_output=True, text=True, check=True)
        logger.info("✅ Modal authentication verified")
        return True
    except subprocess.CalledProcessError:
        logger.warning("⚠️  Modal not authenticated")
        return False


def setup_modal_secrets():
    """Set up Modal secrets for Texas Phase 1."""
    logger.info("🔐 Setting up Modal secrets...")
    
    # Get environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    secrets = {
        'SUPABASE_URL': os.getenv('SUPABASE_URL'),
        'SUPABASE_KEY': os.getenv('SUPABASE_KEY'),
        'PINECONE_API_KEY': os.getenv('PINECONE_API_KEY'),
        'NEO4J_URI': os.getenv('NEO4J_URI'),
        'NEO4J_USER': os.getenv('NEO4J_USER'),
        'NEO4J_PASSWORD': os.getenv('NEO4J_PASSWORD'),
        'VOYAGE_API_KEY': os.getenv('VOYAGE_API_KEY')
    }
    
    # Check all secrets are available
    missing_secrets = [k for k, v in secrets.items() if not v]
    if missing_secrets:
        logger.error(f"❌ Missing secrets: {missing_secrets}")
        return False
    
    # Create Modal secrets
    try:
        # Build the command
        cmd = ['modal', 'secret', 'create', 'texas-phase1-secrets']
        for key, value in secrets.items():
            cmd.extend([f'{key}={value}'])
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        logger.info("✅ Modal secrets created successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        if "already exists" in e.stderr:
            logger.info("✅ Modal secrets already exist")
            return True
        else:
            logger.error(f"❌ Failed to create Modal secrets: {e.stderr}")
            return False


def deploy_modal_app():
    """Deploy the Modal app."""
    logger.info("🚀 Deploying Modal GPU app...")
    
    try:
        # Deploy the Modal app
        result = subprocess.run([
            'modal', 'deploy', 
            'deployment/texas_phase1_modal_config.py'
        ], capture_output=True, text=True, check=True)
        
        logger.info("✅ Modal app deployed successfully!")
        logger.info(f"Deployment output: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Modal deployment failed: {e.stderr}")
        return False


def test_modal_deployment():
    """Test the Modal deployment."""
    logger.info("🧪 Testing Modal deployment...")
    
    try:
        # Test the statistics function
        result = subprocess.run([
            'modal', 'run', 
            'deployment/texas_phase1_modal_config.py::get_processing_statistics'
        ], capture_output=True, text=True, check=True)
        
        logger.info("✅ Modal deployment test successful!")
        logger.info(f"Test output: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Modal test failed: {e.stderr}")
        return False


def main():
    """Main deployment function."""
    
    print("🔥 MODAL GPU DEPLOYMENT FOR TEXAS PHASE 1")
    print("=" * 50)
    
    # Check if Modal is authenticated
    if not check_modal_auth():
        print("\n🔐 Modal Authentication Required")
        print("Please run: modal token new")
        print("Then run this script again.")
        return False
    
    # Set up secrets
    if not setup_modal_secrets():
        print("❌ Failed to set up Modal secrets")
        return False
    
    # Deploy the app
    if not deploy_modal_app():
        print("❌ Failed to deploy Modal app")
        return False
    
    # Test the deployment
    if not test_modal_deployment():
        print("❌ Modal deployment test failed")
        return False
    
    print("\n🎉 MODAL GPU DEPLOYMENT SUCCESSFUL!")
    print("=" * 40)
    print("✅ GPU-accelerated embedding generation deployed")
    print("✅ Ready to process high-complexity medical malpractice cases")
    print("✅ A100 GPU instances available on-demand")
    print("✅ Pay-per-second pricing active")
    print("\nNext steps:")
    print("1. Test with sample documents")
    print("2. Deploy Google Cloud Run Jobs")
    print("3. Process full 400K Texas cases")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
