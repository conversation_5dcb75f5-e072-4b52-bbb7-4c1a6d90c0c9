#!/usr/bin/env python3
"""
Debug Pattern Extraction
Test individual patterns to see what's being extracted
"""

from judge_relationship_enhancer import JudgeRelationshipEnhancer

def test_individual_patterns():
    """Test each pattern individually to see what's being extracted"""
    
    print("🔍 DEBUGGING INDIVIDUAL PATTERN EXTRACTION")
    print("=" * 60)
    
    # Test text with clear full names
    test_text = '''
    Circuit Judge <PERSON> delivered the opinion of the court.
    
    Justice <PERSON>, with whom Justice <PERSON> joins, concurring.
    
    Justice <PERSON> wrote separately.
    
    Judge <PERSON> wrote the preliminary ruling.
    
    <PERSON>, Circuit Judge:
    We hold that...
    
    <PERSON>, J., concurring.
    
    <PERSON>, J., concurring in part.
    '''
    
    print(f"📄 TEST TEXT:")
    print(f"   {test_text[:200]}...")
    
    enhancer = JudgeRelationshipEnhancer()
    
    print(f"\n🔍 TESTING EACH PATTERN INDIVIDUALLY:")
    
    import re
    
    for i, pattern in enumerate(enhancer.judge_patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, test_text, re.IGNORECASE)
        found_matches = []
        
        for match in matches:
            extracted = match.group(1).strip()
            context_start = max(0, match.start() - 30)
            context_end = min(len(test_text), match.end() + 30)
            context = test_text[context_start:context_end].replace('\n', ' ').strip()
            
            found_matches.append({
                'extracted': extracted,
                'context': context,
                'words': len(extracted.split())
            })
        
        if found_matches:
            for j, match in enumerate(found_matches, 1):
                words_status = "✅ Full" if match['words'] > 1 else "❌ Partial"
                print(f"      Match {j}: '{match['extracted']}' ({words_status}, {match['words']} words)")
                print(f"         Context: ...{match['context']}...")
        else:
            print(f"      No matches")
    
    print(f"\n🔄 TESTING FULL EXTRACTION PIPELINE:")
    
    # Test the full extraction pipeline
    judges = enhancer._extract_judges_from_text(test_text)
    
    print(f"   📊 Total judges extracted: {len(judges)}")
    for judge in judges:
        words = len(judge['name'].split())
        words_status = "✅ Full" if words > 1 else "❌ Partial"
        print(f"      - '{judge['name']}' ({words_status}, {words} words, role: {judge['role']})")
    
    # Expected full names
    expected_names = [
        "Jennifer L. Smith",
        "Hugo L. Black", 
        "William O. Douglas",
        "Felix Frankfurter",
        "Sarah Michelle Johnson"
    ]
    
    print(f"\n🎯 COMPARISON WITH EXPECTED:")
    print(f"   Expected: {expected_names}")
    
    extracted_names = [j['name'] for j in judges]
    
    for expected in expected_names:
        found = any(expected in extracted or extracted in expected for extracted in extracted_names)
        status = "✅" if found else "❌"
        print(f"      {status} {expected}")
    
    enhancer.close()


def test_pattern_priority():
    """Test if pattern priority is working correctly"""
    
    print(f"\n🔬 TESTING PATTERN PRIORITY")
    print("=" * 60)
    
    # Text where multiple patterns could match the same judge
    priority_test_text = '''
    Before SMITH, JONES, and WILLIAMS, Circuit Judges.
    
    Circuit Judge Jennifer L. Smith delivered the opinion of the court.
    
    Jennifer L. Smith, Circuit Judge:
    We conclude that...
    '''
    
    print(f"📄 PRIORITY TEST TEXT:")
    print(f"   {priority_test_text}")
    
    enhancer = JudgeRelationshipEnhancer()
    
    # Test extraction
    judges = enhancer._extract_judges_from_text(priority_test_text)
    
    print(f"\n📊 JUDGES EXTRACTED WITH PRIORITY:")
    for judge in judges:
        words = len(judge['name'].split())
        print(f"   - '{judge['name']}' ({words} words)")
    
    # Check if we got the full name instead of partial
    smith_judges = [j for j in judges if 'smith' in j['name'].lower()]
    
    print(f"\n🎯 SMITH EXTRACTION ANALYSIS:")
    print(f"   Smith judges found: {len(smith_judges)}")
    
    for judge in smith_judges:
        words = len(judge['name'].split())
        if words > 1:
            print(f"   ✅ Full name: '{judge['name']}'")
        else:
            print(f"   ❌ Partial name: '{judge['name']}'")
    
    # Expected: Should get "Jennifer L. Smith" not "SMITH"
    expected_full_name = "Jennifer L. Smith"
    found_full_name = any(expected_full_name.lower() in j['name'].lower() for j in judges)
    
    print(f"\n🎯 PRIORITY TEST RESULT:")
    print(f"   Expected: '{expected_full_name}'")
    print(f"   Found full name: {'✅' if found_full_name else '❌'}")
    
    enhancer.close()


if __name__ == "__main__":
    print("🧪 PATTERN EXTRACTION DEBUGGING")
    print("=" * 80)
    
    # Test individual patterns
    test_individual_patterns()
    
    # Test pattern priority
    test_pattern_priority()
    
    print(f"\n🎯 DEBUGGING SUMMARY:")
    print(f"   Check which patterns are extracting full names vs partial names")
    print(f"   Verify that high-priority patterns are being used first")
    print(f"   Identify missing patterns for names like 'Felix Frankfurter'")
