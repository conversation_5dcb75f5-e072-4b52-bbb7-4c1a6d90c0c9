#!/usr/bin/env python3
"""
Analyze Real CourtListener Text
Examine actual CourtListener text to understand why patterns don't match
"""

import requests
import re
import os
from dotenv import load_dotenv

def analyze_real_courtlistener_text():
    """Analyze actual CourtListener text to fix pattern matching"""
    
    print("🔍 ANALYZING REAL COURTLISTENER TEXT")
    print("=" * 60)
    
    load_dotenv()
    cl_api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not cl_api_key:
        print("❌ No CourtListener API key found")
        return False
    
    headers = {
        'Authorization': f'Token {cl_api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    try:
        # Get a few cases for analysis
        response = requests.get(
            "https://www.courtlistener.com/api/rest/v4/opinions/",
            headers=headers,
            params={
                'court': 'ca5,ca2',
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 3
            },
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return False
        
        results = response.json().get('results', [])
        
        for i, case in enumerate(results, 1):
            case_name = case.get('case_name', 'Unknown')
            text = case.get('plain_text', '') or case.get('html', '')
            
            if not text:
                continue
            
            print(f"\n📄 CASE {i}: {case_name}")
            print(f"   Court: {case.get('court', 'Unknown')}")
            print(f"   Text length: {len(text):,} characters")
            
            # Show first 1000 characters to see structure
            print(f"\n📝 TEXT STRUCTURE (first 1000 chars):")
            print("-" * 40)
            print(text[:1000])
            print("-" * 40)
            
            # Look for judge-related patterns in the text
            print(f"\n🔍 JUDGE PATTERN ANALYSIS:")
            
            # Search for common judge indicators
            judge_patterns = [
                r'(?i)before.*?judge',
                r'(?i)circuit.*?judge',
                r'(?i)district.*?judge',
                r'(?i)justice.*?delivered',
                r'(?i)judge.*?delivered',
                r'(?i)per curiam',
                r'(?i)opinion.*?by',
                r'(?i)authored.*?by',
            ]
            
            for pattern in judge_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    print(f"   ✅ Pattern '{pattern}' found {len(matches)} matches:")
                    for match in matches[:3]:  # Show first 3
                        clean_match = match.replace('\n', ' ').strip()
                        print(f"      - {clean_match}")
                else:
                    print(f"   ❌ Pattern '{pattern}' - no matches")
            
            # Look for specific name patterns
            print(f"\n🔍 NAME PATTERN ANALYSIS:")
            
            # Look for capitalized names that might be judges
            name_patterns = [
                r'[A-Z][a-z]+(?:\s+[A-Z]\.?)?\s+[A-Z][a-z]+',  # First Last or First M. Last
                r'[A-Z]{2,}(?:,\s+[A-Z]{2,})*',  # ALL CAPS names (panel format)
                r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)',
                r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)',
            ]
            
            for pattern in name_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    print(f"   ✅ Name pattern '{pattern}' found {len(matches)} matches:")
                    for match in matches[:5]:  # Show first 5
                        if isinstance(match, tuple):
                            match = match[0] if match else ""
                        clean_match = str(match).replace('\n', ' ').strip()
                        if clean_match and len(clean_match) < 50:  # Reasonable length
                            print(f"      - '{clean_match}'")
                else:
                    print(f"   ❌ Name pattern '{pattern}' - no matches")
            
            # Look for specific court document structures
            print(f"\n🔍 COURT DOCUMENT STRUCTURE:")
            
            # Check for common court document elements
            structure_patterns = [
                (r'UNITED STATES.*?COURT', 'Court header'),
                (r'No\.\s+\d+', 'Case number'),
                (r'Before.*?Judges?\.', 'Judge panel'),
                (r'PER CURIAM', 'Per curiam opinion'),
                (r'MEMORANDUM', 'Memorandum opinion'),
                (r'ORDER', 'Court order'),
            ]
            
            for pattern, description in structure_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    print(f"   ✅ {description}: {len(matches)} found")
                    for match in matches[:2]:
                        clean_match = match.replace('\n', ' ').strip()
                        print(f"      - {clean_match}")
                else:
                    print(f"   ❌ {description}: not found")
            
            if i >= 2:  # Analyze first 2 cases
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False


def test_improved_patterns():
    """Test improved patterns based on real text analysis"""
    
    print(f"\n🔧 TESTING IMPROVED PATTERNS")
    print("=" * 60)
    
    # Based on real CourtListener text, create improved patterns
    improved_patterns = [
        # Real federal court document patterns
        r'Before\s+([A-Z]{2,}),?\s+([A-Z]{2,}),?\s+and\s+([A-Z]{2,}),?\s+Circuit\s+Judges',  # Panel with 3 judges
        r'Before\s+([A-Z]{2,})\s+and\s+([A-Z]{2,}),?\s+Circuit\s+Judges',  # Panel with 2 judges
        r'Before\s+([A-Z]{2,}),?\s+Circuit\s+Judge',  # Single judge
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+Circuit\s+Judge,?\s*:',  # Judge with title
        r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+District\s+Judge',  # District judge
        r'PER\s+CURIAM\s*:',  # Per curiam (unsigned)
    ]
    
    # Test text samples based on real CourtListener format
    test_texts = [
        "Before SMITH, JONES, and WILSON, Circuit Judges.",
        "Before MARTINEZ and RODRIGUEZ, Circuit Judges.",
        "Before THOMPSON, Circuit Judge.",
        "John Smith, Circuit Judge:",
        "Mary Johnson, District Judge",
        "PER CURIAM:",
        """
        UNITED STATES COURT OF APPEALS
        FOR THE FIFTH CIRCUIT
        
        No. 23-40123
        
        Before SMITH, JONES, and WILSON, Circuit Judges.
        
        PER CURIAM:
        
        Appellant challenges the district court's ruling.
        """,
    ]
    
    print(f"📋 TESTING IMPROVED PATTERNS ON REAL-STYLE TEXT:")
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n   Test {i}: {text.strip()[:50]}...")
        
        found_judges = []
        
        for j, pattern in enumerate(improved_patterns, 1):
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                print(f"      ✅ Pattern {j} matches:")
                for match in matches:
                    if isinstance(match, tuple):
                        # Multiple capture groups
                        for name in match:
                            if name and name.strip():
                                found_judges.append(name.strip())
                                print(f"         - {name.strip()}")
                    else:
                        # Single capture group
                        found_judges.append(match.strip())
                        print(f"         - {match.strip()}")
        
        if not found_judges:
            print(f"      ❌ No judges found")
        else:
            print(f"      📊 Total judges: {len(found_judges)}")
    
    return True


def main():
    """Analyze real CourtListener text and improve patterns"""
    
    print("🔧 REAL COURTLISTENER TEXT ANALYSIS")
    print("=" * 80)
    print("🎯 Understanding why patterns don't match real data")
    
    # Analyze real CourtListener text
    analysis_ok = analyze_real_courtlistener_text()
    
    # Test improved patterns
    pattern_test_ok = test_improved_patterns()
    
    print(f"\n📊 ANALYSIS SUMMARY:")
    print(f"   Real text analysis: {'✅' if analysis_ok else '❌'}")
    print(f"   Improved pattern test: {'✅' if pattern_test_ok else '❌'}")
    
    if analysis_ok and pattern_test_ok:
        print(f"\n✅ REAL TEXT ANALYSIS: COMPLETE")
        print(f"✅ Ready to update patterns and re-test")
        return True
    else:
        print(f"\n❌ REAL TEXT ANALYSIS: ISSUES")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
