#!/usr/bin/env python3
"""
Analyze Judge Disambiguation Strategy
Show how we prevent mixing up judges with same last names in Neo4j
"""

from real_neo4j_client import RealNeo4jClient
from datetime import datetime

def analyze_current_disambiguation_strategy():
    """Analyze how our current system handles judge disambiguation"""
    
    print("🔍 ANALYZING JUDGE DISAMBIGUATION STRATEGY")
    print("=" * 60)
    
    neo4j_client = RealNeo4jClient()
    
    try:
        with neo4j_client.driver.session() as session:
            # Check for judges with same last names
            result = session.run('''
                MATCH (j:Judge)
                WITH split(j.name, ' ')[-1] as last_name, collect(j) as judges
                WHERE size(judges) > 1
                RETURN last_name, 
                       [judge IN judges | {
                           id: judge.id, 
                           name: judge.name, 
                           court: judge.court,
                           era: judge.era,
                           case_count: size((judge)<-[:DECIDED_BY|:PARTICIPATED|:AUTHORED]-())
                       }] as judge_details
                ORDER BY last_name
                LIMIT 10
            ''')
            
            same_lastname_groups = list(result)
            
            print(f"📊 JUDGES WITH SAME LAST NAMES:")
            print(f"   Found {len(same_lastname_groups)} groups with duplicate last names")
            
            for group in same_lastname_groups:
                last_name = group['last_name']
                judges = group['judge_details']
                
                print(f"\n   👥 LAST NAME: {last_name}")
                print(f"      {len(judges)} judges with this last name:")
                
                for judge in judges:
                    print(f"         - ID: {judge['id']}")
                    print(f"           Name: {judge['name']}")
                    print(f"           Court: {judge['court']}")
                    print(f"           Era: {judge['era']}")
                    print(f"           Cases: {judge['case_count']}")
                    print()
            
            # Show our disambiguation ID strategy
            print(f"\n🔧 OUR DISAMBIGUATION ID STRATEGY:")
            
            result = session.run('''
                MATCH (j:Judge)
                RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court, j.era as judge_era
                ORDER BY j.name
                LIMIT 10
            ''')
            
            sample_judges = list(result)
            
            print(f"   Sample judge IDs showing disambiguation:")
            for judge in sample_judges:
                print(f"      Name: {judge['judge_name']}")
                print(f"      ID: {judge['judge_id']}")
                print(f"      Court: {judge['judge_court']}")
                print(f"      Era: {judge['judge_era']}")
                print()
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        neo4j_client.close()


def show_disambiguation_improvements():
    """Show specific improvements we can make for better disambiguation"""
    
    print(f"\n💡 DISAMBIGUATION IMPROVEMENTS")
    print("=" * 60)
    
    print(f"📋 CURRENT DISAMBIGUATION FEATURES:")
    print(f"   ✅ Unique IDs: judge_[name]_[court]_[era]")
    print(f"   ✅ Court context: Different courts = different judges")
    print(f"   ✅ Era context: Different time periods = different judges")
    print(f"   ✅ Full name preference: 'Ryan M. Harris' beats 'Harris'")
    
    print(f"\n🔧 ADDITIONAL IMPROVEMENTS WE CAN ADD:")
    
    improvements = [
        {
            'title': 'Geographic Disambiguation',
            'description': 'Use court jurisdiction to distinguish judges',
            'example': 'Judge Smith (5th Circuit) vs Judge Smith (2nd Circuit)',
            'implementation': 'Add circuit/district info to judge ID'
        },
        {
            'title': 'Temporal Disambiguation', 
            'description': 'Use case filing dates to separate judge careers',
            'example': 'Judge Johnson (1990s) vs Judge Johnson (2020s)',
            'implementation': 'Enhance era calculation with decade granularity'
        },
        {
            'title': 'Case Pattern Analysis',
            'description': 'Use case types and patterns to distinguish judges',
            'example': 'Judge Wilson (criminal cases) vs Judge Wilson (civil cases)',
            'implementation': 'Add practice area context to judge profiles'
        },
        {
            'title': 'Cross-Reference Validation',
            'description': 'Use external judge databases for validation',
            'example': 'Federal Judicial Center judge database lookup',
            'implementation': 'API integration for judge verification'
        },
        {
            'title': 'Confidence Scoring',
            'description': 'Score judge identity confidence based on available info',
            'example': 'High confidence: full name + court + era',
            'implementation': 'Add confidence score to judge nodes'
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n   {i}. {improvement['title']}:")
        print(f"      Description: {improvement['description']}")
        print(f"      Example: {improvement['example']}")
        print(f"      Implementation: {improvement['implementation']}")


def demonstrate_current_disambiguation():
    """Demonstrate how our current disambiguation works"""
    
    print(f"\n🎯 CURRENT DISAMBIGUATION IN ACTION")
    print("=" * 60)
    
    # Simulate how we handle different scenarios
    scenarios = [
        {
            'scenario': 'Same name, different courts',
            'judge1': {'name': 'Smith', 'court': 'ca5', 'era': '2020s'},
            'judge2': {'name': 'Smith', 'court': 'ca2', 'era': '2020s'},
            'result': 'Different judges: judge_smith_ca5_2020s vs judge_smith_ca2_2020s'
        },
        {
            'scenario': 'Same name, different eras',
            'judge1': {'name': 'Johnson', 'court': 'txnd', 'era': '1990s'},
            'judge2': {'name': 'Johnson', 'court': 'txnd', 'era': '2020s'},
            'result': 'Different judges: judge_johnson_txnd_1990s vs judge_johnson_txnd_2020s'
        },
        {
            'scenario': 'Full name vs partial name',
            'judge1': {'name': 'Harris', 'court': 'ca5', 'era': '2020s'},
            'judge2': {'name': 'Ryan M. Harris', 'court': 'ca5', 'era': '2020s'},
            'result': 'Same judge: Deduplication merges to judge_ryan_m_harris_ca5_2020s'
        },
        {
            'scenario': 'Identical context (potential conflict)',
            'judge1': {'name': 'Wilson', 'court': 'ca5', 'era': '2020s'},
            'judge2': {'name': 'Wilson', 'court': 'ca5', 'era': '2020s'},
            'result': 'Same judge: judge_wilson_ca5_2020s (merged)'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n   {i}. {scenario['scenario']}:")
        print(f"      Judge 1: {scenario['judge1']}")
        print(f"      Judge 2: {scenario['judge2']}")
        print(f"      Result: {scenario['result']}")


def recommend_best_practices():
    """Recommend best practices for judge disambiguation"""
    
    print(f"\n📋 BEST PRACTICES FOR JUDGE DISAMBIGUATION")
    print("=" * 60)
    
    best_practices = [
        "✅ Always prefer full names over partial names in deduplication",
        "✅ Use court context (circuit/district) as primary disambiguator", 
        "✅ Use temporal context (era/decade) as secondary disambiguator",
        "✅ Implement confidence scoring for judge identity certainty",
        "✅ Regular validation against external judge databases",
        "✅ Monitor for disambiguation conflicts and resolve manually",
        "✅ Maintain audit trail of judge merging decisions",
        "✅ Use case patterns to validate judge identity consistency"
    ]
    
    for practice in best_practices:
        print(f"   {practice}")
    
    print(f"\n🎯 PRIORITY IMPROVEMENTS:")
    print(f"   1. 🔧 Enhanced era calculation (decade-level granularity)")
    print(f"   2. 🔧 Confidence scoring for judge identities")
    print(f"   3. 🔧 Cross-reference validation with external databases")
    print(f"   4. 🔧 Manual review workflow for disambiguation conflicts")


def main():
    """Analyze judge disambiguation strategy"""
    
    print("🧪 JUDGE DISAMBIGUATION STRATEGY ANALYSIS")
    print("=" * 80)
    print("🎯 How we prevent mixing up judges with same last names")
    
    # Analyze current strategy
    analyze_current_disambiguation_strategy()
    
    # Show improvements
    show_disambiguation_improvements()
    
    # Demonstrate current disambiguation
    demonstrate_current_disambiguation()
    
    # Recommend best practices
    recommend_best_practices()
    
    print(f"\n🎉 SUMMARY:")
    print(f"✅ Current system uses court + era context for disambiguation")
    print(f"✅ Full names preferred over partial names in deduplication")
    print(f"✅ Multiple improvement strategies available")
    print(f"✅ Best practices identified for production deployment")


if __name__ == "__main__":
    main()
