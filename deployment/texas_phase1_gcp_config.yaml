# Google Cloud Run Jobs Configuration for Texas Phase 1
# Processes Criminal Defense + Personal Injury & Medical Malpractice cases

apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: texas-phase1-caselaw-processor
  labels:
    project: texas-legal-ai-pilot
    phase: phase1
    practice-areas: criminal-defense,personal-injury,medical-malpractice
spec:
  spec:
    taskCount: 2666  # Number of batches for GCP
    parallelism: 1500  # Max concurrent tasks
    taskTimeout: 3600s  # 1 hour timeout
    template:
      spec:
        template:
          spec:
            containers:
            - name: texas-phase1-processor
              image: gcr.io/legal-ai-project/texas-phase1-processor:latest
              resources:
                limits:
                  memory: 8Gi
                  cpu: "4"
                  ephemeral-storage: 10Gi
                requests:
                  memory: 8Gi
                  cpu: "4"
              env:
              - name: PHASE
                value: "texas_phase1"
              - name: TARGET_STATE
                value: "texas"
              - name: PRACTICE_AREAS
                value: "criminal_defense,personal_injury,medical_malpractice"
              - name: BATCH_SIZE
                value: "50"
              - name: PROVIDER
                value: "gcp_cloud_run"
              - name: SUPABASE_URL
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: supabase-url
              - name: SUPABASE_KEY
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: supabase-key
              - name: PINECONE_API_KEY
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: pinecone-api-key
              - name: NEO4J_URI
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: neo4j-uri
              - name: NEO4J_USER
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: neo4j-user
              - name: NEO4J_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: neo4j-password
              - name: GCS_BUCKET
                value: "texas-legal-cases-phase1"
              - name: MODAL_TOKEN
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: modal-token
              - name: VOYAGE_API_KEY
                valueFrom:
                  secretKeyRef:
                    name: texas-phase1-secrets
                    key: voyage-api-key
              - name: LOG_LEVEL
                value: "INFO"
              - name: PROCESSING_TIMEOUT
                value: "3000"
              - name: MAX_RETRIES
                value: "3"
              - name: COMPLEXITY_THRESHOLD
                value: "1.5"
            restartPolicy: OnFailure
            serviceAccountName: texas-phase1-processor-sa

---
# Pub/Sub Topic for batch distribution
apiVersion: pubsub.cnrm.cloud.google.com/v1beta1
kind: PubSubTopic
metadata:
  name: texas-phase1-batches
  labels:
    project: texas-legal-ai-pilot
spec:
  messageRetentionDuration: "604800s"  # 7 days

---
# Pub/Sub Subscription for Cloud Run Jobs
apiVersion: pubsub.cnrm.cloud.google.com/v1beta1
kind: PubSubSubscription
metadata:
  name: texas-phase1-processor-sub
spec:
  topicRef:
    name: texas-phase1-batches
  ackDeadlineSeconds: 600
  messageRetentionDuration: "604800s"
  retryPolicy:
    minimumBackoff: "10s"
    maximumBackoff: "600s"
  deadLetterPolicy:
    deadLetterTopicRef:
      name: texas-phase1-dead-letter
    maxDeliveryAttempts: 5

---
# Service Account for processing
apiVersion: iam.cnrm.cloud.google.com/v1beta1
kind: IAMServiceAccount
metadata:
  name: texas-phase1-processor-sa
spec:
  displayName: "Texas Phase 1 Processor Service Account"

---
# IAM Policy Binding for service account
apiVersion: iam.cnrm.cloud.google.com/v1beta1
kind: IAMPolicyMember
metadata:
  name: texas-phase1-processor-binding
spec:
  member: serviceAccount:texas-phase1-processor-sa@PROJECT_ID.iam.gserviceaccount.com
  role: roles/run.invoker
  resourceRef:
    apiVersion: run.googleapis.com/v1
    kind: Service
    name: texas-phase1-caselaw-processor

---
# Cloud Storage bucket for processed cases
apiVersion: storage.cnrm.cloud.google.com/v1beta1
kind: StorageBucket
metadata:
  name: texas-legal-cases-phase1
spec:
  location: US-CENTRAL1
  storageClass: STANDARD
  uniformBucketLevelAccess: true
  versioning:
    enabled: true
  lifecycle:
    rule:
    - action:
        type: Delete
      condition:
        age: 365  # Delete after 1 year
    - action:
        type: SetStorageClass
        storageClass: NEARLINE
      condition:
        age: 30  # Move to Nearline after 30 days

---
# Monitoring Dashboard
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: texas-phase1-monitoring
  labels:
    app: texas-phase1-processor
spec:
  selector:
    matchLabels:
      app: texas-phase1-processor
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
