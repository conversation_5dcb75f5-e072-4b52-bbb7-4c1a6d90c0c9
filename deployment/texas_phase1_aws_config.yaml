# AWS Lambda Configuration for Texas Phase 1 Overflow Processing
# CloudFormation template for Criminal Defense + Personal Injury cases

AWSTemplateFormatVersion: '2010-09-09'
Description: 'Texas Phase 1 Legal AI Processing - AWS Lambda Overflow'

Parameters:
  ProjectName:
    Type: String
    Default: texas-legal-ai-pilot
    Description: Project name for resource naming
  
  Phase:
    Type: String
    Default: phase1
    Description: Processing phase identifier
  
  SupabaseUrl:
    Type: String
    Description: Supabase database URL
    NoEcho: true
  
  SupabaseKey:
    Type: String
    Description: Supabase service key
    NoEcho: true
  
  PineconeApiKey:
    Type: String
    Description: Pinecone API key
    NoEcho: true
  
  VoyageApiKey:
    Type: String
    Description: Voyage AI API key
    NoEcho: true

Resources:
  # Lambda Function for Texas Phase 1 Processing
  TexasPhase1ProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${ProjectName}-${Phase}-processor'
      Runtime: python3.9
      Handler: lambda_function.lambda_handler
      Code:
        ZipFile: |
          import json
          import logging
          import asyncio
          from texas_phase1_processor import process_texas_phase1_batch
          
          logger = logging.getLogger()
          logger.setLevel(logging.INFO)
          
          def lambda_handler(event, context):
              """AWS Lambda handler for Texas Phase 1 processing."""
              try:
                  # Extract batch data from SQS message
                  if 'Records' in event:
                      for record in event['Records']:
                          batch_data = json.loads(record['body'])
                          
                          # Process the batch
                          result = asyncio.run(process_texas_phase1_batch(batch_data))
                          
                          logger.info(f"Processed batch {batch_data.get('batch_id')}: {result}")
                  
                  return {
                      'statusCode': 200,
                      'body': json.dumps({
                          'message': 'Texas Phase 1 batch processed successfully',
                          'provider': 'aws_lambda'
                      })
                  }
              
              except Exception as e:
                  logger.error(f"Error processing Texas Phase 1 batch: {str(e)}")
                  return {
                      'statusCode': 500,
                      'body': json.dumps({
                          'error': str(e),
                          'provider': 'aws_lambda'
                      })
                  }
      
      MemorySize: 10240  # 10GB memory
      Timeout: 900  # 15 minutes
      ReservedConcurrencyLimit: 1000  # Max concurrent executions
      
      Environment:
        Variables:
          PHASE: !Ref Phase
          TARGET_STATE: 'texas'
          PRACTICE_AREAS: 'criminal_defense,personal_injury,medical_malpractice'
          BATCH_SIZE: '50'
          PROVIDER: 'aws_lambda'
          SUPABASE_URL: !Ref SupabaseUrl
          SUPABASE_KEY: !Ref SupabaseKey
          PINECONE_API_KEY: !Ref PineconeApiKey
          VOYAGE_API_KEY: !Ref VoyageApiKey
          LOG_LEVEL: 'INFO'
          PROCESSING_TIMEOUT: '800'
          MAX_RETRIES: '3'
      
      Role: !GetAtt TexasPhase1LambdaRole.Arn
      
      Tags:
        - Key: Project
          Value: !Ref ProjectName
        - Key: Phase
          Value: !Ref Phase
        - Key: PracticeAreas
          Value: 'criminal-defense,personal-injury,medical-malpractice'

  # IAM Role for Lambda Function
  TexasPhase1LambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${ProjectName}-${Phase}-lambda-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaSQSQueueExecutionRole
      
      Policies:
        - PolicyName: TexasPhase1ProcessingPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                Resource: 
                  - !Sub '${TexasPhase1S3Bucket}/*'
              - Effect: Allow
                Action:
                  - s3:ListBucket
                Resource: 
                  - !GetAtt TexasPhase1S3Bucket.Arn
              - Effect: Allow
                Action:
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:GetQueueAttributes
                Resource: 
                  - !GetAtt TexasPhase1Queue.Arn
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: '*'

  # SQS Queue for batch processing
  TexasPhase1Queue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub '${ProjectName}-${Phase}-processing-queue'
      VisibilityTimeoutSeconds: 960  # 16 minutes (longer than Lambda timeout)
      MessageRetentionPeriod: 1209600  # 14 days
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt TexasPhase1DeadLetterQueue.Arn
        maxReceiveCount: 3
      
      Tags:
        - Key: Project
          Value: !Ref ProjectName
        - Key: Phase
          Value: !Ref Phase

  # Dead Letter Queue for failed messages
  TexasPhase1DeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub '${ProjectName}-${Phase}-dead-letter-queue'
      MessageRetentionPeriod: 1209600  # 14 days
      
      Tags:
        - Key: Project
          Value: !Ref ProjectName
        - Key: Phase
          Value: !Ref Phase

  # Event Source Mapping for Lambda and SQS
  TexasPhase1EventSourceMapping:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt TexasPhase1Queue.Arn
      FunctionName: !Ref TexasPhase1ProcessorFunction
      BatchSize: 1  # Process one batch at a time
      MaximumBatchingWindowInSeconds: 5
      
  # S3 Bucket for processed documents
  TexasPhase1S3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${ProjectName}-${Phase}-processed-cases'
      VersioningConfiguration:
        Status: Enabled
      
      LifecycleConfiguration:
        Rules:
          - Id: ArchiveOldVersions
            Status: Enabled
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 90
                StorageClass: GLACIER
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 365
      
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      
      Tags:
        - Key: Project
          Value: !Ref ProjectName
        - Key: Phase
          Value: !Ref Phase

  # CloudWatch Log Group for Lambda
  TexasPhase1LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/${TexasPhase1ProcessorFunction}'
      RetentionInDays: 30

  # CloudWatch Alarms for monitoring
  TexasPhase1ErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Phase}-lambda-errors'
      AlarmDescription: 'High error rate in Texas Phase 1 Lambda processing'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref TexasPhase1ProcessorFunction
      TreatMissingData: notBreaching

  TexasPhase1DurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Phase}-lambda-duration'
      AlarmDescription: 'High duration in Texas Phase 1 Lambda processing'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 600000  # 10 minutes in milliseconds
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref TexasPhase1ProcessorFunction
      TreatMissingData: notBreaching

Outputs:
  LambdaFunctionArn:
    Description: 'ARN of the Texas Phase 1 Lambda function'
    Value: !GetAtt TexasPhase1ProcessorFunction.Arn
    Export:
      Name: !Sub '${ProjectName}-${Phase}-lambda-arn'
  
  SQSQueueUrl:
    Description: 'URL of the Texas Phase 1 SQS queue'
    Value: !Ref TexasPhase1Queue
    Export:
      Name: !Sub '${ProjectName}-${Phase}-queue-url'
  
  S3BucketName:
    Description: 'Name of the Texas Phase 1 S3 bucket'
    Value: !Ref TexasPhase1S3Bucket
    Export:
      Name: !Sub '${ProjectName}-${Phase}-bucket-name'
