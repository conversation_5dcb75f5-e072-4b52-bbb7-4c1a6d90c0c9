#!/usr/bin/env python3
"""
Phase 4: Pinecone Indexing Fix
Resolves the indexing delay issue that prevents 100% consistency
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# Import real clients
from test_cap_real_pinecone import RealPineconeClient

logger = logging.getLogger(__name__)


@dataclass
class PineconeIndexingResult:
    """Result of Pinecone indexing operation"""
    success: bool
    vectors_stored: int
    vectors_retrievable: int
    indexing_time: float
    retrieval_delay: float
    error: Optional[str] = None


class PineconeIndexingOptimizer:
    """
    Optimizes Pinecone indexing with proper wait/retry mechanisms
    """
    
    def __init__(self):
        self.pinecone_client = RealPineconeClient()
        self.max_wait_time = 60  # Maximum wait time for indexing
        self.check_interval = 2  # Check every 2 seconds
        self.max_retries = 3
    
    async def store_vectors_with_confirmation(self, vectors: List[Dict[str, Any]], case_ids: List[str]) -> PineconeIndexingResult:
        """
        Store vectors in Pinecone and wait for indexing confirmation
        """
        start_time = time.time()
        
        logger.info(f"🔄 Storing {len(vectors)} vectors in Pinecone with confirmation...")
        
        try:
            # Step 1: Store vectors
            upsert_response = self.pinecone_client.index.upsert(vectors)
            storage_time = time.time() - start_time
            
            logger.info(f"✅ Vectors stored in {storage_time:.2f}s, waiting for indexing...")
            
            # Step 2: Wait for indexing with confirmation
            indexing_start = time.time()
            retrieval_success = await self._wait_for_indexing_confirmation(case_ids)
            indexing_time = time.time() - indexing_start
            
            if retrieval_success:
                total_time = time.time() - start_time
                logger.info(f"✅ Indexing confirmed in {indexing_time:.2f}s (total: {total_time:.2f}s)")
                
                return PineconeIndexingResult(
                    success=True,
                    vectors_stored=len(vectors),
                    vectors_retrievable=len(case_ids),
                    indexing_time=indexing_time,
                    retrieval_delay=indexing_time,
                    error=None
                )
            else:
                logger.error(f"❌ Indexing confirmation failed after {indexing_time:.2f}s")
                
                return PineconeIndexingResult(
                    success=False,
                    vectors_stored=len(vectors),
                    vectors_retrievable=0,
                    indexing_time=indexing_time,
                    retrieval_delay=indexing_time,
                    error="Indexing confirmation timeout"
                )
                
        except Exception as e:
            error_time = time.time() - start_time
            logger.error(f"❌ Pinecone storage failed after {error_time:.2f}s: {e}")
            
            return PineconeIndexingResult(
                success=False,
                vectors_stored=0,
                vectors_retrievable=0,
                indexing_time=error_time,
                retrieval_delay=0,
                error=str(e)
            )
    
    async def _wait_for_indexing_confirmation(self, case_ids: List[str]) -> bool:
        """
        Wait for vectors to be indexed and retrievable
        """
        elapsed_time = 0
        
        while elapsed_time < self.max_wait_time:
            # Check if all vectors are retrievable
            retrievable_count = 0
            
            for case_id in case_ids:
                if await self._check_vector_retrievable(case_id):
                    retrievable_count += 1
            
            logger.debug(f"Indexing progress: {retrievable_count}/{len(case_ids)} vectors retrievable")
            
            if retrievable_count == len(case_ids):
                logger.info(f"✅ All {len(case_ids)} vectors confirmed retrievable")
                return True
            
            # Wait before next check
            await asyncio.sleep(self.check_interval)
            elapsed_time += self.check_interval
        
        logger.warning(f"⚠️ Indexing confirmation timeout after {elapsed_time}s")
        return False
    
    async def _check_vector_retrievable(self, case_id: str) -> bool:
        """
        Check if a specific vector is retrievable from Pinecone
        """
        try:
            vector_id = f"{case_id}_chunk_0"
            result = self.pinecone_client.index.fetch([vector_id])
            
            # Check if vector exists and has data
            if result and 'vectors' in result and result['vectors']:
                vector_data = result['vectors'].get(vector_id)
                if vector_data and 'values' in vector_data:
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"Vector retrieval check failed for {case_id}: {e}")
            return False
    
    async def test_indexing_optimization(self) -> Dict[str, Any]:
        """
        Test the Pinecone indexing optimization with real vectors
        """
        logger.info("🧪 TESTING PINECONE INDEXING OPTIMIZATION")
        logger.info("=" * 60)
        
        # Create test vectors
        test_cases = [
            {"id": "pinecone_test_1", "text": "This is test case 1 for Pinecone indexing optimization."},
            {"id": "pinecone_test_2", "text": "This is test case 2 for Pinecone indexing optimization."},
            {"id": "pinecone_test_3", "text": "This is test case 3 for Pinecone indexing optimization."},
            {"id": "pinecone_test_4", "text": "This is test case 4 for Pinecone indexing optimization."},
        ]
        
        # Generate embeddings (mock for now)
        test_vectors = []
        for case in test_cases:
            # Create mock embedding vector (1024 dimensions)
            mock_embedding = [0.1] * 1024
            
            vector = {
                "id": f"{case['id']}_chunk_0",
                "values": mock_embedding,
                "metadata": {
                    "case_id": case['id'],
                    "chunk_index": 0,
                    "text_length": len(case['text']),
                    "test": True
                }
            }
            test_vectors.append(vector)
        
        case_ids = [case['id'] for case in test_cases]
        
        logger.info(f"📊 Testing with {len(test_vectors)} vectors...")
        
        # Test the optimized indexing
        result = await self.store_vectors_with_confirmation(test_vectors, case_ids)
        
        # Report results
        logger.info(f"\n📋 PINECONE INDEXING TEST RESULTS:")
        logger.info(f"   Success: {'✅' if result.success else '❌'}")
        logger.info(f"   Vectors Stored: {result.vectors_stored}")
        logger.info(f"   Vectors Retrievable: {result.vectors_retrievable}")
        logger.info(f"   Indexing Time: {result.indexing_time:.2f}s")
        logger.info(f"   Retrieval Delay: {result.retrieval_delay:.2f}s")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        # Calculate success rate
        if result.vectors_stored > 0:
            success_rate = (result.vectors_retrievable / result.vectors_stored) * 100
        else:
            success_rate = 0
        
        logger.info(f"   Success Rate: {success_rate:.1f}%")
        
        # Determine if this fixes the consistency issue
        if success_rate == 100:
            logger.info(f"🎉 PINECONE INDEXING ISSUE RESOLVED!")
            logger.info(f"   All vectors are immediately retrievable after indexing")
            logger.info(f"   This should bring overall consistency to 100%")
        else:
            logger.warning(f"⚠️ Pinecone indexing still has issues")
            logger.warning(f"   Only {success_rate:.1f}% of vectors are retrievable")
        
        return {
            "success": result.success,
            "success_rate": success_rate,
            "indexing_time": result.indexing_time,
            "vectors_tested": len(test_vectors),
            "issue_resolved": success_rate == 100
        }


async def main():
    """
    Main function to test Pinecone indexing optimization
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize optimizer
    optimizer = PineconeIndexingOptimizer()
    
    # Run test
    results = await optimizer.test_indexing_optimization()
    
    print(f"\n🎯 FINAL ASSESSMENT:")
    if results["issue_resolved"]:
        print(f"✅ Pinecone indexing issue RESOLVED")
        print(f"✅ System ready for 100% consistency testing")
    else:
        print(f"❌ Pinecone indexing issue PERSISTS")
        print(f"❌ Additional optimization needed")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
