#!/usr/bin/env python3
"""
Texas Phase 1 Document Filter

Filters for Criminal Defense + Personal Injury & Medical Malpractice cases
with Texas-specific legal terminology and high-performance processing.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class TexasFilterResult:
    """Result of Texas Phase 1 filtering."""
    is_phase1_case: bool
    practice_area: Optional[str]
    confidence_score: float
    texas_specific_matches: List[str]
    court_jurisdiction: Optional[str]
    complexity_factor: float


class TexasPhase1Filter:
    """High-performance filter for Texas Phase 1 practice areas."""
    
    def __init__(self):
        # Compile Texas-specific patterns for maximum performance
        self.criminal_defense_pattern = self._compile_criminal_defense_pattern()
        self.personal_injury_pattern = self._compile_personal_injury_pattern()
        self.medical_malpractice_pattern = self._compile_medical_malpractice_pattern()
        
        # Texas court patterns
        self.texas_court_patterns = self._compile_texas_court_patterns()
        
        # Texas-specific legal terms
        self.texas_specific_terms = self._compile_texas_specific_terms()
        
        # Performance tracking
        self.filter_stats = {
            'total_processed': 0,
            'criminal_defense': 0,
            'personal_injury': 0,
            'medical_malpractice': 0,
            'filtered_out': 0,
            'texas_specific_matches': 0
        }
    
    def _compile_criminal_defense_pattern(self) -> re.Pattern:
        """Compile criminal defense pattern with Texas-specific terms."""
        
        # High-frequency criminal terms first for performance
        criminal_keywords = [
            # Core criminal terms
            r'\bcriminal\b', r'\bdefendant\b', r'\bprosecution\b', r'\bfelony\b',
            r'\bmisdemeanor\b', r'\bassault\b', r'\bbattery\b', r'\btheft\b',
            r'\brobbery\b', r'\bburglary\b', r'\bmurder\b', r'\bhomicide\b',
            r'\bdrug\b', r'\bdui\b', r'\bdwi\b', r'\bdomestic violence\b',
            
            # Texas-specific criminal terms
            r'\bcapital murder\b', r'\baggravated\b', r'\btexas penal code\b',
            r'\bstate of texas\b', r'\bharris county\b', r'\bdallas county\b',
            r'\btravis county\b', r'\bbexar county\b', r'\btarrant county\b',
            
            # Criminal procedure terms
            r'\bplea\b', r'\bsentencing\b', r'\bprobation\b', r'\bparole\b',
            r'\bconviction\b', r'\barrest\b', r'\bindictment\b', r'\bgrand jury\b',
            
            # Texas criminal courts
            r'\bcriminal district court\b', r'\bcounty criminal court\b',
            r'\bjustice of the peace\b', r'\bmunicipal court\b'
        ]
        
        return re.compile('|'.join(criminal_keywords), re.IGNORECASE)
    
    def _compile_personal_injury_pattern(self) -> re.Pattern:
        """Compile personal injury pattern with Texas-specific terms."""
        
        pi_keywords = [
            # Core personal injury terms
            r'\bpersonal injury\b', r'\bnegligence\b', r'\btort\b', r'\bdamages\b',
            r'\bliability\b', r'\baccident\b', r'\bmotor vehicle\b', r'\bcar accident\b',
            r'\btruck accident\b', r'\bslip and fall\b', r'\bpremises liability\b',
            r'\bproduct liability\b', r'\bwrongful death\b', r'\bpain and suffering\b',
            
            # Texas-specific PI terms
            r'\boil field accident\b', r'\bconstruction accident\b', r'\brefinery accident\b',
            r'\bdram shop\b', r'\btexas tort claims act\b', r'\bproportionate responsibility\b',
            r'\bmodified comparative fault\b', r'\btexas civil practice\b',
            
            # Compensation terms
            r'\bmedical expenses\b', r'\blost wages\b', r'\bdisability\b',
            r'\bcompensation\b', r'\bsettlement\b', r'\bjury verdict\b',
            r'\binsurance\b', r'\buninsured motorist\b', r'\bunderinsured\b',
            
            # Texas industries (high accident rates)
            r'\boil and gas\b', r'\bpetroleum\b', r'\bchemical plant\b',
            r'\brefinery\b', r'\bdrilling\b', r'\bfracking\b'
        ]
        
        return re.compile('|'.join(pi_keywords), re.IGNORECASE)
    
    def _compile_medical_malpractice_pattern(self) -> re.Pattern:
        """Compile medical malpractice pattern with Texas-specific terms."""
        
        malpractice_keywords = [
            # Core medical malpractice terms
            r'\bmedical malpractice\b', r'\bmedical negligence\b', r'\bphysician\b',
            r'\bdoctor\b', r'\bhospital\b', r'\bnurse\b', r'\bsurgery\b',
            r'\bsurgical error\b', r'\bmisdiagnosis\b', r'\bfailure to diagnose\b',
            r'\bmedication error\b', r'\bbirth injury\b', r'\banesthesia\b',
            
            # Texas medical terms
            r'\btexas medical board\b', r'\bmedical center\b', r'\btexas medical\b',
            r'\bhouston medical center\b', r'\bdallas medical\b', r'\baustin medical\b',
            r'\bsan antonio medical\b', r'\bfort worth medical\b',
            
            # Medical standards
            r'\binformed consent\b', r'\bstandard of care\b', r'\bmedical expert\b',
            r'\bhealthcare\b', r'\bpatient\b', r'\bmedical records\b',
            r'\bhospital negligence\b', r'\bemergency room\b', r'\bicu\b',
            
            # Texas healthcare institutions
            r'\bmethodist hospital\b', r'\bmd anderson\b', r'\bbaylor\b',
            r'\bscott and white\b', r'\bparkland\b', r'\bben taub\b'
        ]
        
        return re.compile('|'.join(malpractice_keywords), re.IGNORECASE)
    
    def _compile_texas_court_patterns(self) -> Dict[str, re.Pattern]:
        """Compile Texas court jurisdiction patterns."""
        
        return {
            'criminal': re.compile(
                r'\b(criminal district court|county criminal court|justice court|municipal court)\b.*texas',
                re.IGNORECASE
            ),
            'civil': re.compile(
                r'\b(district court|county court|civil court)\b.*texas',
                re.IGNORECASE
            ),
            'federal': re.compile(
                r'\b(eastern district|southern district|northern district|western district).*texas',
                re.IGNORECASE
            )
        }
    
    def _compile_texas_specific_terms(self) -> re.Pattern:
        """Compile pattern for Texas-specific legal terms."""
        
        texas_terms = [
            r'\btexas\b', r'\btx\b', r'\blone star\b',
            r'\bharris county\b', r'\bdallas county\b', r'\btravis county\b',
            r'\bbexar county\b', r'\btarrant county\b', r'\bcollin county\b',
            r'\bdenton county\b', r'\bfort bend county\b', r'\bmontgomery county\b',
            r'\bhouston\b', r'\bdallas\b', r'\baustin\b', r'\bsan antonio\b',
            r'\bfort worth\b', r'\bel paso\b', r'\barlington\b', r'\bcorpus christi\b',
            r'\btexas supreme court\b', r'\btexas court of appeals\b',
            r'\btexas penal code\b', r'\btexas civil practice\b',
            r'\btexas tort claims act\b', r'\btexas property code\b'
        ]
        
        return re.compile('|'.join(texas_terms), re.IGNORECASE)
    
    def filter_document(self, document_data: Dict) -> TexasFilterResult:
        """
        Filter a document for Texas Phase 1 practice areas.
        
        Args:
            document_data: Document with case_name, text, court, etc.
            
        Returns:
            TexasFilterResult with classification
        """
        self.filter_stats['total_processed'] += 1
        
        # Extract and prepare text for analysis
        case_name = document_data.get('case_name', '').lower()
        case_text = document_data.get('text', '').lower()
        court_name = document_data.get('court', '').lower()
        jurisdiction = document_data.get('jurisdiction', '').lower()
        
        # Combine text (prioritize case name and first 2000 chars for complex cases)
        analysis_text = f"{case_name} {case_text[:2000]} {court_name} {jurisdiction}"
        
        # Check for Texas jurisdiction first (performance optimization)
        texas_matches = self.texas_specific_terms.findall(analysis_text)
        if not texas_matches:
            self.filter_stats['filtered_out'] += 1
            return TexasFilterResult(
                is_phase1_case=False,
                practice_area=None,
                confidence_score=0.0,
                texas_specific_matches=[],
                court_jurisdiction=None,
                complexity_factor=1.0
            )
        
        self.filter_stats['texas_specific_matches'] += 1
        
        # Check each Phase 1 practice area
        practice_area_scores = {}
        
        # Criminal Defense
        criminal_matches = self.criminal_defense_pattern.findall(analysis_text)
        if criminal_matches:
            score = len(criminal_matches)
            # Boost for case name matches
            case_name_matches = self.criminal_defense_pattern.findall(case_name)
            score += len(case_name_matches) * 2
            # Boost for criminal court
            if any(court in court_name for court in ['criminal', 'municipal', 'justice']):
                score += 2
            practice_area_scores['criminal_defense'] = {
                'score': score,
                'complexity': 1.1,
                'matches': criminal_matches[:5]
            }
        
        # Personal Injury
        pi_matches = self.personal_injury_pattern.findall(analysis_text)
        if pi_matches:
            score = len(pi_matches)
            case_name_matches = self.personal_injury_pattern.findall(case_name)
            score += len(case_name_matches) * 2
            # Boost for civil court
            if any(court in court_name for court in ['civil', 'district']):
                score += 2
            # Extra boost for Texas-specific PI terms
            texas_pi_terms = ['dram shop', 'oil field', 'refinery', 'proportionate responsibility']
            if any(term in analysis_text for term in texas_pi_terms):
                score += 3
            practice_area_scores['personal_injury'] = {
                'score': score,
                'complexity': 1.3,
                'matches': pi_matches[:5]
            }
        
        # Medical Malpractice (subset of personal injury but higher complexity)
        malpractice_matches = self.medical_malpractice_pattern.findall(analysis_text)
        if malpractice_matches:
            score = len(malpractice_matches)
            case_name_matches = self.medical_malpractice_pattern.findall(case_name)
            score += len(case_name_matches) * 3  # Higher weight for medical cases
            # Boost for medical institutions
            texas_hospitals = ['methodist', 'md anderson', 'baylor', 'parkland']
            if any(hospital in analysis_text for hospital in texas_hospitals):
                score += 4
            practice_area_scores['medical_malpractice'] = {
                'score': score,
                'complexity': 1.8,  # Highest complexity
                'matches': malpractice_matches[:5]
            }
        
        # Determine best match
        if not practice_area_scores:
            self.filter_stats['filtered_out'] += 1
            return TexasFilterResult(
                is_phase1_case=False,
                practice_area=None,
                confidence_score=0.0,
                texas_specific_matches=texas_matches[:3],
                court_jurisdiction=None,
                complexity_factor=1.0
            )
        
        # Select highest scoring practice area
        best_area = max(practice_area_scores.items(), key=lambda x: x[1]['score'])
        area_name, area_data = best_area
        
        # Update statistics
        self.filter_stats[area_name] += 1
        
        # Determine court jurisdiction
        court_jurisdiction = self._determine_court_jurisdiction(court_name)
        
        return TexasFilterResult(
            is_phase1_case=True,
            practice_area=area_name,
            confidence_score=area_data['score'],
            texas_specific_matches=texas_matches[:3],
            court_jurisdiction=court_jurisdiction,
            complexity_factor=area_data['complexity']
        )
    
    def _determine_court_jurisdiction(self, court_name: str) -> Optional[str]:
        """Determine the court jurisdiction type."""
        
        for jurisdiction, pattern in self.texas_court_patterns.items():
            if pattern.search(court_name):
                return jurisdiction
        
        return 'unknown'
    
    def batch_filter_documents(self, documents: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        Filter a batch of documents for Phase 1 practice areas.
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            Tuple of (filtered_documents, batch_statistics)
        """
        filtered_docs = []
        batch_stats = {
            'total_input': len(documents),
            'total_output': 0,
            'criminal_defense': 0,
            'personal_injury': 0,
            'medical_malpractice': 0,
            'texas_matches': 0,
            'filter_rate': 0.0,
            'avg_complexity': 0.0
        }
        
        total_complexity = 0.0
        
        for doc in documents:
            filter_result = self.filter_document(doc)
            
            if filter_result.is_phase1_case:
                # Add Phase 1 metadata to document
                doc['phase1_practice_area'] = filter_result.practice_area
                doc['confidence_score'] = filter_result.confidence_score
                doc['texas_specific_matches'] = filter_result.texas_specific_matches
                doc['court_jurisdiction'] = filter_result.court_jurisdiction
                doc['complexity_factor'] = filter_result.complexity_factor
                doc['processing_priority'] = 'phase1_high_volume'
                
                filtered_docs.append(doc)
                
                # Update batch statistics
                batch_stats[filter_result.practice_area] += 1
                total_complexity += filter_result.complexity_factor
                
                if filter_result.texas_specific_matches:
                    batch_stats['texas_matches'] += 1
        
        batch_stats['total_output'] = len(filtered_docs)
        batch_stats['filter_rate'] = (len(documents) - len(filtered_docs)) / len(documents) if documents else 0
        batch_stats['avg_complexity'] = total_complexity / len(filtered_docs) if filtered_docs else 0
        
        return filtered_docs, batch_stats
    
    def get_filter_statistics(self) -> Dict:
        """Get comprehensive filtering statistics."""
        
        total = self.filter_stats['total_processed']
        if total == 0:
            return self.filter_stats
        
        stats = self.filter_stats.copy()
        stats['percentages'] = {
            'criminal_defense': (stats['criminal_defense'] / total) * 100,
            'personal_injury': (stats['personal_injury'] / total) * 100,
            'medical_malpractice': (stats['medical_malpractice'] / total) * 100,
            'filtered_out': (stats['filtered_out'] / total) * 100,
            'texas_specific_matches': (stats['texas_specific_matches'] / total) * 100
        }
        
        # Calculate efficiency metrics
        phase1_cases = stats['criminal_defense'] + stats['personal_injury'] + stats['medical_malpractice']
        stats['phase1_efficiency'] = (phase1_cases / total) * 100 if total > 0 else 0
        
        return stats


# Integration function for serverless processing
async def filter_texas_phase1_batch(batch_data: Dict) -> Dict:
    """
    Filter a batch for Texas Phase 1 processing.
    
    Integrates with multi-cloud serverless pipeline.
    """
    
    filter_engine = TexasPhase1Filter()
    
    # Load documents from batch
    documents = batch_data.get('documents', [])
    
    # Apply Texas Phase 1 filtering
    filtered_docs, batch_stats = filter_engine.batch_filter_documents(documents)
    
    # Log results with Texas-specific metrics
    logger.info(f"Texas Phase 1 Batch {batch_data.get('batch_id', 'unknown')}: "
               f"{len(documents)} → {len(filtered_docs)} documents "
               f"({batch_stats['filter_rate']*100:.1f}% filtered out) "
               f"Avg complexity: {batch_stats['avg_complexity']:.2f}")
    
    # Return enhanced batch data
    return {
        'batch_id': batch_data.get('batch_id'),
        'phase': 'texas_phase1',
        'documents': filtered_docs,
        'original_count': len(documents),
        'filtered_count': len(filtered_docs),
        'batch_statistics': batch_stats,
        'practice_area_breakdown': {
            'criminal_defense': batch_stats['criminal_defense'],
            'personal_injury': batch_stats['personal_injury'],
            'medical_malpractice': batch_stats['medical_malpractice']
        },
        'texas_specific_matches': batch_stats['texas_matches'],
        'average_complexity': batch_stats['avg_complexity']
    }


if __name__ == "__main__":
    # Test the Texas Phase 1 filter
    filter_engine = TexasPhase1Filter()
    
    sample_docs = [
        {
            'case_name': 'State of Texas v. Johnson - Aggravated Assault',
            'text': 'The defendant was charged with aggravated assault under the Texas Penal Code...',
            'court': 'Harris County Criminal District Court',
            'jurisdiction': 'texas'
        },
        {
            'case_name': 'Smith v. ExxonMobil - Oil Field Accident',
            'text': 'Personal injury lawsuit arising from oil field accident in Texas. Plaintiff seeks damages for negligence...',
            'court': 'Harris County District Court',
            'jurisdiction': 'texas'
        },
        {
            'case_name': 'Brown v. Methodist Hospital - Medical Malpractice',
            'text': 'Medical malpractice case involving surgical error at Methodist Hospital in Houston, Texas...',
            'court': 'Harris County District Court',
            'jurisdiction': 'texas'
        },
        {
            'case_name': 'Corporate Contract Dispute',
            'text': 'Commercial contract dispute between corporations...',
            'court': 'New York Supreme Court',
            'jurisdiction': 'new_york'
        }
    ]
    
    filtered_docs, stats = filter_engine.batch_filter_documents(sample_docs)
    
    print(f"\n🤠 TEXAS PHASE 1 FILTER TEST")
    print(f"Filtered {len(sample_docs)} → {len(filtered_docs)} documents")
    print(f"Filter rate: {stats['filter_rate']*100:.1f}%")
    print(f"Average complexity: {stats['avg_complexity']:.2f}")
    print(f"Practice area breakdown:")
    print(f"  Criminal Defense: {stats['criminal_defense']}")
    print(f"  Personal Injury: {stats['personal_injury']}")
    print(f"  Medical Malpractice: {stats['medical_malpractice']}")
    
    for doc in filtered_docs:
        print(f"- {doc['case_name']} → {doc['phase1_practice_area']} "
              f"(confidence: {doc['confidence_score']}, complexity: {doc['complexity_factor']})")
