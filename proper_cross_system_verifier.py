#!/usr/bin/env python3
"""
Proper Cross-System Verifier
Actually verifies data content and integrity across all 4 systems:
- Supabase: Case metadata
- GCS: Full text content
- Pinecone: Vector embeddings
- Neo4j: Graph data and relationships
"""

import asyncio
import logging
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from dotenv import load_dotenv
from supabase import create_client

# Import storage clients
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SystemData:
    """Data from a single storage system"""
    system_name: str
    case_id: str
    exists: bool
    data: Dict[str, Any]
    error: Optional[str] = None


@dataclass
class CrossSystemVerificationResult:
    """Result of cross-system verification for a single case"""
    case_id: str
    supabase_data: SystemData
    gcs_data: SystemData
    pinecone_data: SystemData
    neo4j_data: SystemData
    consistency_score: float
    issues: List[str]
    success: bool


class ProperCrossSystemVerifier:
    """Comprehensive cross-system data verification"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize all storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Verification statistics
        self.stats = {
            'cases_verified': 0,
            'systems_checked': 4,
            'consistency_issues': 0,
            'data_integrity_issues': 0,
            'missing_data_issues': 0
        }
        
    def close(self):
        """Close all connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    async def verify_supabase_data(self, case_id: str) -> SystemData:
        """Verify case data in Supabase"""
        
        try:
            response = self.supabase.table('cases').select('*').eq('id', case_id).execute()
            
            if response.data and len(response.data) > 0:
                case_data = response.data[0]
                
                # Verify essential fields
                required_fields = ['id', 'case_name', 'court_id', 'batch_id', 'source']
                missing_fields = [field for field in required_fields if not case_data.get(field)]
                
                return SystemData(
                    system_name='Supabase',
                    case_id=case_id,
                    exists=True,
                    data={
                        'case_name': case_data.get('case_name', ''),
                        'court_id': case_data.get('court_id', ''),
                        'date_filed': case_data.get('date_filed', ''),
                        'batch_id': case_data.get('batch_id', ''),
                        'source': case_data.get('source', ''),
                        'gcs_path': case_data.get('gcs_path', ''),
                        'neo4j_node_id': case_data.get('neo4j_node_id', ''),
                        'missing_fields': missing_fields,
                        'record_complete': len(missing_fields) == 0
                    }
                )
            else:
                return SystemData(
                    system_name='Supabase',
                    case_id=case_id,
                    exists=False,
                    data={},
                    error='Case not found in Supabase'
                )
                
        except Exception as e:
            return SystemData(
                system_name='Supabase',
                case_id=case_id,
                exists=False,
                data={},
                error=f'Supabase query error: {e}'
            )
    
    async def verify_gcs_storage(self, case_id: str, gcs_path: str = None) -> SystemData:
        """Verify case text storage in GCS"""
        
        try:
            # If no GCS path provided, try to get it from Supabase
            if not gcs_path:
                supabase_data = await self.verify_supabase_data(case_id)
                gcs_path = supabase_data.data.get('gcs_path', '')
            
            if not gcs_path:
                return SystemData(
                    system_name='GCS',
                    case_id=case_id,
                    exists=False,
                    data={},
                    error='No GCS path available'
                )
            
            # Try to retrieve text from GCS
            text_content = self.gcs_client.get_text(gcs_path)
            
            if text_content:
                return SystemData(
                    system_name='GCS',
                    case_id=case_id,
                    exists=True,
                    data={
                        'gcs_path': gcs_path,
                        'text_length': len(text_content),
                        'text_sample': text_content[:200] + '...' if len(text_content) > 200 else text_content,
                        'has_substantial_content': len(text_content) > 500,
                        'text_quality': 'good' if len(text_content) > 1000 else 'limited'
                    }
                )
            else:
                return SystemData(
                    system_name='GCS',
                    case_id=case_id,
                    exists=False,
                    data={'gcs_path': gcs_path},
                    error='Text content not retrievable from GCS'
                )
                
        except Exception as e:
            return SystemData(
                system_name='GCS',
                case_id=case_id,
                exists=False,
                data={'gcs_path': gcs_path or 'unknown'},
                error=f'GCS retrieval error: {e}'
            )
    
    async def verify_pinecone_vectors(self, case_id: str) -> SystemData:
        """Verify vector embeddings in Pinecone"""

        try:
            import time

            # Add longer delay to account for Pinecone indexing
            await asyncio.sleep(10)

            # Debug: Print what we're looking for
            print(f"   🔍 Searching Pinecone for case_id: {case_id}")

            # Try multiple query strategies for Pinecone
            query_response = None

            # Strategy 1: Query by case_id filter with different namespaces
            namespaces_to_try = ["tx", "us", "us_case", "tx_case", ""]  # Common namespaces

            for namespace in namespaces_to_try:
                try:
                    print(f"   🔍 Trying namespace: '{namespace}'")
                    query_params = {
                        "filter": {"case_id": case_id},
                        "top_k": 1000,
                        "include_metadata": True
                    }
                    if namespace:
                        query_params["namespace"] = namespace

                    query_response = self.pinecone_client.index.query(**query_params)
                    matches_count = len(query_response.matches) if query_response else 0
                    print(f"   📊 Namespace '{namespace}' returned {matches_count} matches")

                    if query_response and query_response.matches:
                        print(f"   ✅ Found vectors in namespace '{namespace}'")
                        break

                except Exception as e:
                    print(f"   ⚠️ Namespace '{namespace}' query failed: {e}")
                    continue

            # Strategy 2: If filter fails, try fetching by ID patterns
            if not query_response or not query_response.matches:
                try:
                    # Try multiple ID patterns that might be used
                    vector_ids = []
                    # Pattern 1: case_id_chunk_N
                    vector_ids.extend([f"{case_id}_chunk_{i}" for i in range(20)])
                    # Pattern 2: case_id_N (simple numbering)
                    vector_ids.extend([f"{case_id}_{i}" for i in range(20)])
                    # Pattern 3: Just case_id with suffix
                    vector_ids.extend([f"{case_id}_text_{i}" for i in range(20)])

                    print(f"   🔍 Trying to fetch vectors with IDs like: {vector_ids[:3]}...")

                    # Try fetching from different namespaces
                    fetch_response = None
                    for namespace in namespaces_to_try:
                        try:
                            fetch_params = {"ids": vector_ids}
                            if namespace:
                                fetch_params["namespace"] = namespace

                            fetch_response = self.pinecone_client.index.fetch(**fetch_params)
                            if fetch_response and fetch_response.vectors:
                                print(f"   ✅ Found vectors via fetch in namespace '{namespace}'")
                                break
                        except Exception as fetch_e:
                            print(f"   ⚠️ Fetch failed for namespace '{namespace}': {fetch_e}")
                            continue

                    if fetch_response and fetch_response.vectors:
                        print(f"   📊 Pinecone fetch found {len(fetch_response.vectors)} vectors")
                        # Convert fetch response to query-like format
                        class MockMatch:
                            def __init__(self, vector_id, vector_data):
                                self.id = vector_id
                                self.metadata = vector_data.metadata
                                self.values = vector_data.values

                        matches = [MockMatch(vid, vdata) for vid, vdata in fetch_response.vectors.items()]

                        class MockResponse:
                            def __init__(self, matches):
                                self.matches = matches

                        query_response = MockResponse(matches)
                    else:
                        print(f"   ⚠️ Pinecone fetch found no vectors")

                except Exception as e:
                    print(f"   ⚠️ Pinecone fetch failed: {e}")

            # Strategy 3: Last resort - query all recent vectors and filter
            if not query_response or not query_response.matches:
                try:
                    print(f"   🔍 Trying broad query to find any vectors...")
                    # Use a dummy vector to query for similar vectors in different namespaces
                    dummy_vector = [0.1] * 1024
                    broad_response = None

                    for namespace in namespaces_to_try:
                        try:
                            query_params = {
                                "vector": dummy_vector,
                                "top_k": 100,
                                "include_metadata": True
                            }
                            if namespace:
                                query_params["namespace"] = namespace

                            broad_response = self.pinecone_client.index.query(**query_params)
                            if broad_response and broad_response.matches:
                                print(f"   📊 Broad query in namespace '{namespace}' found {len(broad_response.matches)} total vectors")
                                break
                        except Exception as broad_e:
                            print(f"   ⚠️ Broad query failed for namespace '{namespace}': {broad_e}")
                            continue

                    if broad_response and broad_response.matches:
                        print(f"   📊 Broad query found {len(broad_response.matches)} total vectors")
                        # Filter for our case_id
                        filtered_matches = [
                            match for match in broad_response.matches
                            if match.metadata and match.metadata.get('case_id') == case_id
                        ]
                        print(f"   📊 Filtered to {len(filtered_matches)} vectors for our case")
                        broad_response.matches = filtered_matches
                        query_response = broad_response

                except Exception as e:
                    print(f"   ⚠️ Broad Pinecone query failed: {e}")
            
            if query_response and query_response.matches:
                vectors = query_response.matches
                
                # Analyze vector data
                vector_count = len(vectors)
                chunk_numbers = [int(v.metadata.get('chunk_number', 0)) for v in vectors if v.metadata.get('chunk_number')]
                max_chunk = max(chunk_numbers) if chunk_numbers else 0
                
                return SystemData(
                    system_name='Pinecone',
                    case_id=case_id,
                    exists=True,
                    data={
                        'vector_count': vector_count,
                        'max_chunk_number': max_chunk,
                        'chunk_sequence_complete': len(chunk_numbers) == max_chunk + 1 if max_chunk > 0 else True,
                        'sample_metadata': vectors[0].metadata if vectors else {},
                        'vector_dimensions': len(vectors[0].values) if vectors and vectors[0].values else 0,
                        'quality': 'good' if vector_count > 5 else 'limited'
                    }
                )
            else:
                return SystemData(
                    system_name='Pinecone',
                    case_id=case_id,
                    exists=False,
                    data={},
                    error='No vectors found in Pinecone for this case'
                )
                
        except Exception as e:
            return SystemData(
                system_name='Pinecone',
                case_id=case_id,
                exists=False,
                data={},
                error=f'Pinecone query error: {e}'
            )
    
    async def verify_neo4j_graph(self, case_id: str) -> SystemData:
        """Verify case and related data in Neo4j"""
        
        try:
            with self.neo4j_client.driver.session() as session:
                print(f"   🔍 Searching Neo4j for case_id: {case_id}")

                # Get case node and related data with debug info
                # Try multiple relationship directions since judge relationships might be stored differently
                result = session.run('''
                    MATCH (c:Case {id: $case_id})
                    OPTIONAL MATCH (c)-[r1]-(j1:Judge)
                    OPTIONAL MATCH (c)<-[r2]-(j2:Judge)
                    OPTIONAL MATCH (c)-[r3]->(j3:Judge)
                    WITH c,
                         collect(DISTINCT {
                             judge_name: j1.name,
                             judge_id: j1.id,
                             relationship_type: type(r1),
                             confidence_score: j1.confidence_score,
                             external_validated: j1.external_validated
                         }) +
                         collect(DISTINCT {
                             judge_name: j2.name,
                             judge_id: j2.id,
                             relationship_type: type(r2),
                             confidence_score: j2.confidence_score,
                             external_validated: j2.external_validated
                         }) +
                         collect(DISTINCT {
                             judge_name: j3.name,
                             judge_id: j3.id,
                             relationship_type: type(r3),
                             confidence_score: j3.confidence_score,
                             external_validated: j3.external_validated
                         }) as all_judges
                    RETURN c.id as case_id,
                           c.case_name as case_name,
                           c.court as court,
                           c.text as text,
                           c.batch_id as batch_id,
                           c.source as source,
                           [judge IN all_judges WHERE judge.judge_name IS NOT NULL] as judges
                ''', case_id=case_id)

                print(f"   📊 Neo4j query executed for case: {case_id}")
                
                record = result.single()

                if record:
                    case_text = record.get('text', '')
                    judges_raw = record.get('judges', [])
                    judges = [j for j in judges_raw if j.get('judge_name')]

                    print(f"   📊 Neo4j found case with {len(judges)} judges")
                    if judges:
                        for judge in judges:
                            print(f"      - {judge.get('judge_name')} ({judge.get('relationship_type')})")

                    enhanced_features_present = any(
                        j.get('confidence_score') is not None for j in judges
                    )

                    print(f"   📊 Enhanced features present: {enhanced_features_present}")

                    return SystemData(
                        system_name='Neo4j',
                        case_id=case_id,
                        exists=True,
                        data={
                            'case_name': record.get('case_name', ''),
                            'court': record.get('court', ''),
                            'batch_id': record.get('batch_id', ''),
                            'source': record.get('source', ''),
                            'text_length': len(case_text),
                            'has_text': len(case_text) > 100,
                            'judge_count': len(judges),
                            'judges': judges,
                            'enhanced_features_present': enhanced_features_present,
                            'relationships_created': len(judges) > 0
                        }
                    )
                else:
                    return SystemData(
                        system_name='Neo4j',
                        case_id=case_id,
                        exists=False,
                        data={},
                        error='Case node not found in Neo4j'
                    )
                    
        except Exception as e:
            return SystemData(
                system_name='Neo4j',
                case_id=case_id,
                exists=False,
                data={},
                error=f'Neo4j query error: {e}'
            )
    
    async def verify_single_case(self, case_id: str) -> CrossSystemVerificationResult:
        """Verify a single case across all systems"""
        
        print(f"\n🔍 VERIFYING CASE: {case_id}")
        print("=" * 60)
        
        # Verify each system
        supabase_data = await self.verify_supabase_data(case_id)
        print(f"   Supabase: {'✅' if supabase_data.exists else '❌'}")
        
        gcs_data = await self.verify_gcs_storage(case_id, supabase_data.data.get('gcs_path'))
        print(f"   GCS: {'✅' if gcs_data.exists else '❌'}")
        
        pinecone_data = await self.verify_pinecone_vectors(case_id)
        print(f"   Pinecone: {'✅' if pinecone_data.exists else '❌'}")
        
        neo4j_data = await self.verify_neo4j_graph(case_id)
        print(f"   Neo4j: {'✅' if neo4j_data.exists else '❌'}")
        
        # Analyze consistency and issues
        issues = []
        consistency_score = 0.0
        
        # Check basic existence
        systems_with_data = sum([
            supabase_data.exists,
            gcs_data.exists,
            pinecone_data.exists,
            neo4j_data.exists
        ])
        
        if systems_with_data < 4:
            issues.append(f"Missing data in {4 - systems_with_data} systems")
        
        # Check data consistency
        if supabase_data.exists and neo4j_data.exists:
            if supabase_data.data.get('case_name') != neo4j_data.data.get('case_name'):
                issues.append("Case name mismatch between Supabase and Neo4j")
            else:
                consistency_score += 25
        
        # Check text consistency (allow for GCS storing full JSON vs Neo4j storing processed text)
        if gcs_data.exists and neo4j_data.exists:
            gcs_length = gcs_data.data.get('text_length', 0)
            neo4j_length = neo4j_data.data.get('text_length', 0)
            neo4j_has_text = neo4j_data.data.get('has_text', False)

            # More lenient comparison - GCS may store full JSON while Neo4j stores processed text
            if neo4j_has_text and neo4j_length > 500:  # Neo4j has substantial text
                consistency_score += 25
            elif abs(gcs_length - neo4j_length) > max(1000, gcs_length * 0.5):  # Allow 50% variance
                issues.append(f"Significant text length mismatch: GCS({gcs_length}) vs Neo4j({neo4j_length})")
            else:
                consistency_score += 25
        
        # Check vector consistency
        if pinecone_data.exists and gcs_data.exists:
            vector_count = pinecone_data.data.get('vector_count', 0)
            text_length = gcs_data.data.get('text_length', 0)
            expected_vectors = max(1, text_length // 1000)  # Rough estimate
            
            if vector_count < expected_vectors * 0.5:  # Allow 50% variance
                issues.append(f"Vector count too low: {vector_count} vectors for {text_length} chars")
            else:
                consistency_score += 25
        
        # Check enhanced features (judges with relationships and confidence scores)
        if neo4j_data.exists:
            judge_count = neo4j_data.data.get('judge_count', 0)
            enhanced_features_present = neo4j_data.data.get('enhanced_features_present', False)

            if judge_count > 0 or enhanced_features_present:
                consistency_score += 25
            else:
                issues.append("Enhanced features missing in Neo4j (no judges or confidence scores found)")
        
        success = len(issues) == 0 and systems_with_data == 4
        
        print(f"   Consistency Score: {consistency_score:.1f}%")
        print(f"   Issues: {len(issues)}")
        for issue in issues:
            print(f"      - {issue}")
        
        return CrossSystemVerificationResult(
            case_id=case_id,
            supabase_data=supabase_data,
            gcs_data=gcs_data,
            pinecone_data=pinecone_data,
            neo4j_data=neo4j_data,
            consistency_score=consistency_score,
            issues=issues,
            success=success
        )
    
    async def verify_batch(self, case_ids: List[str]) -> Dict[str, Any]:
        """Verify multiple cases and generate comprehensive report"""
        
        print(f"\n🔍 COMPREHENSIVE CROSS-SYSTEM VERIFICATION")
        print("=" * 80)
        print(f"🎯 Verifying {len(case_ids)} cases across all 4 systems")
        print(f"📊 Systems: Supabase + GCS + Pinecone + Neo4j")
        
        results = []
        
        for case_id in case_ids:
            result = await self.verify_single_case(case_id)
            results.append(result)
            self.stats['cases_verified'] += 1
            
            if not result.success:
                self.stats['consistency_issues'] += 1
        
        # Generate comprehensive report
        return self._generate_verification_report(results)
    
    def _generate_verification_report(self, results: List[CrossSystemVerificationResult]) -> Dict[str, Any]:
        """Generate comprehensive verification report"""
        
        total_cases = len(results)
        successful_cases = sum(1 for r in results if r.success)
        
        # System-specific statistics
        system_stats = {
            'supabase': sum(1 for r in results if r.supabase_data.exists),
            'gcs': sum(1 for r in results if r.gcs_data.exists),
            'pinecone': sum(1 for r in results if r.pinecone_data.exists),
            'neo4j': sum(1 for r in results if r.neo4j_data.exists)
        }
        
        # Consistency analysis
        avg_consistency = sum(r.consistency_score for r in results) / total_cases if total_cases > 0 else 0
        
        # Issue analysis
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)
        
        issue_types = {}
        for issue in all_issues:
            issue_types[issue] = issue_types.get(issue, 0) + 1
        
        report = {
            'verification_summary': {
                'total_cases': total_cases,
                'successful_cases': successful_cases,
                'success_rate': (successful_cases / total_cases * 100) if total_cases > 0 else 0,
                'average_consistency_score': avg_consistency
            },
            'system_coverage': system_stats,
            'system_success_rates': {
                system: (count / total_cases * 100) if total_cases > 0 else 0
                for system, count in system_stats.items()
            },
            'issue_analysis': {
                'total_issues': len(all_issues),
                'unique_issue_types': len(issue_types),
                'issue_breakdown': issue_types
            },
            'detailed_results': results,
            'overall_success': successful_cases == total_cases and avg_consistency >= 90.0
        }
        
        return report

    async def verify_data_integrity(self, case_ids: List[str]) -> Dict[str, Any]:
        """Verify data integrity across systems with detailed checks"""

        print(f"\n🔍 DATA INTEGRITY VERIFICATION")
        print("=" * 80)

        integrity_results = {
            'text_consistency': [],
            'metadata_consistency': [],
            'vector_integrity': [],
            'relationship_integrity': [],
            'enhanced_features_integrity': []
        }

        for case_id in case_ids:
            print(f"\n📋 Checking data integrity for: {case_id}")

            # Get data from all systems
            supabase_data = await self.verify_supabase_data(case_id)
            gcs_data = await self.verify_gcs_storage(case_id, supabase_data.data.get('gcs_path'))
            pinecone_data = await self.verify_pinecone_vectors(case_id)
            neo4j_data = await self.verify_neo4j_graph(case_id)

            # Text consistency check
            text_check = self._check_text_consistency(case_id, gcs_data, neo4j_data)
            integrity_results['text_consistency'].append(text_check)

            # Metadata consistency check
            metadata_check = self._check_metadata_consistency(case_id, supabase_data, neo4j_data)
            integrity_results['metadata_consistency'].append(metadata_check)

            # Vector integrity check
            vector_check = self._check_vector_integrity(case_id, gcs_data, pinecone_data)
            integrity_results['vector_integrity'].append(vector_check)

            # Relationship integrity check
            relationship_check = self._check_relationship_integrity(case_id, neo4j_data)
            integrity_results['relationship_integrity'].append(relationship_check)

            # Enhanced features integrity check
            features_check = self._check_enhanced_features_integrity(case_id, neo4j_data)
            integrity_results['enhanced_features_integrity'].append(features_check)

        return self._generate_integrity_report(integrity_results)

    def _check_text_consistency(self, case_id: str, gcs_data: SystemData, neo4j_data: SystemData) -> Dict[str, Any]:
        """Check text consistency between GCS and Neo4j"""

        if not gcs_data.exists or not neo4j_data.exists:
            return {
                'case_id': case_id,
                'consistent': False,
                'issue': 'Missing data in GCS or Neo4j',
                'gcs_exists': gcs_data.exists,
                'neo4j_exists': neo4j_data.exists
            }

        gcs_length = gcs_data.data.get('text_length', 0)
        neo4j_length = neo4j_data.data.get('text_length', 0)
        neo4j_has_text = neo4j_data.data.get('has_text', False)

        # More realistic text consistency check
        # GCS stores full JSON with metadata, Neo4j stores processed text
        # So we expect Neo4j to have less text than GCS

        if not neo4j_has_text:
            return {
                'case_id': case_id,
                'consistent': False,
                'gcs_length': gcs_length,
                'neo4j_length': neo4j_length,
                'neo4j_has_text': neo4j_has_text,
                'issue': 'Neo4j has no substantial text content'
            }

        # If Neo4j has substantial text (>1000 chars), consider it consistent
        # regardless of GCS length difference (since GCS includes metadata)
        if neo4j_length > 1000:
            return {
                'case_id': case_id,
                'consistent': True,
                'gcs_length': gcs_length,
                'neo4j_length': neo4j_length,
                'neo4j_has_text': neo4j_has_text,
                'issue': None
            }

        # For smaller texts, allow reasonable variance
        length_diff = abs(gcs_length - neo4j_length)
        length_consistent = length_diff <= max(500, gcs_length * 0.3)  # 30% variance or 500 chars

        return {
            'case_id': case_id,
            'consistent': length_consistent,
            'gcs_length': gcs_length,
            'neo4j_length': neo4j_length,
            'length_difference': length_diff,
            'neo4j_has_text': neo4j_has_text,
            'issue': None if length_consistent else f'Significant text length mismatch: {length_diff} chars'
        }

    def _check_metadata_consistency(self, case_id: str, supabase_data: SystemData, neo4j_data: SystemData) -> Dict[str, Any]:
        """Check metadata consistency between Supabase and Neo4j"""

        if not supabase_data.exists or not neo4j_data.exists:
            return {
                'case_id': case_id,
                'consistent': False,
                'issue': 'Missing data in Supabase or Neo4j'
            }

        # Check key fields
        fields_to_check = ['case_name', 'batch_id', 'source']
        inconsistencies = []

        for field in fields_to_check:
            supabase_value = supabase_data.data.get(field, '')
            neo4j_value = neo4j_data.data.get(field, '')

            if supabase_value != neo4j_value:
                inconsistencies.append(f'{field}: Supabase("{supabase_value}") != Neo4j("{neo4j_value}")')

        return {
            'case_id': case_id,
            'consistent': len(inconsistencies) == 0,
            'inconsistencies': inconsistencies,
            'issue': '; '.join(inconsistencies) if inconsistencies else None
        }

    def _check_vector_integrity(self, case_id: str, gcs_data: SystemData, pinecone_data: SystemData) -> Dict[str, Any]:
        """Check vector integrity in Pinecone"""

        if not pinecone_data.exists:
            return {
                'case_id': case_id,
                'consistent': False,
                'issue': 'No vectors found in Pinecone'
            }

        vector_count = pinecone_data.data.get('vector_count', 0)
        chunk_sequence_complete = pinecone_data.data.get('chunk_sequence_complete', False)
        vector_dimensions = pinecone_data.data.get('vector_dimensions', 0)

        # Check if vector count makes sense for text length
        text_length = gcs_data.data.get('text_length', 0) if gcs_data.exists else 0
        expected_min_vectors = max(1, text_length // 3000)  # More lenient for CAP data

        issues = []

        # More lenient vector count check for historical CAP data
        if vector_count == 0:
            issues.append('No vectors found')
        elif vector_count < expected_min_vectors and text_length > 5000:
            # Only flag if significantly under-vectorized for large texts
            issues.append(f'Too few vectors: {vector_count} < {expected_min_vectors}')

        # Chunk sequence check - more lenient for CAP data
        if vector_count > 1 and not chunk_sequence_complete:
            issues.append('Chunk sequence incomplete')

        # Vector dimensions check - handle cases where dimensions might be 0 due to loading issues
        if vector_dimensions > 0 and vector_dimensions != 1024:  # Only check if dimensions were retrieved
            issues.append(f'Wrong vector dimensions: {vector_dimensions} != 1024')
        elif vector_dimensions == 0 and vector_count > 0:
            # This might be a retrieval issue, not a storage issue
            issues.append('Vector dimensions not retrieved (possible query issue)')

        return {
            'case_id': case_id,
            'consistent': len(issues) == 0,
            'vector_count': vector_count,
            'expected_min_vectors': expected_min_vectors,
            'chunk_sequence_complete': chunk_sequence_complete,
            'vector_dimensions': vector_dimensions,
            'issues': issues,
            'issue': '; '.join(issues) if issues else None
        }

    def _check_relationship_integrity(self, case_id: str, neo4j_data: SystemData) -> Dict[str, Any]:
        """Check relationship integrity in Neo4j"""

        if not neo4j_data.exists:
            return {
                'case_id': case_id,
                'consistent': False,
                'issue': 'Case not found in Neo4j'
            }

        judge_count = neo4j_data.data.get('judge_count', 0)
        relationships_created = neo4j_data.data.get('relationships_created', False)
        judges = neo4j_data.data.get('judges', [])

        issues = []

        # Check if judges have proper relationships
        for judge in judges:
            if not judge.get('relationship_type'):
                issues.append(f'Judge {judge.get("judge_name")} missing relationship type')

            if not judge.get('judge_id'):
                issues.append(f'Judge {judge.get("judge_name")} missing judge ID')

        return {
            'case_id': case_id,
            'consistent': len(issues) == 0,
            'judge_count': judge_count,
            'relationships_created': relationships_created,
            'issues': issues,
            'issue': '; '.join(issues) if issues else None
        }

    def _check_enhanced_features_integrity(self, case_id: str, neo4j_data: SystemData) -> Dict[str, Any]:
        """Check enhanced features integrity in Neo4j"""

        if not neo4j_data.exists:
            return {
                'case_id': case_id,
                'consistent': False,
                'issue': 'Case not found in Neo4j'
            }

        enhanced_features_present = neo4j_data.data.get('enhanced_features_present', False)
        judges = neo4j_data.data.get('judges', [])

        issues = []
        enhanced_judges = 0

        for judge in judges:
            confidence_score = judge.get('confidence_score')
            external_validated = judge.get('external_validated')

            if confidence_score is not None:
                enhanced_judges += 1
                if confidence_score < 0 or confidence_score > 100:
                    issues.append(f'Invalid confidence score for {judge.get("judge_name")}: {confidence_score}')

            if external_validated is None:
                issues.append(f'Missing external validation for {judge.get("judge_name")}')

        return {
            'case_id': case_id,
            'consistent': len(issues) == 0 and enhanced_features_present,
            'enhanced_features_present': enhanced_features_present,
            'enhanced_judges': enhanced_judges,
            'total_judges': len(judges),
            'issues': issues,
            'issue': '; '.join(issues) if issues else None
        }

    def _generate_integrity_report(self, integrity_results: Dict[str, List]) -> Dict[str, Any]:
        """Generate comprehensive integrity report"""

        report = {
            'integrity_summary': {},
            'detailed_results': integrity_results,
            'overall_integrity': True
        }

        for check_type, results in integrity_results.items():
            total_checks = len(results)
            successful_checks = sum(1 for r in results if r.get('consistent', False))
            success_rate = (successful_checks / total_checks * 100) if total_checks > 0 else 0

            report['integrity_summary'][check_type] = {
                'total_checks': total_checks,
                'successful_checks': successful_checks,
                'success_rate': success_rate,
                'issues': [r.get('issue') for r in results if r.get('issue')]
            }

            if success_rate < 100:
                report['overall_integrity'] = False

        return report


async def main():
    """Test the proper cross-system verifier"""
    
    verifier = ProperCrossSystemVerifier()
    
    try:
        # Test with a few case IDs (you can modify these)
        test_case_ids = [
            "cl_texas_txnd_11108616",
            "cl_texas_txnd_11108615", 
            "cl_newyork_nynd_11108616"
        ]
        
        report = await verifier.verify_batch(test_case_ids)
        
        print(f"\n📊 VERIFICATION REPORT")
        print("=" * 80)
        print(f"Success Rate: {report['verification_summary']['success_rate']:.1f}%")
        print(f"Average Consistency: {report['verification_summary']['average_consistency_score']:.1f}%")
        print(f"Overall Success: {'✅' if report['overall_success'] else '❌'}")
        
        return report['overall_success']
        
    finally:
        verifier.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
