#!/usr/bin/env python3
"""
Test 2: Judge Relationship Integration
Test CourtListener metadata extraction and judge relationship creation
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.api.courtlistener.client import Court<PERSON>istenerClient
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JudgeRelationshipTest:
    """Test judge relationship integration with CourtListener metadata"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Initialize CourtListener client
        self.cl_client = CourtListenerClient()
        
        # Test batch ID
        self.test_batch_id = f"judge_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def get_existing_cases_for_judge_extraction(self) -> list:
        """Get existing cases from database for judge extraction"""

        print(f"📡 GETTING EXISTING CASES FOR JUDGE EXTRACTION")
        print("=" * 60)

        try:
            # Get recent CourtListener cases from database
            cases = self.supabase.table('cases').select('*').eq('source', 'courtlistener').limit(3).execute()

            if not cases.data:
                print("   ⚠️ No existing CourtListener cases found")
                return []

            print(f"   ✅ Found {len(cases.data)} existing CourtListener cases")

            # Transform to our format and add judge extraction
            test_cases = []
            for case_data in cases.data:
                # Get case text from Neo4j
                case_text = self.get_case_text_from_neo4j(case_data['id'])

                if case_text:
                    # Extract judges from text
                    judges = self.extract_judges_from_text(case_text)

                    # Create enhanced case data
                    enhanced_case = {
                        'id': f"{case_data['id']}_judge_enhanced",  # Make unique for this test
                        'source': 'courtlistener',
                        'case_name': case_data.get('case_name', 'Unknown Case'),
                        'court': case_data.get('court', 'unknown'),
                        'court_name': case_data.get('court_name', 'Unknown Court'),
                        'date_filed': case_data.get('date_filed'),
                        'jurisdiction': case_data.get('jurisdiction', 'US'),
                        'text': case_text,
                        'plain_text': case_text,
                        'judges': judges,
                        'citations': case_data.get('citations', []),
                        'precedential_status': case_data.get('precedential_status', 'Unknown')
                    }

                    if judges:  # Only include cases where we found judges
                        test_cases.append(enhanced_case)
                        print(f"   📄 {enhanced_case['case_name'][:50]}...")
                        print(f"      Court: {enhanced_case['court']}")
                        print(f"      Judges found: {len(judges)}")
                        for judge in judges[:2]:
                            print(f"         - {judge['name']} ({judge['role']})")

            print(f"\n📊 JUDGE EXTRACTION SUMMARY:")
            print(f"   Cases with judges: {len(test_cases)}")
            print(f"   Total judges: {sum(len(case.get('judges', [])) for case in test_cases)}")

            return test_cases

        except Exception as e:
            print(f"   ❌ Error getting existing cases: {e}")
            return []

    def get_case_text_from_neo4j(self, case_id: str) -> str:
        """Get case text from Neo4j"""

        try:
            with self.neo4j_client.driver.session() as session:
                result = session.run(
                    'MATCH (c:Case {id: $case_id}) RETURN c.text as text',
                    case_id=case_id
                )
                record = result.single()
                if record and record['text']:
                    return record['text']
        except Exception as e:
            logger.debug(f"Could not get text from Neo4j for {case_id}: {e}")

        return ""
    
    def transform_judge_rich_case(self, case_data: dict, court_info: dict) -> dict:
        """Transform CourtListener case with focus on judge information"""
        
        try:
            # Extract text content
            text_content = ""
            
            if case_data.get('plain_text'):
                text_content = case_data['plain_text']
            elif case_data.get('text'):
                text_content = case_data['text']
            elif case_data.get('html_with_citations'):
                import re
                text_content = re.sub(r'<[^>]+>', '', case_data['html_with_citations'])
            
            # Try to get opinion text if main case doesn't have it
            if not text_content and case_data.get('sub_opinions'):
                for opinion_url in case_data.get('sub_opinions', [])[:1]:
                    try:
                        opinion_id = opinion_url.split('/')[-2]
                        opinion = self.cl_client.get_opinion_raw(opinion_id, full_case=True)
                        if opinion.get('plain_text'):
                            text_content = opinion['plain_text']
                            break
                    except:
                        continue
            
            if not text_content or len(text_content) < 500:
                return None
            
            # Extract comprehensive judge information
            judges = self.extract_comprehensive_judges(case_data, text_content)
            
            # Only return cases with judge information
            if not judges:
                return None
            
            # Transform to our format with rich metadata
            transformed_case = {
                'id': f"cl_{court_info['court']}_{case_data.get('id', 'unknown')}",
                'source': 'courtlistener',
                'case_name': case_data.get('case_name', 'Unknown Case'),
                'court': court_info['court'],
                'court_name': court_info['name'],
                'date_filed': case_data.get('date_filed'),
                'jurisdiction': 'US',
                'text': text_content,
                'plain_text': text_content,
                'judges': judges,
                'citations': case_data.get('citations', []),
                'cl_cluster_id': case_data.get('id'),
                'docket_number': case_data.get('docket_number', ''),
                'precedential_status': case_data.get('precedential_status', 'Unknown'),
                'nature_of_suit': case_data.get('nature_of_suit', ''),
                'syllabus': case_data.get('syllabus', ''),
                'headnotes': case_data.get('headnotes', [])
            }
            
            return transformed_case
            
        except Exception as e:
            logger.error(f"Error transforming judge-rich case: {e}")
            return None
    
    def extract_comprehensive_judges(self, case_data: dict, text_content: str) -> list:
        """Extract comprehensive judge information from CourtListener case"""
        
        judges = []
        seen_judges = set()
        
        # Method 1: From panel data
        if case_data.get('panel'):
            for judge in case_data['panel']:
                if isinstance(judge, dict):
                    judge_name = judge.get('name_full', judge.get('name_last', ''))
                    if judge_name and judge_name not in seen_judges:
                        judges.append({
                            'name': judge_name,
                            'role': 'panel',
                            'court': case_data.get('court', ''),
                            'source': 'panel_data'
                        })
                        seen_judges.add(judge_name)
        
        # Method 2: From author information
        if case_data.get('author_str'):
            author_name = case_data['author_str']
            if author_name and author_name not in seen_judges:
                judges.append({
                    'name': author_name,
                    'role': 'author',
                    'court': case_data.get('court', ''),
                    'source': 'author_data'
                })
                seen_judges.add(author_name)
        
        # Method 3: Extract from text using patterns
        text_judges = self.extract_judges_from_text(text_content)
        for judge in text_judges:
            if judge['name'] not in seen_judges:
                judge['court'] = case_data.get('court', '')
                judge['source'] = 'text_extraction'
                judges.append(judge)
                seen_judges.add(judge['name'])
        
        return judges
    
    def extract_judges_from_text(self, text: str) -> list:
        """Extract judge names from case text using patterns"""
        
        import re
        judges = []
        
        # Common judge title patterns
        judge_patterns = [
            r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Chief\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Circuit\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'District\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in judge_patterns:
            matches = re.finditer(pattern, text[:2000])  # Check first 2000 chars
            for match in matches:
                judge_name = match.group(1).strip()
                if len(judge_name) > 3 and len(judge_name.split()) <= 3:  # Reasonable name length
                    judges.append({
                        'name': judge_name,
                        'role': 'mentioned'
                    })
        
        return judges[:5]  # Limit to 5 judges from text
    
    async def test_judge_relationship_integration(self) -> bool:
        """Test judge relationship integration with metadata extraction"""
        
        print(f"\n🔄 JUDGE RELATIONSHIP INTEGRATION TEST")
        print("=" * 60)
        
        try:
            # Get existing cases and extract judges
            test_cases = self.get_existing_cases_for_judge_extraction()
            
            if not test_cases:
                print("❌ No judge-rich CourtListener test cases retrieved")
                return False
            
            print(f"\n📊 Processing {len(test_cases)} judge-rich cases")
            
            # Show detailed case and judge summary
            total_judges = 0
            for i, case in enumerate(test_cases, 1):
                judges = case.get('judges', [])
                total_judges += len(judges)
                
                print(f"\n   {i}. {case.get('case_name', 'Unknown')} ({case.get('court', 'Unknown')})")
                print(f"      Date: {case.get('date_filed', 'Unknown')}")
                print(f"      Text length: {len(case.get('text', '')):,} characters")
                print(f"      Judges ({len(judges)}):")
                
                for j, judge in enumerate(judges[:3], 1):  # Show first 3 judges
                    print(f"         {j}. {judge.get('name', 'Unknown')} ({judge.get('role', 'Unknown')})")
                
                if len(judges) > 3:
                    print(f"         ... and {len(judges) - 3} more")
            
            print(f"\n   📊 Total judges across all cases: {total_judges}")
            
            # Process through pipeline with judge relationship creation
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing through pipeline with judge relationships...")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify judge relationships in Neo4j
            return await self.verify_judge_relationships()
            
        except Exception as e:
            print(f"❌ Judge relationship integration test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_judge_relationships(self) -> bool:
        """Verify judge nodes and relationships were created in Neo4j"""
        
        print(f"\n🔍 VERIFYING JUDGE RELATIONSHIPS")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # 1. Verify case nodes
                result = session.run(
                    'MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as case_count',
                    batch_id=self.test_batch_id
                )
                case_count = result.single()['case_count']
                print(f"📊 1. CASE NODES: {case_count}")
                
                # 2. Check for judge nodes
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)-[:DECIDED_BY|:PARTICIPATED|:AUTHORED]-(c:Case {batch_id: $batch_id})
                    }
                    RETURN count(j) as judge_count
                ''', batch_id=self.test_batch_id)
                judge_count = result.single()['judge_count']
                print(f"📊 2. JUDGE NODES: {judge_count}")
                
                # 3. Check judge-case relationships
                result = session.run('''
                    MATCH (j:Judge)-[r:DECIDED_BY|:PARTICIPATED|:AUTHORED]-(c:Case {batch_id: $batch_id})
                    RETURN type(r) as rel_type, count(r) as count
                    ORDER BY count DESC
                ''', batch_id=self.test_batch_id)
                
                relationships = list(result)
                total_judge_relationships = sum(record['count'] for record in relationships)
                
                print(f"📊 3. JUDGE-CASE RELATIONSHIPS: {total_judge_relationships}")
                for record in relationships:
                    print(f"      {record['rel_type']}: {record['count']}")
                
                # 4. Show sample judge information
                result = session.run('''
                    MATCH (j:Judge)-[r]-(c:Case {batch_id: $batch_id})
                    RETURN j.name as judge_name, j.court as court, 
                           type(r) as relationship, c.case_name as case_name
                    LIMIT 5
                ''', batch_id=self.test_batch_id)
                
                sample_relationships = list(result)
                
                if sample_relationships:
                    print(f"\n📊 4. SAMPLE JUDGE RELATIONSHIPS:")
                    for i, record in enumerate(sample_relationships, 1):
                        print(f"      {i}. Judge {record['judge_name']} ({record['court']})")
                        print(f"         {record['relationship']} → {record['case_name'][:50]}...")
                
                # 5. Verify metadata richness
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})
                    WHERE c.precedential_status IS NOT NULL
                    RETURN count(c) as cases_with_metadata
                ''', batch_id=self.test_batch_id)
                metadata_count = result.single()['cases_with_metadata']
                print(f"\n📊 5. METADATA ENRICHMENT:")
                print(f"      Cases with precedential status: {metadata_count}/{case_count}")
                
                # Success criteria
                success_criteria = [
                    case_count > 0,
                    judge_count > 0,
                    total_judge_relationships > 0,
                    metadata_count > 0
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 JUDGE RELATIONSHIP VERIFICATION:")
                print(f"   Cases created: {'✅' if case_count > 0 else '❌'}")
                print(f"   Judges created: {'✅' if judge_count > 0 else '❌'}")
                print(f"   Relationships created: {'✅' if total_judge_relationships > 0 else '❌'}")
                print(f"   Metadata enriched: {'✅' if metadata_count > 0 else '❌'}")
                
                if success:
                    print(f"\n🎉 JUDGE RELATIONSHIP INTEGRATION: SUCCESS!")
                    print(f"✅ {judge_count} judges linked to {case_count} cases via {total_judge_relationships} relationships")
                else:
                    print(f"\n❌ JUDGE RELATIONSHIP INTEGRATION: FAILED!")
                
                return success
                
        except Exception as e:
            print(f"❌ Judge relationship verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP JUDGE TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j (cases and judges)
            with self.neo4j_client.driver.session() as session:
                # Delete case nodes and their relationships
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                
                # Delete orphaned judge nodes (judges with no relationships)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test nodes and relationships")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run judge relationship integration test"""
    
    print("🧪 JUDGE RELATIONSHIP INTEGRATION TEST")
    print("=" * 80)
    print("🎯 Testing CourtListener metadata extraction and judge relationships")
    
    test = JudgeRelationshipTest()
    
    try:
        # Run the test
        success = await test.test_judge_relationship_integration()
        
        if success:
            print(f"\n🎉 JUDGE RELATIONSHIP INTEGRATION: SUCCESS!")
            print(f"✅ Judge nodes and relationships created successfully")
            return True
        else:
            print(f"\n❌ JUDGE RELATIONSHIP INTEGRATION: FAILED!")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
