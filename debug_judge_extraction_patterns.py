#!/usr/bin/env python3
"""
Debug Judge Extraction Patterns
Analyze what's being extracted and fix false positives
"""

import re
from real_neo4j_client import RealNeo4jClient

def test_judge_patterns_on_real_text():
    """Test judge patterns on the actual text that was processed"""
    
    print("🔍 DEBUGGING JUDGE EXTRACTION PATTERNS")
    print("=" * 60)
    
    # Get the actual text that was processed
    neo4j_client = RealNeo4jClient()
    
    try:
        with neo4j_client.driver.session() as session:
            # Get recent cases with text
            result = session.run('''
                MATCH (c:Case)
                WHERE c.text IS NOT NULL AND c.text <> ""
                AND c.batch_id CONTAINS "real_cl_judge_test"
                RETURN c.case_name as case_name, c.text as text
                LIMIT 2
            ''')
            
            cases = list(result)
            
            if not cases:
                print("❌ No recent test cases found")
                return
            
            print(f"📊 Found {len(cases)} recent test cases")
            
            # Current problematic patterns
            current_patterns = [
                r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)\b',
                r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)\b',
                r'Chief\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)\b',
                r'Circuit\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)\b',
                r'District\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)\b',
            ]
            
            for i, case in enumerate(cases, 1):
                print(f"\n📄 CASE {i}: {case['case_name']}")
                text = case['text']
                print(f"   Text length: {len(text):,} characters")
                
                # Show first 1000 characters to see the structure
                print(f"\n📝 TEXT SAMPLE (first 1000 chars):")
                print(f"   {text[:1000]}...")
                
                # Test each pattern
                search_text = text[:3000]  # First 3000 chars
                
                print(f"\n🔍 PATTERN TESTING:")
                
                for j, pattern in enumerate(current_patterns, 1):
                    matches = re.finditer(pattern, search_text, re.IGNORECASE)
                    pattern_matches = []
                    
                    for match in matches:
                        extracted = match.group(1).strip()
                        context_start = max(0, match.start() - 50)
                        context_end = min(len(search_text), match.end() + 50)
                        context = search_text[context_start:context_end]
                        
                        pattern_matches.append({
                            'extracted': extracted,
                            'context': context.replace('\n', ' ').strip()
                        })
                    
                    if pattern_matches:
                        print(f"\n   Pattern {j}: {pattern}")
                        for k, match in enumerate(pattern_matches, 1):
                            print(f"      {k}. Extracted: '{match['extracted']}'")
                            print(f"         Context: ...{match['context']}...")
                            
                            # Analyze if this looks like a real judge name
                            is_likely_judge = analyze_judge_likelihood(match['extracted'], match['context'])
                            print(f"         Likely judge: {'✅' if is_likely_judge else '❌'}")
                    else:
                        print(f"\n   Pattern {j}: No matches")
            
            # Suggest improved patterns
            print(f"\n💡 SUGGESTED IMPROVED PATTERNS:")
            improved_patterns = [
                r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|concurred|dissented)',
                r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s*[,:.]',
                r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored|presiding)',
                r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Chief\s+)?(?:Justice|Judge)',
                r'(?:Before|Panel):\s+[^.]*?([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Circuit\s+)?Judge',
            ]
            
            for i, pattern in enumerate(improved_patterns, 1):
                print(f"   {i}. {pattern}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        neo4j_client.close()


def analyze_judge_likelihood(name: str, context: str) -> bool:
    """Analyze if extracted text is likely a real judge name"""
    
    # Check for obvious false positives
    false_positive_indicators = [
        'city', 'county', 'state', 'district', 'court', 'case', 'plaintiff', 'defendant',
        'announced', 'presiding', 'over', 'under', 'above', 'below', 'between',
        'heights', 'valley', 'river', 'mountain', 'street', 'avenue', 'road',
        'company', 'corporation', 'inc', 'llc', 'ltd'
    ]
    
    name_lower = name.lower()
    context_lower = context.lower()
    
    # Check if name contains obvious non-name words
    for indicator in false_positive_indicators:
        if indicator in name_lower:
            return False
    
    # Check context for judge-like patterns
    judge_context_patterns = [
        r'judge\s+' + re.escape(name.lower()),
        r'justice\s+' + re.escape(name.lower()),
        r'delivered\s+(?:the\s+)?opinion',
        r'wrote\s+(?:the\s+)?(?:majority|dissenting|concurring)',
        r'authored\s+(?:the\s+)?opinion'
    ]
    
    has_judge_context = any(re.search(pattern, context_lower) for pattern in judge_context_patterns)
    
    # Name structure analysis
    words = name.split()
    
    # Single word names are suspicious unless clearly in judge context
    if len(words) == 1:
        return has_judge_context
    
    # Multi-word names should look like real names
    if len(words) >= 2:
        # Check if all words are capitalized (good sign)
        properly_capitalized = all(word[0].isupper() for word in words if word)
        
        # Check if contains middle initial pattern
        has_middle_initial = any(len(word) == 2 and word.endswith('.') for word in words)
        
        return properly_capitalized and (has_judge_context or has_middle_initial)
    
    return False


if __name__ == "__main__":
    test_judge_patterns_on_real_text()
