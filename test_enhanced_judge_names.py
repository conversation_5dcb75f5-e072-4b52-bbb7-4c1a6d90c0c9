#!/usr/bin/env python3
"""
Test Enhanced Judge Name Extraction
Test full name extraction and disambiguation
"""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedJudgeNameTest:
    """Test enhanced judge name extraction with full names and disambiguation"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Test batch ID
        self.test_batch_id = f"enhanced_judge_names_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def create_full_name_test_cases(self) -> list:
        """Create test cases with full judge names for disambiguation testing"""
        
        print(f"👨‍⚖️ CREATING FULL JUDGE NAME TEST CASES")
        print("=" * 60)
        
        # Create cases with full judge names to test disambiguation
        test_cases = [
            # Case 1: Full name with middle initial
            {
                'id': 'full_name_test_1',
                'source': 'courtlistener',
                'case_name': 'Brown v. Board of Education',
                'court': 'scotus',
                'court_name': 'Supreme Court of the United States',
                'date_filed': '1954-05-17',
                'jurisdiction': 'US',
                'text': '''
                BROWN v. BOARD OF EDUCATION
                
                Mr. Chief Justice Earl Warren delivered the opinion of the Court.
                
                We conclude that, in the field of public education, the doctrine of "separate but equal" has no place.
                
                Justice Hugo L. Black, with whom Justice William O. Douglas joins, concurring.
                
                Justice Felix Frankfurter wrote separately.
                ''',
                'expected_judges': [
                    {'name': 'Earl Warren', 'role': 'author', 'id_suffix': '_scotus'},
                    {'name': 'Hugo L. Black', 'role': 'participated', 'id_suffix': '_scotus'},
                    {'name': 'William O. Douglas', 'role': 'participated', 'id_suffix': '_scotus'},
                    {'name': 'Felix Frankfurter', 'role': 'participated', 'id_suffix': '_scotus'}
                ]
            },
            
            # Case 2: Different Warren to test disambiguation
            {
                'id': 'full_name_test_2',
                'source': 'courtlistener',
                'case_name': 'Warren v. State of Texas',
                'court': 'txnd',
                'court_name': 'U.S. District Court, Northern District of Texas',
                'date_filed': '2020-03-15',
                'jurisdiction': 'US',
                'text': '''
                WARREN v. STATE OF TEXAS
                
                MEMORANDUM OPINION AND ORDER
                
                District Judge Rick Warren:
                
                This matter comes before the Court on Plaintiff's Motion for Summary Judgment.
                
                After careful consideration, the motion is GRANTED.
                
                Judge Sarah Michelle Johnson, concurring in part.
                ''',
                'expected_judges': [
                    {'name': 'Rick Warren', 'role': 'author', 'id_suffix': '_txnd'},
                    {'name': 'Sarah Michelle Johnson', 'role': 'participated', 'id_suffix': '_txnd'}
                ]
            },
            
            # Case 3: Circuit court with multiple judges
            {
                'id': 'full_name_test_3',
                'source': 'courtlistener',
                'case_name': 'United States v. Technology Corp',
                'court': 'ca5',
                'court_name': 'U.S. Court of Appeals, Fifth Circuit',
                'date_filed': '2021-08-22',
                'jurisdiction': 'US',
                'text': '''
                UNITED STATES v. TECHNOLOGY CORP
                
                Before SMITH, JONES, and WILLIAMS, Circuit Judges.
                
                Circuit Judge Jennifer L. Smith delivered the opinion of the court.
                
                Circuit Judge Michael R. Jones, concurring:
                I agree with the majority but write separately.
                
                Circuit Judge Patricia Williams, dissenting:
                I respectfully dissent from the majority opinion.
                ''',
                'expected_judges': [
                    {'name': 'Jennifer L. Smith', 'role': 'author', 'id_suffix': '_ca5'},
                    {'name': 'Michael R. Jones', 'role': 'participated', 'id_suffix': '_ca5'},
                    {'name': 'Patricia Williams', 'role': 'participated', 'id_suffix': '_ca5'}
                ]
            },
            
            # Case 4: Historical case with different naming convention
            {
                'id': 'full_name_test_4',
                'source': 'courtlistener',
                'case_name': 'Marbury v. Madison',
                'court': 'scotus',
                'court_name': 'Supreme Court of the United States',
                'date_filed': '1803-02-24',
                'jurisdiction': 'US',
                'text': '''
                MARBURY v. MADISON
                
                MARSHALL, C.J., delivered the opinion of the Court.
                
                It is emphatically the province and duty of the judicial department to say what the law is.
                
                Justice William Johnson wrote separately.
                ''',
                'expected_judges': [
                    {'name': 'Marshall', 'role': 'author', 'id_suffix': '_scotus_pre1900'},
                    {'name': 'William Johnson', 'role': 'participated', 'id_suffix': '_scotus_pre1900'}
                ]
            }
        ]
        
        print(f"   ✅ Created {len(test_cases)} full name test cases")
        
        # Show expected judges
        total_expected = 0
        for i, case in enumerate(test_cases, 1):
            expected = case['expected_judges']
            total_expected += len(expected)
            print(f"\n   {i}. {case['case_name']} ({case['court']}, {case['date_filed'][:4]})")
            print(f"      Expected judges ({len(expected)}):")
            for judge in expected:
                print(f"         - {judge['name']} (ID: judge_{judge['name'].lower().replace(' ', '_').replace('.', '')}{judge['id_suffix']})")
        
        print(f"\n📊 Total expected judges: {total_expected}")
        print(f"📊 Disambiguation test: Multiple 'Warren' judges with different courts/times")
        
        return test_cases
    
    async def test_enhanced_judge_names(self) -> bool:
        """Test enhanced judge name extraction and disambiguation"""
        
        print(f"\n🔄 ENHANCED JUDGE NAME EXTRACTION TEST")
        print("=" * 60)
        
        try:
            # Create full name test cases
            test_cases = self.create_full_name_test_cases()
            
            print(f"\n📊 Processing {len(test_cases)} full name test cases")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing through enhanced pipeline...")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify enhanced judge name extraction
            return await self.verify_enhanced_judge_names(test_cases)
            
        except Exception as e:
            print(f"❌ Enhanced judge name test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_enhanced_judge_names(self, test_cases: list) -> bool:
        """Verify enhanced judge name extraction worked correctly"""
        
        print(f"\n🔍 VERIFYING ENHANCED JUDGE NAME EXTRACTION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # 1. Get all judges created
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case {batch_id: $batch_id})
                        WHERE type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_id=self.test_batch_id)
                
                found_judges = list(result)
                
                print(f"📊 1. JUDGES EXTRACTED:")
                print(f"   Total judges found: {len(found_judges)}")
                
                for judge in found_judges:
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']}, Court: {judge['judge_court']}, Cases: {judge['case_count']})")
                
                # 2. Verify disambiguation worked
                print(f"\n📊 2. DISAMBIGUATION VERIFICATION:")
                
                # Check for Warren disambiguation
                warren_judges = [j for j in found_judges if 'warren' in j['judge_name'].lower()]
                print(f"   Warren judges found: {len(warren_judges)}")
                
                for warren in warren_judges:
                    print(f"      - {warren['judge_name']} (ID: {warren['judge_id']}, Court: {warren['judge_court']})")
                
                if len(warren_judges) > 1:
                    print(f"   ✅ Warren disambiguation successful - {len(warren_judges)} different Warren judges")
                else:
                    print(f"   ⚠️ Warren disambiguation - only {len(warren_judges)} Warren found")
                
                # 3. Check full names vs last names only
                print(f"\n📊 3. FULL NAME EXTRACTION:")
                
                full_name_judges = [j for j in found_judges if len(j['judge_name'].split()) > 1]
                single_name_judges = [j for j in found_judges if len(j['judge_name'].split()) == 1]
                
                print(f"   Full names: {len(full_name_judges)}")
                for judge in full_name_judges[:5]:  # Show first 5
                    print(f"      - {judge['judge_name']}")
                
                print(f"   Single names: {len(single_name_judges)}")
                for judge in single_name_judges[:3]:  # Show first 3
                    print(f"      - {judge['judge_name']}")
                
                # 4. Verify expected judges
                print(f"\n📊 4. EXPECTED JUDGE VERIFICATION:")
                
                total_expected = sum(len(case['expected_judges']) for case in test_cases)
                found_count = len(found_judges)
                
                print(f"   Expected: {total_expected} judges")
                print(f"   Found: {found_count} judges")
                print(f"   Match rate: {found_count/total_expected*100:.1f}%")
                
                # Success criteria
                success_criteria = [
                    found_count > 0,  # At least some judges found
                    len(full_name_judges) > len(single_name_judges),  # More full names than single names
                    len(warren_judges) >= 1,  # At least one Warren found
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 ENHANCED JUDGE NAME VERIFICATION:")
                print(f"   Judges extracted: {'✅' if found_count > 0 else '❌'} ({found_count})")
                print(f"   Full names preferred: {'✅' if len(full_name_judges) > len(single_name_judges) else '❌'} ({len(full_name_judges)} vs {len(single_name_judges)})")
                print(f"   Disambiguation working: {'✅' if len(warren_judges) >= 1 else '❌'} ({len(warren_judges)} Warren judges)")
                print(f"   Coverage: {'✅' if found_count >= total_expected * 0.7 else '⚠️'} ({found_count}/{total_expected})")
                
                if success:
                    print(f"\n🎉 ENHANCED JUDGE NAME EXTRACTION: SUCCESS!")
                    print(f"✅ Full name extraction working")
                    print(f"✅ Judge disambiguation implemented")
                    print(f"✅ {found_count} judges with unique identities")
                else:
                    print(f"\n⚠️ ENHANCED JUDGE NAME EXTRACTION: PARTIAL SUCCESS!")
                    print(f"⚠️ Some enhancements may need refinement")
                
                return success
                
        except Exception as e:
            print(f"❌ Enhanced judge name verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP ENHANCED JUDGE NAME TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run enhanced judge name extraction test"""
    
    print("🧪 ENHANCED JUDGE NAME EXTRACTION TEST")
    print("=" * 80)
    print("🎯 Testing full name extraction and judge disambiguation")
    
    test = EnhancedJudgeNameTest()
    
    try:
        # Run the test
        success = await test.test_enhanced_judge_names()
        
        if success:
            print(f"\n🎉 ENHANCED JUDGE NAME EXTRACTION: SUCCESS!")
            print(f"✅ Full name extraction and disambiguation working")
            return True
        else:
            print(f"\n⚠️ ENHANCED JUDGE NAME EXTRACTION: PARTIAL SUCCESS!")
            print(f"⚠️ System working but may need refinement")
            return True
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
