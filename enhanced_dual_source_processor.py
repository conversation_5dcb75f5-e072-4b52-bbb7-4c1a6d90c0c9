#!/usr/bin/env python3
"""
Enhanced Dual-Source Processor
Integrates the new enhanced CourtListener processor with existing CAP processing
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor

# Import existing dual-source components
from dual_source_coordinator import DualSourceCoordinator
from enhanced_cap_processor import EnhancedCAPProcessor

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'enhanced_dual_source_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MockGCSClient:
    """Mock GCS client for enhanced processing"""
    def __init__(self):
        self.name = "mock_gcs"
        logger.info("✅ Mock GCS client initialized")


class MockPineconeClient:
    """Mock Pinecone client for enhanced processing"""
    def __init__(self):
        self.name = "mock_pinecone"
        logger.info("✅ Mock Pinecone client initialized")


class MockNeo4jClient:
    """Mock Neo4j client for enhanced processing"""
    def __init__(self):
        self.name = "mock_neo4j"
        logger.info("✅ Mock Neo4j client initialized")


async def setup_enhanced_clients():
    """Setup clients for enhanced processing"""
    logger.info("🔧 Setting up enhanced processing clients...")
    
    # Supabase client (real)
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
    
    supabase: Client = create_client(supabase_url, supabase_key)
    logger.info("✅ Supabase client initialized")
    
    # Mock clients for enhanced features
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    return supabase, gcs_client, pinecone_client, neo4j_client


async def run_enhanced_dual_source_processing(
    jurisdiction: str = 'tx',
    start_year: int = 1994,
    end_year: int = 2025,
    courtlistener_only: bool = False,
    cap_only: bool = False
):
    """
    Run enhanced dual-source processing
    
    Args:
        jurisdiction: Jurisdiction to process (tx, ny, fl)
        start_year: Starting year for CourtListener processing
        end_year: Ending year for CourtListener processing
        courtlistener_only: Process only CourtListener with enhanced features
        cap_only: Process only CAP data (requires existing CourtListener data)
    """
    
    logger.info("🚀 ENHANCED DUAL-SOURCE PROCESSING")
    logger.info("=" * 80)
    logger.info(f"📍 Jurisdiction: {jurisdiction.upper()}")
    logger.info(f"📅 Year Range: {start_year}-{end_year}")
    logger.info(f"🎯 Mode: {'CourtListener Only' if courtlistener_only else 'CAP Only' if cap_only else 'Full Dual-Source'}")
    
    overall_start = datetime.now()
    results = {}
    
    try:
        # Phase 1: Enhanced CourtListener Processing
        if not cap_only:
            logger.info("\n" + "="*60)
            logger.info("📡 PHASE 1: ENHANCED COURTLISTENER PROCESSING")
            logger.info("="*60)
            
            # Get API key
            api_key = os.getenv("COURTLISTENER_API_KEY")
            if not api_key:
                raise ValueError("COURTLISTENER_API_KEY must be set")
            
            # Setup enhanced clients
            supabase, gcs_client, pinecone_client, neo4j_client = await setup_enhanced_clients()
            
            # Initialize enhanced processor
            async with ChunkedCourtListenerProcessor(
                api_key=api_key,
                supabase_client=supabase,
                gcs_client=gcs_client,
                pinecone_client=pinecone_client,
                neo4j_client=neo4j_client,
                chunk_size=10,  # Process exactly 10 cases for production validation
                batch_size=10   # Process all 10 in one batch
            ) as processor:
                
                logger.info("🎯 Enhanced CourtListener processor initialized")
                
                # Process with enhanced features
                cl_results = await processor.process_jurisdiction_chunked(
                    jurisdiction=jurisdiction,
                    start_year=start_year,
                    end_year=end_year,
                    resume=True
                )
                
                results['courtlistener'] = cl_results
                
                logger.info("✅ Enhanced CourtListener processing complete!")
                logger.info(f"📊 Results: {cl_results}")
        
        # Phase 2: CAP Processing (if not CourtListener-only)
        if not courtlistener_only:
            logger.info("\n" + "="*60)
            logger.info("📚 PHASE 2: CASE LAW ACCESS PROJECT PROCESSING")
            logger.info("="*60)
            
            # Initialize CAP processor
            cap_processor = EnhancedCAPProcessor(
                data_dir="data/caselaw_access_project",
                target_state=jurisdiction
            )
            
            # Process CAP data
            cap_results = await cap_processor.process_jurisdiction_comprehensive(jurisdiction)
            
            results['cap'] = cap_results
            
            logger.info("✅ CAP processing complete!")
            logger.info(f"📊 CAP Results: Processed {cap_results.processed_cases:,} cases")
        
        # Phase 3: Cross-Source Analysis (if full dual-source)
        if not courtlistener_only and not cap_only:
            logger.info("\n" + "="*60)
            logger.info("🔍 PHASE 3: CROSS-SOURCE ANALYSIS")
            logger.info("="*60)
            
            # Analyze cross-source consistency
            cross_analysis = await analyze_cross_source_consistency(jurisdiction)
            results['cross_analysis'] = cross_analysis
            
            logger.info("✅ Cross-source analysis complete!")
        
        # Final Summary
        overall_end = datetime.now()
        duration = overall_end - overall_start
        
        logger.info("\n" + "="*80)
        logger.info("🎉 ENHANCED DUAL-SOURCE PROCESSING COMPLETE")
        logger.info("="*80)
        logger.info(f"⏱️ Total Duration: {duration}")
        logger.info(f"📍 Jurisdiction: {jurisdiction.upper()}")
        
        if 'courtlistener' in results:
            cl_cases = results['courtlistener'].get('total_cases_processed', 0)
            logger.info(f"📡 CourtListener Cases: {cl_cases:,}")
        
        if 'cap' in results:
            cap_cases = results['cap'].processed_cases
            logger.info(f"📚 CAP Cases: {cap_cases:,}")
        
        if 'cross_analysis' in results:
            total_unique = results['cross_analysis'].get('unique_cases', 0)
            logger.info(f"🎯 Total Unique Cases: {total_unique:,}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Enhanced dual-source processing failed: {e}")
        raise


async def analyze_cross_source_consistency(jurisdiction: str):
    """Analyze consistency between CourtListener and CAP data"""
    
    logger.info("🔍 Analyzing cross-source consistency...")
    
    # Setup Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    # Count cases by source
    cl_result = supabase.table('cases').select('id', count='exact').eq(
        'source', 'courtlistener'
    ).eq('jurisdiction', jurisdiction.upper()).execute()
    
    cap_result = supabase.table('cases').select('id', count='exact').eq(
        'source', 'caselaw_access_project'
    ).eq('jurisdiction', jurisdiction.upper()).execute()
    
    cl_count = cl_result.count or 0
    cap_count = cap_result.count or 0
    total_count = cl_count + cap_count
    
    # Analyze enhanced features coverage
    enhanced_result = supabase.table('cases').select('id', count='exact').filter(
        'gcs_path', 'not.is', 'null'
    ).eq('jurisdiction', jurisdiction.upper()).execute()
    
    enhanced_count = enhanced_result.count or 0
    
    analysis = {
        'courtlistener_cases': cl_count,
        'cap_cases': cap_count,
        'total_cases': total_count,
        'unique_cases': total_count,  # Simplified - actual dedup would be more complex
        'enhanced_cases': enhanced_count,
        'enhancement_coverage': enhanced_count / max(total_count, 1),
        'sources_ratio': {
            'courtlistener': cl_count / max(total_count, 1),
            'cap': cap_count / max(total_count, 1)
        }
    }
    
    logger.info(f"   📡 CourtListener: {cl_count:,} cases")
    logger.info(f"   📚 CAP: {cap_count:,} cases")
    logger.info(f"   🎯 Total: {total_count:,} cases")
    logger.info(f"   ✨ Enhanced: {enhanced_count:,} cases ({analysis['enhancement_coverage']:.1%})")
    
    return analysis


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced Dual-Source Legal Corpus Processor")
    parser.add_argument("--jurisdiction", default="tx", help="Jurisdiction to process (tx, ny, fl)")
    parser.add_argument("--start-year", type=int, default=1994, help="Starting year for CourtListener")
    parser.add_argument("--end-year", type=int, default=2025, help="Ending year for CourtListener")
    parser.add_argument("--courtlistener-only", action="store_true", help="Process only CourtListener")
    parser.add_argument("--cap-only", action="store_true", help="Process only CAP data")
    
    args = parser.parse_args()
    
    # Load environment
    load_dotenv()
    
    try:
        results = await run_enhanced_dual_source_processing(
            jurisdiction=args.jurisdiction,
            start_year=args.start_year,
            end_year=args.end_year,
            courtlistener_only=args.courtlistener_only,
            cap_only=args.cap_only
        )
        
        logger.info("🎯 Processing completed successfully")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️ Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"💥 Processing failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
