#!/usr/bin/env python3
"""
Test Real Data Full Names
Test the improved full name extraction on real CourtListener and CAP data
"""

import asyncio
import logging
import os
import requests
import time
import json
import gzip
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealDataFullNameTest:
    """Test full name extraction with real data from both sources"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # CAP data path
        self.cap_data_path = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
        
        # Test batch ID
        self.test_batch_id = f"real_full_names_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_real_courtlistener_cases(self, limit: int = 3) -> list:
        """Fetch real CourtListener cases with judge information"""
        
        print(f"🌐 FETCHING REAL COURTLISTENER CASES")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        real_cases = []
        
        try:
            # Search for recent federal cases with substantial text
            search_params = {
                'court': 'scotus,ca5,ca2,ca9,txnd,nysd',  # Mix of courts
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 15  # Get more to filter for judge info
            }
            
            print(f"   🔍 Searching CourtListener API...")
            
            response = requests.get(
                f"{self.cl_base_url}/opinions/",
                headers=headers,
                params=search_params,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ API request failed: {response.status_code}")
                return []
            
            search_results = response.json()
            results = search_results.get('results', [])
            
            print(f"   📊 Found {len(results)} search results")
            
            # Process opinions for judge information
            for opinion_data in results[:limit * 2]:  # Get extra to filter
                if len(real_cases) >= limit:
                    break
                
                try:
                    print(f"   📄 Processing: {opinion_data.get('case_name', 'Unknown')[:50]}...")
                    
                    time.sleep(0.2)  # Rate limit
                    
                    # Check text content
                    plain_text = opinion_data.get('plain_text', '')
                    html_content = opinion_data.get('html', '')
                    text_content = plain_text or html_content
                    
                    if not text_content or len(text_content) < 1000:
                        print(f"      ⚠️ Insufficient text content ({len(text_content)} chars)")
                        continue
                    
                    # Look for judge patterns
                    text_lower = text_content.lower()
                    judge_indicators = ['judge', 'justice', 'circuit judge', 'district judge', 'delivered', 'opinion']
                    
                    if not any(indicator in text_lower for indicator in judge_indicators):
                        print(f"      ⚠️ No judge indicators found")
                        continue
                    
                    # Create processing format
                    case_data = {
                        'id': f"cl_real_full_{opinion_data.get('id', 'unknown')}",
                        'source': 'courtlistener',
                        'case_name': opinion_data.get('case_name', 'Unknown'),
                        'court': opinion_data.get('court', ''),
                        'court_name': self._get_court_name(opinion_data.get('court', '')),
                        'date_filed': opinion_data.get('date_filed', ''),
                        'jurisdiction': 'US',
                        'text': text_content,
                        'precedential_status': opinion_data.get('precedential_status', 'Unknown')
                    }
                    
                    real_cases.append(case_data)
                    
                    print(f"      ✅ Case {len(real_cases)}: {case_data['case_name'][:50]}...")
                    print(f"         Court: {case_data['court_name']}")
                    print(f"         Text length: {len(text_content):,} characters")
                    
                except Exception as e:
                    print(f"      ❌ Error processing case: {e}")
                    continue
            
            print(f"\n📊 REAL COURTLISTENER CASES FETCHED:")
            print(f"   Total cases: {len(real_cases)}")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
            return []
    
    def load_real_cap_cases(self, limit: int = 2) -> list:
        """Load real CAP cases from local data files"""
        
        print(f"\n📂 LOADING REAL CAP CASES")
        print("=" * 60)
        
        real_cases = []
        
        try:
            # Look for CAP data files
            cap_files = []
            if os.path.exists(self.cap_data_path):
                for file in os.listdir(self.cap_data_path):
                    if file.startswith('cap_') and file.endswith('.jsonl.gz'):
                        cap_files.append(os.path.join(self.cap_data_path, file))
            
            if not cap_files:
                print(f"   ❌ No CAP files found in {self.cap_data_path}")
                return []
            
            print(f"   📁 Found {len(cap_files)} CAP files")
            
            # Load cases from first file
            for file_path in cap_files[:1]:  # Just first file
                print(f"   📄 Loading from: {os.path.basename(file_path)}")
                
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if len(real_cases) >= limit:
                            break
                        
                        try:
                            case_data = json.loads(line.strip())
                            
                            # Check if case has substantial text and judge indicators
                            text = case_data.get('text', '')
                            if text and len(text) > 1000:
                                text_lower = text.lower()
                                judge_indicators = ['judge', 'justice', 'court', 'opinion', 'delivered']
                                
                                if any(indicator in text_lower for indicator in judge_indicators):
                                    # Add source and processing info
                                    case_data['source'] = 'caselaw_access_project'
                                    case_data['id'] = f"cap_real_full_{case_data.get('id', line_num)}"
                                    real_cases.append(case_data)
                                    
                                    print(f"      ✅ Case {len(real_cases)}: {case_data.get('name', 'Unknown')[:50]}...")
                                    print(f"         Text length: {len(text):,} characters")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            continue
                
                if real_cases:
                    break  # Found cases in first file
            
            print(f"\n📊 REAL CAP CASES LOADED:")
            print(f"   Total cases: {len(real_cases)}")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error loading real CAP cases: {e}")
            return []
    
    def _get_court_name(self, court_id: str) -> str:
        """Get full court name from court ID"""
        court_names = {
            'scotus': 'Supreme Court of the United States',
            'ca5': 'U.S. Court of Appeals, Fifth Circuit',
            'ca2': 'U.S. Court of Appeals, Second Circuit',
            'ca9': 'U.S. Court of Appeals, Ninth Circuit',
            'txnd': 'U.S. District Court, Northern District of Texas',
            'nysd': 'U.S. District Court, Southern District of New York',
        }
        return court_names.get(court_id, f'Court {court_id}')
    
    async def test_real_data_full_names(self) -> bool:
        """Test full name extraction with real data from both sources"""
        
        print(f"\n🔄 REAL DATA FULL NAME EXTRACTION TEST")
        print("=" * 60)
        
        try:
            # Fetch real data from both sources
            cl_cases = self.fetch_real_courtlistener_cases(2)
            cap_cases = self.load_real_cap_cases(2)
            
            all_real_cases = cl_cases + cap_cases
            
            if not all_real_cases:
                print(f"❌ No real data found to test")
                return False
            
            print(f"\n📊 REAL DATA SUMMARY:")
            print(f"   CourtListener cases: {len(cl_cases)}")
            print(f"   CAP cases: {len(cap_cases)}")
            print(f"   Total real cases: {len(all_real_cases)}")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing real data through improved full name extraction...")
            
            # Process CourtListener cases
            if cl_cases:
                print(f"\n📄 Processing {len(cl_cases)} real CourtListener cases...")
                cl_result = await processor.process_coherent_batch(
                    raw_cases=cl_cases,
                    source_type='courtlistener',
                    batch_id=f"{self.test_batch_id}_cl"
                )
                print(f"   CourtListener Result: Success={cl_result['success']}, Processed={cl_result['processed']}")
            
            # Process CAP cases
            if cap_cases:
                print(f"\n📄 Processing {len(cap_cases)} real CAP cases...")
                cap_result = await processor.process_coherent_batch(
                    raw_cases=cap_cases,
                    source_type='caselaw_access_project',
                    batch_id=f"{self.test_batch_id}_cap"
                )
                print(f"   CAP Result: Success={cap_result['success']}, Processed={cap_result['processed']}")
            
            # Verify full name extraction on real data
            return await self.verify_real_data_full_names()
            
        except Exception as e:
            print(f"❌ Real data full name test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_real_data_full_names(self) -> bool:
        """Verify full name extraction worked on real data"""
        
        print(f"\n🔍 VERIFYING REAL DATA FULL NAME EXTRACTION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get all judges from real data
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case)
                        WHERE c.batch_id STARTS WITH $batch_prefix
                        AND type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_prefix=self.test_batch_id)
                
                all_judges = list(result)
                
                print(f"📊 1. JUDGES FROM REAL DATA:")
                print(f"   Total judges found: {len(all_judges)}")
                
                for judge in all_judges:
                    words = len(judge['judge_name'].split())
                    word_status = "✅ Full" if words > 1 else "❌ Partial"
                    print(f"      - {judge['judge_name']} ({word_status}, {words} words, ID: {judge['judge_id']})")
                
                # Analyze full name vs partial name extraction
                full_name_judges = [j for j in all_judges if len(j['judge_name'].split()) > 1]
                single_name_judges = [j for j in all_judges if len(j['judge_name'].split()) == 1]
                
                print(f"\n📊 2. FULL NAME ANALYSIS:")
                print(f"   Full names (2+ words): {len(full_name_judges)}")
                for judge in full_name_judges[:10]:  # Show first 10
                    print(f"      ✅ {judge['judge_name']}")
                
                print(f"   Single names (1 word): {len(single_name_judges)}")
                for judge in single_name_judges[:5]:  # Show first 5
                    print(f"      ❌ {judge['judge_name']}")
                
                # Check for middle initials and full names
                middle_initial_judges = [j for j in full_name_judges 
                                       if any(len(word) == 2 and word.endswith('.') 
                                             for word in j['judge_name'].split())]
                
                three_word_judges = [j for j in full_name_judges 
                                   if len(j['judge_name'].split()) >= 3]
                
                print(f"\n📊 3. QUALITY METRICS:")
                print(f"   Judges with middle initials: {len(middle_initial_judges)}")
                for judge in middle_initial_judges[:5]:
                    print(f"      ✅ {judge['judge_name']}")
                
                print(f"   Judges with 3+ words: {len(three_word_judges)}")
                for judge in three_word_judges[:5]:
                    print(f"      ✅ {judge['judge_name']}")
                
                # Success criteria for real data
                total_judges = len(all_judges)
                full_name_ratio = len(full_name_judges) / total_judges if total_judges > 0 else 0
                quality_ratio = len(middle_initial_judges) / total_judges if total_judges > 0 else 0
                
                success_criteria = [
                    total_judges > 0,  # At least some judges found
                    full_name_ratio >= 0.7,  # At least 70% full names
                    len(single_name_judges) <= len(full_name_judges),  # More full names than single names
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 REAL DATA FULL NAME VERIFICATION:")
                print(f"   Total judges: {'✅' if total_judges > 0 else '❌'} ({total_judges})")
                print(f"   Full name ratio: {'✅' if full_name_ratio >= 0.7 else '❌'} ({full_name_ratio:.1%})")
                print(f"   Quality ratio: {'✅' if quality_ratio > 0 else '⚠️'} ({quality_ratio:.1%} with middle initials)")
                print(f"   Full > Partial: {'✅' if len(single_name_judges) <= len(full_name_judges) else '❌'} ({len(full_name_judges)} vs {len(single_name_judges)})")
                
                if success:
                    print(f"\n🎉 REAL DATA FULL NAME EXTRACTION: SUCCESS!")
                    print(f"✅ Full name extraction working on real data")
                    print(f"✅ {len(full_name_judges)} judges with full names")
                    print(f"✅ {full_name_ratio:.1%} full name ratio")
                    print(f"✅ Production ready with real CourtListener and CAP data")
                else:
                    print(f"\n⚠️ REAL DATA FULL NAME EXTRACTION: NEEDS IMPROVEMENT!")
                    if total_judges == 0:
                        print(f"❌ No judges found in real data")
                    if full_name_ratio < 0.7:
                        print(f"❌ Low full name ratio: {full_name_ratio:.1%}")
                
                return success
                
        except Exception as e:
            print(f"❌ Real data full name verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP REAL DATA FULL NAME TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().like('batch_id', f'{self.test_batch_id}%').execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case) WHERE c.batch_id STARTS WITH $batch_prefix DETACH DELETE c', 
                          batch_prefix=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run real data full name extraction test"""
    
    print("🧪 REAL DATA FULL NAME EXTRACTION TEST")
    print("=" * 80)
    print("🎯 Testing improved full name extraction on real CourtListener and CAP data")
    
    test = RealDataFullNameTest()
    
    try:
        # Run the test
        success = await test.test_real_data_full_names()
        
        if success:
            print(f"\n🎉 REAL DATA FULL NAME EXTRACTION: SUCCESS!")
            print(f"✅ Full name extraction verified on real data")
            print(f"✅ Production ready for both CourtListener and CAP sources")
            return True
        else:
            print(f"\n❌ REAL DATA FULL NAME EXTRACTION: FAILED!")
            print(f"❌ Full name extraction needs refinement for real data")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
