#!/usr/bin/env python3
"""
Comprehensive Dual-Source Legal Data Processing

Master execution script for processing both Court Listener API and Case Law Access Project data
with full coordination, deduplication, quality assurance, and monitoring.

Usage:
    python execute_comprehensive_dual_source_processing.py [options]

Features:
- Coordinated processing of Court Listener + CAP data
- Cross-source duplicate prevention
- Practice area classification (7 areas)
- Priority state focus (TX, NY, FL)
- Quality assurance and validation
- Performance monitoring and reporting
- Checkpoint/resume capability
"""

import argparse
import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

from dual_source_coordinator import DualSourceCoordinator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'comprehensive_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    
    parser = argparse.ArgumentParser(
        description='Comprehensive Dual-Source Legal Data Processing',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all priority states
  python execute_comprehensive_dual_source_processing.py

  # Process specific jurisdiction
  python execute_comprehensive_dual_source_processing.py --jurisdiction tx

  # Resume from checkpoint
  python execute_comprehensive_dual_source_processing.py --resume checkpoint_id

  # Use custom CAP data directory
  python execute_comprehensive_dual_source_processing.py --cap-data-dir /path/to/cap/data
        """
    )
    
    parser.add_argument(
        '--jurisdiction',
        choices=['tx', 'ny', 'fl', 'all'],
        default='all',
        help='Jurisdiction to process (default: all priority states)'
    )
    
    parser.add_argument(
        '--cap-data-dir',
        type=str,
        default='data/caselaw_access_project',
        help='Directory containing Case Law Access Project data'
    )
    
    parser.add_argument(
        '--resume',
        type=str,
        help='Resume from checkpoint ID'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform dry run without actual processing'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser.parse_args()


async def main():
    """Main execution function."""
    
    # Parse arguments
    args = parse_arguments()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Print banner
    print("=" * 80)
    print("🚀 COMPREHENSIVE DUAL-SOURCE LEGAL DATA PROCESSING")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Jurisdiction: {args.jurisdiction}")
    print(f"CAP Data Directory: {args.cap_data_dir}")
    print(f"Resume Checkpoint: {args.resume or 'None'}")
    print(f"Dry Run: {args.dry_run}")
    print("=" * 80)
    
    # Validate CAP data directory
    cap_data_path = Path(args.cap_data_dir)
    if not cap_data_path.exists():
        logger.warning(f"⚠️ CAP data directory not found: {cap_data_path}")
        logger.info("   Processing will continue with Court Listener data only")
    else:
        cap_files = list(cap_data_path.rglob("*.jsonl.gz"))
        logger.info(f"✅ Found {len(cap_files)} CAP data files")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No actual processing will occur")
        logger.info("   This would process:")
        if args.jurisdiction == 'all':
            logger.info("   - Texas (Court Listener + CAP data)")
            logger.info("   - New York (Court Listener + CAP data)")
            logger.info("   - Florida (Court Listener + CAP data)")
        else:
            logger.info(f"   - {args.jurisdiction.upper()} (Court Listener + CAP data)")
        logger.info("   - 7 practice areas per jurisdiction")
        logger.info("   - Cross-source deduplication")
        logger.info("   - Quality assurance and validation")
        return True
    
    try:
        # Initialize coordinator
        logger.info("🔧 Initializing Dual-Source Coordinator...")
        coordinator = DualSourceCoordinator(cap_data_dir=args.cap_data_dir)
        
        # Process based on jurisdiction argument
        if args.jurisdiction == 'all':
            logger.info("🌟 Processing all priority states...")
            results = await coordinator.process_all_priority_states()
            
            # Check overall success
            success = any(stats.unique_cases_total > 0 for stats in results.values())
            
            if success:
                logger.info("🎉 COMPREHENSIVE PROCESSING COMPLETED SUCCESSFULLY!")
                
                # Print final summary
                total_unique = sum(stats.unique_cases_total for stats in results.values())
                total_cl = sum(stats.cl_cases_processed for stats in results.values())
                total_cap = sum(stats.cap_cases_processed for stats in results.values())
                
                print("\n" + "=" * 60)
                print("📊 FINAL PROCESSING SUMMARY")
                print("=" * 60)
                print(f"Total Unique Cases: {total_unique:,}")
                print(f"Court Listener Cases: {total_cl:,}")
                print(f"CAP Cases: {total_cap:,}")
                print("=" * 60)
                
                for jurisdiction, stats in results.items():
                    print(f"\n{jurisdiction.upper()}:")
                    print(f"  Unique Cases: {stats.unique_cases_total:,}")
                    print(f"  Court Listener: {stats.cl_cases_processed:,}")
                    print(f"  CAP: {stats.cap_cases_processed:,}")
                    print(f"  Practice Areas: {len(stats.practice_area_coverage)}")
                    
                    # Show practice area breakdown
                    if stats.practice_area_coverage:
                        print("  Practice Area Coverage:")
                        for area, coverage in stats.practice_area_coverage.items():
                            total_area = coverage.get('total', 0)
                            if total_area > 0:
                                print(f"    {area}: {total_area:,} cases")
                
            else:
                logger.error("❌ COMPREHENSIVE PROCESSING FAILED")
                return False
                
        else:
            logger.info(f"🎯 Processing single jurisdiction: {args.jurisdiction}")
            stats = await coordinator.process_jurisdiction_coordinated(
                jurisdiction=args.jurisdiction,
                resume_checkpoint=args.resume
            )
            
            success = stats.unique_cases_total > 0
            
            if success:
                logger.info(f"🎉 {args.jurisdiction.upper()} PROCESSING COMPLETED SUCCESSFULLY!")
                print(f"\n📊 {args.jurisdiction.upper()} SUMMARY:")
                print(f"Unique Cases: {stats.unique_cases_total:,}")
                print(f"Court Listener: {stats.cl_cases_processed:,}")
                print(f"CAP: {stats.cap_cases_processed:,}")
                print(f"Practice Areas: {len(stats.practice_area_coverage)}")
            else:
                logger.error(f"❌ {args.jurisdiction.upper()} PROCESSING FAILED")
                return False
        
        return success
        
    except KeyboardInterrupt:
        logger.info("⏹️ Processing interrupted by user")
        return False
        
    except Exception as e:
        logger.error(f"❌ Unexpected error during processing: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        
        if success:
            print("\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
            print("✅ All systems operational")
            print("✅ Data processed and stored")
            print("✅ Quality assurance passed")
            print("✅ Ready for production use")
            sys.exit(0)
        else:
            print("\n❌ PROCESSING FAILED")
            print("Please check logs for details")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {e}")
        sys.exit(1)
