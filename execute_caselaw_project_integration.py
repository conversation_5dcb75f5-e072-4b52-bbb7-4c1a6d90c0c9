#!/usr/bin/env python3
"""
Case Law Project Integration Script

This script integrates the Case Law Project data with the existing legal database system.
It processes bulk JSONL files from the Case Law Project and stores them in the same
infrastructure used for Court Listener data.
"""

import os
import sys
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.caselaw_access_processor import CaselawAccessProcessor
from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('caselaw_project_integration.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class CaselawProjectIntegrator:
    """Main class for integrating Case Law Project data"""
    
    def __init__(self, data_dir: str = "data/caselaw_access_project"):
        """
        Initialize the integrator.
        
        Args:
            data_dir: Directory containing Case Law Project JSONL files
        """
        self.data_dir = Path(data_dir)
        self.processor = CaselawAccessProcessor(str(self.data_dir))
        self.supabase = SupabaseConnector()
        
        # Ensure data directory exists
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized Case Law Project integrator with data directory: {self.data_dir}")
    
    def discover_jsonl_files(self) -> List[Path]:
        """
        Discover all JSONL files in the data directory.
        
        Returns:
            List of JSONL file paths
        """
        jsonl_files = []
        
        # Look for compressed and uncompressed JSONL files
        patterns = ["*.jsonl", "*.jsonl.gz", "*.json", "*.json.gz"]
        
        for pattern in patterns:
            jsonl_files.extend(self.data_dir.glob(pattern))
            # Also search subdirectories
            jsonl_files.extend(self.data_dir.glob(f"**/{pattern}"))
        
        logger.info(f"Discovered {len(jsonl_files)} JSONL files")
        return sorted(jsonl_files)
    
    async def process_all_files(self, max_files: Optional[int] = None) -> Dict[str, any]:
        """
        Process all discovered JSONL files.
        
        Args:
            max_files: Maximum number of files to process (None for all)
            
        Returns:
            Processing results summary
        """
        files = self.discover_jsonl_files()
        
        if max_files:
            files = files[:max_files]
            logger.info(f"Limited processing to {max_files} files")
        
        if not files:
            logger.warning("No JSONL files found to process")
            return {"total_files": 0, "processed_files": 0, "total_documents": 0}
        
        logger.info(f"Starting processing of {len(files)} files")

        # Load existing data for fast duplicate detection
        logger.info("🔄 Initializing fast duplicate detection...")
        await self.processor.deduplicator.load_existing_data()

        total_results = {
            "total_files": len(files),
            "processed_files": 0,
            "failed_files": 0,
            "total_documents": 0,
            "successful_documents": 0,
            "failed_documents": 0,
            "duplicate_documents": 0,
            "start_time": datetime.now(),
            "file_results": {}
        }
        
        for i, file_path in enumerate(files, 1):
            logger.info(f"\n[{i}/{len(files)}] Processing file: {file_path.name}")
            logger.info("-" * 60)
            
            try:
                file_results = await self.processor.process_file(file_path)
                
                total_results["processed_files"] += 1
                total_results["total_documents"] += sum(file_results.values())
                total_results["successful_documents"] += file_results.get("success", 0)
                total_results["failed_documents"] += file_results.get("failed", 0)
                total_results["duplicate_documents"] += file_results.get("duplicates", 0)
                total_results["file_results"][str(file_path)] = file_results
                
                logger.info(f"✅ File completed:")
                logger.info(f"   Success: {file_results.get('success', 0)}")
                logger.info(f"   Failed: {file_results.get('failed', 0)}")
                logger.info(f"   Duplicates: {file_results.get('duplicates', 0)}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process file {file_path}: {e}")
                total_results["failed_files"] += 1
                total_results["file_results"][str(file_path)] = {"error": str(e)}
        
        total_results["end_time"] = datetime.now()
        total_results["duration"] = (total_results["end_time"] - total_results["start_time"]).total_seconds()
        
        return total_results
    
    def process_single_file(self, file_path: str) -> Dict[str, any]:
        """
        Process a single JSONL file synchronously.
        
        Args:
            file_path: Path to the JSONL file
            
        Returns:
            Processing results
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        logger.info(f"Processing single file: {file_path}")
        
        # Run async processing in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            results = loop.run_until_complete(self.processor.process_file(file_path))
            return results
        finally:
            loop.close()
    
    def get_processing_stats(self) -> Dict[str, any]:
        """
        Get current processing statistics from the database.
        
        Returns:
            Statistics dictionary
        """
        try:
            # Query cases from Case Law Project
            result = self.supabase.client.table('cases').select(
                'source_type, jurisdiction, created_at'
            ).eq('source_type', 'caselaw_access_project').execute()
            
            cases = result.data
            
            # Aggregate statistics
            stats = {
                "total_cases": len(cases),
                "jurisdictions": {},
                "processing_dates": {}
            }
            
            for case in cases:
                jurisdiction = case.get('jurisdiction', 'unknown')
                created_date = case.get('created_at', '')[:10]  # YYYY-MM-DD
                
                # Count by jurisdiction
                stats["jurisdictions"][jurisdiction] = stats["jurisdictions"].get(jurisdiction, 0) + 1
                
                # Count by processing date
                stats["processing_dates"][created_date] = stats["processing_dates"].get(created_date, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {"error": str(e)}


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Case Law Project Integration")
    parser.add_argument(
        "--data-dir", 
        default="data/caselaw_access_project",
        help="Directory containing Case Law Project JSONL files"
    )
    parser.add_argument(
        "--file", 
        help="Process a single specific file"
    )
    parser.add_argument(
        "--max-files", 
        type=int,
        help="Maximum number of files to process"
    )
    parser.add_argument(
        "--stats", 
        action="store_true",
        help="Show current processing statistics"
    )
    
    args = parser.parse_args()
    
    # Initialize integrator
    integrator = CaselawProjectIntegrator(args.data_dir)
    
    if args.stats:
        # Show statistics
        logger.info("Fetching processing statistics...")
        stats = integrator.get_processing_stats()
        
        print("\n" + "="*60)
        print("CASE LAW PROJECT PROCESSING STATISTICS")
        print("="*60)
        
        if "error" in stats:
            print(f"Error: {stats['error']}")
        else:
            print(f"Total Cases Processed: {stats['total_cases']}")
            print(f"\nBy Jurisdiction:")
            for jurisdiction, count in sorted(stats['jurisdictions'].items()):
                print(f"  {jurisdiction}: {count}")
            
            print(f"\nRecent Processing Activity:")
            recent_dates = sorted(stats['processing_dates'].items(), reverse=True)[:10]
            for date, count in recent_dates:
                print(f"  {date}: {count} cases")
        
        return
    
    if args.file:
        # Process single file
        try:
            results = integrator.process_single_file(args.file)
            
            print(f"\n" + "="*60)
            print("SINGLE FILE PROCESSING COMPLETE")
            print("="*60)
            print(f"File: {args.file}")
            print(f"Success: {results.get('success', 0)}")
            print(f"Failed: {results.get('failed', 0)}")
            print(f"Duplicates: {results.get('duplicates', 0)}")
            
        except Exception as e:
            logger.error(f"Failed to process file: {e}")
            sys.exit(1)
    
    else:
        # Process all files
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                integrator.process_all_files(max_files=args.max_files)
            )
            
            print(f"\n" + "="*60)
            print("CASE LAW PROJECT INTEGRATION COMPLETE")
            print("="*60)
            print(f"Total Files: {results['total_files']}")
            print(f"Processed Files: {results['processed_files']}")
            print(f"Failed Files: {results['failed_files']}")
            print(f"Total Documents: {results['total_documents']}")
            print(f"Successful Documents: {results['successful_documents']}")
            print(f"Failed Documents: {results['failed_documents']}")
            print(f"Duplicate Documents: {results['duplicate_documents']}")
            print(f"Duration: {results['duration']:.1f} seconds")
            
            if results['successful_documents'] > 0:
                success_rate = (results['successful_documents'] / results['total_documents']) * 100
                print(f"Success Rate: {success_rate:.2f}%")
            
        except Exception as e:
            logger.error(f"Integration failed: {e}")
            sys.exit(1)
        finally:
            loop.close()


if __name__ == "__main__":
    main()
