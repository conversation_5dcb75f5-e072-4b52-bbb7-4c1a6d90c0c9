#!/usr/bin/env python3
import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

def test_court_codes():
    api_key = os.getenv('COURTLISTENER_API_KEY')
    headers = {
        'Authorization': f'Token {api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    # Test various court code formats
    test_courts = [
        # Known working
        'tex', 'texcrimapp', 'txnd', 'txsd', 'txed', 'txwd', 'ca5',
        
        # Test appellate court formats
        'texapp1st', 'texapp2nd', 'texapp3rd', 'texapp4th', 'texapp5th',
        'texapp6th', 'texapp7th', 'texapp8th', 'texapp9th', 'texapp10th',
        'texapp11th', 'texapp12th', 'texapp13th', 'texapp14th',
        
        # Alternative formats
        'tex-app-1st', 'tex-app-2nd', 'tex-app-3rd',
        'texapp-1st', 'texapp-2nd', 'texapp-3rd'
    ]
    
    results = {}
    
    for court_code in test_courts:
        print(f'Testing {court_code}...')
        
        try:
            url = 'https://www.courtlistener.com/api/rest/v4/search/'
            params = {
                'q': f'court_id:{court_code}',
                'type': 'o',
                'page_size': 1,
                'format': 'json'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                count = data.get('count', 0)
                results[court_code] = {
                    'status': 'success',
                    'total_count': count,
                    'has_data': count > 0
                }
                print(f'  ✅ {court_code}: {count:,} opinions')
            else:
                results[court_code] = {
                    'status': f'error_{response.status_code}',
                    'has_data': False
                }
                print(f'  ❌ {court_code}: HTTP {response.status_code}')
                
        except Exception as e:
            results[court_code] = {
                'status': 'exception',
                'error': str(e),
                'has_data': False
            }
            print(f'  ❌ {court_code}: {e}')
    
    return results

if __name__ == '__main__':
    print('Testing CourtListener court codes...')
    results = test_court_codes()
    
    # Save results
    with open('court_code_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f'\\n=== SUMMARY ===')
    working_courts = [code for code, result in results.items() if result.get('has_data', False)]
    zero_courts = [code for code, result in results.items() if result.get('status') == 'success' and not result.get('has_data', False)]
    error_courts = [code for code, result in results.items() if 'error' in result.get('status', '')]
    
    print(f'Working courts ({len(working_courts)}): {working_courts}')
    print(f'Zero opinion courts ({len(zero_courts)}): {zero_courts}')
    print(f'Error courts ({len(error_courts)}): {error_courts}')
    
    # Calculate total available
    total_opinions = sum(result.get('total_count', 0) for result in results.values())
    print(f'\\nTotal opinions available: {total_opinions:,}')
    
    print(f'\\nResults saved to court_code_test_results.json')
