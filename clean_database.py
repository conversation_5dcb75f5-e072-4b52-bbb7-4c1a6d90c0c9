#!/usr/bin/env python3
"""
Database Cleanup Script
Cleans test data and prepares for production run
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

def main():
    """Clean database and prepare for production"""
    
    print("🧹 DATABASE CLEANUP SCRIPT")
    print("=" * 50)
    
    # Load environment
    load_dotenv()
    
    # Setup Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set")
        return 1
    
    supabase = create_client(supabase_url, supabase_key)
    
    # Get current state
    print("📊 Current Database State:")
    result = supabase.table('cases').select('source, batch_id', count='exact').execute()
    total_cases = result.count or 0
    
    # Count test cases
    test_result = supabase.table('cases').select('id', count='exact').like('batch_id', 'chunk_%').execute()
    test_cases = test_result.count or 0
    
    # Count courtlistener cases
    cl_result = supabase.table('cases').select('id', count='exact').eq('source', 'courtlistener').execute()
    cl_cases = cl_result.count or 0
    
    print(f"   Total cases: {total_cases}")
    print(f"   Test cases (chunk_*): {test_cases}")
    print(f"   CourtListener cases: {cl_cases}")
    print(f"   Other cases: {total_cases - test_cases - cl_cases}")
    
    if test_cases == 0 and cl_cases == 0:
        print("✅ Database is already clean!")
        return 0
    
    # Confirm cleanup
    print(f"\n⚠️  This will delete {test_cases + cl_cases} cases:")
    print(f"   • {test_cases} test cases (batch_id LIKE 'chunk_%')")
    print(f"   • {cl_cases} CourtListener cases (source = 'courtlistener')")
    
    response = input("\nProceed with cleanup? (yes/no): ").lower().strip()
    
    if response != 'yes':
        print("❌ Cleanup cancelled")
        return 1
    
    # Perform cleanup
    print("\n🧹 Starting cleanup...")
    
    deleted_count = 0
    
    # Delete test cases
    if test_cases > 0:
        print(f"   Deleting {test_cases} test cases...")
        result = supabase.table('cases').delete().like('batch_id', 'chunk_%').execute()
        deleted_test = len(result.data) if result.data else 0
        deleted_count += deleted_test
        print(f"   ✅ Deleted {deleted_test} test cases")
    
    # Delete CourtListener cases
    if cl_cases > 0:
        print(f"   Deleting {cl_cases} CourtListener cases...")
        result = supabase.table('cases').delete().eq('source', 'courtlistener').execute()
        deleted_cl = len(result.data) if result.data else 0
        deleted_count += deleted_cl
        print(f"   ✅ Deleted {deleted_cl} CourtListener cases")
    
    # Verify cleanup
    print("\n🔍 Verifying cleanup...")
    final_result = supabase.table('cases').select('id', count='exact').execute()
    final_count = final_result.count or 0
    
    print(f"   Cases before cleanup: {total_cases}")
    print(f"   Cases deleted: {deleted_count}")
    print(f"   Cases remaining: {final_count}")
    
    if final_count == total_cases - deleted_count:
        print("✅ Cleanup completed successfully!")
        
        # Clean checkpoints
        import glob
        checkpoint_files = glob.glob("checkpoints/*.json")
        if checkpoint_files:
            print(f"\n🗂️  Found {len(checkpoint_files)} checkpoint files")
            clean_checkpoints = input("Delete checkpoint files? (yes/no): ").lower().strip()
            
            if clean_checkpoints == 'yes':
                for checkpoint_file in checkpoint_files:
                    os.remove(checkpoint_file)
                print(f"✅ Deleted {len(checkpoint_files)} checkpoint files")
        
        print("\n🎯 Database is ready for production processing!")
        print("\nNext steps:")
        print("1. Run: python enhanced_texas_processor.py")
        print("2. Monitor progress in the terminal UI")
        print("3. Check logs: enhanced_processing_*.log")
        
        return 0
    else:
        print("❌ Cleanup verification failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
