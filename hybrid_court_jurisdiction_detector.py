#!/usr/bin/env python3
"""
Hybrid Court Jurisdiction Detector
Combines regex (fast) + Gemini 2.0 Flash (accurate) for optimal performance
"""

import os
import re
from typing import Dict, Optional
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

class HybridCourtJurisdictionDetector:
    """Hybrid detector using regex first, then Gemini fallback."""
    
    def __init__(self):
        """Initialize both regex and Gemini components."""
        
        # Initialize Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if api_key:
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.0-flash')
            self.gemini_available = True
            print("✅ Gemini 2.0 Flash initialized for fallback detection")
        else:
            self.gemini_available = False
            print("⚠️ GEMINI_API_KEY not found - using regex-only mode")
        
        # Regex patterns for fast detection
        self.regex_patterns = self._build_regex_patterns()
        
        # Valid jurisdictions
        self.valid_jurisdictions = {
            'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 
            'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 
            'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana', 
            'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 
            'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 
            'new_hampshire', 'new_jersey', 'new_mexico', 'new_york', 
            'north_carolina', 'north_dakota', 'ohio', 'oklahoma', 'oregon', 
            'pennsylvania', 'rhode_island', 'south_carolina', 'south_dakota', 
            'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 
            'west_virginia', 'wisconsin', 'wyoming', 'federal', 'district_of_columbia'
        }
        
        # Statistics tracking
        self.stats = {
            'total_cases': 0,
            'regex_classified': 0,
            'gemini_classified': 0,
            'unclassified': 0
        }
    
    def _build_regex_patterns(self):
        """Build optimized regex patterns for court detection."""
        
        patterns = {
            'texas': [
                r'supreme court of texas',
                r'texas.*supreme court',
                r'court of.*appeals.*texas',
                r'texas.*court of.*appeals',
                r'court of civil appeals.*texas',
                r'texas.*court of civil appeals'
            ],
            'california': [
                r'supreme court of california',
                r'california supreme court',
                r'court of.*appeal.*california',
                r'california.*court of.*appeal'
            ],
            'new_york': [
                r'court of appeals.*new york',
                r'new york.*court of appeals',
                r'appellate division.*new york'
            ],
            'florida': [
                r'supreme court of florida',
                r'florida supreme court',
                r'district court of appeal.*florida'
            ],
            'illinois': [
                r'supreme court.*illinois',
                r'illinois.*supreme court',
                r'appellate court.*illinois'
            ],
            'missouri': [
                r'supreme court.*missouri',
                r'missouri.*supreme court',
                r'court of appeals.*missouri',
                r'missouri.*court of appeals'
            ],
            'federal': [
                r'supreme court of the united states',
                r'united states supreme court',
                r'u\.s\. supreme court',
                r'united states court of appeals',
                r'u\.s\. court of appeals',
                r'united states district court',
                r'u\.s\. district court'
            ]
        }
        
        # Compile patterns for efficiency
        compiled = {}
        for jurisdiction, pattern_list in patterns.items():
            combined = '|'.join(f'({pattern})' for pattern in pattern_list)
            compiled[jurisdiction] = re.compile(combined, re.IGNORECASE)
        
        return compiled
    
    def _detect_with_regex(self, case_text: str) -> Optional[str]:
        """Fast regex-based detection."""
        
        # Focus on first 1500 characters where court info appears
        text_start = case_text[:1500].lower()
        
        # Check each jurisdiction pattern
        for jurisdiction, pattern in self.regex_patterns.items():
            if pattern.search(text_start):
                return jurisdiction
        
        return None
    
    def _detect_with_gemini(self, case_text: str) -> Optional[str]:
        """Gemini-based detection for cases regex couldn't handle."""
        
        if not self.gemini_available:
            return None
        
        try:
            # Use first 1000 characters
            text_excerpt = case_text[:1000]
            
            prompt = f"""Look at this legal case text and identify the court that decided it.

Case text:
{text_excerpt}

What court decided this case? Look for phrases like "Supreme Court of [State]", "[State] Court of Appeals", "U.S. District Court", etc.

Respond with just the state name (like "texas", "california", "new_york") or "federal" for federal courts. If you can't determine it, respond with "unknown".

Court jurisdiction:"""
            
            response = self.gemini_model.generate_content(prompt)
            
            if not response or not response.text:
                return None
            
            # Clean response
            raw_response = response.text.strip().lower()
            
            # Handle negative responses
            if raw_response in ['unknown', 'unclear', 'cannot determine', 'not specified']:
                return None
            
            # Normalize jurisdiction names
            jurisdiction_mapping = {
                'new hampshire': 'new_hampshire',
                'new jersey': 'new_jersey', 
                'new mexico': 'new_mexico',
                'new york': 'new_york',
                'north carolina': 'north_carolina',
                'north dakota': 'north_dakota',
                'rhode island': 'rhode_island',
                'south carolina': 'south_carolina',
                'south dakota': 'south_dakota',
                'west virginia': 'west_virginia',
                'district of columbia': 'district_of_columbia'
            }
            
            normalized = jurisdiction_mapping.get(raw_response, raw_response)
            
            # Validate jurisdiction
            if normalized in self.valid_jurisdictions:
                return normalized
            
            # Try to find jurisdiction within response
            for jurisdiction in self.valid_jurisdictions:
                if jurisdiction in raw_response or jurisdiction.replace('_', ' ') in raw_response:
                    return jurisdiction
            
            return None
            
        except Exception as e:
            # Silently handle API errors
            return None
    
    def detect_jurisdiction(self, case_data: Dict) -> str:
        """Hybrid detection: regex first, then Gemini fallback."""
        
        self.stats['total_cases'] += 1
        
        case_text = case_data.get('text', '')
        if not case_text or len(case_text.strip()) < 50:
            self.stats['unclassified'] += 1
            return 'unclassified'
        
        # Step 1: Try regex detection (fast)
        jurisdiction = self._detect_with_regex(case_text)
        if jurisdiction:
            self.stats['regex_classified'] += 1
            return jurisdiction
        
        # Step 2: Try Gemini detection (accurate)
        jurisdiction = self._detect_with_gemini(case_text)
        if jurisdiction:
            self.stats['gemini_classified'] += 1
            return jurisdiction
        
        # Step 3: Unclassified
        self.stats['unclassified'] += 1
        return 'unclassified'
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        
        total = self.stats['total_cases']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'regex_rate': (self.stats['regex_classified'] / total) * 100,
            'gemini_rate': (self.stats['gemini_classified'] / total) * 100,
            'total_classified_rate': ((self.stats['regex_classified'] + self.stats['gemini_classified']) / total) * 100,
            'unclassified_rate': (self.stats['unclassified'] / total) * 100
        }

def test_hybrid_detector():
    """Test the hybrid detector on sample cases."""
    
    print("🔄 TESTING HYBRID COURT JURISDICTION DETECTOR")
    print("=" * 60)
    
    detector = HybridCourtJurisdictionDetector()
    
    # Test cases
    test_cases = [
        {
            'text': 'Supreme Court of Texas. This case involves property rights...',
            'expected': 'texas'
        },
        {
            'text': 'Court of Civil Appeals of Texas. Galveston. The plaintiff seeks damages...',
            'expected': 'texas'
        },
        {
            'text': 'United States Supreme Court. We granted certiorari to resolve...',
            'expected': 'federal'
        },
        {
            'text': 'Some obscure court case with unclear jurisdiction information...',
            'expected': 'unclassified'  # This should test Gemini fallback
        }
    ]
    
    print("🧪 Testing on sample cases...")
    
    correct = 0
    for i, test_case in enumerate(test_cases, 1):
        case_data = {'text': test_case['text']}
        detected = detector.detect_jurisdiction(case_data)
        expected = test_case['expected']
        
        is_correct = detected == expected or (expected == 'unclassified' and detected != 'unclassified')
        if is_correct:
            correct += 1
        
        print(f"  Test {i}: {'✅' if is_correct else '❌'} {detected} (expected: {expected})")
    
    accuracy = (correct / len(test_cases)) * 100
    stats = detector.get_performance_stats()
    
    print(f"\n📊 HYBRID PERFORMANCE:")
    print(f"  Accuracy: {accuracy:.1f}%")
    print(f"  Regex classified: {stats['regex_rate']:.1f}%")
    print(f"  Gemini classified: {stats['gemini_rate']:.1f}%")
    print(f"  Total classified: {stats['total_classified_rate']:.1f}%")
    
    print(f"\n🎯 ASSESSMENT:")
    if stats['total_classified_rate'] >= 80:
        print("🎉 EXCELLENT: Hybrid approach achieving target >80% classification!")
    elif stats['total_classified_rate'] >= 60:
        print("✅ GOOD: Significant improvement with hybrid approach")
    else:
        print("⚠️ NEEDS WORK: Still not achieving target performance")
    
    return detector

if __name__ == "__main__":
    detector = test_hybrid_detector()
    print("\n🚀 Hybrid detector ready for CAP file processing!")
