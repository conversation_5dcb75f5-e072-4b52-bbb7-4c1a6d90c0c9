#!/usr/bin/env python3
"""
Test Real Data Judge Disambiguation
Test judge disambiguation with actual CAP and CourtListener data
"""

import asyncio
import logging
import os
import json
import gzip
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealDataJudgeDisambiguationTest:
    """Test judge disambiguation with real CAP and CourtListener data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # Test batch ID
        self.test_batch_id = f"real_data_judge_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # CAP data path
        self.cap_data_path = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def load_real_cap_cases(self, limit: int = 5) -> list:
        """Load real CAP cases from local data files"""
        
        print(f"📂 LOADING REAL CAP CASES")
        print("=" * 60)
        
        real_cases = []
        
        try:
            # Look for CAP data files (any cap_*.jsonl.gz files)
            cap_files = []
            if os.path.exists(self.cap_data_path):
                for file in os.listdir(self.cap_data_path):
                    if file.startswith('cap_') and file.endswith('.jsonl.gz'):
                        cap_files.append(os.path.join(self.cap_data_path, file))
            
            if not cap_files:
                print(f"   ❌ No CAP files found in {self.cap_data_path}")
                return []

            print(f"   📁 Found {len(cap_files)} CAP files")

            # Load cases from first file
            for file_path in cap_files[:1]:  # Just first file
                print(f"   📄 Loading from: {os.path.basename(file_path)}")
                
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if len(real_cases) >= limit:
                            break
                        
                        try:
                            case_data = json.loads(line.strip())
                            
                            # Check if case has text and looks like it might have judge info
                            text = case_data.get('text', '')
                            if text and len(text) > 500:
                                # Look for potential judge indicators
                                text_lower = text.lower()
                                judge_indicators = ['judge', 'justice', 'court', 'opinion', 'delivered']
                                
                                if any(indicator in text_lower for indicator in judge_indicators):
                                    # Add source and processing info
                                    case_data['source'] = 'caselaw_access_project'
                                    case_data['id'] = f"cap_real_{case_data.get('id', line_num)}"
                                    real_cases.append(case_data)
                                    
                                    print(f"      ✅ Case {len(real_cases)}: {case_data.get('name', 'Unknown')[:50]}...")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"      ⚠️ Error processing line {line_num}: {e}")
                            continue
                
                if real_cases:
                    break  # Found cases in first file
            
            print(f"\n📊 REAL CAP CASES LOADED:")
            print(f"   Total cases: {len(real_cases)}")
            
            for i, case in enumerate(real_cases, 1):
                text_len = len(case.get('text', ''))
                print(f"   {i}. {case.get('name', 'Unknown')[:40]}... ({text_len:,} chars)")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error loading real CAP cases: {e}")
            return []
    
    def load_existing_courtlistener_cases(self, limit: int = 3) -> list:
        """Load existing CourtListener cases from Supabase"""
        
        print(f"\n📂 LOADING EXISTING COURTLISTENER CASES")
        print("=" * 60)
        
        try:
            # Get existing CourtListener cases with text
            response = self.supabase.table('cases').select('*').eq('source', 'court_listener').limit(limit).execute()
            
            existing_cases = response.data if response.data else []
            
            print(f"   📊 Found {len(existing_cases)} existing CourtListener cases")
            
            # Convert to processing format
            real_cases = []
            for case in existing_cases:
                # Create processing format
                processing_case = {
                    'id': f"cl_existing_{case['id']}",
                    'source': 'courtlistener',
                    'case_name': case.get('case_name', ''),
                    'court': case.get('court', ''),
                    'court_name': case.get('court_name', ''),
                    'date_filed': case.get('date_filed', ''),
                    'jurisdiction': case.get('jurisdiction', 'US'),
                    'text': case.get('text_content', ''),  # Use stored text
                    'precedential_status': case.get('precedential_status', 'Unknown')
                }
                
                # Only include if has text
                if processing_case['text'] and len(processing_case['text']) > 100:
                    real_cases.append(processing_case)
                    print(f"      ✅ Case {len(real_cases)}: {processing_case['case_name'][:50]}...")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error loading existing CourtListener cases: {e}")
            return []
    
    async def test_real_data_judge_disambiguation(self) -> bool:
        """Test judge disambiguation with real data from both sources"""
        
        print(f"\n🔄 REAL DATA JUDGE DISAMBIGUATION TEST")
        print("=" * 60)
        
        try:
            # Load real data from both sources
            cap_cases = self.load_real_cap_cases(3)
            cl_cases = self.load_existing_courtlistener_cases(2)
            
            all_real_cases = cap_cases + cl_cases
            
            if not all_real_cases:
                print(f"❌ No real data found to test")
                return False
            
            print(f"\n📊 REAL DATA SUMMARY:")
            print(f"   CAP cases: {len(cap_cases)}")
            print(f"   CourtListener cases: {len(cl_cases)}")
            print(f"   Total real cases: {len(all_real_cases)}")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing real data through judge disambiguation pipeline...")
            
            # Process CAP cases
            if cap_cases:
                print(f"\n📄 Processing {len(cap_cases)} real CAP cases...")
                cap_result = await processor.process_coherent_batch(
                    raw_cases=cap_cases,
                    source_type='caselaw_access_project',
                    batch_id=f"{self.test_batch_id}_cap"
                )
                print(f"   CAP Result: Success={cap_result['success']}, Processed={cap_result['processed']}")
            
            # Process CourtListener cases
            if cl_cases:
                print(f"\n📄 Processing {len(cl_cases)} real CourtListener cases...")
                cl_result = await processor.process_coherent_batch(
                    raw_cases=cl_cases,
                    source_type='courtlistener',
                    batch_id=f"{self.test_batch_id}_cl"
                )
                print(f"   CourtListener Result: Success={cl_result['success']}, Processed={cl_result['processed']}")
            
            # Verify judge disambiguation worked on real data
            return await self.verify_real_data_judge_disambiguation()
            
        except Exception as e:
            print(f"❌ Real data judge disambiguation test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_real_data_judge_disambiguation(self) -> bool:
        """Verify judge disambiguation worked on real data"""
        
        print(f"\n🔍 VERIFYING REAL DATA JUDGE DISAMBIGUATION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # 1. Get judges from real CAP data
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case)
                        WHERE c.batch_id STARTS WITH $batch_prefix_cap
                        AND type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_prefix_cap=f"{self.test_batch_id}_cap")
                
                cap_judges = list(result)
                
                # 2. Get judges from real CourtListener data
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case)
                        WHERE c.batch_id STARTS WITH $batch_prefix_cl
                        AND type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_prefix_cl=f"{self.test_batch_id}_cl")
                
                cl_judges = list(result)
                
                print(f"📊 1. JUDGES FROM REAL CAP DATA:")
                print(f"   CAP judges found: {len(cap_judges)}")
                for judge in cap_judges:
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']}, Court: {judge['judge_court']}, Cases: {judge['case_count']})")
                
                print(f"\n📊 2. JUDGES FROM REAL COURTLISTENER DATA:")
                print(f"   CourtListener judges found: {len(cl_judges)}")
                for judge in cl_judges:
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']}, Court: {judge['judge_court']}, Cases: {judge['case_count']})")
                
                # 3. Check for cross-source disambiguation
                all_judges = cap_judges + cl_judges
                judge_names = [j['judge_name'] for j in all_judges]
                unique_names = set(judge_names)
                
                print(f"\n📊 3. CROSS-SOURCE DISAMBIGUATION:")
                print(f"   Total judge entries: {len(all_judges)}")
                print(f"   Unique judge names: {len(unique_names)}")
                print(f"   Disambiguation working: {'✅' if len(all_judges) >= len(unique_names) else '❌'}")
                
                # 4. Show sample judge IDs for disambiguation verification
                if all_judges:
                    print(f"\n📊 4. SAMPLE JUDGE IDS (Disambiguation Check):")
                    for judge in all_judges[:5]:
                        print(f"      {judge['judge_name']} → {judge['judge_id']}")
                
                # Success criteria
                success_criteria = [
                    len(cap_judges) > 0 or len(cl_judges) > 0,  # At least some judges found
                    len(all_judges) > 0,  # Total judges found
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 REAL DATA JUDGE DISAMBIGUATION VERIFICATION:")
                print(f"   CAP judges extracted: {'✅' if len(cap_judges) > 0 else '⚠️'} ({len(cap_judges)})")
                print(f"   CourtListener judges extracted: {'✅' if len(cl_judges) > 0 else '⚠️'} ({len(cl_judges)})")
                print(f"   Total judges found: {'✅' if len(all_judges) > 0 else '❌'} ({len(all_judges)})")
                print(f"   Disambiguation IDs: {'✅' if all_judges else '❌'}")
                
                if success:
                    print(f"\n🎉 REAL DATA JUDGE DISAMBIGUATION: SUCCESS!")
                    print(f"✅ Judge disambiguation working on real data")
                    print(f"✅ Both CAP and CourtListener data processed")
                    print(f"✅ {len(all_judges)} judges with unique identities")
                else:
                    print(f"\n⚠️ REAL DATA JUDGE DISAMBIGUATION: LIMITED SUCCESS!")
                    print(f"⚠️ Some real data may not contain extractable judge information")
                
                return success
                
        except Exception as e:
            print(f"❌ Real data judge disambiguation verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP REAL DATA TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().like('batch_id', f'{self.test_batch_id}%').execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case) WHERE c.batch_id STARTS WITH $batch_prefix DETACH DELETE c', 
                          batch_prefix=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run real data judge disambiguation test"""
    
    print("🧪 REAL DATA JUDGE DISAMBIGUATION TEST")
    print("=" * 80)
    print("🎯 Testing judge disambiguation with actual CAP and CourtListener data")
    
    test = RealDataJudgeDisambiguationTest()
    
    try:
        # Run the test
        success = await test.test_real_data_judge_disambiguation()
        
        if success:
            print(f"\n🎉 REAL DATA JUDGE DISAMBIGUATION: SUCCESS!")
            print(f"✅ Judge disambiguation verified on real data")
            return True
        else:
            print(f"\n⚠️ REAL DATA JUDGE DISAMBIGUATION: LIMITED SUCCESS!")
            print(f"⚠️ Real data may have limited judge information")
            return True  # Still consider it a success for infrastructure
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
