#!/usr/bin/env python3
"""
Fixed Gemini Court Detector
Simplified approach based on debugging insights
"""

import os
import gzip
import json
from pathlib import Path
from collections import defaultdict
import google.generativeai as genai
from dotenv import load_dotenv
import time

load_dotenv()

class FixedGeminiCourtDetector:
    """Simplified, high-performance Gemini court detector."""
    
    def __init__(self):
        """Initialize with simplified approach."""
        
        # Configure Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        
        # Valid jurisdictions for validation
        self.valid_jurisdictions = {
            'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 
            'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 
            'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana', 
            'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 
            'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 
            'new hampshire', 'new jersey', 'new mexico', 'new york', 
            'north carolina', 'north dakota', 'ohio', 'oklahoma', 'oregon', 
            'pennsylvania', 'rhode island', 'south carolina', 'south dakota', 
            'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 
            'west virginia', 'wisconsin', 'wyoming', 'federal', 'district of columbia'
        }
        
        # Normalize jurisdiction names
        self.jurisdiction_mapping = {
            'new hampshire': 'new_hampshire',
            'new jersey': 'new_jersey', 
            'new mexico': 'new_mexico',
            'new york': 'new_york',
            'north carolina': 'north_carolina',
            'north dakota': 'north_dakota',
            'rhode island': 'rhode_island',
            'south carolina': 'south_carolina',
            'south dakota': 'south_dakota',
            'west virginia': 'west_virginia',
            'district of columbia': 'district_of_columbia'
        }
    
    def detect_court_jurisdiction(self, case_data):
        """Detect court jurisdiction using simplified Gemini approach."""
        
        case_text = case_data.get('text', '')
        if not case_text or len(case_text.strip()) < 50:
            return 'unclassified'
        
        # Use first 1000 characters where court info typically appears
        text_excerpt = case_text[:1000]
        
        # Simple, direct prompt
        prompt = f"""Look at this legal case text and identify the court that decided it.

Case text:
{text_excerpt}

What court decided this case? Look for phrases like "Supreme Court of [State]", "[State] Court of Appeals", "U.S. District Court", etc.

Respond with just the state name (like "texas", "california", "new york") or "federal" for federal courts. If you can't determine it, respond with "unknown".

Court jurisdiction:"""
        
        try:
            response = self.model.generate_content(prompt)
            
            if not response or not response.text:
                return 'unclassified'
            
            # Clean and normalize response
            raw_response = response.text.strip().lower()
            
            # Handle common variations
            if raw_response in ['unknown', 'unclear', 'cannot determine', 'not specified']:
                return 'unclassified'
            
            # Normalize jurisdiction name
            normalized = self.jurisdiction_mapping.get(raw_response, raw_response)
            
            # Validate jurisdiction
            if normalized in self.valid_jurisdictions:
                return normalized
            
            # Try to find jurisdiction within response
            for jurisdiction in self.valid_jurisdictions:
                if jurisdiction in raw_response or jurisdiction.replace('_', ' ') in raw_response:
                    return jurisdiction
            
            return 'unclassified'
            
        except Exception as e:
            print(f"  ⚠️ Gemini error: {e}")
            return 'unclassified'

def test_fixed_gemini_on_cap_sample():
    """Test the fixed Gemini detector on a larger CAP sample."""
    
    print("🧠 TESTING FIXED GEMINI ON CAP SAMPLE")
    print("=" * 50)
    
    try:
        detector = FixedGeminiCourtDetector()
    except Exception as e:
        print(f"❌ Error initializing detector: {e}")
        return
    
    # Load sample cases
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    if not cap_files:
        print("❌ No CAP files found")
        return
    
    print(f"📁 Testing on sample from: {cap_files[0].name}")
    
    # Load every 200th case for a good sample
    sample_cases = []
    total_cases = 0
    
    with gzip.open(cap_files[0], 'rt', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if line.strip():
                total_cases += 1
                try:
                    case_data = json.loads(line)
                    # Take every 200th case for representative sample
                    if line_num % 200 == 0 and len(sample_cases) < 50:
                        sample_cases.append(case_data)
                except:
                    continue
    
    print(f"📊 File has {total_cases:,} total cases")
    print(f"🎯 Testing on {len(sample_cases)} sample cases")
    print()
    
    # Process sample
    jurisdiction_counts = defaultdict(int)
    start_time = time.time()
    
    for i, case_data in enumerate(sample_cases, 1):
        print(f"  Processing {i}/{len(sample_cases)}...", end=" ")
        
        jurisdiction = detector.detect_court_jurisdiction(case_data)
        jurisdiction_counts[jurisdiction] += 1
        
        print(f"✅ {jurisdiction}")
        
        # Small delay to respect API limits
        time.sleep(0.1)
    
    processing_time = time.time() - start_time
    
    # Calculate results
    total_sample = len(sample_cases)
    classified = total_sample - jurisdiction_counts.get('unclassified', 0)
    classification_rate = (classified / total_sample) * 100 if total_sample > 0 else 0
    
    print(f"\n📊 FIXED GEMINI RESULTS")
    print("=" * 40)
    
    print(f"Sample size: {total_sample}")
    print(f"Classified: {classified}")
    print(f"Classification rate: {classification_rate:.1f}%")
    print(f"Processing time: {processing_time:.1f} seconds")
    
    print(f"\n🗺️ JURISDICTION DISTRIBUTION:")
    sorted_jurisdictions = sorted(jurisdiction_counts.items(), key=lambda x: x[1], reverse=True)
    for jurisdiction, count in sorted_jurisdictions:
        percentage = (count / total_sample) * 100 if total_sample > 0 else 0
        print(f"  {jurisdiction}: {count} ({percentage:.1f}%)")
    
    # Extrapolate to full file
    if classification_rate > 0:
        estimated_classified = int((classification_rate / 100) * total_cases)
        estimated_texas = int((jurisdiction_counts.get('texas', 0) / total_sample) * total_cases)
        
        print(f"\n📈 EXTRAPOLATION TO FULL FILE:")
        print(f"  Estimated classified: {estimated_classified:,} ({classification_rate:.1f}%)")
        print(f"  Estimated Texas cases: {estimated_texas:,}")
        print(f"  Estimated unclassified: {total_cases - estimated_classified:,}")
    
    print(f"\n🎯 ASSESSMENT:")
    if classification_rate >= 80:
        print("🎉 EXCELLENT: >80% classification rate achieved!")
        print("✅ Fixed Gemini approach is working as expected")
    elif classification_rate >= 60:
        print("✅ GOOD: >60% classification rate - significant improvement")
    elif classification_rate >= 40:
        print("⚠️ MODERATE: >40% classification rate - better than before")
    else:
        print("❌ POOR: <40% classification rate - still needs work")
    
    print(f"\n💡 CONCLUSION:")
    print(f"Fixed Gemini achieves {classification_rate:.1f}% classification rate")
    print(f"Ready to implement hybrid approach with regex fallback")
    
    return {
        'classification_rate': classification_rate,
        'estimated_texas_cases': estimated_texas if 'estimated_texas' in locals() else 0,
        'total_cases': total_cases
    }

if __name__ == "__main__":
    results = test_fixed_gemini_on_cap_sample()
