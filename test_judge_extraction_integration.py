#!/usr/bin/env python3
"""
Test Judge Extraction Integration
Tests the complete judge extraction pipeline with real data
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.judge_extraction_service import JudgeExtractionService
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JudgeExtractionIntegrationTest:
    """Test judge extraction integration with real data"""
    
    def __init__(self):
        self.judge_extractor = JudgeExtractionService()
        self.supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_KEY')
        )
        self.test_batch_id = f"judge_integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    async def test_courtlistener_judge_extraction(self):
        """Test judge extraction from CourtListener data"""
        print("\n🔍 TESTING COURTLISTENER JUDGE EXTRACTION")
        print("=" * 60)
        
        # Sample CourtListener case data
        courtlistener_case = {
            'id': 'cl_test_12345',
            'case_name': 'Smith v. Jones',
            'author_str': 'Justice Miranda M. Du',
            'plain_text': 'The Honorable Miranda M. Du, U.S. District Judge, delivered the opinion of the court...',
            'court': 'nvd',
            'date_filed': '2023-05-15',
            'source': 'courtlistener'
        }
        
        # Extract judges
        judges = self.judge_extractor.extract_judges_from_courtlistener(courtlistener_case)
        
        print(f"📊 Extracted {len(judges)} judges:")
        for i, judge in enumerate(judges, 1):
            print(f"   {i}. {judge.name} (confidence: {judge.confidence:.2f})")
            print(f"      Full name: {judge.full_name}")
            print(f"      Source: {judge.source}")
            print(f"      Method: {judge.extraction_method}")
        
        # Test judge metadata creation
        judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, courtlistener_case)
        
        print(f"\n📋 Storage Format:")
        print(f"   Judge Name: {judge_name}")
        print(f"   Judge Metadata: {judge_metadata}")
        
        # Test validation
        is_valid = self.judge_extractor.validate_judge_extraction(judges)
        print(f"   Validation: {'✅ PASS' if is_valid else '❌ FAIL'}")
        
        return len(judges) > 0 and is_valid
    
    async def test_cap_judge_extraction(self):
        """Test judge extraction from CAP data"""
        print("\n🔍 TESTING CAP JUDGE EXTRACTION")
        print("=" * 60)
        
        # Sample CAP case data
        cap_case = {
            'id': 'cap_test_67890',
            'name': 'State v. Johnson',
            'text': 'BRENNAN, J., delivered the opinion of the Court. MARSHALL, J., concurred in the judgment...',
            'court': {'name': 'Supreme Court of Texas'},
            'decision_date': '1985-03-20',
            'source': 'caselaw_access_project'
        }
        
        # Extract judges
        judges = self.judge_extractor.extract_judges_from_cap(cap_case)
        
        print(f"📊 Extracted {len(judges)} judges:")
        for i, judge in enumerate(judges, 1):
            print(f"   {i}. {judge.name} (confidence: {judge.confidence:.2f})")
            print(f"      Full name: {judge.full_name}")
            print(f"      Source: {judge.source}")
            print(f"      Method: {judge.extraction_method}")
        
        # Test judge metadata creation
        judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, cap_case)
        
        print(f"\n📋 Storage Format:")
        print(f"   Judge Name: {judge_name}")
        print(f"   Judge Metadata: {judge_metadata}")
        
        # Test validation
        is_valid = self.judge_extractor.validate_judge_extraction(judges)
        print(f"   Validation: {'✅ PASS' if is_valid else '❌ FAIL'}")
        
        return len(judges) > 0 and is_valid
    
    async def test_database_integration(self):
        """Test judge data storage in database"""
        print("\n🔍 TESTING DATABASE INTEGRATION")
        print("=" * 60)
        
        # Test case with judge data
        test_case = {
            'id': f'judge_test_{self.test_batch_id}',
            'case_name': 'Integration Test Case',
            'judge_name': 'Justice Test',
            'judge_metadata': {
                'judges': [{
                    'name': 'Justice Test',
                    'full_name': 'Justice Test Smith',
                    'confidence': 0.95,
                    'source': 'test',
                    'court': 'Test Court'
                }],
                'primary_judge': 'Justice Test',
                'extraction_stats': {
                    'total_extracted': 1,
                    'avg_confidence': 0.95
                }
            },
            'jurisdiction': 'TX',
            'source': 'test',
            'created_at': datetime.now().isoformat()
        }
        
        try:
            # Insert test case
            response = self.supabase.table('cases').insert(test_case).execute()
            print(f"✅ Successfully inserted test case with judge data")
            
            # Verify insertion
            verify_response = self.supabase.table('cases').select('*').eq('id', test_case['id']).execute()
            
            if verify_response.data:
                case_data = verify_response.data[0]
                print(f"📊 Verified case data:")
                print(f"   ID: {case_data['id']}")
                print(f"   Judge Name: {case_data['judge_name']}")
                print(f"   Judge Metadata: {case_data['judge_metadata']}")
                
                return True
            else:
                print("❌ Failed to verify case insertion")
                return False
                
        except Exception as e:
            print(f"❌ Database integration test failed: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        print("\n🧹 CLEANING UP TEST DATA")
        print("=" * 60)
        
        try:
            # Delete test cases
            response = self.supabase.table('cases').delete().like('id', f'judge_test_{self.test_batch_id}%').execute()
            print("✅ Cleaned up test data from database")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
    
    async def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 JUDGE EXTRACTION INTEGRATION TESTS")
        print("=" * 60)
        print(f"Test Batch ID: {self.test_batch_id}")
        
        results = []
        
        # Test CourtListener extraction
        cl_result = await self.test_courtlistener_judge_extraction()
        results.append(("CourtListener Extraction", cl_result))
        
        # Test CAP extraction
        cap_result = await self.test_cap_judge_extraction()
        results.append(("CAP Extraction", cap_result))
        
        # Test database integration
        db_result = await self.test_database_integration()
        results.append(("Database Integration", db_result))
        
        # Clean up
        await self.cleanup_test_data()
        
        # Summary
        print("\n🎯 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name}: {status}")
            if not result:
                all_passed = False
        
        print(f"\n🎉 OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        
        return all_passed


async def main():
    """Main test function"""
    test = JudgeExtractionIntegrationTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n🎉 Judge extraction integration is working correctly!")
        return 0
    else:
        print("\n❌ Judge extraction integration has issues that need to be addressed.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
