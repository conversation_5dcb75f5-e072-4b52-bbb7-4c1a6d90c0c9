#!/usr/bin/env python3
"""
Dual-Source Processing Coordinator

Coordinates processing between Court Listener API and Case Law Access Project data:
1. Optimal processing order (Court Listener first, then CAP)
2. Coverage gap analysis and filling (temporal separation prevents duplicates)
3. Quality assurance across both sources
4. Comprehensive reporting and statistics

Note: Cross-source deduplication removed - 1994 cutoff prevents CAP/CourtListener overlap
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

from enhanced_court_listener_processor import EnhancedCourtListenerProcessor, ProcessingStats
from enhanced_cap_processor import EnhancedCAPProcessor, CAPProcessingStats
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DualSourceStats:
    """Comprehensive statistics for dual-source processing."""
    jurisdiction: str
    start_time: datetime
    end_time: Optional[datetime] = None
    
    # Court Listener stats
    cl_cases_fetched: int = 0
    cl_cases_processed: int = 0
    cl_duplicates: int = 0
    cl_errors: int = 0
    
    # CAP stats
    cap_files_processed: int = 0
    cap_cases_total: int = 0
    cap_cases_processed: int = 0
    cap_duplicates: int = 0
    cap_errors: int = 0
    
    # Cross-source analysis
    cross_source_duplicates: int = 0
    coverage_gaps_filled: int = 0
    unique_cases_total: int = 0
    
    # Practice area breakdown
    practice_area_coverage: Dict[str, Dict[str, int]] = None
    
    def __post_init__(self):
        if self.practice_area_coverage is None:
            self.practice_area_coverage = {}


class DualSourceCoordinator:
    """Coordinates processing between Court Listener and CAP data sources."""
    
    def __init__(self, cap_data_dir: str = "data/caselaw_access_project"):
        """Initialize dual-source coordinator."""
        
        # Initialize processors
        self.court_listener_processor = EnhancedCourtListenerProcessor()
        self.cap_processor = EnhancedCAPProcessor(data_dir=cap_data_dir)
        
        # Core components
        self.supabase = SupabaseConnector()
        self.checkpoint_manager = CheckpointManager()
        
        # Configuration
        self.priority_states = ['tx', 'ny', 'fl']
        self.priority_practice_areas = [
            'personal injury',
            'criminal defense', 
            'family law',
            'estate planning',
            'immigration law',
            'real estate',
            'bankruptcy'
        ]
        
        logger.info("✅ Dual-Source Coordinator initialized")
        logger.info(f"   Priority states: {self.priority_states}")
        logger.info(f"   Practice areas: {len(self.priority_practice_areas)}")
    
    async def analyze_existing_coverage(self, jurisdiction: str) -> Dict[str, Any]:
        """Analyze existing case coverage in the database."""
        
        logger.info(f"📊 Analyzing existing coverage for {jurisdiction}")
        
        try:
            # Query existing cases by source
            cl_query = self.supabase.client.table('cases').select(
                'id, primary_practice_area, source'
            ).eq('jurisdiction', jurisdiction).eq('source', 'court_listener')

            cap_query = self.supabase.client.table('cases').select(
                'id, primary_practice_area, source'
            ).eq('jurisdiction', jurisdiction).eq('source', 'caselaw_access_project')
            
            cl_result = cl_query.execute()
            cap_result = cap_query.execute()
            
            cl_cases = cl_result.data if cl_result.data else []
            cap_cases = cap_result.data if cap_result.data else []
            
            # Analyze by practice area
            coverage = {
                'court_listener': {'total': len(cl_cases), 'by_practice_area': {}},
                'caselaw_access_project': {'total': len(cap_cases), 'by_practice_area': {}},
                'total_unique': len(cl_cases) + len(cap_cases),
                'coverage_gaps': []
            }
            
            # Count by practice area
            for case in cl_cases:
                area = case.get('primary_practice_area', 'general')
                coverage['court_listener']['by_practice_area'][area] = \
                    coverage['court_listener']['by_practice_area'].get(area, 0) + 1

            for case in cap_cases:
                area = case.get('primary_practice_area', 'general')
                coverage['caselaw_access_project']['by_practice_area'][area] = \
                    coverage['caselaw_access_project']['by_practice_area'].get(area, 0) + 1
            
            # Identify coverage gaps
            for practice_area in self.priority_practice_areas:
                cl_count = coverage['court_listener']['by_practice_area'].get(practice_area, 0)
                cap_count = coverage['caselaw_access_project']['by_practice_area'].get(practice_area, 0)
                
                if cl_count == 0 and cap_count == 0:
                    coverage['coverage_gaps'].append(practice_area)
            
            logger.info(f"   Court Listener cases: {coverage['court_listener']['total']:,}")
            logger.info(f"   CAP cases: {coverage['caselaw_access_project']['total']:,}")
            logger.info(f"   Coverage gaps: {len(coverage['coverage_gaps'])} practice areas")
            
            return coverage
            
        except Exception as e:
            logger.error(f"❌ Error analyzing coverage for {jurisdiction}: {e}")
            return {
                'court_listener': {'total': 0, 'by_practice_area': {}},
                'caselaw_access_project': {'total': 0, 'by_practice_area': {}},
                'total_unique': 0,
                'coverage_gaps': self.priority_practice_areas
            }
    
    async def process_jurisdiction_coordinated(self, jurisdiction: str, 
                                             resume_checkpoint: Optional[str] = None) -> DualSourceStats:
        """Process a jurisdiction with coordinated dual-source approach."""
        
        logger.info(f"🚀 COORDINATED DUAL-SOURCE PROCESSING: {jurisdiction.upper()}")
        logger.info("=" * 60)
        
        stats = DualSourceStats(
            jurisdiction=jurisdiction,
            start_time=datetime.now()
        )
        
        # Create master checkpoint
        checkpoint_id = f"dual_source_{jurisdiction}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Phase 1: Analyze existing coverage
            logger.info("📊 Phase 1: Coverage Analysis")
            coverage = await self.analyze_existing_coverage(jurisdiction)
            
            # Phase 2: Court Listener Processing (Recent/Real-time cases)
            logger.info("⚖️ Phase 2: Court Listener Processing")
            logger.info("   Processing recent and high-priority cases...")
            
            cl_stats = await self.court_listener_processor.process_jurisdiction_comprehensive(
                jurisdiction=jurisdiction,
                resume_checkpoint=resume_checkpoint
            )
            
            # Update stats
            stats.cl_cases_fetched = cl_stats.total_fetched
            stats.cl_cases_processed = cl_stats.total_processed
            stats.cl_duplicates = cl_stats.duplicates_prevented
            stats.cl_errors = cl_stats.errors
            
            # Save checkpoint after Court Listener
            await self.checkpoint_manager.save_checkpoint(
                checkpoint_id=f"{checkpoint_id}_cl_complete",
                data={
                    'jurisdiction': jurisdiction,
                    'process_type': 'dual_source',
                    'phase': 'court_listener_complete',
                    'cl_stats': cl_stats.__dict__,
                    'status': 'running',
                    'start_time': stats.start_time.isoformat(),
                    'total_items': cl_stats.total_fetched,
                    'processed_items': cl_stats.total_processed
                }
            )
            
            logger.info(f"✅ Court Listener phase complete: {stats.cl_cases_processed:,} cases processed")
            
            # Phase 3: CAP Processing (Historical/Comprehensive archive)
            logger.info("📚 Phase 3: Case Law Access Project Processing")
            logger.info("   Processing historical and comprehensive archive...")
            
            cap_stats = await self.cap_processor.process_jurisdiction_comprehensive(jurisdiction)
            
            # Update stats
            stats.cap_files_processed = cap_stats.files_processed
            stats.cap_cases_total = cap_stats.total_cases
            stats.cap_cases_processed = cap_stats.processed_cases
            stats.cap_duplicates = cap_stats.duplicates_prevented
            stats.cap_errors = cap_stats.errors
            
            # Save checkpoint after CAP
            await self.checkpoint_manager.save_checkpoint(
                checkpoint_id=f"{checkpoint_id}_cap_complete",
                data={
                    'jurisdiction': jurisdiction,
                    'process_type': 'dual_source',
                    'phase': 'cap_complete',
                    'cl_stats': cl_stats.__dict__,
                    'cap_stats': cap_stats.__dict__,
                    'status': 'running',
                    'start_time': stats.start_time.isoformat(),
                    'total_items': cap_stats.total_cases,
                    'processed_items': cap_stats.processed_cases
                }
            )
            
            logger.info(f"✅ CAP phase complete: {stats.cap_cases_processed:,} cases processed")
            
            # Phase 4: Coverage summary (cross-source analysis removed - 1994 cutoff prevents overlap)
            logger.info("📊 Phase 4: Coverage Summary")

            # Simple coverage calculation without cross-source deduplication
            stats.cross_source_duplicates = 0  # No duplicates due to temporal separation
            stats.coverage_gaps_filled = 0
            stats.unique_cases_total = cl_stats.total_processed + cap_stats.processed_cases
            stats.practice_area_coverage = self._calculate_practice_area_coverage(cl_stats, cap_stats)
            
            # Phase 5: Final quality assurance
            logger.info("✅ Phase 5: Quality Assurance")
            await self._run_quality_assurance(jurisdiction, stats)
            
        except Exception as e:
            logger.error(f"❌ Error in coordinated processing for {jurisdiction}: {e}")
            stats.cl_errors += 1
            stats.cap_errors += 1
        
        finally:
            stats.end_time = datetime.now()
            duration = stats.end_time - stats.start_time
            
            # Final summary
            logger.info(f"\n🎉 COORDINATED PROCESSING COMPLETE: {jurisdiction.upper()}")
            logger.info("=" * 50)
            logger.info(f"Duration: {duration}")
            logger.info(f"Court Listener: {stats.cl_cases_processed:,} processed, {stats.cl_duplicates:,} duplicates")
            logger.info(f"CAP: {stats.cap_cases_processed:,} processed, {stats.cap_duplicates:,} duplicates")
            logger.info(f"Cross-source duplicates: {stats.cross_source_duplicates:,}")
            logger.info(f"Unique cases total: {stats.unique_cases_total:,}")
            logger.info(f"Coverage gaps filled: {stats.coverage_gaps_filled:,}")
        
        return stats
    
    def _calculate_practice_area_coverage(self, cl_stats: ProcessingStats,
                                         cap_stats: CAPProcessingStats) -> Dict[str, int]:
        """Calculate simple practice area coverage without cross-source deduplication."""

        logger.info("   📊 Calculating practice area coverage...")

        # Simple aggregation - no deduplication needed due to temporal separation
        coverage = {}

        # Add CourtListener practice areas
        if hasattr(cl_stats, 'practice_area_breakdown'):
            for area, count in cl_stats.practice_area_breakdown.items():
                coverage[area] = coverage.get(area, 0) + count

        # Add CAP practice areas
        if hasattr(cap_stats, 'practice_area_breakdown'):
            for area, count in cap_stats.practice_area_breakdown.items():
                coverage[area] = coverage.get(area, 0) + count

        logger.info(f"   ✅ Practice area coverage calculated")
        return coverage
    
    async def _run_quality_assurance(self, jurisdiction: str, stats: DualSourceStats):
        """Run final quality assurance checks."""
        
        logger.info("   🔍 Running quality assurance checks...")
        
        try:
            # Check data quality metrics
            total_cases = stats.unique_cases_total
            total_errors = stats.cl_errors + stats.cap_errors
            error_rate = (total_errors / max(total_cases, 1)) * 100
            
            # Check practice area coverage
            covered_areas = len([area for area, coverage in stats.practice_area_coverage.items() 
                               if coverage['total'] > 0])
            coverage_rate = (covered_areas / len(self.priority_practice_areas)) * 100
            
            logger.info(f"   📊 Quality Metrics:")
            logger.info(f"      Total cases: {total_cases:,}")
            logger.info(f"      Error rate: {error_rate:.2f}%")
            logger.info(f"      Practice area coverage: {coverage_rate:.1f}% ({covered_areas}/{len(self.priority_practice_areas)})")
            
            # Quality assessment
            if error_rate < 5.0 and coverage_rate >= 70.0:
                logger.info("   ✅ Quality assurance: PASSED")
            elif error_rate < 10.0 and coverage_rate >= 50.0:
                logger.info("   ⚠️ Quality assurance: ACCEPTABLE")
            else:
                logger.info("   ❌ Quality assurance: NEEDS IMPROVEMENT")
            
        except Exception as e:
            logger.error(f"   ❌ Error in quality assurance: {e}")
    
    async def process_all_priority_states(self) -> Dict[str, DualSourceStats]:
        """Process all priority states with coordinated dual-source approach."""
        
        logger.info("🌟 COMPREHENSIVE DUAL-SOURCE PROCESSING")
        logger.info("=" * 70)
        logger.info("Processing Strategy:")
        logger.info("1. Court Listener API (Recent/Real-time cases)")
        logger.info("2. Case Law Access Project (Historical/Comprehensive)")
        logger.info("3. Cross-source deduplication and analysis")
        logger.info("4. Quality assurance and validation")
        logger.info("")
        
        results = {}
        overall_start = datetime.now()
        
        for jurisdiction in self.priority_states:
            try:
                logger.info(f"\n{'='*20} {jurisdiction.upper()} {'='*20}")
                stats = await self.process_jurisdiction_coordinated(jurisdiction)
                results[jurisdiction] = stats
                
                # Brief pause between states
                await asyncio.sleep(2.0)
                
            except Exception as e:
                logger.error(f"❌ Failed coordinated processing for {jurisdiction}: {e}")
                results[jurisdiction] = DualSourceStats(
                    jurisdiction=jurisdiction,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    cl_errors=1,
                    cap_errors=1
                )
        
        # Final comprehensive summary
        overall_duration = datetime.now() - overall_start
        
        total_cl_processed = sum(stats.cl_cases_processed for stats in results.values())
        total_cap_processed = sum(stats.cap_cases_processed for stats in results.values())
        total_unique = sum(stats.unique_cases_total for stats in results.values())
        total_errors = sum(stats.cl_errors + stats.cap_errors for stats in results.values())
        
        logger.info("\n" + "="*70)
        logger.info("🎉 COMPREHENSIVE DUAL-SOURCE PROCESSING COMPLETE!")
        logger.info("="*70)
        logger.info(f"Total duration: {overall_duration}")
        logger.info(f"Court Listener cases: {total_cl_processed:,}")
        logger.info(f"CAP cases: {total_cap_processed:,}")
        logger.info(f"Total unique cases: {total_unique:,}")
        logger.info(f"Total errors: {total_errors}")
        logger.info("")
        
        # State-by-state summary
        for jurisdiction, stats in results.items():
            logger.info(f"{jurisdiction.upper()}:")
            logger.info(f"  Court Listener: {stats.cl_cases_processed:,} cases")
            logger.info(f"  CAP: {stats.cap_cases_processed:,} cases")
            logger.info(f"  Unique total: {stats.unique_cases_total:,}")
            logger.info(f"  Practice areas covered: {len(stats.practice_area_coverage)}")
        
        return results


async def main():
    """Main function for dual-source coordinated processing."""
    
    coordinator = DualSourceCoordinator()
    results = await coordinator.process_all_priority_states()
    
    # Return success if at least one state processed successfully
    success = any(stats.unique_cases_total > 0 for stats in results.values())
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
