#!/usr/bin/env python3
"""
CourtListener Cluster API Judge Data Quality Verification
Comprehensive investigation of judge field population rates across jurisdictions
"""

import asyncio
import httpx
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

load_dotenv()

class ClusterJudgeDataAnalyzer:
    """Analyzes judge data quality in CourtListener cluster API responses"""
    
    def __init__(self):
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        self.base_url = 'https://www.courtlistener.com/api/rest/v4'
        self.results = {}
        self.detailed_samples = []
    
    async def comprehensive_judge_data_analysis(self):
        """Comprehensive analysis of judge data across multiple dimensions"""
        print("🔍 COURTLISTENER CLUSTER JUDGE DATA QUALITY VERIFICATION")
        print("=" * 80)
        
        async with httpx.AsyncClient(
            headers={'Authorization': f'Token {self.api_key}'},
            timeout=30.0
        ) as client:
            
            # Test scenarios covering different court types and time periods
            test_scenarios = [
                # Federal Courts (should have high-quality data)
                ("Supreme Court", "scotus", "2023-01-01", "2024-12-31"),
                ("Federal Circuit Courts", "ca1,ca2,ca3,ca4,ca5", "2023-01-01", "2024-12-31"),
                ("Federal District Courts", "txnd,nysd,cacd,flsd", "2023-01-01", "2024-12-31"),
                
                # State Courts (may have different data quality)
                ("Texas State Courts", "tex,texcrimapp,texapp1", "2023-01-01", "2024-12-31"),
                ("California State Courts", "cal,calctapp", "2023-01-01", "2024-12-31"),
                ("New York State Courts", "ny,nyappdiv", "2023-01-01", "2024-12-31"),
                
                # Historical data (different time periods)
                ("Historical Federal (2020-2022)", "scotus,ca1,ca2", "2020-01-01", "2022-12-31"),
                ("Historical State (2020-2022)", "tex,cal,ny", "2020-01-01", "2022-12-31"),
                
                # Recent data (2024 only)
                ("Recent Federal (2024)", "scotus,ca1,ca2,ca3", "2024-01-01", "2024-12-31"),
                ("Recent State (2024)", "tex,cal,ny", "2024-01-01", "2024-12-31")
            ]
            
            for scenario_name, courts, start_date, end_date in test_scenarios:
                print(f"\n📊 {scenario_name}")
                print("-" * 60)
                
                analysis_result = await self._analyze_scenario(client, courts, start_date, end_date)
                self.results[scenario_name] = analysis_result
                
                self._display_scenario_results(scenario_name, analysis_result)
    
    async def _analyze_scenario(self, client: httpx.AsyncClient, courts: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """Analyze judge data for a specific scenario"""
        try:
            # Step 1: Get sample opinions
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': start_date,
                'date_filed__lte': end_date,
                'page_size': 15,
                'format': 'json'
            })
            
            if response.status_code != 200:
                return {'error': f'Opinions API failed: HTTP {response.status_code}'}
            
            opinions_data = response.json()
            opinions = opinions_data.get('results', [])
            
            if not opinions:
                return {'error': 'No opinions found for this scenario'}
            
            # Step 2: Analyze cluster data for each opinion
            cluster_analysis = {
                'total_opinions': len(opinions),
                'opinions_with_clusters': 0,
                'clusters_analyzed': 0,
                'clusters_with_judges': 0,
                'clusters_with_empty_judges': 0,
                'clusters_with_null_judges': 0,
                'judge_field_samples': [],
                'cluster_fetch_errors': 0,
                'detailed_samples': []
            }
            
            for i, opinion in enumerate(opinions):
                cluster_url = opinion.get('cluster')
                if not cluster_url:
                    continue
                
                cluster_analysis['opinions_with_clusters'] += 1
                
                # Fetch cluster data
                try:
                    cluster_response = await client.get(cluster_url)
                    if cluster_response.status_code == 200:
                        cluster_data = cluster_response.json()
                        cluster_analysis['clusters_analyzed'] += 1
                        
                        # Analyze judges field
                        judges_field = cluster_data.get('judges')
                        
                        sample_record = {
                            'opinion_id': opinion.get('id'),
                            'cluster_id': cluster_data.get('id'),
                            'case_name': cluster_data.get('case_name', 'N/A'),
                            'court': cluster_data.get('court', 'N/A'),
                            'date_filed': cluster_data.get('date_filed', 'N/A'),
                            'judges_field': judges_field,
                            'judges_type': type(judges_field).__name__,
                            'judges_length': len(judges_field) if judges_field else 0
                        }
                        
                        if judges_field is None:
                            cluster_analysis['clusters_with_null_judges'] += 1
                            sample_record['judges_status'] = 'null'
                        elif judges_field == "":
                            cluster_analysis['clusters_with_empty_judges'] += 1
                            sample_record['judges_status'] = 'empty_string'
                        elif judges_field and len(str(judges_field).strip()) > 0:
                            cluster_analysis['clusters_with_judges'] += 1
                            sample_record['judges_status'] = 'has_data'
                            # Store first few samples with judge data
                            if len([s for s in cluster_analysis['detailed_samples'] if s['judges_status'] == 'has_data']) < 3:
                                cluster_analysis['detailed_samples'].append(sample_record)
                        else:
                            cluster_analysis['clusters_with_empty_judges'] += 1
                            sample_record['judges_status'] = 'empty_other'
                        
                        # Store sample for detailed analysis (first 5 records)
                        if len(cluster_analysis['judge_field_samples']) < 5:
                            cluster_analysis['judge_field_samples'].append(sample_record)
                    else:
                        cluster_analysis['cluster_fetch_errors'] += 1
                except Exception as e:
                    cluster_analysis['cluster_fetch_errors'] += 1
            
            # Calculate rates
            if cluster_analysis['clusters_analyzed'] > 0:
                cluster_analysis['judge_data_rate'] = (cluster_analysis['clusters_with_judges'] / cluster_analysis['clusters_analyzed']) * 100
                cluster_analysis['empty_rate'] = (cluster_analysis['clusters_with_empty_judges'] / cluster_analysis['clusters_analyzed']) * 100
                cluster_analysis['null_rate'] = (cluster_analysis['clusters_with_null_judges'] / cluster_analysis['clusters_analyzed']) * 100
            else:
                cluster_analysis['judge_data_rate'] = 0
                cluster_analysis['empty_rate'] = 0
                cluster_analysis['null_rate'] = 0
            
            return cluster_analysis
            
        except Exception as e:
            return {'error': f'Analysis failed: {str(e)}'}
    
    def _display_scenario_results(self, scenario: str, results: Dict[str, Any]):
        """Display results for a scenario"""
        if 'error' in results:
            print(f"   ❌ {results['error']}")
            return
        
        print(f"   📈 Analysis Results:")
        print(f"      Total opinions sampled: {results['total_opinions']}")
        print(f"      Opinions with cluster URLs: {results['opinions_with_clusters']}")
        print(f"      Clusters successfully analyzed: {results['clusters_analyzed']}")
        print(f"      Clusters with judge data: {results['clusters_with_judges']}")
        print(f"      Clusters with empty judges field: {results['clusters_with_empty_judges']}")
        print(f"      Clusters with null judges field: {results['clusters_with_null_judges']}")
        print(f"      Judge data rate: {results['judge_data_rate']:.1f}%")
        print(f"      Empty judges rate: {results['empty_rate']:.1f}%")
        print(f"      Null judges rate: {results['null_rate']:.1f}%")
        
        # Show sample judge field data
        if results['judge_field_samples']:
            print(f"   📋 Sample Judge Field Data:")
            for i, sample in enumerate(results['judge_field_samples'][:3], 1):
                print(f"      Sample {i}: {sample['case_name'][:40]}...")
                print(f"         Judges field: \"{sample['judges_field']}\"")
                print(f"         Type: {sample['judges_type']}, Status: {sample['judges_status']}")
        
        # Show cases with actual judge data
        if results['detailed_samples']:
            print(f"   ✅ Cases WITH Judge Data Found:")
            for sample in results['detailed_samples']:
                print(f"      • {sample['case_name'][:50]}...")
                print(f"        Judges: \"{sample['judges_field']}\"")
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive report with findings and recommendations"""
        report = {
            'summary': {},
            'findings': [],
            'evidence': {},
            'recommendations': []
        }
        
        # Aggregate statistics
        total_clusters = 0
        total_with_judges = 0
        total_empty = 0
        scenarios_with_judge_data = 0
        
        for scenario, results in self.results.items():
            if 'error' not in results:
                total_clusters += results['clusters_analyzed']
                total_with_judges += results['clusters_with_judges']
                total_empty += results['clusters_with_empty_judges']
                
                if results['clusters_with_judges'] > 0:
                    scenarios_with_judge_data += 1
        
        # Generate summary
        if total_clusters > 0:
            overall_judge_rate = (total_with_judges / total_clusters) * 100
            overall_empty_rate = (total_empty / total_clusters) * 100
        else:
            overall_judge_rate = 0
            overall_empty_rate = 0
        
        report['summary'] = {
            'total_scenarios_tested': len(self.results),
            'total_clusters_analyzed': total_clusters,
            'total_with_judge_data': total_with_judges,
            'overall_judge_data_rate': overall_judge_rate,
            'overall_empty_rate': overall_empty_rate,
            'scenarios_with_judge_data': scenarios_with_judge_data
        }
        
        # Generate findings
        if overall_judge_rate > 50:
            report['findings'].append("FINDING: Majority of clusters contain judge data")
        elif overall_judge_rate > 10:
            report['findings'].append("FINDING: Significant portion of clusters contain judge data")
        else:
            report['findings'].append("FINDING: Very few clusters contain judge data")
        
        if scenarios_with_judge_data > len(self.results) / 2:
            report['findings'].append("FINDING: Judge data availability varies significantly by jurisdiction/time period")
        
        return report

async def main():
    """Main execution function"""
    analyzer = ClusterJudgeDataAnalyzer()
    
    # Run comprehensive analysis
    await analyzer.comprehensive_judge_data_analysis()
    
    # Generate report
    report = analyzer.generate_comprehensive_report()
    
    print("\n🎯 COMPREHENSIVE FINDINGS REPORT")
    print("=" * 60)
    print(f"Total scenarios tested: {report['summary']['total_scenarios_tested']}")
    print(f"Total clusters analyzed: {report['summary']['total_clusters_analyzed']}")
    print(f"Clusters with judge data: {report['summary']['total_with_judge_data']}")
    print(f"Overall judge data rate: {report['summary']['overall_judge_data_rate']:.1f}%")
    print(f"Scenarios with judge data: {report['summary']['scenarios_with_judge_data']}")
    
    print("\n📋 Key Findings:")
    for finding in report['findings']:
        print(f"   • {finding}")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    with open(f'cluster_judge_data_analysis_{timestamp}.json', 'w') as f:
        json.dump({
            'analysis_results': analyzer.results,
            'comprehensive_report': report,
            'timestamp': timestamp
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: cluster_judge_data_analysis_{timestamp}.json")

if __name__ == "__main__":
    asyncio.run(main())
