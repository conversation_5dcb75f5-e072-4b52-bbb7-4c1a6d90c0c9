#!/usr/bin/env python3
"""
Author URL Filtering Strategy Analysis
Investigates trade-offs between author URL filtering vs comprehensive case coverage
"""

import asyncio
import httpx
import os
import json
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

load_dotenv()

class AuthorFilteringAnalyzer:
    """Analyzes the implications of different author URL filtering strategies"""
    
    def __init__(self):
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        self.base_url = 'https://www.courtlistener.com/api/rest/v4'
        self.results = {}
    
    async def analyze_filtering_strategies(self):
        """Compare different filtering approaches"""
        print("🔍 AUTHOR URL FILTERING STRATEGY ANALYSIS")
        print("=" * 70)
        
        async with httpx.AsyncClient(
            headers={'Authorization': f'Token {self.api_key}'},
            timeout=30.0
        ) as client:
            
            # Test different court types and jurisdictions
            test_scenarios = [
                ("Federal Circuit Courts", "ca1,ca2,ca3,ca4,ca5"),
                ("Supreme Court", "scotus"),
                ("Texas State Courts", "tex,texcrimapp,texapp1,texapp2"),
                ("Federal District (Sample)", "txnd,txsd,nynd,nysd"),
                ("Mixed High-Profile", "scotus,ca1,ca2,tex,nysd")
            ]
            
            for scenario_name, courts in test_scenarios:
                print(f"\n📊 {scenario_name}")
                print("-" * 50)
                
                # Strategy 1: No author filtering (current production approach)
                no_filter_stats = await self._test_no_filtering(client, courts)
                
                # Strategy 2: Author URL filtering (proposed fix)
                author_filter_stats = await self._test_author_filtering(client, courts)
                
                # Strategy 3: Hybrid approach analysis
                hybrid_stats = await self._analyze_hybrid_potential(client, courts)
                
                # Store results
                self.results[scenario_name] = {
                    'no_filter': no_filter_stats,
                    'author_filter': author_filter_stats,
                    'hybrid_potential': hybrid_stats
                }
                
                # Display comparison
                self._display_strategy_comparison(scenario_name, no_filter_stats, author_filter_stats, hybrid_stats)
    
    async def _test_no_filtering(self, client: httpx.AsyncClient, courts: str) -> Dict[str, Any]:
        """Test current production approach - no author filtering"""
        try:
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'page_size': 20,
                'format': 'json'
            })
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Analyze judge extraction potential
                with_authors = sum(1 for case in results if self._has_author_url(case))
                with_clusters = sum(1 for case in results if case.get('cluster'))
                with_text = sum(1 for case in results if self._has_extractable_text(case))
                
                return {
                    'total_cases': len(results),
                    'available_count': data.get('count', 0),
                    'with_author_urls': with_authors,
                    'with_cluster_urls': with_clusters,
                    'with_extractable_text': with_text,
                    'author_url_rate': (with_authors / len(results) * 100) if results else 0,
                    'cluster_rate': (with_clusters / len(results) * 100) if results else 0,
                    'text_extraction_rate': (with_text / len(results) * 100) if results else 0
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _test_author_filtering(self, client: httpx.AsyncClient, courts: str) -> Dict[str, Any]:
        """Test proposed fix - author URL filtering"""
        try:
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'author__isnull': False,
                'page_size': 20,
                'format': 'json'
            })
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # All should have author URLs by definition
                with_authors = sum(1 for case in results if self._has_author_url(case))
                with_clusters = sum(1 for case in results if case.get('cluster'))
                
                return {
                    'total_cases': len(results),
                    'available_count': data.get('count', 0),
                    'with_author_urls': with_authors,
                    'with_cluster_urls': with_clusters,
                    'author_url_rate': (with_authors / len(results) * 100) if results else 0,
                    'cluster_rate': (with_clusters / len(results) * 100) if results else 0
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _analyze_hybrid_potential(self, client: httpx.AsyncClient, courts: str) -> Dict[str, Any]:
        """Analyze potential for hybrid approach"""
        try:
            # Get sample of cases without author URLs
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'author__isnull': True,
                'page_size': 10,
                'format': 'json'
            })
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Analyze alternative extraction potential
                with_clusters = sum(1 for case in results if case.get('cluster'))
                with_text = sum(1 for case in results if self._has_extractable_text(case))
                
                return {
                    'cases_without_authors': len(results),
                    'available_without_authors': data.get('count', 0),
                    'cluster_potential': with_clusters,
                    'text_potential': with_text,
                    'cluster_rate': (with_clusters / len(results) * 100) if results else 0,
                    'text_rate': (with_text / len(results) * 100) if results else 0
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _has_author_url(self, case: Dict[str, Any]) -> bool:
        """Check if case has valid author URL"""
        author = case.get('author')
        return author and isinstance(author, str) and author.startswith('http')
    
    def _has_extractable_text(self, case: Dict[str, Any]) -> bool:
        """Check if case has text suitable for judge extraction"""
        # This is a simplified check - in reality we'd need to fetch the full text
        return bool(case.get('plain_text') or case.get('html'))
    
    def _display_strategy_comparison(self, scenario: str, no_filter: Dict, author_filter: Dict, hybrid: Dict):
        """Display comparison of strategies"""
        print(f"   📈 Strategy Comparison:")
        
        if 'error' not in no_filter:
            print(f"      No Filtering (Current):")
            print(f"         Total available: {no_filter['available_count']}")
            print(f"         Sample analyzed: {no_filter['total_cases']}")
            print(f"         Author URL rate: {no_filter['author_url_rate']:.1f}%")
            print(f"         Cluster rate: {no_filter['cluster_rate']:.1f}%")
            print(f"         Text extraction rate: {no_filter['text_extraction_rate']:.1f}%")
        
        if 'error' not in author_filter:
            print(f"      Author Filtering (Proposed):")
            print(f"         Total available: {author_filter['available_count']}")
            print(f"         Sample analyzed: {author_filter['total_cases']}")
            print(f"         Author URL rate: {author_filter['author_url_rate']:.1f}%")
            print(f"         Cluster rate: {author_filter['cluster_rate']:.1f}%")
        
        if 'error' not in hybrid:
            print(f"      Cases Without Authors: <AUTHORS>
            print(f"         Total available: {hybrid['available_without_authors']}")
            print(f"         Cluster potential: {hybrid['cluster_rate']:.1f}%")
            print(f"         Text potential: {hybrid['text_rate']:.1f}%")
    
    def generate_recommendations(self) -> Dict[str, Any]:
        """Generate strategic recommendations based on analysis"""
        recommendations = {
            'strategy': 'hybrid',
            'rationale': [],
            'implementation': {},
            'trade_offs': {}
        }
        
        # Analyze results to generate recommendations
        total_with_authors = 0
        total_without_authors = 0
        total_cluster_potential = 0
        
        for scenario, data in self.results.items():
            if 'error' not in data['author_filter']:
                total_with_authors += data['author_filter']['available_count']
            if 'error' not in data['hybrid_potential']:
                total_without_authors += data['hybrid_potential']['available_without_authors']
                total_cluster_potential += data['hybrid_potential']['cluster_potential']
        
        # Generate recommendations based on data
        if total_with_authors > 0 and total_without_authors > 0:
            recommendations['strategy'] = 'hybrid'
            recommendations['rationale'].append(
                f"Hybrid approach recommended: {total_with_authors} cases with author URLs "
                f"+ {total_without_authors} cases without authors but with other extraction potential"
            )
        elif total_with_authors > total_without_authors * 2:
            recommendations['strategy'] = 'author_filtering'
            recommendations['rationale'].append(
                f"Author filtering recommended: {total_with_authors} cases with authors "
                f"significantly outweigh {total_without_authors} without"
            )
        else:
            recommendations['strategy'] = 'comprehensive'
            recommendations['rationale'].append(
                "Comprehensive approach recommended: significant value in cases without author URLs"
            )
        
        return recommendations

async def main():
    """Main execution function"""
    analyzer = AuthorFilteringAnalyzer()
    
    # Run analysis
    await analyzer.analyze_filtering_strategies()
    
    # Generate recommendations
    recommendations = analyzer.generate_recommendations()
    
    print("\n🎯 STRATEGIC RECOMMENDATIONS")
    print("=" * 50)
    print(f"Recommended Strategy: {recommendations['strategy'].upper()}")
    for rationale in recommendations['rationale']:
        print(f"   • {rationale}")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    with open(f'author_filtering_analysis_{timestamp}.json', 'w') as f:
        json.dump({
            'analysis_results': analyzer.results,
            'recommendations': recommendations,
            'timestamp': timestamp
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: author_filtering_analysis_{timestamp}.json")

if __name__ == "__main__":
    asyncio.run(main())
