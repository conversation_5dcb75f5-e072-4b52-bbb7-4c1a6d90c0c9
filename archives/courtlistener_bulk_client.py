"""
Court Listener Bulk Data Client

Fetches data from Court Listener API for all missing jurisdictions
to achieve complete US coverage (57 jurisdictions).
"""

import os
import time
import logging
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
from dotenv import load_dotenv
import sys

# Add graph module to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from graph.graph_sync import write_opinion

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class JurisdictionStatus:
    """Status of jurisdiction processing"""
    code: str
    name: str
    cases_fetched: int
    status: str  # 'pending', 'processing', 'completed', 'failed'
    error_message: Optional[str] = None
    last_updated: Optional[datetime] = None

class CourtListenerBulkClient:
    """
    Bulk data client for Court Listener API.
    
    Fetches data for all missing US jurisdictions to achieve complete coverage.
    """
    
    def __init__(self, api_key: str = None):
        """Initialize the bulk client."""
        self.api_key = api_key or os.getenv("COURTLISTENER_API_KEY")
        self.base_url = "https://www.courtlistener.com/api/rest/v4"
        self.session = requests.Session()
        
        # Only add authorization if API key is provided
        headers = {"User-Agent": "TexasLawsPersonalInjury/1.0"}
        if self.api_key:
            headers["Authorization"] = f"Token {self.api_key}"
        
        self.session.headers.update(headers)
        
        # Rate limiting - Court Listener: 5,000 queries/hour = ~83/minute
        self.requests_per_hour = 5000
        self.requests_per_minute = 83
        self.min_delay_seconds = 0.72  # 60/83 = 0.72 seconds between requests
        self.last_request_time = 0
        self.request_count_this_hour = 0
        self.hour_start_time = time.time()
        
        # All US jurisdictions
        self.all_jurisdictions = {
            'us': 'Federal',
            'al': 'Alabama', 'ak': 'Alaska', 'az': 'Arizona', 'ar': 'Arkansas',
            'ca': 'California', 'co': 'Colorado', 'ct': 'Connecticut', 'de': 'Delaware',
            'fl': 'Florida', 'ga': 'Georgia', 'hi': 'Hawaii', 'id': 'Idaho',
            'il': 'Illinois', 'in': 'Indiana', 'ia': 'Iowa', 'ks': 'Kansas',
            'ky': 'Kentucky', 'la': 'Louisiana', 'me': 'Maine', 'md': 'Maryland',
            'ma': 'Massachusetts', 'mi': 'Michigan', 'mn': 'Minnesota', 'ms': 'Mississippi',
            'mo': 'Missouri', 'mt': 'Montana', 'ne': 'Nebraska', 'nv': 'Nevada',
            'nh': 'New Hampshire', 'nj': 'New Jersey', 'nm': 'New Mexico', 'ny': 'New York',
            'nc': 'North Carolina', 'nd': 'North Dakota', 'oh': 'Ohio', 'ok': 'Oklahoma',
            'or': 'Oregon', 'pa': 'Pennsylvania', 'ri': 'Rhode Island', 'sc': 'South Carolina',
            'sd': 'South Dakota', 'tn': 'Tennessee', 'tx': 'Texas', 'ut': 'Utah',
            'vt': 'Vermont', 'va': 'Virginia', 'wa': 'Washington', 'wv': 'West Virginia',
            'wi': 'Wisconsin', 'wy': 'Wyoming',
            'dc': 'District of Columbia', 'pr': 'Puerto Rico', 'vi': 'Virgin Islands',
            'gu': 'Guam', 'as': 'American Samoa', 'mp': 'Northern Mariana Islands'
        }
        
        # Current coverage (from audit)
        self.current_jurisdictions = {'ca', 'tx', 'ny'}
        
        # Missing jurisdictions to process
        self.missing_jurisdictions = set(self.all_jurisdictions.keys()) - self.current_jurisdictions
        
        logger.info(f"CourtListener bulk client initialized")
        logger.info(f"  Total jurisdictions: {len(self.all_jurisdictions)}")
        logger.info(f"  Current coverage: {len(self.current_jurisdictions)}")
        logger.info(f"  Missing jurisdictions: {len(self.missing_jurisdictions)}")
    
    def _rate_limit(self):
        """
        Implement rate limiting for Court Listener API.
        Respects 5,000 queries per hour limit with proper spacing.
        """
        current_time = time.time()

        # Reset hourly counter if needed
        if current_time - self.hour_start_time >= 3600:
            self.request_count_this_hour = 0
            self.hour_start_time = current_time

        # Check if we've hit the hourly limit
        if self.request_count_this_hour >= self.requests_per_hour:
            # Wait until the next hour
            wait_time = 3600 - (current_time - self.hour_start_time)
            if wait_time > 0:
                logger.warning(f"Hourly rate limit reached. Waiting {wait_time:.1f} seconds...")
                time.sleep(wait_time)
                self.request_count_this_hour = 0
                self.hour_start_time = time.time()

        # Ensure minimum delay between requests
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay_seconds:
            sleep_time = self.min_delay_seconds - time_since_last
            time.sleep(sleep_time)

        self.last_request_time = time.time()
        self.request_count_this_hour += 1
    
    def fetch_jurisdiction_opinions(self, jurisdiction: str, limit: int = 1000) -> List[Dict]:
        """
        Fetch opinions for a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code (e.g., 'fl', 'ca')
            limit: Maximum number of opinions to fetch
            
        Returns:
            List of opinion dictionaries
        """
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/opinions/"
            params = {
                'court__jurisdiction': jurisdiction,
                'format': 'json',
                'order_by': '-date_created',
                'page_size': min(limit, 100)  # API limit
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            opinions = data.get('results', [])
            
            logger.info(f"Fetched {len(opinions)} opinions for {jurisdiction}")
            return opinions
            
        except Exception as e:
            logger.error(f"Error fetching opinions for {jurisdiction}: {e}")
            return []
    
    def fetch_jurisdiction_cases(self, jurisdiction: str, limit: int = 1000, practice_areas: List[str] = None) -> List[Dict]:
        """
        Fetch cases for a specific jurisdiction with pagination support and practice area filtering.

        Args:
            jurisdiction: Jurisdiction code
            limit: Maximum number of cases to fetch (0 = no limit, fetch all)
            practice_areas: List of practice areas to filter for (e.g., ['personal injury', 'criminal defense'])

        Returns:
            List of case dictionaries
        """
        # Check if API key is available
        if not self.api_key:
            logger.warning(f"No API key provided. Returning mock data for {jurisdiction}")
            return self._generate_mock_cases(jurisdiction, min(limit, 5))

        all_cases = []
        cursor = None  # For cursor-based pagination
        page_size = 100  # API limit is 100 per page

        # Build practice area query filter with comprehensive search terms
        practice_area_query = ""
        if practice_areas:
            # Create comprehensive search terms for each practice area
            area_terms = []
            for area in practice_areas:
                area_lower = area.lower().strip()

                if area_lower == 'personal injury':
                    area_terms.extend([
                        'personal injury', 'negligence', 'tort', 'damages', 'liability',
                        'malpractice', 'accident', 'wrongful death', 'slip and fall',
                        'motor vehicle', 'car accident', 'premises liability'
                    ])
                elif area_lower == 'criminal defense':
                    area_terms.extend([
                        'criminal', 'defendant', 'prosecution', 'felony', 'misdemeanor',
                        'indictment', 'plea', 'sentencing', 'conviction', 'appeal',
                        'habeas corpus', 'constitutional rights', 'search and seizure'
                    ])
                elif area_lower == 'family law':
                    area_terms.extend([
                        'family', 'divorce', 'custody', 'domestic', 'marriage',
                        'child support', 'alimony', 'adoption', 'paternity',
                        'domestic violence', 'restraining order', 'visitation'
                    ])
                elif area_lower == 'estate planning':
                    area_terms.extend([
                        'estate', 'probate', 'will', 'trust', 'inheritance',
                        'executor', 'beneficiary', 'guardianship', 'conservatorship',
                        'power of attorney', 'estate administration', 'testamentary'
                    ])
                elif area_lower == 'immigration law':
                    area_terms.extend([
                        'immigration', 'deportation', 'asylum', 'visa', 'naturalization',
                        'green card', 'citizenship', 'refugee', 'removal proceedings',
                        'immigration court', 'border', 'customs'
                    ])
                elif area_lower == 'real estate':
                    area_terms.extend([
                        'real estate', 'landlord', 'tenant', 'property', 'lease',
                        'eviction', 'foreclosure', 'title', 'deed', 'mortgage',
                        'zoning', 'easement', 'property rights', 'residential'
                    ])
                elif area_lower == 'bankruptcy':
                    area_terms.extend([
                        'bankruptcy', 'debtor', 'creditor', 'chapter', 'discharge',
                        'liquidation', 'reorganization', 'automatic stay',
                        'trustee', 'secured debt', 'unsecured debt'
                    ])

            if area_terms:
                # Remove duplicates and create OR query
                unique_terms = list(set(area_terms))
                practice_area_query = f" AND ({' OR '.join(unique_terms)})"

        try:
            current_page = 0  # Track current page for court rotation

            # Setup URL and base params - use search endpoint for proper case metadata
            url = f"{self.base_url}/search/"
            base_params = {
                'format': 'json',
                'type': 'o',  # Opinions - this gets us cluster data with metadata
                'ordering': '-dateFiled',
                'page_size': page_size
            }

            # Add jurisdiction filtering using correct court IDs from your guide
            if jurisdiction == 'tx':
                # Use correct Texas court IDs from CourtListener v4
                texas_courts = [
                    "tex", "texcrimapp",  # State-wide courts
                    *[f"texapp{i}" for i in range(1, 15)],  # texapp1-14 (no leading zero)
                    "txnd", "txsd", "txed", "txwd",  # Federal district courts
                    "ca5"  # Federal appellate (covers TX)
                ]
                base_params['court'] = texas_courts
            elif jurisdiction == 'ny':
                base_params['q'] = 'court_id:(ny OR nynd OR nyed OR nysd OR nywd OR ca2)'
            elif jurisdiction == 'fl':
                base_params['q'] = 'court_id:(fla OR flnd OR flmd OR flsd OR ca11)'

            while limit == 0 or len(all_cases) < limit:
                self._rate_limit()

                # Calculate remaining cases needed for this request
                if limit > 0:
                    remaining = limit - len(all_cases)
                    current_page_size = min(page_size, remaining)
                else:
                    current_page_size = page_size

                # Build params for this request
                params = base_params.copy()
                params['page_size'] = current_page_size

                # Add cursor for pagination if we have one
                if cursor:
                    params['cursor'] = cursor

                # Debug: Log the exact request being made
                logger.debug(f"Making request to: {url}")
                logger.debug(f"With params: {params}")

                # Make the API request
                response = self.session.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                page_cases = data.get('results', [])
                all_cases.extend(page_cases)

                # Get next URL and ensure page_size=100 is preserved (critical for v3/v4)
                next_url = data.get('next')
                cursor = None
                if next_url:
                    # CourtListener API omits page_size in next URLs - must append it
                    if 'page_size=' not in next_url:
                        separator = '&' if '?' in next_url else '?'
                        next_url = f"{next_url}{separator}page_size=100"

                    # Extract cursor parameter from next URL for subsequent requests
                    if 'cursor=' in next_url:
                        import urllib.parse
                        parsed_url = urllib.parse.urlparse(next_url)
                        query_params = urllib.parse.parse_qs(parsed_url.query)
                        cursor = query_params.get('cursor', [None])[0]

                logger.info(f"Fetched {len(page_cases)} cases for {jurisdiction} (total: {len(all_cases)}/{limit})")
                logger.debug(f"Next cursor: {cursor}")
                logger.debug(f"Page size requested: {current_page_size}, received: {len(page_cases)}")

                # Break if we got no cases (end of data) or no more pages
                if len(page_cases) == 0 or not cursor:
                    break

            logger.info(f"Completed fetching {len(all_cases)} cases for {jurisdiction}")
            if limit > 0:
                return all_cases[:limit]  # Ensure we don't exceed the limit
            else:
                return all_cases  # Return all cases when no limit specified

        except Exception as e:
            logger.error(f"Error fetching cases for {jurisdiction}: {e}")
            return all_cases  # Return what we have so far
    
    def _generate_mock_cases(self, jurisdiction: str, count: int) -> List[Dict]:
        """Generate mock cases for testing without API key."""
        mock_cases = []
        
        for i in range(count):
            case = {
                'id': f"mock_{jurisdiction}_{i+1}",
                'case_name': f"Mock Case {i+1} v. Example",
                'case_name_short': f"Mock Case {i+1}",
                'date_filed': f"2023-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
                'court': {
                    'full_name': f"{self.all_jurisdictions.get(jurisdiction, jurisdiction)} Supreme Court",
                    'short_name': f"{jurisdiction.upper()}. S. Ct."
                },
                'citations': [
                    {'volume': str(100 + i), 'reporter': 'P.3d', 'page': str(100 + i * 10)}
                ],
                'sub_opinions': [
                    {'text': f"This is mock opinion text for case {i+1} in {jurisdiction}..."}
                ],
                'absolute_url': f"https://www.courtlistener.com/opinion/mock_{jurisdiction}_{i+1}/",
                'docket_number': f"2023-CV-{i+1:03d}"
            }
            mock_cases.append(case)
        
        return mock_cases
    
    def process_missing_jurisdictions(self, cases_per_jurisdiction: int = 1000) -> Dict[str, JurisdictionStatus]:
        """
        Process all missing jurisdictions.
        
        Args:
            cases_per_jurisdiction: Number of cases to fetch per jurisdiction
            
        Returns:
            Dictionary of jurisdiction processing status
        """
        logger.info(f"Processing {len(self.missing_jurisdictions)} missing jurisdictions...")
        
        results = {}
        
        for jurisdiction in sorted(self.missing_jurisdictions):
            jurisdiction_name = self.all_jurisdictions[jurisdiction]
            
            logger.info(f"Processing {jurisdiction} ({jurisdiction_name})...")
            
            status = JurisdictionStatus(
                code=jurisdiction,
                name=jurisdiction_name,
                cases_fetched=0,
                status='processing',
                last_updated=datetime.now()
            )
            
            try:
                # Fetch cases for this jurisdiction
                cases = self.fetch_jurisdiction_cases(jurisdiction, cases_per_jurisdiction)
                
                if cases:
                    # Process cases through existing pipeline
                    processed_count = self._process_cases_through_pipeline(cases, jurisdiction)
                    
                    status.cases_fetched = processed_count
                    status.status = 'completed'
                    
                    logger.info(f"✅ {jurisdiction}: {processed_count} cases processed")
                else:
                    status.status = 'completed'
                    status.error_message = 'No cases found'
                    logger.warning(f"⚠️ {jurisdiction}: No cases found")
                
            except Exception as e:
                status.status = 'failed'
                status.error_message = str(e)
                logger.error(f"❌ {jurisdiction}: {e}")
            
            results[jurisdiction] = status
        
        # Summary
        total_cases = sum(status.cases_fetched for status in results.values())
        completed = sum(1 for status in results.values() if status.status == 'completed')
        failed = sum(1 for status in results.values() if status.status == 'failed')
        
        logger.info(f"\\n📊 Processing Summary:")
        logger.info(f"  Total jurisdictions processed: {len(results)}")
        logger.info(f"  Completed: {completed}")
        logger.info(f"  Failed: {failed}")
        logger.info(f"  Total cases fetched: {total_cases:,}")
        
        return results
    
    def _process_cases_through_pipeline(self, cases: List[Dict], jurisdiction: str) -> int:
        """
        Process cases through the existing pipeline.
        
        Args:
            cases: List of cases to process
            jurisdiction: Jurisdiction code
            
        Returns:
            Number of cases processed
        """
        try:
            from src.processing.caselaw_access_processor import CaselawAccessProcessor
            
            # Initialize processor
            processor = CaselawAccessProcessor()
            
            # Process cases through the pipeline
            processed_count = 0
            for case_data in cases:
                try:
                    # Transform Court Listener format to internal format
                    transformed_case = self._transform_court_listener_case(case_data, jurisdiction)

                    if transformed_case:  # Check if case passed time window filter
                        # Process through pipeline
                        if processor.process_single_case(transformed_case):
                            processed_count += 1

                            # Sync to Neo4j graph
                            self._sync_case_to_graph(transformed_case, case_data)
                        
                except Exception as e:
                    logger.warning(f"Failed to process case {case_data.get('id', 'unknown')}: {e}")
                    continue
            
            processor.close()
            return processed_count

        except Exception as e:
            logger.error(f"Error processing cases through pipeline: {e}")
            return 0

    def _sync_case_to_graph(self, transformed_case: Dict, original_case_data: Dict):
        """Sync processed case to Neo4j graph."""
        try:
            # Prepare opinion data for graph sync
            opinion_data = {
                'opinion_id': f"cl_{original_case_data.get('id', 'unknown')}",
                'court_id': self._map_court_listener_court_id(original_case_data.get('court', {})),
                'year_filed': self._extract_year_from_date(transformed_case.get('decision_date')),
                'practice_areas': self._extract_practice_areas(transformed_case),
                'citation': self._extract_primary_citation(original_case_data.get('citations', [])),
                'docket_number': transformed_case.get('docket_number'),
                'gcs_uri': f"gs://texas-laws-cases/cl_{original_case_data.get('id', 'unknown')}.json",
                'case_name': transformed_case.get('name'),
                'date_filed': transformed_case.get('decision_date'),
                'source': 'court_listener',
                'source_window': 'modern'  # CourtListener is modern window
            }

            # Write to graph using sync module
            write_opinion(opinion_data)

        except Exception as e:
            logger.error(f"Failed to sync case {original_case_data.get('id')} to Neo4j: {e}")
            # Don't fail the entire processing for graph sync issues

    def _map_court_listener_court_id(self, court_data: Dict) -> str:
        """Map CourtListener court data to standardized court ID."""
        court_id = court_data.get('id', '')

        # Map common CourtListener court IDs to our standard IDs
        mapping = {
            'tex': 'TXSC',
            'texcrimapp': 'TCCA',
            'texapp1st': 'TXCOA01',
            'texapp2nd': 'TXCOA02',
            'texapp3rd': 'TXCOA03',
            'texapp4th': 'TXCOA04',
            'texapp5th': 'TXCOA05',
            'texapp6th': 'TXCOA06',
            'texapp7th': 'TXCOA07',
            'texapp8th': 'TXCOA08',
            'texapp9th': 'TXCOA09',
            'texapp10th': 'TXCOA10',
            'texapp11th': 'TXCOA11',
            'texapp12th': 'TXCOA12',
            'texapp13th': 'TXCOA13',
            'texapp14th': 'TXCOA14',
            'txnd': 'TXND',
            'txsd': 'TXSD',
            'txed': 'TXED',
            'txwd': 'TXWD',
            'ca5': 'CA5'
        }

        return mapping.get(court_id, court_id.upper())

    def _extract_year_from_date(self, date_str: str) -> Optional[int]:
        """Extract year from date string."""
        if not date_str:
            return None
        try:
            if isinstance(date_str, str):
                return int(date_str[:4])
            return None
        except (ValueError, TypeError):
            return None

    def _extract_practice_areas(self, case_data: Dict) -> List[str]:
        """Extract practice areas from case data."""
        # This would be enhanced with actual practice area classification
        # For now, return a default
        return ['general']

    def _extract_primary_citation(self, citations: List[Dict]) -> Optional[str]:
        """Extract primary citation from citations list."""
        if not citations:
            return None

        # Return the first citation or the one marked as primary
        for citation in citations:
            if citation.get('type') == 'primary' or citation.get('primary'):
                return citation.get('cite', '')

        # Fallback to first citation
        return citations[0].get('cite', '') if citations else None
    
    def _transform_court_listener_case(self, case_data: Dict, jurisdiction: str) -> Dict:
        """
        Transform Court Listener case format to internal format.
        
        Args:
            case_data: Raw case data from Court Listener
            jurisdiction: Jurisdiction code
            
        Returns:
            Transformed case data
        """
        # Apply time window filter for CourtListener: 1994-01-01 ≤ date_filed ≤ 2025-12-31
        date_filed = case_data.get('date_filed')
        if date_filed:
            try:
                from datetime import datetime
                if isinstance(date_filed, str):
                    filed_date = datetime.fromisoformat(date_filed.replace('Z', '+00:00'))
                else:
                    filed_date = date_filed

                # Check if date falls within CourtListener modern window
                if filed_date.year < 1994 or filed_date.year > 2025:
                    logger.info(f"Skipping case {case_data.get('id')}: date {filed_date.year} outside CL window (1994-2025)")
                    return None

            except Exception as e:
                logger.warning(f"Could not parse date for case {case_data.get('id')}: {e}")

        # Classify document type for Court Listener data
        document_type = self._classify_court_listener_document_type(case_data)

        return {
            'id': case_data.get('id'),
            'name': case_data.get('case_name', ''),
            'name_abbreviation': case_data.get('case_name_short', ''),
            'decision_date': case_data.get('date_filed'),
            'court': {
                'name': case_data.get('court', {}).get('full_name', ''),
                'name_abbreviation': case_data.get('court', {}).get('short_name', '')
            },
            'jurisdiction': {
                'name': jurisdiction,
                'name_long': self.all_jurisdictions.get(jurisdiction, jurisdiction)
            },
            'citations': case_data.get('citations', []),
            'opinions': case_data.get('sub_opinions', []),
            'document_type': document_type,  # Add document type classification
            'source_window': 'modern',  # CourtListener handles modern era (1994-2025)
            'source': 'court_listener',
            'url': case_data.get('absolute_url', ''),
            'docket_number': case_data.get('docket_number', '')
        }

    def _classify_court_listener_document_type(self, case_data: Dict) -> str:
        """Classify Court Listener document type."""

        # Court Listener already filters for opinions with type=o parameter
        # But let's double-check using opinion metadata
        opinions = case_data.get('opinions', [])

        if opinions:
            # Check opinion types
            for opinion in opinions:
                opinion_type = opinion.get('type', '').lower()

                # Court Listener opinion types
                if 'opinion' in opinion_type:
                    # Check precedential status
                    precedential_status = opinion.get('precedential_status', '').lower()
                    if precedential_status == 'published':
                        return 'precedential'
                    elif precedential_status == 'unpublished':
                        return 'nonprecedential'
                    else:
                        return 'opinion'
                elif 'order' in opinion_type:
                    return 'order'

        # Fallback: Since we use type=o filter, assume it's an opinion
        return 'opinion'

    def get_processing_estimate(self) -> Dict[str, Any]:
        """Get processing time and resource estimates."""
        missing_count = len(self.missing_jurisdictions)
        cases_per_jurisdiction = 1000  # Conservative estimate
        total_cases = missing_count * cases_per_jurisdiction
        
        # Processing rate from audit: 6,702 cases/second
        processing_rate = 6702
        processing_time_seconds = total_cases / processing_rate
        processing_time_hours = processing_time_seconds / 3600
        
        # API rate limiting: 60 requests/minute
        api_requests_needed = missing_count * 10  # ~10 requests per jurisdiction
        api_time_minutes = api_requests_needed / 60
        api_time_hours = api_time_minutes / 60
        
        return {
            'missing_jurisdictions': missing_count,
            'estimated_cases': total_cases,
            'processing_time_hours': processing_time_hours,
            'api_time_hours': api_time_hours,
            'total_time_hours': max(processing_time_hours, api_time_hours),
            'recommended_batch_size': 100
        }
    
    def create_processing_plan(self) -> Dict[str, Any]:
        """Create a detailed processing plan."""
        estimate = self.get_processing_estimate()
        
        plan = {
            'overview': {
                'total_jurisdictions': len(self.missing_jurisdictions),
                'estimated_cases': estimate['estimated_cases'],
                'estimated_time_hours': estimate['total_time_hours']
            },
            'phases': [
                {
                    'name': 'High Priority States',
                    'jurisdictions': ['fl', 'pa', 'il', 'oh', 'mi', 'ga', 'nc', 'va', 'wa', 'az'],
                    'estimated_cases': 10000,
                    'priority': 'high'
                },
                {
                    'name': 'Medium Priority States',
                    'jurisdictions': ['ma', 'md', 'wi', 'mn', 'co', 'al', 'sc', 'la', 'ky', 'or'],
                    'estimated_cases': 8000,
                    'priority': 'medium'
                },
                {
                    'name': 'Federal and Territories',
                    'jurisdictions': ['us', 'dc', 'pr', 'vi', 'gu', 'as', 'mp'],
                    'estimated_cases': 15000,
                    'priority': 'high'
                },
                {
                    'name': 'Remaining States',
                    'jurisdictions': list(self.missing_jurisdictions - set(['fl', 'pa', 'il', 'oh', 'mi', 'ga', 'nc', 'va', 'wa', 'az', 'ma', 'md', 'wi', 'mn', 'co', 'al', 'sc', 'la', 'ky', 'or', 'us', 'dc', 'pr', 'vi', 'gu', 'as', 'mp'])),
                    'estimated_cases': 5000,
                    'priority': 'low'
                }
            ]
        }
        
        return plan

def main():
    """Test the Court Listener bulk client."""
    client = CourtListenerBulkClient()
    
    # Get processing estimate
    estimate = client.get_processing_estimate()
    print("📊 Processing Estimate:")
    print(f"  Missing jurisdictions: {estimate['missing_jurisdictions']}")
    print(f"  Estimated cases: {estimate['estimated_cases']:,}")
    print(f"  Estimated time: {estimate['total_time_hours']:.1f} hours")
    
    # Create processing plan
    plan = client.create_processing_plan()
    print("\\n📋 Processing Plan:")
    for phase in plan['phases']:
        print(f"  {phase['name']}: {len(phase['jurisdictions'])} jurisdictions")
        print(f"    Estimated cases: {phase['estimated_cases']:,}")
        print(f"    Priority: {phase['priority']}")

if __name__ == "__main__":
    main()