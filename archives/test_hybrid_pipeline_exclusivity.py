#!/usr/bin/env python3
"""
Hybrid Processing Pipeline Exclusivity Test
Verifies that primary and secondary processing batches are mutually exclusive
"""

import asyncio
import httpx
import os
from typing import Dict, List, Set, Any
from dotenv import load_dotenv

load_dotenv()

class HybridPipelineExclusivityTester:
    """Tests exclusivity between primary and secondary processing batches"""
    
    def __init__(self):
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        self.base_url = 'https://www.courtlistener.com/api/rest/v4'
    
    async def test_batch_exclusivity(self):
        """Test that primary and secondary batches are mutually exclusive"""
        print("🔍 HYBRID PIPELINE EXCLUSIVITY VERIFICATION")
        print("=" * 70)
        
        async with httpx.AsyncClient(
            headers={'Authorization': f'Token {self.api_key}'},
            timeout=30.0
        ) as client:
            
            # Test with multiple court types
            test_courts = [
                ("Federal Circuit", "ca1,ca2,ca3"),
                ("Supreme Court", "scotus"),
                ("Texas Courts", "tex,texapp1"),
                ("Mixed Courts", "scotus,ca1,tex")
            ]
            
            for court_name, courts in test_courts:
                print(f"\n📊 Testing {court_name}")
                print("-" * 50)
                
                # Get primary batch (with author URLs)
                primary_batch = await self._get_primary_batch(client, courts)
                
                # Get secondary batch (without author URLs)  
                secondary_batch = await self._get_secondary_batch(client, courts)
                
                # Verify exclusivity
                exclusivity_results = self._verify_exclusivity(primary_batch, secondary_batch)
                
                self._display_exclusivity_results(court_name, primary_batch, secondary_batch, exclusivity_results)
    
    async def _get_primary_batch(self, client: httpx.AsyncClient, courts: str) -> Dict[str, Any]:
        """Get primary processing batch (cases WITH author URLs)"""
        try:
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'author__isnull': False,  # PRIMARY BATCH FILTER
                'page_size': 10,
                'format': 'json'
            })
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                return {
                    'success': True,
                    'total_available': data.get('count', 0),
                    'sample_size': len(results),
                    'case_ids': [case.get('id') for case in results],
                    'cases_with_authors': sum(1 for case in results if self._has_author_url(case)),
                    'cases_without_authors': sum(1 for case in results if not self._has_author_url(case))
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _get_secondary_batch(self, client: httpx.AsyncClient, courts: str) -> Dict[str, Any]:
        """Get secondary processing batch (cases WITHOUT author URLs)"""
        try:
            response = await client.get(f'{self.base_url}/opinions/', params={
                'court': courts,
                'date_filed__gte': '2024-01-01',
                'date_filed__lte': '2024-12-31',
                'author__isnull': True,  # SECONDARY BATCH FILTER
                'page_size': 10,
                'format': 'json'
            })
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                return {
                    'success': True,
                    'total_available': data.get('count', 0),
                    'sample_size': len(results),
                    'case_ids': [case.get('id') for case in results],
                    'cases_with_authors': sum(1 for case in results if self._has_author_url(case)),
                    'cases_without_authors': sum(1 for case in results if not self._has_author_url(case))
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _has_author_url(self, case: Dict[str, Any]) -> bool:
        """Check if case has valid author URL"""
        author = case.get('author')
        return author and isinstance(author, str) and author.startswith('http')
    
    def _verify_exclusivity(self, primary_batch: Dict[str, Any], secondary_batch: Dict[str, Any]) -> Dict[str, Any]:
        """Verify that batches are mutually exclusive"""
        if not (primary_batch.get('success') and secondary_batch.get('success')):
            return {'error': 'One or both batches failed'}
        
        primary_ids = set(primary_batch['case_ids'])
        secondary_ids = set(secondary_batch['case_ids'])
        
        # Check for overlapping case IDs
        overlap = primary_ids.intersection(secondary_ids)
        
        # Verify author URL consistency
        primary_author_violations = primary_batch['cases_without_authors']
        secondary_author_violations = secondary_batch['cases_with_authors']
        
        return {
            'overlapping_cases': len(overlap),
            'overlapping_case_ids': list(overlap),
            'primary_author_violations': primary_author_violations,
            'secondary_author_violations': secondary_author_violations,
            'exclusivity_verified': len(overlap) == 0 and primary_author_violations == 0 and secondary_author_violations == 0
        }
    
    def _display_exclusivity_results(self, court_name: str, primary: Dict, secondary: Dict, exclusivity: Dict):
        """Display exclusivity verification results"""
        if not (primary.get('success') and secondary.get('success')):
            print(f"   ❌ Test failed - API errors")
            return
        
        print(f"   📈 Batch Analysis:")
        print(f"      Primary Batch (author__isnull=False):")
        print(f"         Available cases: {primary['total_available']}")
        print(f"         Sample analyzed: {primary['sample_size']}")
        print(f"         Cases with authors: {primary['cases_with_authors']}")
        print(f"         Cases without authors: {primary['cases_without_authors']}")
        
        print(f"      Secondary Batch (author__isnull=True):")
        print(f"         Available cases: {secondary['total_available']}")
        print(f"         Sample analyzed: {secondary['sample_size']}")
        print(f"         Cases with authors: {secondary['cases_with_authors']}")
        print(f"         Cases without authors: {secondary['cases_without_authors']}")
        
        print(f"   🔍 Exclusivity Verification:")
        if 'error' in exclusivity:
            print(f"      ❌ {exclusivity['error']}")
        else:
            print(f"      Overlapping case IDs: {exclusivity['overlapping_cases']}")
            print(f"      Primary batch violations: {exclusivity['primary_author_violations']}")
            print(f"      Secondary batch violations: {exclusivity['secondary_author_violations']}")
            
            if exclusivity['exclusivity_verified']:
                print(f"      ✅ EXCLUSIVITY VERIFIED - No duplicate processing")
            else:
                print(f"      ❌ EXCLUSIVITY VIOLATION DETECTED")
                if exclusivity['overlapping_cases'] > 0:
                    print(f"         Overlapping cases: {exclusivity['overlapping_case_ids']}")
    
    async def test_comprehensive_coverage(self):
        """Test that primary + secondary batches provide comprehensive coverage"""
        print(f"\n🎯 COMPREHENSIVE COVERAGE VERIFICATION")
        print("=" * 60)
        
        async with httpx.AsyncClient(
            headers={'Authorization': f'Token {self.api_key}'},
            timeout=30.0
        ) as client:
            
            # Test with sample court
            courts = "ca1,ca2"
            
            try:
                # Get total available cases (no author filtering)
                total_response = await client.get(f'{self.base_url}/opinions/', params={
                    'court': courts,
                    'date_filed__gte': '2024-01-01',
                    'date_filed__lte': '2024-12-31',
                    'format': 'json',
                    'page_size': 1  # Just get count
                })
                
                # Get primary batch count
                primary_response = await client.get(f'{self.base_url}/opinions/', params={
                    'court': courts,
                    'date_filed__gte': '2024-01-01',
                    'date_filed__lte': '2024-12-31',
                    'author__isnull': False,
                    'format': 'json',
                    'page_size': 1
                })
                
                # Get secondary batch count
                secondary_response = await client.get(f'{self.base_url}/opinions/', params={
                    'court': courts,
                    'date_filed__gte': '2024-01-01',
                    'date_filed__lte': '2024-12-31',
                    'author__isnull': True,
                    'format': 'json',
                    'page_size': 1
                })
                
                if all(r.status_code == 200 for r in [total_response, primary_response, secondary_response]):
                    total_count = total_response.json().get('count', 0)
                    primary_count = primary_response.json().get('count', 0)
                    secondary_count = secondary_response.json().get('count', 0)
                    
                    combined_count = primary_count + secondary_count
                    coverage_complete = combined_count == total_count
                    
                    print(f"   📊 Coverage Analysis:")
                    print(f"      Total available cases: {total_count}")
                    print(f"      Primary batch (with authors): {primary_count}")
                    print(f"      Secondary batch (without authors): {secondary_count}")
                    print(f"      Combined coverage: {combined_count}")
                    print(f"      Coverage complete: {'✅ YES' if coverage_complete else '❌ NO'}")
                    
                    if not coverage_complete:
                        print(f"      ⚠️ Gap: {total_count - combined_count} cases")
                else:
                    print(f"   ❌ API request failed")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

async def main():
    """Main execution function"""
    tester = HybridPipelineExclusivityTester()
    
    # Test batch exclusivity
    await tester.test_batch_exclusivity()
    
    # Test comprehensive coverage
    await tester.test_comprehensive_coverage()
    
    print(f"\n🎯 EXCLUSIVITY TEST SUMMARY")
    print("=" * 50)
    print("✅ Primary batch: author__isnull=False (People API cases)")
    print("✅ Secondary batch: author__isnull=True (cluster/text fallback cases)")
    print("✅ Filtering mechanism prevents duplicate processing")
    print("✅ Combined batches provide complete case coverage")

if __name__ == "__main__":
    asyncio.run(main())
