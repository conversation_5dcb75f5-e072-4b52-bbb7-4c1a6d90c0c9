#!/usr/bin/env python3
"""
Test Real CourtListener Judge Disambiguation
Fetch real CourtListener data and test judge disambiguation
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealCourtListenerJudgeTest:
    """Test judge disambiguation with real CourtListener API data"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API (V4)
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"real_cl_judge_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def fetch_real_courtlistener_cases(self, limit: int = 5) -> list:
        """Fetch real cases from CourtListener API with judge information"""
        
        print(f"🌐 FETCHING REAL COURTLISTENER CASES")
        print("=" * 60)
        
        if not self.cl_api_key:
            print(f"❌ No CourtListener API key found")
            return []
        
        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        real_cases = []
        
        try:
            # Search for recent federal cases with opinions (likely to have judge info)
            search_params = {
                'court': 'scotus,ca5,ca2,txnd,nysd',  # Federal courts
                'filed_after': '2020-01-01',  # Recent cases
                'ordering': '-date_filed',  # Most recent first (V4 uses 'ordering')
                'page_size': 20  # Get more to filter for judge info
            }

            print(f"   🔍 Searching CourtListener API V4...")
            print(f"   📊 Parameters: {search_params}")

            response = requests.get(
                f"{self.cl_base_url}/opinions/",
                headers=headers,
                params=search_params,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ API request failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return []
            
            search_results = response.json()
            results = search_results.get('results', [])
            
            print(f"   📊 Found {len(results)} search results")
            
            # Process opinions directly (V4 returns full opinion data)
            for opinion_data in results[:limit * 2]:  # Get extra to filter
                if len(real_cases) >= limit:
                    break

                try:
                    print(f"   📄 Processing: {opinion_data.get('case_name', 'Unknown')[:50]}...")

                    # Add delay to respect rate limits
                    time.sleep(0.2)
                    
                    # Check if case has text content
                    plain_text = opinion_data.get('plain_text', '')
                    html_content = opinion_data.get('html', '')
                    text_content = plain_text or html_content
                    
                    if not text_content or len(text_content) < 500:
                        print(f"      ⚠️ No substantial text content")
                        continue
                    
                    # Look for judge indicators in text
                    text_lower = text_content.lower()
                    judge_indicators = ['judge', 'justice', 'circuit judge', 'district judge', 'delivered', 'opinion']
                    
                    if not any(indicator in text_lower for indicator in judge_indicators):
                        print(f"      ⚠️ No judge indicators found")
                        continue
                    
                    # Create processing format
                    case_data = {
                        'id': f"cl_real_{opinion_data.get('id', 'unknown')}",
                        'source': 'courtlistener',
                        'case_name': opinion_data.get('case_name', 'Unknown'),
                        'court': opinion_data.get('court', ''),
                        'court_name': self._get_court_name(opinion_data.get('court', '')),
                        'date_filed': opinion_data.get('date_filed', ''),
                        'jurisdiction': 'US',
                        'plain_text': plain_text,
                        'html': html_content,
                        'text': text_content,  # For processing
                        'precedential_status': opinion_data.get('precedential_status', 'Unknown'),
                        'citations': opinion_data.get('citations', [])
                    }
                    
                    real_cases.append(case_data)
                    
                    print(f"      ✅ Case {len(real_cases)}: {case_data['case_name'][:50]}...")
                    print(f"         Court: {case_data['court_name']}")
                    print(f"         Text length: {len(text_content):,} characters")
                    print(f"         Date: {case_data['date_filed']}")
                    
                except Exception as e:
                    print(f"      ❌ Error processing case: {e}")
                    continue
            
            print(f"\n📊 REAL COURTLISTENER CASES FETCHED:")
            print(f"   Total cases: {len(real_cases)}")
            
            return real_cases
            
        except Exception as e:
            print(f"❌ Error fetching CourtListener cases: {e}")
            return []
    
    def _get_court_name(self, court_id: str) -> str:
        """Get full court name from court ID"""
        court_names = {
            'scotus': 'Supreme Court of the United States',
            'ca1': 'U.S. Court of Appeals, First Circuit',
            'ca2': 'U.S. Court of Appeals, Second Circuit',
            'ca3': 'U.S. Court of Appeals, Third Circuit',
            'ca4': 'U.S. Court of Appeals, Fourth Circuit',
            'ca5': 'U.S. Court of Appeals, Fifth Circuit',
            'ca6': 'U.S. Court of Appeals, Sixth Circuit',
            'ca7': 'U.S. Court of Appeals, Seventh Circuit',
            'ca8': 'U.S. Court of Appeals, Eighth Circuit',
            'ca9': 'U.S. Court of Appeals, Ninth Circuit',
            'ca10': 'U.S. Court of Appeals, Tenth Circuit',
            'ca11': 'U.S. Court of Appeals, Eleventh Circuit',
            'cadc': 'U.S. Court of Appeals, D.C. Circuit',
            'cafc': 'U.S. Court of Appeals, Federal Circuit',
            'txnd': 'U.S. District Court, Northern District of Texas',
            'txed': 'U.S. District Court, Eastern District of Texas',
            'txsd': 'U.S. District Court, Southern District of Texas',
            'txwd': 'U.S. District Court, Western District of Texas',
            'nynd': 'U.S. District Court, Northern District of New York',
            'nyed': 'U.S. District Court, Eastern District of New York',
            'nysd': 'U.S. District Court, Southern District of New York',
            'nywd': 'U.S. District Court, Western District of New York',
        }
        return court_names.get(court_id, f'Court {court_id}')
    
    async def test_real_courtlistener_judge_disambiguation(self) -> bool:
        """Test judge disambiguation with real CourtListener data"""
        
        print(f"\n🔄 REAL COURTLISTENER JUDGE DISAMBIGUATION TEST")
        print("=" * 60)
        
        try:
            # Fetch real CourtListener cases
            real_cases = self.fetch_real_courtlistener_cases(3)
            
            if not real_cases:
                print(f"❌ No real CourtListener cases fetched")
                return False
            
            print(f"\n📊 PROCESSING {len(real_cases)} REAL COURTLISTENER CASES")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing real CourtListener data through judge disambiguation pipeline...")
            
            result = await processor.process_coherent_batch(
                raw_cases=real_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify judge disambiguation worked on real CourtListener data
            return await self.verify_real_courtlistener_judge_disambiguation(real_cases)
            
        except Exception as e:
            print(f"❌ Real CourtListener judge disambiguation test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_real_courtlistener_judge_disambiguation(self, original_cases: list) -> bool:
        """Verify judge disambiguation worked on real CourtListener data"""
        
        print(f"\n🔍 VERIFYING REAL COURTLISTENER JUDGE DISAMBIGUATION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # 1. Get judges extracted from real CourtListener data
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case {batch_id: $batch_id})
                        WHERE type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           count(*) as case_count
                    ORDER BY j.name
                ''', batch_id=self.test_batch_id)
                
                real_judges = list(result)
                
                print(f"📊 1. JUDGES FROM REAL COURTLISTENER DATA:")
                print(f"   Total judges found: {len(real_judges)}")
                
                for judge in real_judges:
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']}, Court: {judge['judge_court']}, Cases: {judge['case_count']})")
                
                # 2. Show sample text that was processed
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})
                    WHERE c.text IS NOT NULL AND c.text <> ""
                    RETURN c.case_name as case_name, substring(c.text, 0, 300) as text_sample
                    LIMIT 2
                ''', batch_id=self.test_batch_id)
                
                text_samples = list(result)
                
                print(f"\n📊 2. SAMPLE TEXT PROCESSED:")
                for i, sample in enumerate(text_samples, 1):
                    print(f"   {i}. {sample['case_name']}")
                    print(f"      Text: {sample['text_sample']}...")
                
                # 3. Check disambiguation features
                print(f"\n📊 3. DISAMBIGUATION FEATURES:")
                
                # Check for full names vs single names
                full_name_judges = [j for j in real_judges if len(j['judge_name'].split()) > 1]
                single_name_judges = [j for j in real_judges if len(j['judge_name'].split()) == 1]
                
                print(f"   Full names: {len(full_name_judges)}")
                for judge in full_name_judges:
                    print(f"      - {judge['judge_name']}")
                
                print(f"   Single names: {len(single_name_judges)}")
                for judge in single_name_judges:
                    print(f"      - {judge['judge_name']}")
                
                # Check unique IDs
                unique_ids = set(j['judge_id'] for j in real_judges)
                print(f"   Unique judge IDs: {len(unique_ids)}")
                
                # 4. Show original case info for context
                print(f"\n📊 4. ORIGINAL CASE CONTEXT:")
                for i, case in enumerate(original_cases, 1):
                    print(f"   {i}. {case['case_name']}")
                    print(f"      Court: {case['court_name']}")
                    print(f"      Date: {case['date_filed']}")
                    print(f"      Text length: {len(case.get('text', '')):,} chars")
                
                # Success criteria
                success_criteria = [
                    len(real_judges) > 0,  # At least some judges found
                    len(unique_ids) == len(real_judges),  # All judges have unique IDs
                    len(text_samples) > 0,  # Text was processed
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 REAL COURTLISTENER JUDGE DISAMBIGUATION VERIFICATION:")
                print(f"   Judges extracted: {'✅' if len(real_judges) > 0 else '❌'} ({len(real_judges)})")
                print(f"   Unique IDs: {'✅' if len(unique_ids) == len(real_judges) else '❌'} ({len(unique_ids)}/{len(real_judges)})")
                print(f"   Text processed: {'✅' if len(text_samples) > 0 else '❌'} ({len(text_samples)} samples)")
                print(f"   Full names extracted: {'✅' if len(full_name_judges) > 0 else '⚠️'} ({len(full_name_judges)})")
                
                if success and len(real_judges) > 0:
                    print(f"\n🎉 REAL COURTLISTENER JUDGE DISAMBIGUATION: SUCCESS!")
                    print(f"✅ Judge disambiguation working on real CourtListener data")
                    print(f"✅ {len(real_judges)} real judges extracted with unique identities")
                    print(f"✅ Full pipeline working with live API data")
                elif len(real_judges) == 0:
                    print(f"\n⚠️ REAL COURTLISTENER JUDGE DISAMBIGUATION: NO JUDGES FOUND!")
                    print(f"⚠️ Real CourtListener cases may not contain extractable judge patterns")
                    print(f"⚠️ This indicates judge extraction patterns may need refinement for real data")
                else:
                    print(f"\n⚠️ REAL COURTLISTENER JUDGE DISAMBIGUATION: PARTIAL SUCCESS!")
                    print(f"⚠️ Some issues with disambiguation or processing")
                
                return success and len(real_judges) > 0
                
        except Exception as e:
            print(f"❌ Real CourtListener judge disambiguation verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP REAL COURTLISTENER TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run real CourtListener judge disambiguation test"""
    
    print("🧪 REAL COURTLISTENER JUDGE DISAMBIGUATION TEST")
    print("=" * 80)
    print("🎯 Testing judge disambiguation with live CourtListener API data")
    
    test = RealCourtListenerJudgeTest()
    
    try:
        # Run the test
        success = await test.test_real_courtlistener_judge_disambiguation()
        
        if success:
            print(f"\n🎉 REAL COURTLISTENER JUDGE DISAMBIGUATION: SUCCESS!")
            print(f"✅ Judge disambiguation verified with real CourtListener data")
            return True
        else:
            print(f"\n❌ REAL COURTLISTENER JUDGE DISAMBIGUATION: FAILED!")
            print(f"❌ Judge extraction may need refinement for real CourtListener data")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
