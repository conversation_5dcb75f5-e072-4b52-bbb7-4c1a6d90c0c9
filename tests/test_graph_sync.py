#!/usr/bin/env python3
"""
Unit tests for Neo4j Graph Sync module.
Tests opinion insertion, citation relationships, and idempotency.
"""

import pytest
import os
import sys
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from graph.graph_sync import Neo4jGraphSync, OpinionData, write_opinion, write_citation

class TestNeo4jGraphSync:
    """Test cases for Neo4j graph synchronization."""
    
    @pytest.fixture
    def mock_driver(self):
        """Mock Neo4j driver for testing."""
        with patch('graph.graph_sync.GraphDatabase.driver') as mock_driver:
            mock_session = Mock()
            mock_tx = Mock()
            
            mock_driver.return_value.session.return_value.__enter__.return_value = mock_session
            mock_session.execute_write = Mock()
            mock_session.run = Mock()
            
            yield mock_driver
    
    @pytest.fixture
    def graph_sync(self, mock_driver):
        """Create GraphSync instance with mocked driver."""
        sync = Neo4jGraphSync(
            uri='bolt://test:7687',
            username='test',
            password='test'
        )
        sync.batch_size = 2  # Small batch for testing
        return sync
    
    def test_initialization(self):
        """Test GraphSync initialization."""
        sync = Neo4jGraphSync()
        
        assert sync.uri == os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        assert sync.username == os.getenv('NEO4J_USERNAME', 'neo4j')
        assert sync.batch_size == 1000
        assert sync.pending_opinions == []
        assert sync.pending_citations == []
    
    def test_write_opinion_batching(self, graph_sync, mock_driver):
        """Test opinion batching and writing."""
        # Create test opinions
        opinion1 = OpinionData(
            opinion_id='test_001',
            court_id='TXSC',
            year=2020,
            practice_areas=['personal_injury'],
            citation='123 S.W.3d 456',
            case_name='Test v. Case'
        )
        
        opinion2 = OpinionData(
            opinion_id='test_002',
            court_id='TXND',
            year=2021,
            practice_areas=['criminal_defense', 'family_law'],
            citation='456 F.3d 789',
            case_name='Another v. Test'
        )
        
        # Write opinions (should trigger batch flush at size 2)
        graph_sync.write_opinion(opinion1)
        assert len(graph_sync.pending_opinions) == 1
        
        graph_sync.write_opinion(opinion2)
        # Should have flushed due to batch size
        assert len(graph_sync.pending_opinions) == 0
        
        # Verify session.execute_write was called
        mock_session = mock_driver.return_value.session.return_value.__enter__.return_value
        mock_session.execute_write.assert_called_once()
    
    def test_write_citation_batching(self, graph_sync, mock_driver):
        """Test citation batching and writing."""
        # Write citations (should trigger batch flush at size 2)
        graph_sync.write_citation('test_001', 'test_002')
        assert len(graph_sync.pending_citations) == 1
        
        graph_sync.write_citation('test_002', 'test_003')
        # Should have flushed due to batch size
        assert len(graph_sync.pending_citations) == 0
        
        # Verify session.execute_write was called
        mock_session = mock_driver.return_value.session.return_value.__enter__.return_value
        mock_session.execute_write.assert_called_once()
    
    def test_flush_operations(self, graph_sync, mock_driver):
        """Test manual flushing of pending operations."""
        # Add pending operations
        opinion = OpinionData(
            opinion_id='test_flush',
            court_id='TXSC',
            year=2020,
            practice_areas=['personal_injury']
        )
        
        graph_sync.pending_opinions.append(opinion)
        graph_sync.pending_citations.append({'citing': 'test_001', 'cited': 'test_002'})
        
        # Manual flush
        graph_sync.flush_all()
        
        assert len(graph_sync.pending_opinions) == 0
        assert len(graph_sync.pending_citations) == 0
        
        # Verify both flushes were called
        mock_session = mock_driver.return_value.session.return_value.__enter__.return_value
        assert mock_session.execute_write.call_count == 2
    
    def test_get_stats(self, graph_sync, mock_driver):
        """Test graph statistics retrieval."""
        # Mock the session.run calls for stats
        mock_session = mock_driver.return_value.session.return_value.__enter__.return_value
        
        # Mock node counts query result
        mock_node_result = Mock()
        mock_node_result.single.return_value = {
            'courts': 21,
            'practice_areas': 7,
            'opinions': 100
        }
        
        # Mock relationship counts query result
        mock_rel_result = Mock()
        mock_rel_result.single.return_value = {
            'hears': 100,
            'has_pa': 150,
            'cites': 50
        }
        
        mock_session.run.side_effect = [mock_node_result, mock_rel_result]
        
        stats = graph_sync.get_stats()
        
        assert stats['nodes']['courts'] == 21
        assert stats['nodes']['practice_areas'] == 7
        assert stats['nodes']['opinions'] == 100
        assert stats['nodes']['total'] == 128
        
        assert stats['relationships']['hears_before'] == 100
        assert stats['relationships']['has_pa'] == 150
        assert stats['relationships']['cites'] == 50
        assert stats['relationships']['total'] == 300
        
        assert 'timestamp' in stats
    
    def test_convenience_functions(self, mock_driver):
        """Test convenience functions for pipeline integration."""
        with patch('graph.graph_sync._graph_sync', None):
            # Test write_opinion convenience function
            opinion_data = {
                'id': 'conv_test_001',
                'court_id': 'TXSC',
                'year_filed': 2020,
                'practice_areas': ['personal_injury'],
                'citation': '123 S.W.3d 456',
                'case_name': 'Convenience v. Test'
            }
            
            write_opinion(opinion_data)
            
            # Test write_citation convenience function
            write_citation('conv_test_001', 'conv_test_002')
            
            # Should have created global instance
            from graph.graph_sync import _graph_sync
            assert _graph_sync is not None

class TestIntegrationScenario:
    """Integration test with realistic scenario."""
    
    @pytest.fixture
    def mock_neo4j_session(self):
        """Mock complete Neo4j session for integration test."""
        with patch('graph.graph_sync.GraphDatabase.driver') as mock_driver:
            mock_session = Mock()
            mock_tx = Mock()
            
            # Mock session context manager
            mock_driver.return_value.session.return_value.__enter__.return_value = mock_session
            mock_driver.return_value.session.return_value.__exit__.return_value = None
            
            # Mock transaction execution
            mock_session.execute_write = Mock()
            
            # Mock stats queries
            mock_node_result = Mock()
            mock_node_result.single.return_value = {
                'courts': 2, 'practice_areas': 2, 'opinions': 2
            }
            
            mock_rel_result = Mock()
            mock_rel_result.single.return_value = {
                'hears': 2, 'has_pa': 3, 'cites': 1
            }
            
            mock_session.run.side_effect = [mock_node_result, mock_rel_result]
            
            yield mock_session
    
    def test_two_opinions_with_citation(self, mock_neo4j_session):
        """Test inserting two mock opinions with a citation relationship."""
        sync = Neo4jGraphSync()
        sync.batch_size = 10  # Prevent auto-flush for this test
        
        # Create two test opinions
        opinion1 = OpinionData(
            opinion_id='integration_001',
            court_id='TXSC',
            year=2020,
            practice_areas=['personal_injury'],
            citation='100 S.W.3d 200',
            case_name='Plaintiff v. Defendant',
            source='court_listener',
            source_window='modern'
        )
        
        opinion2 = OpinionData(
            opinion_id='integration_002',
            court_id='TXND',
            year=2021,
            practice_areas=['personal_injury', 'criminal_defense'],
            citation='200 F.3d 300',
            case_name='State v. Defendant',
            source='court_listener',
            source_window='modern'
        )
        
        # Write opinions
        sync.write_opinion(opinion1)
        sync.write_opinion(opinion2)
        
        # Add citation relationship (opinion2 cites opinion1)
        sync.write_citation('integration_002', 'integration_001')
        
        # Flush all operations
        sync.flush_all()
        
        # Verify operations were executed
        assert mock_neo4j_session.execute_write.call_count == 2  # opinions + citations
        
        # Get stats to verify counts
        stats = sync.get_stats()
        
        # Verify expected counts
        assert stats['nodes']['courts'] == 2
        assert stats['nodes']['practice_areas'] == 2
        assert stats['nodes']['opinions'] == 2
        assert stats['nodes']['total'] == 6
        
        assert stats['relationships']['hears_before'] == 2
        assert stats['relationships']['has_pa'] == 3  # opinion1: 1, opinion2: 2
        assert stats['relationships']['cites'] == 1
        assert stats['relationships']['total'] == 6

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
