#!/usr/bin/env python3
"""
Test 1: Basic CourtListener Processing
Test CourtListener data processing across all 4 storage systems
"""

import asyncio
import logging
import os
import requests
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CourtListenerBasicTest:
    """Test basic CourtListener processing across all storage systems"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize all storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API setup (using v4 as required for new users)
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"cl_basic_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def get_courtlistener_test_cases(self, num_cases: int = 5) -> list:
        """Get real CourtListener cases via API - NO FALLBACK TO MOCK DATA"""

        print(f"📡 FETCHING REAL COURTLISTENER TEST CASES")
        print("=" * 60)

        if not self.cl_api_key:
            print("❌ COURTLISTENER_API_KEY not found in environment")
            print("❌ Cannot proceed without real API access")
            return []

        print(f"🔑 Using API key: {self.cl_api_key[:10]}...")

        # Test API connectivity first
        if not self._test_api_connectivity():
            print("❌ CourtListener API connectivity failed")
            return []

        return self._fetch_real_cases(num_cases)

    def _test_api_connectivity(self) -> bool:
        """Test CourtListener API connectivity"""

        print("🔍 Testing CourtListener API connectivity...")

        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }

        try:
            # Test with a simple API endpoint
            test_url = f"{self.cl_base_url}/courts/"
            response = requests.get(test_url, headers=headers, timeout=10)

            print(f"   API Response: {response.status_code}")

            if response.status_code == 200:
                print("   ✅ API connectivity successful")
                return True
            elif response.status_code == 403:
                print("   ❌ 403 Forbidden - API key may be invalid or expired")
                print(f"   Response: {response.text[:200]}")
                return False
            else:
                print(f"   ❌ API error: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                return False

        except Exception as e:
            print(f"   ❌ API connectivity test failed: {e}")
            return False

    def _fetch_real_cases(self, num_cases: int) -> list:
        """Fetch real cases from CourtListener API"""

        headers = {
            'Authorization': f'Token {self.cl_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }
        
        test_cases = []
        
        # Define test courts (mix of levels)
        test_courts = [
            {"court": "scotus", "name": "Supreme Court of the United States", "level": "supreme"},
            {"court": "ca5", "name": "U.S. Court of Appeals, Fifth Circuit", "level": "appellate"},
            {"court": "ca9", "name": "U.S. Court of Appeals, Ninth Circuit", "level": "appellate"},
            {"court": "txnd", "name": "U.S. District Court, Northern District of Texas", "level": "district"},
            {"court": "txsd", "name": "U.S. District Court, Southern District of Texas", "level": "district"}
        ]
        
        for court_info in test_courts[:num_cases]:
            court_id = court_info["court"]
            court_name = court_info["name"]
            
            print(f"\n📄 Fetching case from {court_name} ({court_id})")
            
            try:
                # Search for opinions from this court
                search_url = f"{self.cl_base_url}/search/"
                params = {
                    'type': 'o',  # opinions
                    'court': court_id,
                    'order_by': 'dateFiled desc',
                    'format': 'json'
                }
                
                response = requests.get(search_url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    search_results = response.json()
                    
                    if search_results.get('results'):
                        # Get the first result
                        opinion = search_results['results'][0]
                        
                        # Get full opinion details
                        opinion_url = f"{self.cl_base_url}/opinions/{opinion['id']}/"
                        detail_response = requests.get(opinion_url, headers=headers, timeout=30)
                        
                        if detail_response.status_code == 200:
                            full_opinion = detail_response.json()
                            
                            # Transform to our expected format
                            case_data = self.transform_courtlistener_case(full_opinion, court_info)
                            
                            if case_data:
                                test_cases.append(case_data)
                                print(f"   ✅ Retrieved: {case_data.get('case_name', 'Unknown')}")
                            else:
                                print(f"   ⚠️ No usable text content")
                        else:
                            print(f"   ❌ Failed to get opinion details: {detail_response.status_code}")
                    else:
                        print(f"   ⚠️ No opinions found for {court_id}")
                else:
                    print(f"   ❌ Search failed: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error fetching from {court_id}: {e}")
                continue
        
        print(f"\n📊 COURTLISTENER FETCH SUMMARY:")
        print(f"   Successfully retrieved: {len(test_cases)} cases")
        print(f"   Courts represented: {len(set(case.get('court') for case in test_cases))}")
        
        return test_cases
    
    def transform_courtlistener_case(self, cl_opinion: dict, court_info: dict) -> dict:
        """Transform CourtListener opinion to our case format"""
        
        try:
            # Extract text content
            text_content = ""
            if cl_opinion.get('plain_text'):
                text_content = cl_opinion['plain_text']
            elif cl_opinion.get('html_with_citations'):
                # Strip HTML tags for plain text
                import re
                text_content = re.sub(r'<[^>]+>', '', cl_opinion['html_with_citations'])
            
            if not text_content or len(text_content) < 500:
                return None
            
            # Get cluster information for case name
            cluster = cl_opinion.get('cluster', {})
            
            # Transform to our format
            case_data = {
                'id': f"cl_{cl_opinion['id']}",
                'source': 'courtlistener',
                'case_name': cluster.get('case_name', 'Unknown Case'),
                'court': court_info['court'],
                'court_name': court_info['name'],
                'court_level': court_info['level'],
                'date_filed': cluster.get('date_filed'),
                'jurisdiction': 'US',  # Federal
                'text': text_content,
                'plain_text': text_content,
                'judges': self.extract_judges(cl_opinion, cluster),
                'citations': cluster.get('citations', []),
                'cl_opinion_id': cl_opinion['id'],
                'cl_cluster_id': cluster.get('id'),
                'author': cl_opinion.get('author_str', ''),
                'type': cl_opinion.get('type', 'Unknown')
            }
            
            return case_data
            
        except Exception as e:
            logger.error(f"Error transforming CourtListener case: {e}")
            return None
    
    def extract_judges(self, opinion: dict, cluster: dict) -> list:
        """Extract judge information from CourtListener data"""
        
        judges = []
        
        # From opinion author
        if opinion.get('author_str'):
            judges.append({
                'name': opinion['author_str'],
                'role': 'author'
            })
        
        # From cluster judges
        if cluster.get('judges'):
            for judge in cluster['judges']:
                if isinstance(judge, dict):
                    judges.append({
                        'name': judge.get('name_full', judge.get('name_last', 'Unknown')),
                        'role': 'panel'
                    })
                elif isinstance(judge, str):
                    judges.append({
                        'name': judge,
                        'role': 'panel'
                    })
        
        return judges
    
    async def test_basic_processing(self) -> bool:
        """Test basic CourtListener processing through all storage systems"""
        
        print(f"\n🔄 BASIC COURTLISTENER PROCESSING TEST")
        print("=" * 60)
        
        try:
            # Get test cases
            test_cases = self.get_courtlistener_test_cases(5)
            
            if not test_cases:
                print("❌ No CourtListener test cases retrieved")
                return False
            
            print(f"\n📊 Processing {len(test_cases)} CourtListener cases")
            
            # Show case summary
            for i, case in enumerate(test_cases, 1):
                print(f"   {i}. {case.get('case_name', 'Unknown')} ({case.get('court', 'Unknown')})")
                print(f"      Text length: {len(case.get('text', '')):,} characters")
                print(f"      Judges: {len(case.get('judges', []))}")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            print(f"\n🔄 Processing through full pipeline...")
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed: {result}")
                return False
            
            # Verify all storage systems
            return await self.verify_all_storage_systems()
            
        except Exception as e:
            print(f"❌ Basic processing test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_all_storage_systems(self) -> bool:
        """Verify data exists in all 4 storage systems"""
        
        print(f"\n🔍 VERIFYING ALL STORAGE SYSTEMS")
        print("=" * 60)
        
        try:
            # 1. Supabase verification
            print(f"📊 1. SUPABASE VERIFICATION:")
            cases = self.supabase.table('cases').select('*').eq('batch_id', self.test_batch_id).execute()
            supabase_count = len(cases.data)
            print(f"   Cases stored: {supabase_count}")
            
            if supabase_count == 0:
                print(f"   ❌ No cases found in Supabase")
                return False
            
            # Show sample case data
            sample_case = cases.data[0]
            print(f"   Sample case: {sample_case.get('case_name', 'N/A')}")
            print(f"   Court: {sample_case.get('court', 'N/A')}")
            print(f"   Source: {sample_case.get('source', 'N/A')}")
            print(f"   GCS path: {sample_case.get('gcs_path', 'N/A')}")
            
            # 2. GCS verification
            print(f"\n📊 2. GCS VERIFICATION:")
            gcs_paths = [case.get('gcs_path') for case in cases.data if case.get('gcs_path')]
            gcs_files_exist = 0
            
            for gcs_path in gcs_paths:
                if gcs_path:
                    try:
                        if gcs_path.startswith('gs://'):
                            bucket_path = gcs_path.split('/', 3)[-1]
                        else:
                            bucket_path = gcs_path
                        
                        blob = self.gcs_client.bucket.blob(bucket_path)
                        if blob.exists():
                            gcs_files_exist += 1
                    except:
                        pass
            
            print(f"   GCS files verified: {gcs_files_exist}/{len(gcs_paths)}")
            
            # 3. Pinecone verification
            print(f"\n📊 3. PINECONE VERIFICATION:")
            # Count vectors for this batch (approximate)
            total_vectors = 0
            for case in cases.data:
                vector_count = case.get('vector_count', 0)
                total_vectors += vector_count
            
            print(f"   Total vectors created: {total_vectors}")
            
            # 4. Neo4j verification
            print(f"\n📊 4. NEO4J VERIFICATION:")
            with self.neo4j_client.driver.session() as session:
                # Count case nodes
                result = session.run('MATCH (c:Case {batch_id: $batch_id}) RETURN count(c) as count', 
                                   batch_id=self.test_batch_id)
                neo4j_count = result.single()['count']
                print(f"   Case nodes: {neo4j_count}")
                
                # Check for enhanced data
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})
                    WHERE c.case_name IS NOT NULL AND c.case_name <> ""
                    RETURN count(c) as enriched_count
                ''', batch_id=self.test_batch_id)
                enriched_count = result.single()['enriched_count']
                print(f"   Cases with metadata: {enriched_count}")
                
                # Check relationships
                result = session.run('''
                    MATCH (c:Case {batch_id: $batch_id})-[r]-()
                    RETURN count(r) as rel_count
                ''', batch_id=self.test_batch_id)
                rel_count = result.single()['rel_count']
                print(f"   Relationships: {rel_count}")
            
            # 5. Cross-system consistency check
            print(f"\n📊 5. CROSS-SYSTEM CONSISTENCY:")
            consistency_issues = []
            
            if supabase_count != gcs_files_exist:
                consistency_issues.append(f"Supabase ({supabase_count}) vs GCS ({gcs_files_exist})")
            
            if supabase_count != neo4j_count:
                consistency_issues.append(f"Supabase ({supabase_count}) vs Neo4j ({neo4j_count})")
            
            if total_vectors == 0:
                consistency_issues.append("No vectors created in Pinecone")
            
            if consistency_issues:
                print(f"   ❌ Consistency issues:")
                for issue in consistency_issues:
                    print(f"      - {issue}")
                return False
            else:
                print(f"   ✅ Perfect consistency across all systems")
            
            # 6. CourtListener-specific verification
            print(f"\n📊 6. COURTLISTENER-SPECIFIC VERIFICATION:")
            
            # Check for federal courts
            federal_courts = set(case.get('court') for case in cases.data)
            print(f"   Federal courts: {federal_courts}")
            
            # Check for judge information
            cases_with_judges = sum(1 for case in cases.data if case.get('judges'))
            print(f"   Cases with judge info: {cases_with_judges}/{supabase_count}")
            
            # Check court levels
            court_levels = set(case.get('court_level') for case in cases.data if case.get('court_level'))
            print(f"   Court levels: {court_levels}")
            
            print(f"\n🎉 ALL STORAGE SYSTEMS VERIFIED!")
            print(f"✅ CourtListener data successfully processed across all 4 systems")
            
            return True
            
        except Exception as e:
            print(f"❌ Storage verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from all systems"""
        
        print(f"\n🧹 CLEANING UP TEST DATA")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean GCS (test documents)
            blobs = self.gcs_client.bucket.list_blobs(prefix=f"legal/us/cases/{self.test_batch_id}")
            for blob in blobs:
                blob.delete()
            print("   ✅ Cleaned GCS test documents")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
            print("   ✅ Cleaned Neo4j test nodes")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run CourtListener basic processing test"""
    
    print("🧪 COURTLISTENER BASIC PROCESSING TEST")
    print("=" * 80)
    print("🎯 Testing CourtListener data across all 4 storage systems")
    
    test = CourtListenerBasicTest()
    
    try:
        # Run the test
        success = await test.test_basic_processing()
        
        if success:
            print(f"\n🎉 COURTLISTENER BASIC PROCESSING: SUCCESS!")
            print(f"✅ All storage systems working with CourtListener data")
            return True
        else:
            print(f"\n❌ COURTLISTENER BASIC PROCESSING: FAILED!")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
