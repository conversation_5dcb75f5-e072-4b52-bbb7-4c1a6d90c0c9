#!/usr/bin/env python3
"""
Debug Text Extraction Issue
Find and fix why "No text found for context analysis" in real data
"""

import requests
import os
from dotenv import load_dotenv
from judge_relationship_enhancer import JudgeRelationshipEnhancer

def debug_courtlistener_text_extraction():
    """Debug why CourtListener text is not reaching judge extraction"""
    
    print("🔍 DEBUGGING COURTLISTENER TEXT EXTRACTION")
    print("=" * 60)
    
    load_dotenv()
    cl_api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not cl_api_key:
        print("❌ No CourtListener API key found")
        return False
    
    headers = {
        'Authorization': f'Token {cl_api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    try:
        # Get a single case for debugging
        response = requests.get(
            "https://www.courtlistener.com/api/rest/v4/opinions/",
            headers=headers,
            params={
                'court': 'ca5',
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 1
            },
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return False
        
        results = response.json().get('results', [])
        
        if not results:
            print("❌ No results from API")
            return False
        
        case = results[0]
        
        print(f"📄 DEBUG CASE: {case.get('case_name', 'Unknown')}")
        print(f"   Court: {case.get('court', 'Unknown')}")
        print(f"   Date: {case.get('date_filed', 'Unknown')}")
        
        # Check all text fields
        plain_text = case.get('plain_text', '')
        html_content = case.get('html', '')
        
        print(f"\n📝 TEXT FIELD ANALYSIS:")
        print(f"   plain_text: {len(plain_text)} characters")
        if plain_text:
            print(f"      Sample: {plain_text[:200]}...")
        else:
            print(f"      ❌ No plain_text content")
        
        print(f"   html: {len(html_content)} characters")
        if html_content:
            print(f"      Sample: {html_content[:200]}...")
        else:
            print(f"      ❌ No html content")
        
        # Test what our pipeline would use
        pipeline_text = plain_text or html_content
        print(f"\n🔄 PIPELINE TEXT SELECTION:")
        print(f"   Selected text: {len(pipeline_text)} characters")
        
        if not pipeline_text:
            print(f"   ❌ ISSUE FOUND: No text content available")
            return False
        
        # Test judge extraction directly
        print(f"\n🧪 TESTING JUDGE EXTRACTION DIRECTLY:")
        
        enhancer = JudgeRelationshipEnhancer()
        judges = enhancer._extract_judges_from_text(pipeline_text)
        
        print(f"   Judges extracted: {len(judges)}")
        for judge in judges:
            print(f"      - {judge['name']} (role: {judge['role']})")
        
        enhancer.close()
        
        # Create test case format
        test_case = {
            'id': f"debug_case_{case.get('id', 'unknown')}",
            'source': 'courtlistener',
            'case_name': case.get('case_name', 'Unknown'),
            'court': case.get('court', ''),
            'court_name': f"Court {case.get('court', '')}",
            'date_filed': case.get('date_filed', ''),
            'jurisdiction': 'US',
            'text': pipeline_text,  # This is the key field
            'precedential_status': case.get('precedential_status', 'Unknown')
        }
        
        print(f"\n📋 TEST CASE FORMAT:")
        print(f"   ID: {test_case['id']}")
        print(f"   Text length: {len(test_case['text'])} characters")
        print(f"   Has text: {'✅' if test_case['text'] else '❌'}")
        
        return len(test_case['text']) > 0
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        return False


def test_pipeline_text_flow():
    """Test how text flows through the pipeline"""
    
    print(f"\n🔄 TESTING PIPELINE TEXT FLOW")
    print("=" * 60)
    
    # Create a simple test case with known text
    test_case = {
        'id': 'pipeline_test_case',
        'source': 'courtlistener',
        'case_name': 'Test v. Pipeline',
        'court': 'ca5',
        'court_name': 'U.S. Court of Appeals, Fifth Circuit',
        'date_filed': '2023-01-01',
        'jurisdiction': 'US',
        'text': '''
        UNITED STATES COURT OF APPEALS
        FOR THE FIFTH CIRCUIT
        
        TEST v. PIPELINE
        
        Before SMITH, JONES, and WILSON, Circuit Judges.
        
        Circuit Judge John Smith delivered the opinion of the court.
        
        We reverse the district court's judgment.
        ''',
        'precedential_status': 'Published'
    }
    
    print(f"📄 TEST CASE:")
    print(f"   Name: {test_case['case_name']}")
    print(f"   Text length: {len(test_case['text'])} characters")
    print(f"   Expected judges: Smith, Jones, Wilson, John Smith")
    
    # Test judge extraction
    enhancer = JudgeRelationshipEnhancer()
    judges = enhancer._extract_judges_from_text(test_case['text'])
    
    print(f"\n🧪 JUDGE EXTRACTION RESULT:")
    print(f"   Judges found: {len(judges)}")
    
    for judge in judges:
        words = len(judge['name'].split())
        word_status = "✅ Full" if words > 1 else "⚠️ Partial"
        print(f"      - {judge['name']} ({word_status}, {words} words, role: {judge['role']})")
    
    enhancer.close()
    
    success = len(judges) > 0
    
    if success:
        print(f"\n✅ PIPELINE TEXT FLOW: WORKING")
        print(f"✅ Judge extraction functioning correctly")
    else:
        print(f"\n❌ PIPELINE TEXT FLOW: BROKEN")
        print(f"❌ Judge extraction not working")
    
    return success


def main():
    """Debug and fix text extraction issue"""
    
    print("🔧 TEXT EXTRACTION DEBUG AND FIX")
    print("=" * 80)
    print("🎯 Finding why 'No text found for context analysis'")
    
    # Debug CourtListener text extraction
    cl_text_ok = debug_courtlistener_text_extraction()
    
    # Test pipeline text flow
    pipeline_ok = test_pipeline_text_flow()
    
    print(f"\n📊 DEBUG SUMMARY:")
    print(f"   CourtListener text extraction: {'✅' if cl_text_ok else '❌'}")
    print(f"   Pipeline text flow: {'✅' if pipeline_ok else '❌'}")
    
    if cl_text_ok and pipeline_ok:
        print(f"\n✅ TEXT EXTRACTION: WORKING")
        print(f"✅ Ready to re-run enhanced real data test")
        return True
    else:
        print(f"\n❌ TEXT EXTRACTION: ISSUES FOUND")
        if not cl_text_ok:
            print(f"❌ CourtListener API not returning text content")
        if not pipeline_ok:
            print(f"❌ Judge extraction pipeline broken")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
