#!/usr/bin/env python3
"""
Investigate Harris Extraction
Check if <PERSON><PERSON>" and "<PERSON>" are actually different judges or extraction errors
"""

import re

def analyze_harris_extraction():
    """Analyze the Harris extraction issue"""
    
    print("🔍 INVESTIGATING HARRIS EXTRACTION")
    print("=" * 60)
    
    # The sample text we saw from the CourtListener test
    sample_text = """
                             2025 UT App 114



                  THE UTAH COURT OF APPEALS

                     COTTONWOOD HEIGHTS CITY,
                            Petitioner,
                                 v.
                    HONORABLE KRISTINE JOHNSON,
                           Respondent.


    Before RYAN, HARRIS, and COTTONWOOD, Circuit Judges.
    
    RYAN, Circuit Judge:
    
    This case involves... Judge <PERSON> delivered the opinion.
    
    Circuit Judge <PERSON> concurred in the judgment.
    """
    
    print("📄 ANALYZING SAMPLE TEXT:")
    print(f"   {sample_text[:200]}...")
    
    # Test current patterns
    from judge_relationship_enhancer import JudgeRelationshipEnhancer
    
    enhancer = JudgeRelationshipEnhancer()
    
    print(f"\n🔍 PATTERN ANALYSIS:")
    
    # Test each pattern individually
    patterns = enhancer.judge_patterns
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, sample_text, re.IGNORECASE)
        for match in matches:
            extracted = match.group(1).strip() if match.groups() else match.group(0)
            context_start = max(0, match.start() - 30)
            context_end = min(len(sample_text), match.end() + 30)
            context = sample_text[context_start:context_end].replace('\n', ' ').strip()
            
            print(f"      → Extracted: '{extracted}'")
            print(f"      → Context: ...{context}...")
    
    # The real issue: Let's check what the actual CourtListener text contains
    print(f"\n🎯 LIKELY ISSUE ANALYSIS:")
    print(f"   1. 'Harris' extracted from: 'Before RYAN, HARRIS, and COTTONWOOD, Circuit Judges'")
    print(f"   2. 'Ryan M. Harris' extracted from: Different part of text or different case")
    print(f"   3. These might be:")
    print(f"      a) Same judge (Ryan M. Harris) mentioned in different formats")
    print(f"      b) Different judges with same last name (unlikely)")
    print(f"      c) Extraction error creating duplicates")
    
    # Test the specific problematic patterns
    print(f"\n🔬 TESTING SPECIFIC PATTERNS:")
    
    # Pattern that would extract "HARRIS" from panel listing
    panel_pattern = r'Before[^.]*?([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),\s+(?:Circuit\s+)?Judge'
    panel_matches = re.findall(panel_pattern, sample_text, re.IGNORECASE)
    print(f"   Panel pattern matches: {panel_matches}")
    
    # Pattern that would extract full names
    full_name_pattern = r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)\s+(?:delivered|wrote|authored)'
    full_name_matches = re.findall(full_name_pattern, sample_text, re.IGNORECASE)
    print(f"   Full name pattern matches: {full_name_matches}")
    
    print(f"\n💡 RECOMMENDED SOLUTION:")
    print(f"   1. Add deduplication logic to merge 'Harris' and 'Ryan M. Harris'")
    print(f"   2. Prefer full names over partial names")
    print(f"   3. Check if extracted names are subsets of other extracted names")
    
    enhancer.close()


def test_name_deduplication():
    """Test name deduplication logic"""
    
    print(f"\n🔧 TESTING NAME DEDUPLICATION")
    print("=" * 60)
    
    # Simulate the extracted judges
    extracted_judges = [
        {'name': 'Harris', 'role': 'participated', 'source': 'text_extraction'},
        {'name': 'Ryan M. Harris', 'role': 'author', 'source': 'text_extraction'},
    ]
    
    print(f"📊 BEFORE DEDUPLICATION:")
    for judge in extracted_judges:
        print(f"   - {judge['name']} ({judge['role']})")
    
    # Deduplication logic
    deduplicated_judges = []
    seen_names = set()
    
    # Sort by name length (longer names first to prefer full names)
    sorted_judges = sorted(extracted_judges, key=lambda x: len(x['name']), reverse=True)
    
    for judge in sorted_judges:
        name = judge['name']
        
        # Check if this name is a subset of any already seen name
        is_subset = False
        for seen_name in seen_names:
            if name.lower() in seen_name.lower() or seen_name.lower() in name.lower():
                print(f"   🔄 '{name}' conflicts with '{seen_name}'")
                # Prefer the longer, more complete name
                if len(name) > len(seen_name):
                    # Remove the shorter name and add the longer one
                    deduplicated_judges = [j for j in deduplicated_judges if j['name'] != seen_name]
                    seen_names.remove(seen_name)
                    deduplicated_judges.append(judge)
                    seen_names.add(name)
                    print(f"   ✅ Keeping longer name: '{name}'")
                else:
                    print(f"   ✅ Keeping existing longer name: '{seen_name}'")
                is_subset = True
                break
        
        if not is_subset:
            deduplicated_judges.append(judge)
            seen_names.add(name)
    
    print(f"\n📊 AFTER DEDUPLICATION:")
    for judge in deduplicated_judges:
        print(f"   - {judge['name']} ({judge['role']})")
    
    print(f"\n🎯 DEDUPLICATION RESULT:")
    print(f"   Before: {len(extracted_judges)} judges")
    print(f"   After: {len(deduplicated_judges)} judges")
    print(f"   Success: {'✅' if len(deduplicated_judges) == 1 else '❌'}")


if __name__ == "__main__":
    print("🧪 HARRIS EXTRACTION INVESTIGATION")
    print("=" * 80)
    
    # Analyze the extraction issue
    analyze_harris_extraction()
    
    # Test deduplication solution
    test_name_deduplication()
    
    print(f"\n🎯 CONCLUSION:")
    print(f"   The 'Harris' + 'Ryan M. Harris' issue is likely:")
    print(f"   1. Same judge mentioned in different formats")
    print(f"   2. Panel listing: 'HARRIS, Circuit Judge'")
    print(f"   3. Opinion attribution: 'Judge Ryan M. Harris delivered'")
    print(f"   4. Solution: Implement name deduplication with full name preference")
