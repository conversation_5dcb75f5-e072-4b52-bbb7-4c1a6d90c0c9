#!/usr/bin/env python3
"""
CAP Texas Cases Analysis
Simple script to count actual Texas cases available in CAP files
"""

import os
import gzip
import json
import logging
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CAPTexasAnalyzer:
    """Analyzer for Texas cases in CAP files."""
    
    def __init__(self, data_dir: str = "data/caselaw_access_project"):
        """Initialize the analyzer."""
        self.data_dir = Path(data_dir)
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'total_cases_all_states': 0,
            'texas_cases': 0,
            'texas_courts': set(),
            'texas_years': defaultdict(int),
            'texas_case_types': defaultdict(int),
            'sample_texas_cases': [],
            'file_breakdown': {},
            'errors': [],
            'sample_all_cases': [],  # Sample cases from all states
            'jurisdictions_found': defaultdict(int),
            'courts_found': defaultdict(int)
        }
    
    def is_texas_case(self, case_data: Dict) -> bool:
        """Determine if a case is from Texas."""
        
        # Check jurisdiction field
        jurisdiction = case_data.get('jurisdiction', {})
        if isinstance(jurisdiction, dict):
            name = jurisdiction.get('name', '').lower()
            name_long = jurisdiction.get('name_long', '').lower()
            if 'texas' in name or 'texas' in name_long or 'tx' in name:
                return True
        elif isinstance(jurisdiction, str):
            if 'texas' in jurisdiction.lower() or 'tx' in jurisdiction.lower():
                return True
        
        # Check court field
        court = case_data.get('court', {})
        if isinstance(court, dict):
            court_name = court.get('name', '').lower()
            court_name_long = court.get('name_long', '').lower()
            if 'texas' in court_name or 'texas' in court_name_long:
                return True
        elif isinstance(court, str):
            if 'texas' in court.lower():
                return True
        
        # Check reporter field (Texas reporters)
        reporter = case_data.get('reporter', {})
        if isinstance(reporter, dict):
            reporter_name = reporter.get('full_name', '').lower()
            if any(tx_reporter in reporter_name for tx_reporter in [
                'texas', 'tex.', 's.w.', 'southwestern'
            ]):
                return True
        
        # Check case name for Texas indicators
        name = case_data.get('name', '').lower()
        if 'state of texas' in name or 'texas v.' in name or 'v. texas' in name:
            return True
        
        return False
    
    def extract_case_info(self, case_data: Dict) -> Dict:
        """Extract key information from a Texas case."""
        
        # Get court information
        court_info = case_data.get('court', {})
        if isinstance(court_info, dict):
            court_name = court_info.get('name', 'Unknown Court')
        else:
            court_name = str(court_info) if court_info else 'Unknown Court'
        
        # Get year
        decision_date = case_data.get('decision_date')
        year = 'Unknown'
        if decision_date:
            try:
                year = decision_date.split('-')[0]
            except:
                pass
        
        # Get case type/nature
        case_type = 'Unknown'
        if 'criminal' in case_data.get('name', '').lower():
            case_type = 'Criminal'
        elif 'civil' in case_data.get('name', '').lower():
            case_type = 'Civil'
        elif any(keyword in case_data.get('name', '').lower() for keyword in [
            'divorce', 'custody', 'family'
        ]):
            case_type = 'Family'
        elif any(keyword in case_data.get('name', '').lower() for keyword in [
            'injury', 'negligence', 'accident', 'malpractice'
        ]):
            case_type = 'Personal Injury'
        
        return {
            'id': case_data.get('id', 'Unknown'),
            'name': case_data.get('name', 'Unknown Case'),
            'court': court_name,
            'year': year,
            'case_type': case_type,
            'decision_date': decision_date
        }
    
    def analyze_cap_file(self, file_path: Path) -> Dict:
        """Analyze a single CAP file for Texas cases."""
        
        logger.info(f"📁 Analyzing: {file_path.name}")
        
        file_stats = {
            'file_name': file_path.name,
            'total_cases': 0,
            'texas_cases': 0,
            'texas_cases_list': [],
            'error': None
        }
        
        try:
            # Handle compressed files
            if file_path.suffix == '.gz':
                opener = gzip.open
            else:
                opener = open
            
            with opener(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        if line.strip():
                            case_data = json.loads(line)
                            file_stats['total_cases'] += 1

                            # Collect sample cases and jurisdiction info for analysis
                            if len(self.stats['sample_all_cases']) < 20:
                                sample_info = {
                                    'name': case_data.get('name', 'Unknown'),
                                    'jurisdiction': case_data.get('jurisdiction', {}),
                                    'court': case_data.get('court', {}),
                                    'reporter': case_data.get('reporter', {})
                                }
                                self.stats['sample_all_cases'].append(sample_info)

                            # Track jurisdictions and courts
                            jurisdiction = case_data.get('jurisdiction', {})
                            if isinstance(jurisdiction, dict):
                                jur_name = jurisdiction.get('name', 'Unknown')
                                self.stats['jurisdictions_found'][jur_name] += 1

                            court = case_data.get('court', {})
                            if isinstance(court, dict):
                                court_name = court.get('name', 'Unknown')
                                self.stats['courts_found'][court_name] += 1

                            # Check if this is a Texas case
                            if self.is_texas_case(case_data):
                                file_stats['texas_cases'] += 1

                                # Extract case information
                                case_info = self.extract_case_info(case_data)
                                file_stats['texas_cases_list'].append(case_info)

                                # Update global stats
                                self.stats['texas_courts'].add(case_info['court'])
                                self.stats['texas_years'][case_info['year']] += 1
                                self.stats['texas_case_types'][case_info['case_type']] += 1

                                # Keep sample cases
                                if len(self.stats['sample_texas_cases']) < 10:
                                    self.stats['sample_texas_cases'].append(case_info)
                    
                    except json.JSONDecodeError as e:
                        if line_num <= 5:  # Only log first few JSON errors
                            logger.warning(f"JSON decode error in {file_path.name} line {line_num}: {e}")
                    except Exception as e:
                        logger.warning(f"Error processing line {line_num} in {file_path.name}: {e}")
        
        except Exception as e:
            error_msg = f"Error reading {file_path.name}: {e}"
            logger.error(error_msg)
            file_stats['error'] = error_msg
            self.stats['errors'].append(error_msg)
        
        logger.info(f"  📊 {file_path.name}: {file_stats['total_cases']:,} total, {file_stats['texas_cases']:,} Texas cases")
        
        return file_stats
    
    def analyze_all_files(self) -> Dict:
        """Analyze all CAP files for Texas cases."""
        
        logger.info("🔍 ANALYZING CAP FILES FOR TEXAS CASES")
        logger.info("=" * 60)
        
        if not self.data_dir.exists():
            logger.error(f"CAP data directory not found: {self.data_dir}")
            return self.stats
        
        # Find all CAP files
        cap_files = []
        for pattern in ['*.jsonl', '*.jsonl.gz', '*.json', '*.json.gz']:
            cap_files.extend(self.data_dir.glob(pattern))
        
        cap_files.sort()
        self.stats['total_files'] = len(cap_files)
        
        logger.info(f"📁 Found {len(cap_files)} CAP files to analyze")
        print()
        
        # Analyze each file
        for file_path in cap_files:
            file_stats = self.analyze_cap_file(file_path)
            self.stats['file_breakdown'][file_path.name] = file_stats
            self.stats['processed_files'] += 1
            self.stats['total_cases_all_states'] += file_stats['total_cases']
            self.stats['texas_cases'] += file_stats['texas_cases']
        
        return self.stats
    
    def print_analysis_results(self):
        """Print comprehensive analysis results."""
        
        print("\n" + "="*80)
        print("🤠 TEXAS CASES IN CAP FILES - ANALYSIS RESULTS")
        print("="*80)
        
        print(f"📁 Files Analyzed: {self.stats['processed_files']}/{self.stats['total_files']}")
        print(f"📊 Total Cases (All States): {self.stats['total_cases_all_states']:,}")
        print(f"🎯 Texas Cases Found: {self.stats['texas_cases']:,}")
        
        if self.stats['total_cases_all_states'] > 0:
            texas_percentage = (self.stats['texas_cases'] / self.stats['total_cases_all_states']) * 100
            print(f"📈 Texas Percentage: {texas_percentage:.2f}%")
        
        print()
        
        # File breakdown
        print("📋 FILE BREAKDOWN:")
        print("-" * 50)
        for file_name, file_stats in self.stats['file_breakdown'].items():
            if file_stats['texas_cases'] > 0:
                print(f"  {file_name}: {file_stats['texas_cases']:,} Texas cases ({file_stats['total_cases']:,} total)")
        
        print()
        
        # Texas courts
        if self.stats['texas_courts']:
            print(f"🏛️  TEXAS COURTS ({len(self.stats['texas_courts'])}):")
            for court in sorted(self.stats['texas_courts'])[:10]:  # Show top 10
                print(f"  • {court}")
            if len(self.stats['texas_courts']) > 10:
                print(f"  ... and {len(self.stats['texas_courts']) - 10} more courts")
        
        print()
        
        # Year distribution
        if self.stats['texas_years']:
            print("📅 YEAR DISTRIBUTION (Top 10):")
            sorted_years = sorted(self.stats['texas_years'].items(), key=lambda x: x[1], reverse=True)
            for year, count in sorted_years[:10]:
                print(f"  {year}: {count:,} cases")
        
        print()
        
        # Case type distribution
        if self.stats['texas_case_types']:
            print("⚖️  CASE TYPE DISTRIBUTION:")
            sorted_types = sorted(self.stats['texas_case_types'].items(), key=lambda x: x[1], reverse=True)
            for case_type, count in sorted_types:
                percentage = (count / self.stats['texas_cases']) * 100 if self.stats['texas_cases'] > 0 else 0
                print(f"  {case_type}: {count:,} ({percentage:.1f}%)")
        
        print()
        
        # Sample cases
        if self.stats['sample_texas_cases']:
            print("📝 SAMPLE TEXAS CASES:")
            for i, case in enumerate(self.stats['sample_texas_cases'][:5], 1):
                print(f"  {i}. {case['name']} ({case['year']})")
                print(f"     Court: {case['court']}")
                print(f"     Type: {case['case_type']}")
                print()
        
        # Errors
        if self.stats['errors']:
            print(f"⚠️  ERRORS ENCOUNTERED: {len(self.stats['errors'])}")
            for error in self.stats['errors'][:3]:  # Show first 3 errors
                print(f"  • {error}")
        
        print()
        
        # Assessment
        print("🎯 ASSESSMENT:")
        if self.stats['texas_cases'] == 0:
            print("❌ No Texas cases found - may need to adjust detection logic")
        elif self.stats['texas_cases'] < 1000:
            print(f"⚠️  Low count ({self.stats['texas_cases']:,}) - may be limited dataset or detection issues")
        elif self.stats['texas_cases'] < 5000:
            print(f"✅ Moderate count ({self.stats['texas_cases']:,}) - reasonable for specialized dataset")
        else:
            print(f"🎉 High count ({self.stats['texas_cases']:,}) - excellent dataset for processing!")
        
        print(f"\n💡 CONCLUSION: CAP files contain {self.stats['texas_cases']:,} Texas cases available for processing")

def main():
    """Main execution function."""
    
    analyzer = CAPTexasAnalyzer()
    
    # Run analysis
    results = analyzer.analyze_all_files()
    
    # Print results
    analyzer.print_analysis_results()
    
    # Return count for programmatic use
    return results['texas_cases']

if __name__ == "__main__":
    texas_case_count = main()
