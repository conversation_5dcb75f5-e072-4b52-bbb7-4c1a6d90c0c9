#!/usr/bin/env python3
"""
Hybrid Production Pipeline Test
Tests the enhanced ChunkedCourtListenerProcessor with client-side filtering
"""

import asyncio
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from dotenv import load_dotenv

# Import required components
from supabase import create_client, Client
from google.cloud import storage
from pinecone import Pinecone
from neo4j import AsyncGraphDatabase
import httpx

from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridProductionPipelineTester:
    """Tests the hybrid processing pipeline with real CourtListener data"""
    
    def __init__(self):
        self.test_results = {}
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize all database clients"""
        logger.info("🔧 Initializing database clients for testing...")
        
        # Supabase client
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        self.supabase: Client = create_client(supabase_url, supabase_key)
        
        # GCS client
        self.gcs_client = storage.Client()
        
        # Pinecone client
        pinecone_api_key = os.getenv('PINECONE_API_KEY')
        self.pinecone_client = Pinecone(api_key=pinecone_api_key)
        
        # Neo4j client
        neo4j_uri = os.getenv('NEO4J_URI')
        neo4j_user = os.getenv('NEO4J_USER')
        neo4j_password = os.getenv('NEO4J_PASSWORD')
        self.neo4j_driver = AsyncGraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        logger.info("✅ All database clients initialized")
    
    async def test_hybrid_processing_pipeline(self) -> Dict[str, Any]:
        """Test the complete hybrid processing pipeline"""
        logger.info("🚀 STARTING HYBRID PRODUCTION PIPELINE TEST")
        logger.info("=" * 80)
        
        test_start_time = datetime.now()
        
        try:
            # Step 1: Verify database cleanup
            await self._verify_clean_databases()
            
            # Step 2: Run hybrid processing pipeline
            processing_results = await self._run_hybrid_processing()
            
            # Step 3: Analyze results
            analysis_results = await self._analyze_processing_results()
            
            # Step 4: Verify cross-system consistency
            consistency_results = await self._verify_cross_system_consistency()
            
            # Compile final results
            self.test_results = {
                'test_duration': datetime.now() - test_start_time,
                'processing_results': processing_results,
                'analysis_results': analysis_results,
                'consistency_results': consistency_results,
                'success': True
            }
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Hybrid pipeline test failed: {e}")
            self.test_results = {
                'test_duration': datetime.now() - test_start_time,
                'error': str(e),
                'success': False
            }
            return self.test_results
    
    async def _verify_clean_databases(self):
        """Verify all databases are clean before testing"""
        logger.info("\n🔍 Verifying Clean Database State")
        logger.info("-" * 50)
        
        # Check Supabase
        supabase_count = self.supabase.table('cases').select('id', count='exact').execute()
        cases_count = supabase_count.count if supabase_count.count else 0
        logger.info(f"   Supabase cases: {cases_count}")
        
        # Check Neo4j
        async with self.neo4j_driver.session() as session:
            node_result = await session.run("MATCH (n) RETURN count(n) as count")
            node_record = await node_result.single()
            nodes_count = node_record['count'] if node_record else 0
            logger.info(f"   Neo4j nodes: {nodes_count}")
        
        # Note: GCS and Pinecone cleanup may still be in progress
        logger.info("   ℹ️ GCS and Pinecone cleanup may still be in progress")
        logger.info("   ✅ Database state verified for testing")
    
    async def _run_hybrid_processing(self) -> Dict[str, Any]:
        """Run the hybrid processing pipeline with real data"""
        logger.info("\n🔄 Running Hybrid Processing Pipeline")
        logger.info("-" * 50)
        
        # Configure for small-scale test (50-60 cases)
        api_key = os.getenv('COURTLISTENER_API_KEY')
        
        # Use recent date range to get fresh data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days
        
        logger.info(f"   📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        logger.info(f"   🎯 Target: 50-60 real CourtListener cases")
        
        # Initialize processor with small batch size for testing
        async with ChunkedCourtListenerProcessor(
            api_key=api_key,
            supabase_client=self.supabase,
            gcs_client=self.gcs_client,
            pinecone_client=self.pinecone_client,
            neo4j_client=self.neo4j_driver,
            chunk_size=60,  # Small chunk size to limit total cases
            batch_size=20   # Small batches for testing
        ) as processor:
            
            # Process with custom date range
            results = await processor.process_jurisdiction_chunked(
                jurisdiction='tx',
                start_year=start_date.year,
                end_year=end_date.year,
                resume=False  # Fresh start
            )
            
            logger.info(f"   ✅ Processing completed: {results}")
            return results
    
    async def _analyze_processing_results(self) -> Dict[str, Any]:
        """Analyze the processing results for batch exclusivity and judge extraction"""
        logger.info("\n📊 Analyzing Processing Results")
        logger.info("-" * 50)
        
        analysis = {
            'total_cases_processed': 0,
            'primary_batch_cases': 0,
            'secondary_batch_cases': 0,
            'judge_extraction_methods': {
                'api_people': 0,
                'api_cluster': 0,
                'text_plain_text': 0
            },
            'people_api_success_rate': 0,
            'court_metadata_enhanced': 0
        }
        
        try:
            # Get all processed cases from Supabase
            cases_result = self.supabase.table('cases').select('*').execute()
            cases = cases_result.data if cases_result.data else []
            
            analysis['total_cases_processed'] = len(cases)
            logger.info(f"   Total cases processed: {len(cases)}")
            
            # Analyze batch types and judge extraction methods
            for case in cases:
                # Check batch type (if stored)
                batch_type = case.get('_batch_type', 'unknown')
                if batch_type == 'primary':
                    analysis['primary_batch_cases'] += 1
                elif batch_type == 'secondary':
                    analysis['secondary_batch_cases'] += 1
                
                # Check judge extraction method
                judge_metadata = case.get('judge_metadata', {})
                if isinstance(judge_metadata, dict):
                    extraction_method = judge_metadata.get('extraction_method', 'unknown')
                    if extraction_method in analysis['judge_extraction_methods']:
                        analysis['judge_extraction_methods'][extraction_method] += 1
                
                # Check for People API integration
                if case.get('author') and case.get('judge_name'):
                    analysis['people_api_success_rate'] += 1
                
                # Check for court metadata enhancement
                if case.get('court_metadata'):
                    analysis['court_metadata_enhanced'] += 1
            
            # Calculate rates
            if analysis['total_cases_processed'] > 0:
                analysis['people_api_success_rate'] = (analysis['people_api_success_rate'] / analysis['total_cases_processed']) * 100
                analysis['court_enhancement_rate'] = (analysis['court_metadata_enhanced'] / analysis['total_cases_processed']) * 100
            
            logger.info(f"   Primary batch cases: {analysis['primary_batch_cases']}")
            logger.info(f"   Secondary batch cases: {analysis['secondary_batch_cases']}")
            logger.info(f"   Judge extraction methods: {analysis['judge_extraction_methods']}")
            logger.info(f"   People API success rate: {analysis['people_api_success_rate']:.1f}%")
            logger.info(f"   Court metadata enhanced: {analysis['court_metadata_enhanced']} cases")
            
            return analysis
            
        except Exception as e:
            logger.error(f"   ❌ Analysis failed: {e}")
            return {'error': str(e)}
    
    async def _verify_cross_system_consistency(self) -> Dict[str, Any]:
        """Verify data consistency across all systems"""
        logger.info("\n🔗 Verifying Cross-System Consistency")
        logger.info("-" * 50)
        
        consistency = {
            'supabase_count': 0,
            'gcs_count': 0,
            'pinecone_count': 0,
            'neo4j_count': 0,
            'consistency_verified': False
        }
        
        try:
            # Supabase count
            supabase_result = self.supabase.table('cases').select('id', count='exact').execute()
            consistency['supabase_count'] = supabase_result.count if supabase_result.count else 0
            
            # GCS count (approximate - check for case files)
            try:
                bucket = self.gcs_client.bucket('texas-laws-personalinjury')
                blobs = list(bucket.list_blobs(prefix='cases/'))
                consistency['gcs_count'] = len(blobs)
            except Exception as e:
                logger.warning(f"   GCS count failed: {e}")
                consistency['gcs_count'] = 0
            
            # Pinecone count
            try:
                index = self.pinecone_client.Index('texas-laws-voyage3large')
                stats = index.describe_index_stats()
                consistency['pinecone_count'] = stats.total_vector_count
            except Exception as e:
                logger.warning(f"   Pinecone count failed: {e}")
                consistency['pinecone_count'] = 0
            
            # Neo4j count
            async with self.neo4j_driver.session() as session:
                result = await session.run("MATCH (n:Case) RETURN count(n) as count")
                record = await result.single()
                consistency['neo4j_count'] = record['count'] if record else 0
            
            logger.info(f"   Supabase: {consistency['supabase_count']} cases")
            logger.info(f"   GCS: {consistency['gcs_count']} files")
            logger.info(f"   Pinecone: {consistency['pinecone_count']} vectors")
            logger.info(f"   Neo4j: {consistency['neo4j_count']} nodes")
            
            # Check consistency (allowing for some variation due to processing)
            base_count = consistency['supabase_count']
            if base_count > 0:
                consistency['consistency_verified'] = (
                    consistency['neo4j_count'] >= base_count * 0.8 and  # Allow 20% variance
                    consistency['pinecone_count'] >= base_count * 0.8
                )
            
            if consistency['consistency_verified']:
                logger.info("   ✅ Cross-system consistency verified")
            else:
                logger.warning("   ⚠️ Cross-system consistency issues detected")
            
            return consistency
            
        except Exception as e:
            logger.error(f"   ❌ Consistency check failed: {e}")
            return {'error': str(e)}
    
    async def close(self):
        """Close all database connections"""
        await self.neo4j_driver.close()

async def main():
    """Main execution function"""
    tester = HybridProductionPipelineTester()
    
    try:
        # Run comprehensive test
        results = await tester.test_hybrid_processing_pipeline()
        
        logger.info("\n🎯 HYBRID PIPELINE TEST SUMMARY")
        logger.info("=" * 60)
        
        if results['success']:
            logger.info("✅ Hybrid processing pipeline test SUCCESSFUL")
            logger.info(f"   Test duration: {results['test_duration']}")
            logger.info(f"   Cases processed: {results.get('analysis_results', {}).get('total_cases_processed', 0)}")
            logger.info(f"   Primary batch: {results.get('analysis_results', {}).get('primary_batch_cases', 0)}")
            logger.info(f"   Secondary batch: {results.get('analysis_results', {}).get('secondary_batch_cases', 0)}")
        else:
            logger.error("❌ Hybrid processing pipeline test FAILED")
            logger.error(f"   Error: {results.get('error', 'Unknown error')}")
        
        return results
        
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
