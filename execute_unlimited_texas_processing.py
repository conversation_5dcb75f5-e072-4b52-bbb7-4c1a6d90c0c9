#!/usr/bin/env python3
"""
Unlimited Texas Processing with Enhanced Classification
Removes artificial limits and integrates Gemini full-text classification
"""

import os
import sys
import logging
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

load_dotenv()

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'unlimited_texas_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class UnlimitedTexasProcessor:
    """Processor that removes artificial limits and uses enhanced classification."""
    
    def __init__(self):
        """Initialize unlimited processor."""
        
        # Import processors
        from dual_source_coordinator import DualSourceCoordinator
        from enhanced_processing_with_classification import EnhancedCaseProcessor
        
        self.coordinator = DualSourceCoordinator()
        self.enhanced_classifier = EnhancedCaseProcessor()
        
        # Remove artificial limits
        self.remove_processing_limits()
        
        # Statistics tracking
        self.stats = {
            'start_time': None,
            'end_time': None,
            'court_listener_cases': 0,
            'cap_cases': 0,
            'total_unique_cases': 0,
            'high_confidence_classifications': 0,
            'medium_confidence_classifications': 0,
            'low_confidence_classifications': 0,
            'practice_area_distribution': {},
            'processing_errors': 0
        }
        
        logger.info("🚀 Unlimited Texas Processor initialized")
        logger.info("✅ Artificial limits removed")
        logger.info("✅ Enhanced Gemini classification enabled")
    
    def remove_processing_limits(self):
        """Remove artificial processing limits from all components."""
        
        logger.info("🔧 REMOVING PROCESSING LIMITS")
        logger.info("=" * 50)
        
        try:
            # Override Court Listener limits
            if hasattr(self.coordinator, 'court_listener_processor'):
                self.coordinator.court_listener_processor.target_cases_per_state = 100000
                self.coordinator.court_listener_processor.batch_size = 1000
                logger.info("✅ Court Listener: Increased to 100K cases, 1K batch size")
            
            # Override CAP limits
            if hasattr(self.coordinator, 'cap_processor'):
                self.coordinator.cap_processor.max_files_per_batch = 50000
                logger.info("✅ CAP: Increased to 50K files per batch")
            
            # Override any other limits in the coordinator
            self.coordinator.max_cases_per_jurisdiction = 100000
            
            logger.info("✅ All processing limits removed successfully")
            
        except Exception as e:
            logger.warning(f"Could not remove all limits: {e}")
    
    async def process_texas_comprehensive(self) -> Dict:
        """Process Texas with unlimited data and enhanced classification."""
        
        logger.info("🚀 STARTING UNLIMITED TEXAS PROCESSING")
        logger.info("=" * 70)
        
        self.stats['start_time'] = time.time()
        
        # Step 1: Check available data sources
        await self.analyze_available_data()
        
        # Step 2: Process with unlimited settings
        logger.info("📊 PROCESSING WITH UNLIMITED SETTINGS")
        logger.info("- Court Listener: No case limits")
        logger.info("- CAP: Process all available files")
        logger.info("- Classification: Gemini full-text during processing")
        logger.info()
        
        try:
            # Use the dual source coordinator with removed limits
            results = await self.coordinator.process_jurisdiction_comprehensive('tx')
            
            # Update statistics
            self.stats['court_listener_cases'] = getattr(results, 'cl_cases_processed', 0)
            self.stats['cap_cases'] = getattr(results, 'cap_cases_processed', 0)
            self.stats['total_unique_cases'] = getattr(results, 'unique_cases_total', 0)
            
            logger.info(f"✅ Processing completed successfully")
            logger.info(f"Court Listener cases: {self.stats['court_listener_cases']:,}")
            logger.info(f"CAP cases: {self.stats['cap_cases']:,}")
            logger.info(f"Total unique cases: {self.stats['total_unique_cases']:,}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive processing: {e}")
            self.stats['processing_errors'] += 1
            raise
        
        finally:
            self.stats['end_time'] = time.time()
    
    async def analyze_available_data(self):
        """Analyze what data is available for processing."""
        
        logger.info("🔍 ANALYZING AVAILABLE DATA SOURCES")
        logger.info("=" * 50)
        
        # Check CAP data availability
        cap_data_dir = "data/caselaw_access_project"  # Use relative path

        if os.path.exists(cap_data_dir):
            # Count CAP files (including compressed files)
            cap_files = []
            for root, dirs, files in os.walk(cap_data_dir):
                for file in files:
                    if file.endswith(('.json', '.html', '.jsonl', '.jsonl.gz', '.json.gz')):
                        cap_files.append(os.path.join(root, file))

            logger.info(f"📁 CAP files available: {len(cap_files):,}")

            # Estimate cases per file (each .jsonl.gz file contains thousands of cases)
            estimated_cap_cases = len(cap_files) * 1000  # More realistic estimate for compressed files
            logger.info(f"📊 Estimated CAP cases: {estimated_cap_cases:,}")
        else:
            logger.warning(f"⚠️ CAP data directory not found: {cap_data_dir}")
        
        # Estimate Court Listener availability
        logger.info("🔍 Estimating Court Listener data...")
        
        try:
            # This is a rough estimate based on Texas court system
            estimated_cl_cases = 50000  # Conservative estimate for Texas
            logger.info(f"📊 Estimated Court Listener cases: {estimated_cl_cases:,}")
        except Exception as e:
            logger.warning(f"Could not estimate Court Listener data: {e}")
        
        logger.info("✅ Data source analysis complete")
    
    async def verify_results(self) -> Dict:
        """Verify the processing results."""
        
        logger.info("🔍 VERIFYING PROCESSING RESULTS")
        logger.info("=" * 50)
        
        try:
            from supabase import create_client, Client
            
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_KEY')
            supabase: Client = create_client(url, key)
            
            # Get total case count
            result = supabase.table('cases').select('*', count='exact').eq('jurisdiction', 'tx').execute()
            total_cases = result.count
            
            logger.info(f"📊 Total Texas cases in database: {total_cases:,}")
            
            # Get practice area distribution
            cases_data = supabase.table('cases').select('primary_practice_area, classification_confidence').eq('jurisdiction', 'tx').execute()
            
            practice_areas = {}
            confidence_levels = {'VERY_HIGH': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'NONE': 0}
            
            for case in cases_data.data:
                # Practice area distribution
                area = case.get('primary_practice_area', 'None')
                practice_areas[area] = practice_areas.get(area, 0) + 1
                
                # Confidence distribution
                confidence = case.get('classification_confidence', 0.0)
                if confidence >= 0.9:
                    confidence_levels['VERY_HIGH'] += 1
                elif confidence >= 0.7:
                    confidence_levels['HIGH'] += 1
                elif confidence >= 0.5:
                    confidence_levels['MEDIUM'] += 1
                elif confidence > 0.0:
                    confidence_levels['LOW'] += 1
                else:
                    confidence_levels['NONE'] += 1
            
            # Update statistics
            self.stats['total_unique_cases'] = total_cases
            self.stats['practice_area_distribution'] = practice_areas
            self.stats['high_confidence_classifications'] = confidence_levels['VERY_HIGH'] + confidence_levels['HIGH']
            self.stats['medium_confidence_classifications'] = confidence_levels['MEDIUM']
            self.stats['low_confidence_classifications'] = confidence_levels['LOW']
            
            # Print results
            logger.info("\n📋 PRACTICE AREA DISTRIBUTION:")
            for area, count in sorted(practice_areas.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_cases) * 100 if total_cases > 0 else 0
                logger.info(f"  {area}: {count:,} ({percentage:.1f}%)")
            
            logger.info("\n🎯 CLASSIFICATION CONFIDENCE:")
            for level, count in confidence_levels.items():
                percentage = (count / total_cases) * 100 if total_cases > 0 else 0
                logger.info(f"  {level}: {count:,} ({percentage:.1f}%)")
            
            # Success metrics
            success_rate = (total_cases / 1000) * 100  # Compare to original 1000
            high_confidence_rate = ((confidence_levels['VERY_HIGH'] + confidence_levels['HIGH']) / total_cases) * 100 if total_cases > 0 else 0
            
            logger.info(f"\n✅ SUCCESS METRICS:")
            logger.info(f"Case count improvement: {success_rate:.1f}% (vs original 1000)")
            logger.info(f"High confidence classifications: {high_confidence_rate:.1f}%")
            
            return {
                'total_cases': total_cases,
                'practice_area_distribution': practice_areas,
                'confidence_distribution': confidence_levels,
                'success_rate': success_rate,
                'high_confidence_rate': high_confidence_rate
            }
            
        except Exception as e:
            logger.error(f"Error verifying results: {e}")
            return {'error': str(e)}
    
    def print_final_summary(self, verification_results: Dict):
        """Print comprehensive final summary."""
        
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] and self.stats['start_time'] else 0
        
        print("\n" + "="*80)
        print("🎉 UNLIMITED TEXAS PROCESSING COMPLETE")
        print("="*80)
        
        print(f"⏱️  Processing Time: {duration/60:.1f} minutes")
        print(f"📊 Total Cases: {verification_results.get('total_cases', 0):,}")
        print(f"🎯 Success Rate: {verification_results.get('success_rate', 0):.1f}% improvement")
        print(f"🔥 High Confidence: {verification_results.get('high_confidence_rate', 0):.1f}%")
        
        if verification_results.get('total_cases', 0) > 10000:
            print("🎉 EXCELLENT: Achieved 10K+ cases target!")
        elif verification_results.get('total_cases', 0) > 5000:
            print("✅ GOOD: Significant improvement in case coverage")
        else:
            print("⚠️ NEEDS INVESTIGATION: Case count still low")

async def main():
    """Main execution function."""
    
    print("🚀 UNLIMITED TEXAS PROCESSING WITH ENHANCED CLASSIFICATION")
    print("=" * 70)
    print("Removing artificial limits and integrating Gemini full-text classification")
    print()
    
    # Confirm processing
    response = input("This will process maximum available cases. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Processing cancelled")
        return
    
    processor = UnlimitedTexasProcessor()
    
    try:
        # Run unlimited processing
        results = await processor.process_texas_comprehensive()
        
        # Verify results
        verification = await processor.verify_results()
        
        # Print final summary
        processor.print_final_summary(verification)
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        print(f"❌ Processing failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
