#!/usr/bin/env python3
"""
Test CAP Data Enhanced Cross-System Tracking
Verifies that CAP data gets the same enhanced cross-system tracking as CourtListener
"""

import asyncio
import logging
import os
import sys
import json
import gzip
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.atomic_storage_pipeline import AtomicStoragePipeline
from src.processing.cross_system_validator import CrossSystemValidator
from source_agnostic_processor import CoherentCase, SourceAgnosticProcessor

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockGCSClient:
    """Enhanced mock GCS client that tracks storage operations"""
    def __init__(self):
        self.name = "mock_gcs"
        self.stored_objects = {}
        logger.info("✅ Enhanced Mock GCS client initialized")
    
    def simulate_storage(self, object_name: str, content: str):
        """Simulate storing an object with detailed tracking"""
        self.stored_objects[object_name] = {
            'content': content,
            'size': len(content),
            'timestamp': datetime.now().isoformat(),
            'content_type': 'application/json'
        }
        logger.debug(f"Mock GCS: Stored {object_name} ({len(content)} bytes)")
        return True
    
    def get_object_count(self) -> int:
        return len(self.stored_objects)
    
    def get_object(self, object_name: str):
        return self.stored_objects.get(object_name)
    
    def list_objects_for_case(self, case_id: str):
        """List all objects for a specific case"""
        return [obj for path, obj in self.stored_objects.items() if case_id in path]


class MockPineconeClient:
    """Enhanced mock Pinecone client that tracks vector operations"""
    def __init__(self):
        self.name = "mock_pinecone"
        self.stored_vectors = {}
        self.vector_metadata = {}
        logger.info("✅ Enhanced Mock Pinecone client initialized")
    
    def simulate_upsert(self, vectors: list):
        """Simulate upserting vectors with detailed tracking"""
        for vector in vectors:
            vector_id = vector['id']
            self.stored_vectors[vector_id] = {
                'values': vector['values'],
                'metadata': vector['metadata'],
                'timestamp': datetime.now().isoformat(),
                'dimensions': len(vector['values'])
            }
            
            # Track by case for easy lookup
            case_id = vector['metadata'].get('case_id')
            if case_id:
                if case_id not in self.vector_metadata:
                    self.vector_metadata[case_id] = []
                self.vector_metadata[case_id].append(vector_id)
        
        logger.debug(f"Mock Pinecone: Stored {len(vectors)} vectors")
        return True
    
    def get_vector_count(self) -> int:
        return len(self.stored_vectors)
    
    def get_vectors_for_case(self, case_id: str) -> list:
        """Get all vectors for a specific case"""
        return self.vector_metadata.get(case_id, [])
    
    def get_vector_details(self, vector_id: str):
        return self.stored_vectors.get(vector_id)


class MockNeo4jClient:
    """Enhanced mock Neo4j client that tracks node operations"""
    def __init__(self):
        self.name = "mock_neo4j"
        self.stored_nodes = {}
        self.relationships = []
        logger.info("✅ Enhanced Mock Neo4j client initialized")
    
    def simulate_node_creation(self, node_id: str, properties: dict):
        """Simulate creating a node with detailed tracking"""
        self.stored_nodes[node_id] = {
            'properties': properties,
            'labels': ['Case'],
            'timestamp': datetime.now().isoformat()
        }
        logger.debug(f"Mock Neo4j: Created node {node_id}")
        return True
    
    def get_node_count(self) -> int:
        return len(self.stored_nodes)
    
    def get_node(self, node_id: str):
        return self.stored_nodes.get(node_id)
    
    def get_nodes_by_case_id(self, case_id: str):
        """Get nodes for a specific case"""
        return [node for node_id, node in self.stored_nodes.items() 
                if node['properties'].get('case_id') == case_id]


async def setup_enhanced_test_clients():
    """Setup enhanced test clients for CAP testing"""
    logger.info("🔧 Setting up enhanced test clients for CAP testing...")
    
    # Load environment
    load_dotenv()
    
    # Supabase client (real)
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
    
    supabase: Client = create_client(supabase_url, supabase_key)
    logger.info("✅ Supabase client initialized (REAL)")
    
    # Enhanced mock clients
    gcs_client = MockGCSClient()
    pinecone_client = MockPineconeClient()
    neo4j_client = MockNeo4jClient()
    
    return supabase, gcs_client, pinecone_client, neo4j_client


def create_sample_cap_data():
    """Create sample CAP data for testing"""
    
    sample_cases = [
        {
            'id': 'tex-1975-03-15-smith-v-jones',
            'name': 'Smith v. Jones Construction Co.',
            'court': {'name': 'Texas Supreme Court', 'id': 'tex'},
            'decision_date': '1975-03-15T00:00:00Z',
            'text': 'This case involves a construction accident where the plaintiff was injured due to negligent safety practices. The court found that the defendant failed to provide adequate safety measures, resulting in significant damages to the plaintiff. The case establishes important precedent for construction site liability.',
            'jurisdiction': 'TX',
            'docket_number': '75-CV-1234',
            'citations': ['123 S.W.2d 456'],
            'volume': '123',
            'reporter': 'S.W.2d',
            'first_page': '456'
        },
        {
            'id': 'tex-1982-07-22-brown-v-medical-center',
            'name': 'Brown v. Memorial Medical Center',
            'court': {'name': 'Texas Court of Appeals, First District', 'id': 'tex-app-1st'},
            'decision_date': '1982-07-22T00:00:00Z',
            'text': 'Medical malpractice case involving failure to diagnose. The plaintiff suffered complications due to delayed diagnosis of a serious condition. The court examined the standard of care and found the defendant hospital liable for the physician\'s negligence. This case clarifies the scope of hospital liability for staff physicians.',
            'jurisdiction': 'TX',
            'docket_number': '82-CA-5678',
            'citations': ['456 S.W.2d 789'],
            'volume': '456',
            'reporter': 'S.W.2d',
            'first_page': '789'
        },
        {
            'id': 'tex-1988-11-30-wilson-v-insurance-co',
            'name': 'Wilson v. State Farm Insurance Co.',
            'court': {'name': 'Texas Court of Appeals, Third District', 'id': 'tex-app-3rd'},
            'decision_date': '1988-11-30T00:00:00Z',
            'text': 'Insurance coverage dispute involving personal injury claims. The plaintiff sought coverage for injuries sustained in an automobile accident. The insurance company denied coverage based on policy exclusions. The court interpreted the policy language and found in favor of the plaintiff, establishing important precedent for insurance coverage interpretation.',
            'jurisdiction': 'TX',
            'docket_number': '88-CV-9012',
            'citations': ['789 S.W.2d 123'],
            'volume': '789',
            'reporter': 'S.W.2d',
            'first_page': '123'
        }
    ]
    
    return sample_cases


async def test_cap_enhanced_cross_system_tracking():
    """Test CAP data processing with enhanced cross-system tracking"""
    
    logger.info("🧪 TESTING CAP DATA ENHANCED CROSS-SYSTEM TRACKING")
    logger.info("=" * 80)
    
    # Setup enhanced test clients
    supabase, gcs_client, pinecone_client, neo4j_client = await setup_enhanced_test_clients()
    
    # Initialize source-agnostic processor
    processor = SourceAgnosticProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client
    )
    
    # Create sample CAP data
    sample_cap_cases = create_sample_cap_data()
    logger.info(f"📚 Created {len(sample_cap_cases)} sample CAP cases")
    
    # Clean any existing test data
    logger.info("🧹 Cleaning existing test data...")
    try:
        supabase.table('cases').delete().like('batch_id', 'test_cap_%').execute()
        logger.info("✅ Existing test data cleaned")
    except Exception as e:
        logger.warning(f"Warning cleaning test data: {e}")
    
    # Process CAP cases through enhanced pipeline
    batch_id = f"test_cap_enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    logger.info("🔄 Processing CAP cases through enhanced pipeline...")
    result = await processor.process_coherent_batch(
        raw_cases=sample_cap_cases,
        source_type='caselaw_access_project',
        batch_id=batch_id
    )
    
    logger.info(f"📊 Processing result: {result}")
    
    if not result['success']:
        logger.error("❌ CAP processing failed")
        return False
    
    # Verify cross-system tracking
    logger.info("🔍 Verifying enhanced cross-system tracking...")
    
    # Check Supabase records
    supabase_result = supabase.table('cases').select('*').eq('batch_id', batch_id).execute()
    supabase_cases = supabase_result.data
    
    logger.info(f"📊 CROSS-SYSTEM TRACKING VERIFICATION:")
    logger.info(f"   Supabase cases: {len(supabase_cases)}")
    logger.info(f"   GCS objects: {gcs_client.get_object_count()}")
    logger.info(f"   Pinecone vectors: {pinecone_client.get_vector_count()}")
    logger.info(f"   Neo4j nodes: {neo4j_client.get_node_count()}")
    
    # Detailed verification for each case
    for case in supabase_cases:
        case_id = case['id']
        logger.info(f"\n🔍 Detailed verification for case {case_id}:")
        
        # Check Supabase tracking fields
        logger.info(f"   📄 Supabase tracking:")
        logger.info(f"      GCS Path: {case.get('gcs_path', 'MISSING')}")
        logger.info(f"      Pinecone ID: {case.get('pinecone_id', 'MISSING')}")
        logger.info(f"      Neo4j Node: {case.get('neo4j_node_id', 'MISSING')}")
        logger.info(f"      Vector Count: {case.get('word_count', 0)}")
        
        # Check GCS storage
        gcs_objects = gcs_client.list_objects_for_case(case_id)
        logger.info(f"   📁 GCS storage: {len(gcs_objects)} objects")
        
        # Check Pinecone vectors
        pinecone_vectors = pinecone_client.get_vectors_for_case(case_id)
        logger.info(f"   🔍 Pinecone vectors: {len(pinecone_vectors)} vectors")
        if pinecone_vectors:
            sample_vector = pinecone_client.get_vector_details(pinecone_vectors[0])
            logger.info(f"      Sample vector metadata: {list(sample_vector['metadata'].keys())}")
        
        # Check Neo4j nodes
        neo4j_nodes = neo4j_client.get_nodes_by_case_id(case_id)
        logger.info(f"   🕸️ Neo4j nodes: {len(neo4j_nodes)} nodes")
        if neo4j_nodes:
            logger.info(f"      Node properties: {list(neo4j_nodes[0]['properties'].keys())}")
    
    # Cross-system consistency check
    logger.info("\n🎯 CROSS-SYSTEM CONSISTENCY CHECK:")
    
    expected_cases = len(sample_cap_cases)
    actual_supabase = len(supabase_cases)
    actual_gcs = gcs_client.get_object_count()
    actual_pinecone_cases = len(set(case['id'] for case in supabase_cases if case.get('pinecone_id')))
    actual_neo4j = neo4j_client.get_node_count()
    
    consistency_scores = {
        'supabase': actual_supabase / expected_cases,
        'gcs': actual_gcs / expected_cases,
        'pinecone': actual_pinecone_cases / expected_cases,
        'neo4j': actual_neo4j / expected_cases
    }
    
    logger.info(f"   Expected cases: {expected_cases}")
    logger.info(f"   Supabase: {actual_supabase} ({consistency_scores['supabase']:.1%})")
    logger.info(f"   GCS: {actual_gcs} ({consistency_scores['gcs']:.1%})")
    logger.info(f"   Pinecone: {actual_pinecone_cases} ({consistency_scores['pinecone']:.1%})")
    logger.info(f"   Neo4j: {actual_neo4j} ({consistency_scores['neo4j']:.1%})")
    
    # Overall assessment
    all_consistent = all(score >= 0.95 for score in consistency_scores.values())
    
    logger.info("\n" + "=" * 80)
    if all_consistent:
        logger.info("✅ CAP ENHANCED CROSS-SYSTEM TRACKING: SUCCESS")
        logger.info("✅ All systems show consistent 1:1 tracking")
        logger.info("✅ CAP data gets same enhanced features as CourtListener")
        logger.info("✅ Ready for production dual-source processing")
    else:
        logger.error("❌ CAP ENHANCED CROSS-SYSTEM TRACKING: ISSUES FOUND")
        for system, score in consistency_scores.items():
            if score < 0.95:
                logger.error(f"   ❌ {system.title()}: {score:.1%} consistency")
    
    logger.info("=" * 80)
    
    return all_consistent


async def main():
    """Main test execution"""
    try:
        success = await test_cap_enhanced_cross_system_tracking()
        
        if success:
            print("\n🎉 CAP ENHANCED CROSS-SYSTEM TRACKING VERIFIED!")
            print("✅ CAP data gets same enhanced features as CourtListener")
            print("✅ Ready for coherent dual-source processing")
            return 0
        else:
            print("\n❌ CAP ENHANCED CROSS-SYSTEM TRACKING FAILED")
            print("❌ Issues found - review logs for details")
            return 1
            
    except Exception as e:
        logger.error(f"💥 Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
