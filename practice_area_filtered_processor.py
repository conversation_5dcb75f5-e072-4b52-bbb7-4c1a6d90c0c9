#!/usr/bin/env python3
"""
Practice Area Filtered Multi-Cloud Processor

Filters processing to specific practice areas:
- Criminal Defense
- Personal Injury  
- Medical Malpractice
- Family Law

This dramatically reduces dataset size and processing costs.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from pathlib import Path
import json
import re

logger = logging.getLogger(__name__)


@dataclass
class PracticeAreaConfig:
    """Configuration for a specific practice area."""
    area_name: str
    keywords: List[str]
    court_types: List[str]
    estimated_percentage: float  # % of total cases in this area
    complexity_factor: float  # Processing complexity multiplier
    priority: int


class PracticeAreaFilteredProcessor:
    """Processes only specific practice areas for maximum efficiency."""
    
    def __init__(self):
        self.target_practice_areas = {
            'criminal_defense': PracticeAreaConfig(
                area_name='Criminal Defense',
                keywords=[
                    'criminal', 'defendant', 'prosecution', 'felony', 'misdemeanor',
                    'assault', 'battery', 'theft', 'robbery', 'burglary', 'murder',
                    'homicide', 'drug', 'dui', 'dwi', 'domestic violence',
                    'plea', 'sentencing', 'probation', 'parole', 'conviction'
                ],
                court_types=['criminal', 'superior', 'district', 'municipal'],
                estimated_percentage=0.35,  # ~35% of cases
                complexity_factor=1.1,
                priority=1
            ),
            'personal_injury': PracticeAreaConfig(
                area_name='Personal Injury',
                keywords=[
                    'personal injury', 'negligence', 'tort', 'damages', 'liability',
                    'accident', 'motor vehicle', 'slip and fall', 'premises liability',
                    'product liability', 'wrongful death', 'pain and suffering',
                    'medical expenses', 'lost wages', 'disability', 'compensation',
                    'settlement', 'jury verdict', 'insurance'
                ],
                court_types=['civil', 'superior', 'district'],
                estimated_percentage=0.15,  # ~15% of cases
                complexity_factor=1.2,
                priority=2
            ),
            'medical_malpractice': PracticeAreaConfig(
                area_name='Medical Malpractice',
                keywords=[
                    'medical malpractice', 'medical negligence', 'physician',
                    'doctor', 'hospital', 'nurse', 'surgery', 'surgical error',
                    'misdiagnosis', 'failure to diagnose', 'medication error',
                    'birth injury', 'anesthesia', 'informed consent',
                    'standard of care', 'medical expert', 'healthcare'
                ],
                court_types=['civil', 'superior'],
                estimated_percentage=0.03,  # ~3% of cases
                complexity_factor=1.8,  # Very complex cases
                priority=3
            ),
            'family_law': PracticeAreaConfig(
                area_name='Family Law',
                keywords=[
                    'divorce', 'custody', 'child support', 'alimony', 'spousal support',
                    'adoption', 'paternity', 'domestic relations', 'marital property',
                    'visitation', 'guardianship', 'family court', 'juvenile',
                    'child welfare', 'parental rights', 'marriage', 'separation'
                ],
                court_types=['family', 'domestic relations', 'juvenile', 'probate'],
                estimated_percentage=0.12,  # ~12% of cases
                complexity_factor=0.9,
                priority=4
            )
        }
        
        # Total estimated coverage: 65% of all cases
        self.total_coverage = sum(area.estimated_percentage for area in self.target_practice_areas.values())
        
        self.priority_states = {
            'tx': {'name': 'Texas', 'total_cases': 800000},
            'ny': {'name': 'New York', 'total_cases': 600000},
            'fl': {'name': 'Florida', 'total_cases': 500000}
        }
    
    def estimate_filtered_dataset(self) -> Dict[str, Any]:
        """Estimate the size of filtered dataset by practice area."""
        
        results = {}
        total_original_cases = 0
        total_filtered_cases = 0
        
        for state_code, state_info in self.priority_states.items():
            original_cases = state_info['total_cases']
            total_original_cases += original_cases
            
            state_results = {
                'state_name': state_info['name'],
                'original_cases': original_cases,
                'practice_areas': {},
                'total_filtered': 0
            }
            
            for area_code, area_config in self.target_practice_areas.items():
                # Estimate cases in this practice area for this state
                estimated_cases = int(original_cases * area_config.estimated_percentage)
                
                state_results['practice_areas'][area_code] = {
                    'area_name': area_config.area_name,
                    'estimated_cases': estimated_cases,
                    'percentage': area_config.estimated_percentage * 100,
                    'complexity': area_config.complexity_factor,
                    'priority': area_config.priority
                }
                
                state_results['total_filtered'] += estimated_cases
            
            total_filtered_cases += state_results['total_filtered']
            results[state_code] = state_results
        
        # Calculate overall reduction
        reduction_percentage = ((total_original_cases - total_filtered_cases) / total_original_cases) * 100
        
        results['summary'] = {
            'total_original_cases': total_original_cases,
            'total_filtered_cases': total_filtered_cases,
            'reduction_percentage': reduction_percentage,
            'cases_eliminated': total_original_cases - total_filtered_cases,
            'coverage_percentage': self.total_coverage * 100
        }
        
        return results
    
    def calculate_filtered_processing_costs(self) -> Dict[str, Any]:
        """Calculate processing costs for filtered dataset."""
        
        dataset_estimate = self.estimate_filtered_dataset()
        total_filtered_cases = dataset_estimate['summary']['total_filtered_cases']
        
        # Processing parameters
        docs_per_batch = 50
        total_batches = (total_filtered_cases + docs_per_batch - 1) // docs_per_batch
        
        # Cloud capacity
        gcp_capacity = 1500  # Cloud Run Jobs
        aws_capacity = 1000  # Lambda overflow
        total_capacity = gcp_capacity + aws_capacity
        
        # Calculate processing rounds
        if total_batches <= total_capacity:
            processing_rounds = 1
        else:
            processing_rounds = (total_batches + total_capacity - 1) // total_capacity
        
        # Time calculation (weighted by complexity)
        weighted_complexity = self._calculate_weighted_complexity()
        base_time_per_case = 2.0  # seconds
        adjusted_time_per_case = base_time_per_case * weighted_complexity
        
        round_time_minutes = (docs_per_batch * adjusted_time_per_case) / 60
        total_time_hours = (processing_rounds * round_time_minutes) / 60
        
        # Cost calculation
        gcp_batches = min(total_batches, gcp_capacity * processing_rounds)
        aws_batches = max(0, total_batches - gcp_batches)
        
        # GCP Cloud Run Jobs cost
        gcp_cost = gcp_batches * 8 * (round_time_minutes/60) * 0.024  # 8GB * time * $0.024/GB-hour
        
        # AWS Lambda cost
        aws_compute_cost = aws_batches * 10 * (round_time_minutes/60) * 0.0000166667 * 3600  # 10GB * time
        aws_request_cost = aws_batches * 0.0000002  # $0.20 per 1M requests
        aws_cost = aws_compute_cost + aws_request_cost
        
        # GPU embedding cost (10% of processing time)
        gpu_cost = total_time_hours * 0.1 * 1.10  # $1.10/hour for A100
        
        total_cost = gcp_cost + aws_cost + gpu_cost
        
        return {
            'dataset_size': total_filtered_cases,
            'total_batches': total_batches,
            'processing_rounds': processing_rounds,
            'time_hours': total_time_hours,
            'time_minutes': total_time_hours * 60,
            'costs': {
                'gcp_primary': gcp_cost,
                'aws_overflow': aws_cost,
                'gpu_embedding': gpu_cost,
                'total': total_cost
            },
            'resource_allocation': {
                'gcp_batches': gcp_batches,
                'aws_batches': aws_batches,
                'concurrent_functions': min(total_capacity, total_batches)
            },
            'efficiency': {
                'cost_per_case': total_cost / total_filtered_cases,
                'cases_per_hour': total_filtered_cases / total_time_hours,
                'cases_per_dollar': total_filtered_cases / total_cost
            }
        }
    
    def _calculate_weighted_complexity(self) -> float:
        """Calculate weighted average complexity across practice areas."""
        total_weight = 0
        weighted_sum = 0
        
        for area_config in self.target_practice_areas.values():
            weight = area_config.estimated_percentage
            complexity = area_config.complexity_factor
            
            weighted_sum += weight * complexity
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 1.0
    
    def create_practice_area_filters(self) -> Dict[str, Any]:
        """Create filtering logic for identifying practice areas."""
        
        filters = {}
        
        for area_code, area_config in self.target_practice_areas.items():
            # Create regex patterns for efficient matching
            keyword_pattern = '|'.join([
                r'\b' + re.escape(keyword.lower()) + r'\b' 
                for keyword in area_config.keywords
            ])
            
            court_pattern = '|'.join([
                r'\b' + re.escape(court.lower()) + r'\b'
                for court in area_config.court_types
            ])
            
            filters[area_code] = {
                'area_name': area_config.area_name,
                'keyword_regex': re.compile(keyword_pattern, re.IGNORECASE),
                'court_regex': re.compile(court_pattern, re.IGNORECASE),
                'priority': area_config.priority,
                'complexity': area_config.complexity_factor
            }
        
        return filters
    
    def estimate_full_dataset_impact(self) -> Dict[str, Any]:
        """Estimate impact on full 6.7M case dataset."""
        
        full_dataset_cases = 6700000
        filtered_cases = int(full_dataset_cases * self.total_coverage)
        
        # Calculate processing metrics for full filtered dataset
        docs_per_batch = 50
        total_batches = (filtered_cases + docs_per_batch - 1) // docs_per_batch
        
        # Multi-cloud capacity
        total_capacity = 3000  # GCP + AWS + Azure
        processing_rounds = (total_batches + total_capacity - 1) // total_capacity
        
        weighted_complexity = self._calculate_weighted_complexity()
        round_time_minutes = (docs_per_batch * 2.0 * weighted_complexity) / 60
        total_time_hours = (processing_rounds * round_time_minutes) / 60
        
        # Cost estimation
        estimated_cost = filtered_cases * 0.0002  # $0.0002 per case based on previous analysis
        
        return {
            'original_dataset': full_dataset_cases,
            'filtered_dataset': filtered_cases,
            'reduction': {
                'cases_eliminated': full_dataset_cases - filtered_cases,
                'percentage_reduction': ((full_dataset_cases - filtered_cases) / full_dataset_cases) * 100
            },
            'processing': {
                'time_hours': total_time_hours,
                'time_minutes': total_time_hours * 60,
                'estimated_cost': estimated_cost,
                'cost_per_case': estimated_cost / filtered_cases
            },
            'savings': {
                'time_saved_hours': (full_dataset_cases - filtered_cases) * 2.0 / 3600,  # Time saved
                'cost_saved': (full_dataset_cases - filtered_cases) * 0.0002  # Cost saved
            }
        }


def analyze_practice_area_filtering():
    """Analyze the impact of practice area filtering."""
    
    processor = PracticeAreaFilteredProcessor()
    
    print("\n🎯 PRACTICE AREA FILTERING ANALYSIS")
    print("=" * 80)
    
    # Analyze priority states
    dataset_estimate = processor.estimate_filtered_dataset()
    processing_costs = processor.calculate_filtered_processing_costs()
    
    print(f"\n📊 PRIORITY STATES FILTERING RESULTS")
    print("-" * 50)
    
    for state_code, state_data in dataset_estimate.items():
        if state_code == 'summary':
            continue
            
        print(f"\n{state_data['state_name']}:")
        print(f"  Original cases: {state_data['original_cases']:,}")
        print(f"  Filtered cases: {state_data['total_filtered']:,}")
        print(f"  Reduction: {((state_data['original_cases'] - state_data['total_filtered']) / state_data['original_cases'] * 100):.1f}%")
        
        print(f"  Practice area breakdown:")
        for area_code, area_data in state_data['practice_areas'].items():
            print(f"    {area_data['area_name']}: {area_data['estimated_cases']:,} cases ({area_data['percentage']:.1f}%)")
    
    # Summary
    summary = dataset_estimate['summary']
    print(f"\n🎯 OVERALL IMPACT")
    print("-" * 30)
    print(f"Original dataset: {summary['total_original_cases']:,} cases")
    print(f"Filtered dataset: {summary['total_filtered_cases']:,} cases")
    print(f"Cases eliminated: {summary['cases_eliminated']:,}")
    print(f"Reduction: {summary['reduction_percentage']:.1f}%")
    print(f"Coverage: {summary['coverage_percentage']:.1f}% of relevant cases")
    
    # Processing costs
    print(f"\n💰 PROCESSING COSTS (FILTERED)")
    print("-" * 30)
    print(f"Processing time: {processing_costs['time_minutes']:.1f} minutes")
    print(f"Total cost: ${processing_costs['costs']['total']:.2f}")
    print(f"Cost per case: ${processing_costs['efficiency']['cost_per_case']:.6f}")
    print(f"Cases per hour: {processing_costs['efficiency']['cases_per_hour']:,.0f}")
    print(f"Cases per dollar: {processing_costs['efficiency']['cases_per_dollar']:,.0f}")
    
    # Full dataset impact
    full_impact = processor.estimate_full_dataset_impact()
    print(f"\n🌍 FULL DATASET IMPACT (6.7M CASES)")
    print("-" * 40)
    print(f"Original: {full_impact['original_dataset']:,} cases")
    print(f"Filtered: {full_impact['filtered_dataset']:,} cases")
    print(f"Reduction: {full_impact['reduction']['percentage_reduction']:.1f}%")
    print(f"Processing time: {full_impact['processing']['time_minutes']:.1f} minutes")
    print(f"Processing cost: ${full_impact['processing']['estimated_cost']:.2f}")
    print(f"Time saved: {full_impact['savings']['time_saved_hours']:.1f} hours")
    print(f"Cost saved: ${full_impact['savings']['cost_saved']:.2f}")


if __name__ == "__main__":
    analyze_practice_area_filtering()
