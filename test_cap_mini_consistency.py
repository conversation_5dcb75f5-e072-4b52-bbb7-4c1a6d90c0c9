#!/usr/bin/env python3
"""
Mini test of CAP data processing using the actual pipeline.
Process 50 CAP records and verify perfect database consistency.
"""

import asyncio
import json
import gzip
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

# Import with proper module path
from src.processing.caselaw_access_processor import CaselawAccessProcessor, CaselawDocument

async def test_cap_mini_consistency():
    """Test CAP processing with 50 records using the actual pipeline."""
    
    print("🧪 CAP MINI CONSISTENCY TEST")
    print("=" * 60)
    print("Processing 50 CAP records with actual pipeline")
    print("=" * 60)
    
    try:
        # Get baseline database counts
        print("📊 BASELINE DATABASE COUNTS:")
        baseline = await get_database_counts()
        print(f"   Supabase: {baseline['supabase']:,}")
        print(f"   Neo4j: {baseline['neo4j']:,}")
        print(f"   Pinecone: {baseline['pinecone']:,}")
        
        # Initialize the actual CAP processor
        print(f"\n🔧 Initializing CaselawAccessProcessor...")
        processor = CaselawAccessProcessor()
        print("✅ Processor initialized")
        
        # Read 50 records from CAP data
        cap_file = "data/caselaw_access_project/cap_00000.jsonl.gz"
        print(f"\n📂 Reading from: {cap_file}")
        
        test_records = []
        with gzip.open(cap_file, 'rt') as f:
            for i, line in enumerate(f):
                if i >= 50:  # Process first 50 records
                    break
                test_records.append(json.loads(line))
        
        print(f"📥 Loaded {len(test_records)} test records")
        
        # Process records using the actual pipeline
        print(f"\n🔄 PROCESSING WITH ACTUAL PIPELINE:")
        processed_count = 0
        duplicate_count = 0
        error_count = 0
        
        start_time = datetime.now()
        
        for i, record in enumerate(test_records):
            try:
                # Convert CAP record to CaselawDocument
                doc = convert_cap_to_caselaw_document(record)
                
                # Process through the actual pipeline
                success = await processor.process_document(doc)
                
                if success:
                    processed_count += 1
                    if (processed_count % 10) == 0:
                        print(f"   ✅ Processed {processed_count}/50 ({processed_count*2}%)")
                else:
                    duplicate_count += 1
                    if duplicate_count <= 3:  # Show first few duplicates
                        print(f"   ⚠️  Duplicate detected: {doc.id}")
                
            except Exception as e:
                error_count += 1
                if error_count <= 3:  # Show first 3 errors
                    print(f"   ❌ Error processing record {i+1}: {e}")
        
        end_time = datetime.now()
        processing_time = end_time - start_time
        
        # Get final database counts
        print(f"\n📊 FINAL DATABASE COUNTS:")
        final = await get_database_counts()
        print(f"   Supabase: {final['supabase']:,} (+{final['supabase'] - baseline['supabase']:,})")
        print(f"   Neo4j: {final['neo4j']:,} (+{final['neo4j'] - baseline['neo4j']:,})")
        print(f"   Pinecone: {final['pinecone']:,} (+{final['pinecone'] - baseline['pinecone']:,})")
        
        # Calculate increases
        supabase_increase = final['supabase'] - baseline['supabase']
        neo4j_increase = final['neo4j'] - baseline['neo4j']
        pinecone_increase = final['pinecone'] - baseline['pinecone']
        
        # Verify perfect consistency
        print(f"\n🎯 CONSISTENCY VERIFICATION:")
        print(f"   Records processed: {processed_count}")
        print(f"   Duplicates detected: {duplicate_count}")
        print(f"   Errors: {error_count}")
        print(f"   Processing time: {processing_time}")
        print(f"   Rate: {processed_count / processing_time.total_seconds():.1f} records/second")
        
        print(f"\n   Database Increases:")
        print(f"   Supabase: +{supabase_increase}")
        print(f"   Neo4j: +{neo4j_increase}")
        print(f"   Pinecone: +{pinecone_increase}")
        
        # Check perfect consistency
        if supabase_increase == neo4j_increase == processed_count:
            print(f"\n   ✅ PERFECT CONSISTENCY ACHIEVED!")
            print(f"   ✅ All {processed_count} records in all databases")
            print(f"   ✅ Supabase ↔ Neo4j: 100% synchronized")
            consistency_rate = 100.0
        elif supabase_increase == neo4j_increase:
            print(f"\n   ✅ EXCELLENT CONSISTENCY!")
            print(f"   ✅ Supabase ↔ Neo4j: 100% synchronized")
            print(f"   ⚠️  Some records may have been filtered")
            consistency_rate = 99.0
        else:
            print(f"\n   ❌ CONSISTENCY ISSUE DETECTED!")
            print(f"   ❌ Supabase: +{supabase_increase}, Neo4j: +{neo4j_increase}")
            print(f"   🔧 Pipeline needs investigation")
            consistency_rate = 0.0
        
        # Pinecone consistency (vectors may have propagation delay)
        expected_vectors = processed_count * 3  # ~3 chunks per case
        if pinecone_increase >= expected_vectors * 0.7:  # 70% threshold for propagation
            print(f"   ✅ Pinecone: Good vector coverage ({pinecone_increase} vectors)")
        elif pinecone_increase > 0:
            print(f"   ⚠️  Pinecone: Partial vectors ({pinecone_increase}, may be propagation delay)")
        else:
            print(f"   ❌ Pinecone: No vectors added")
        
        return consistency_rate >= 99.0, processed_count, error_count
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 1

def convert_cap_to_caselaw_document(record):
    """Convert CAP JSON record to CaselawDocument using the same format as the processor."""
    
    metadata = record.get('metadata', {})
    
    # Parse dates
    added_date = datetime.fromisoformat(record.get('added', '2024-01-01T00:00:00').replace('Z', '+00:00'))
    created_date = datetime.fromisoformat(record.get('created', '2024-01-01T00:00:00').replace('Z', '+00:00'))
    
    # Create document with proper ID format (this will be processed by _generate_case_id)
    doc = CaselawDocument(
        # Required fields
        id=record['id'],  # Keep original ID, processor will generate storage ID
        source='Caselaw Access Project',
        added=added_date,
        created=created_date,
        author=metadata.get('author', 'Unknown Court'),
        license=metadata.get('license', 'Public Domain'),
        url=metadata.get('url', ''),
        text=record.get('text', ''),
        
        # Optional fields that will be extracted/classified
        case_name='',  # Will be extracted from text
        docket_number='',  # Will be extracted from text
        date_filed=None,  # Will be extracted from text
        court='',  # Will be extracted from text
        jurisdiction='',  # Will be classified
        practice_area='',  # Will be classified
        precedential_status='',
        citation_count=0,
        judges=[],
        parties=[]
    )
    
    return doc

async def get_database_counts():
    """Get current counts from all databases."""
    
    counts = {'supabase': 0, 'neo4j': 0, 'pinecone': 0}
    
    try:
        # Supabase count
        from src.processing.storage.supabase_connector import SupabaseConnector
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        counts['supabase'] = response.count

        # Neo4j count
        from src.processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            counts['neo4j'] = result.single()['count']
        neo4j.close()
        
        # Pinecone count
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        counts['pinecone'] = stats.total_vector_count
        
    except Exception as e:
        print(f"⚠️  Error getting database counts: {e}")
    
    return counts

async def verify_sample_records():
    """Verify a few sample records are properly stored with correct IDs."""
    
    print(f"\n🔍 VERIFYING SAMPLE RECORDS:")
    
    try:
        from src.processing.storage.supabase_connector import SupabaseConnector
        from src.processing.storage.neo4j_connector import Neo4jConnector
        
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        
        # Get 3 most recent cases from Supabase
        response = supabase.client.table('cases').select('id, case_name, source').order('created_at', desc=True).limit(3).execute()
        recent_cases = response.data
        
        print(f"   Recent Supabase cases:")
        for case in recent_cases:
            print(f"     {case['id']}: {case['case_name'][:50]}... (source: {case['source']})")
        
        # Check if these cases exist in Neo4j
        with neo4j.driver.session() as session:
            for case in recent_cases:
                result = session.run("MATCH (d:Document {id: $id}) RETURN d.id, d.title", {"id": case['id']})
                record = result.single()
                if record:
                    print(f"     ✅ Found in Neo4j: {record['d.id']}")
                else:
                    print(f"     ❌ Missing from Neo4j: {case['id']}")
        
        neo4j.close()
        
    except Exception as e:
        print(f"   ⚠️  Error verifying records: {e}")

async def main():
    """Main test function."""
    
    print("🧪 CAP MINI CONSISTENCY TEST")
    print("=" * 80)
    print("Testing 50 CAP records with actual CaselawAccessProcessor pipeline")
    print("This will verify that CAP processing maintains perfect consistency")
    print("=" * 80)
    
    # Run the test
    success, processed, errors = await test_cap_mini_consistency()
    
    # Verify sample records
    if processed > 0:
        await verify_sample_records()
    
    print(f"\n🎯 FINAL TEST RESULTS:")
    if success and processed > 0:
        print(f"   ✅ CAP PROCESSING CONSISTENCY VERIFIED!")
        print(f"   ✅ Processed {processed} records successfully")
        print(f"   ✅ All databases perfectly synchronized")
        print(f"   ✅ Same pipeline that achieved Court Listener consistency")
        print(f"   🚀 READY FOR FULL-SCALE CAP PROCESSING")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Process full CAP dataset (640,000 records)")
        print(f"   2. Expect perfect consistency across all databases")
        print(f"   3. Monitor processing for any issues")
        print(f"   4. Achieve massive database population")
        
        # Estimate full processing
        processing_rate = processed / 60  # rough estimate per minute
        full_time_hours = 640000 / (processing_rate * 60)
        print(f"\n📊 FULL PROCESSING ESTIMATES:")
        print(f"   Total records: 640,000")
        print(f"   Estimated time: {full_time_hours:.1f} hours")
        print(f"   Expected final database size: ~640,100 cases")
        print(f"   Expected cost: ~$192 for embeddings")
        
    elif processed == 0:
        print(f"   ⚠️  NO RECORDS PROCESSED")
        print(f"   ⚠️  All records were duplicates or errors")
        print(f"   📊 This might be expected if CAP data overlaps with Court Listener")
        
    else:
        print(f"   ❌ CONSISTENCY ISSUES DETECTED")
        print(f"   ❌ Processed: {processed}, Errors: {errors}")
        print(f"   🔧 MUST FIX BEFORE SCALING")
        
        print(f"\n📋 REQUIRED ACTIONS:")
        print(f"   1. Debug consistency issues")
        print(f"   2. Fix CAP processing pipeline")
        print(f"   3. Re-test before full processing")

if __name__ == "__main__":
    asyncio.run(main())
