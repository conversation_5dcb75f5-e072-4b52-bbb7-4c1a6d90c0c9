#!/usr/bin/env python3
"""
Practice Area Document Filter

Efficiently filters documents to target practice areas during processing.
Integrates with the multi-cloud serverless pipeline.
"""

import re
import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


@dataclass
class FilterResult:
    """Result of practice area filtering."""
    is_target_practice_area: bool
    practice_area: Optional[str]
    confidence_score: float
    matching_keywords: List[str]
    court_type_match: bool


class PracticeAreaDocumentFilter:
    """High-performance document filter for target practice areas."""
    
    def __init__(self):
        # Compiled regex patterns for maximum performance
        self.practice_area_patterns = self._compile_practice_area_patterns()
        self.court_type_patterns = self._compile_court_type_patterns()
        
        # Performance tracking
        self.filter_stats = {
            'total_processed': 0,
            'criminal_defense': 0,
            'personal_injury': 0,
            'medical_malpractice': 0,
            'family_law': 0,
            'filtered_out': 0
        }
    
    def _compile_practice_area_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for each practice area."""
        
        patterns = {}
        
        # Criminal Defense - High-frequency keywords first for performance
        criminal_keywords = [
            r'\bcriminal\b', r'\bdefendant\b', r'\bprosecution\b', r'\bfelony\b',
            r'\bmisdemeanor\b', r'\bassault\b', r'\bbattery\b', r'\btheft\b',
            r'\brobbery\b', r'\bburglary\b', r'\bmurder\b', r'\bhomicide\b',
            r'\bdrug\b', r'\bdui\b', r'\bdwi\b', r'\bdomestic violence\b',
            r'\bplea\b', r'\bsentencing\b', r'\bprobation\b', r'\bparole\b',
            r'\bconviction\b', r'\barrest\b', r'\bindictment\b'
        ]
        patterns['criminal_defense'] = re.compile(
            '|'.join(criminal_keywords), re.IGNORECASE
        )
        
        # Personal Injury
        pi_keywords = [
            r'\bpersonal injury\b', r'\bnegligence\b', r'\btort\b', r'\bdamages\b',
            r'\bliability\b', r'\baccident\b', r'\bmotor vehicle\b', r'\bslip and fall\b',
            r'\bpremises liability\b', r'\bproduct liability\b', r'\bwrongful death\b',
            r'\bpain and suffering\b', r'\bmedical expenses\b', r'\blost wages\b',
            r'\bdisability\b', r'\bcompensation\b', r'\bsettlement\b', r'\bjury verdict\b',
            r'\binsurance\b', r'\bcar accident\b', r'\bwork injury\b'
        ]
        patterns['personal_injury'] = re.compile(
            '|'.join(pi_keywords), re.IGNORECASE
        )
        
        # Medical Malpractice
        malpractice_keywords = [
            r'\bmedical malpractice\b', r'\bmedical negligence\b', r'\bphysician\b',
            r'\bdoctor\b', r'\bhospital\b', r'\bnurse\b', r'\bsurgery\b',
            r'\bsurgical error\b', r'\bmisdiagnosis\b', r'\bfailure to diagnose\b',
            r'\bmedication error\b', r'\bbirth injury\b', r'\banesthesia\b',
            r'\binformed consent\b', r'\bstandard of care\b', r'\bmedical expert\b',
            r'\bhealthcare\b', r'\bpatient\b'
        ]
        patterns['medical_malpractice'] = re.compile(
            '|'.join(malpractice_keywords), re.IGNORECASE
        )
        
        # Family Law
        family_keywords = [
            r'\bdivorce\b', r'\bcustody\b', r'\bchild support\b', r'\balimony\b',
            r'\bspousal support\b', r'\badoption\b', r'\bpaternity\b',
            r'\bdomestic relations\b', r'\bmarital property\b', r'\bvisitation\b',
            r'\bguardianship\b', r'\bfamily court\b', r'\bjuvenile\b',
            r'\bchild welfare\b', r'\bparental rights\b', r'\bmarriage\b',
            r'\bseparation\b', r'\bchild abuse\b'
        ]
        patterns['family_law'] = re.compile(
            '|'.join(family_keywords), re.IGNORECASE
        )
        
        return patterns
    
    def _compile_court_type_patterns(self) -> Dict[str, re.Pattern]:
        """Compile court type patterns for additional filtering."""
        
        court_patterns = {}
        
        court_patterns['criminal'] = re.compile(
            r'\b(criminal|superior|district|municipal|magistrate)\s+court\b',
            re.IGNORECASE
        )
        
        court_patterns['civil'] = re.compile(
            r'\b(civil|superior|district|common pleas)\s+court\b',
            re.IGNORECASE
        )
        
        court_patterns['family'] = re.compile(
            r'\b(family|domestic relations|juvenile|probate)\s+court\b',
            re.IGNORECASE
        )
        
        return court_patterns
    
    def filter_document(self, document_data: Dict) -> FilterResult:
        """
        Filter a single document for target practice areas.
        
        Args:
            document_data: Document with case_name, text, court, etc.
            
        Returns:
            FilterResult with practice area classification
        """
        self.filter_stats['total_processed'] += 1
        
        # Extract text fields for analysis
        case_name = document_data.get('case_name', '').lower()
        case_text = document_data.get('text', '').lower()
        court_name = document_data.get('court', '').lower()
        
        # Combine text for analysis (prioritize case name and first 1000 chars)
        analysis_text = f"{case_name} {case_text[:1000]} {court_name}"
        
        # Check each practice area
        best_match = None
        best_score = 0.0
        matching_keywords = []
        
        for area_name, pattern in self.practice_area_patterns.items():
            matches = pattern.findall(analysis_text)
            if matches:
                # Calculate confidence score based on number and position of matches
                score = len(matches)
                
                # Boost score if matches are in case name (more reliable)
                case_name_matches = pattern.findall(case_name)
                score += len(case_name_matches) * 2
                
                # Boost score for court type alignment
                court_boost = self._get_court_type_boost(area_name, court_name)
                score += court_boost
                
                if score > best_score:
                    best_score = score
                    best_match = area_name
                    matching_keywords = matches[:5]  # Top 5 matches
        
        # Determine if this is a target document
        is_target = best_match is not None and best_score >= 1.0
        
        if is_target:
            self.filter_stats[best_match] += 1
        else:
            self.filter_stats['filtered_out'] += 1
        
        # Check court type match
        court_type_match = self._check_court_type_match(best_match, court_name) if best_match else False
        
        return FilterResult(
            is_target_practice_area=is_target,
            practice_area=best_match,
            confidence_score=best_score,
            matching_keywords=matching_keywords,
            court_type_match=court_type_match
        )
    
    def _get_court_type_boost(self, practice_area: str, court_name: str) -> float:
        """Get confidence boost based on court type alignment."""
        
        court_alignments = {
            'criminal_defense': ['criminal', 'superior', 'district', 'municipal'],
            'personal_injury': ['civil', 'superior', 'district'],
            'medical_malpractice': ['civil', 'superior'],
            'family_law': ['family', 'domestic', 'juvenile', 'probate']
        }
        
        if practice_area not in court_alignments:
            return 0.0
        
        for court_type in court_alignments[practice_area]:
            if court_type in court_name:
                return 1.0
        
        return 0.0
    
    def _check_court_type_match(self, practice_area: str, court_name: str) -> bool:
        """Check if court type matches the practice area."""
        
        if not practice_area:
            return False
        
        court_type_map = {
            'criminal_defense': 'criminal',
            'personal_injury': 'civil',
            'medical_malpractice': 'civil',
            'family_law': 'family'
        }
        
        expected_court_type = court_type_map.get(practice_area)
        if not expected_court_type:
            return False
        
        return expected_court_type in self.court_type_patterns and \
               bool(self.court_type_patterns[expected_court_type].search(court_name))
    
    def batch_filter_documents(self, documents: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        Filter a batch of documents efficiently.
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            Tuple of (filtered_documents, filter_statistics)
        """
        filtered_docs = []
        batch_stats = {
            'total_input': len(documents),
            'total_output': 0,
            'by_practice_area': {},
            'filter_rate': 0.0
        }
        
        for doc in documents:
            filter_result = self.filter_document(doc)
            
            if filter_result.is_target_practice_area:
                # Add practice area metadata to document
                doc['practice_area'] = filter_result.practice_area
                doc['practice_area_confidence'] = filter_result.confidence_score
                doc['matching_keywords'] = filter_result.matching_keywords
                doc['court_type_match'] = filter_result.court_type_match
                
                filtered_docs.append(doc)
                
                # Update batch statistics
                area = filter_result.practice_area
                if area not in batch_stats['by_practice_area']:
                    batch_stats['by_practice_area'][area] = 0
                batch_stats['by_practice_area'][area] += 1
        
        batch_stats['total_output'] = len(filtered_docs)
        batch_stats['filter_rate'] = (len(documents) - len(filtered_docs)) / len(documents) if documents else 0
        
        return filtered_docs, batch_stats
    
    def get_filter_statistics(self) -> Dict:
        """Get current filtering statistics."""
        
        total = self.filter_stats['total_processed']
        if total == 0:
            return self.filter_stats
        
        stats = self.filter_stats.copy()
        stats['percentages'] = {
            'criminal_defense': (stats['criminal_defense'] / total) * 100,
            'personal_injury': (stats['personal_injury'] / total) * 100,
            'medical_malpractice': (stats['medical_malpractice'] / total) * 100,
            'family_law': (stats['family_law'] / total) * 100,
            'filtered_out': (stats['filtered_out'] / total) * 100
        }
        
        return stats
    
    def reset_statistics(self):
        """Reset filtering statistics."""
        for key in self.filter_stats:
            self.filter_stats[key] = 0


# Integration function for serverless processing
async def filter_batch_for_serverless(batch_data: Dict) -> Dict:
    """
    Filter a batch of documents for serverless processing.
    
    This function integrates with the multi-cloud serverless pipeline.
    """
    
    filter_engine = PracticeAreaDocumentFilter()
    
    # Load documents from batch
    documents = batch_data.get('documents', [])
    
    # Apply filtering
    filtered_docs, batch_stats = filter_engine.batch_filter_documents(documents)
    
    # Log filtering results
    logger.info(f"Batch {batch_data.get('batch_id', 'unknown')}: "
               f"{len(documents)} → {len(filtered_docs)} documents "
               f"({batch_stats['filter_rate']*100:.1f}% filtered out)")
    
    # Return filtered batch
    return {
        'batch_id': batch_data.get('batch_id'),
        'documents': filtered_docs,
        'original_count': len(documents),
        'filtered_count': len(filtered_docs),
        'filter_statistics': batch_stats,
        'practice_area_breakdown': batch_stats['by_practice_area']
    }


if __name__ == "__main__":
    # Test the filter with sample data
    filter_engine = PracticeAreaDocumentFilter()
    
    sample_docs = [
        {
            'case_name': 'State v. Johnson - Criminal Assault Case',
            'text': 'The defendant was charged with criminal assault and battery...',
            'court': 'Superior Criminal Court'
        },
        {
            'case_name': 'Smith v. Hospital - Medical Malpractice',
            'text': 'The plaintiff alleges medical negligence by the physician...',
            'court': 'Civil Superior Court'
        },
        {
            'case_name': 'Jones v. City - Personal Injury Slip and Fall',
            'text': 'The plaintiff suffered injuries due to negligence and premises liability...',
            'court': 'Civil District Court'
        },
        {
            'case_name': 'Brown v. Brown - Divorce and Child Custody',
            'text': 'The parties seek divorce and resolution of child custody matters...',
            'court': 'Family Court'
        },
        {
            'case_name': 'Corporate Contract Dispute',
            'text': 'The parties dispute the terms of a commercial contract...',
            'court': 'Commercial Court'
        }
    ]
    
    filtered_docs, stats = filter_engine.batch_filter_documents(sample_docs)
    
    print(f"\nFiltered {len(sample_docs)} → {len(filtered_docs)} documents")
    print(f"Filter rate: {stats['filter_rate']*100:.1f}%")
    print(f"Practice area breakdown: {stats['by_practice_area']}")
    
    for doc in filtered_docs:
        print(f"- {doc['case_name']} → {doc['practice_area']} (confidence: {doc['practice_area_confidence']})")
