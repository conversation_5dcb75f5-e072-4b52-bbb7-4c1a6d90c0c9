#!/usr/bin/env python3
"""
Check if specific cases from Supabase exist in Neo4j.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector

async def check_specific_cases():
    """Check if specific recent cases exist in Neo4j."""
    
    # Recent cases from Supabase
    test_cases = [
        "cl_10637562",
        "cl_10637563", 
        "cl_10637564",
        "cl_10637565",
        "cl_10637566",
        "cl_10637567",
        "cl_10637568",
        "cl_10637569",
        "cl_10637570",
        "cl_10637571"
    ]
    
    try:
        print("🔍 CHECKING SPECIFIC CASES IN NEO4J")
        print("=" * 60)
        
        # Initialize Neo4j connector
        neo4j = Neo4jConnector()
        
        found_count = 0
        missing_cases = []
        
        with neo4j.driver.session() as session:
            for case_id in test_cases:
                # Check if Document node exists with this ID
                result = session.run(
                    "MATCH (d:Document {id: $case_id}) RETURN d.id as id, d.title as title",
                    {"case_id": case_id}
                )
                
                record = result.single()
                if record:
                    found_count += 1
                    print(f"   ✅ Found: {case_id}")
                else:
                    missing_cases.append(case_id)
                    print(f"   ❌ Missing: {case_id}")
        
        neo4j.close()
        
        print(f"\n📊 RESULTS:")
        print(f"   Total checked: {len(test_cases)}")
        print(f"   Found in Neo4j: {found_count}")
        print(f"   Missing from Neo4j: {len(missing_cases)}")
        
        if missing_cases:
            print(f"\n❌ MISSING CASES:")
            for case_id in missing_cases:
                print(f"     {case_id}")
        
        return found_count, missing_cases
        
    except Exception as e:
        print(f"❌ Error checking Neo4j: {e}")
        import traceback
        traceback.print_exc()
        return 0, test_cases

async def check_neo4j_storage_errors():
    """Check for common Neo4j storage issues."""
    
    try:
        print(f"\n🔍 CHECKING NEO4J STORAGE PATTERNS")
        print("=" * 60)
        
        neo4j = Neo4jConnector()
        
        with neo4j.driver.session() as session:
            # Check for Document nodes with null IDs
            result = session.run("MATCH (d:Document) WHERE d.id IS NULL RETURN count(d) as null_id_count")
            null_id_count = result.single()['null_id_count']
            print(f"   Document nodes with null ID: {null_id_count}")
            
            # Check for Document nodes by source
            result = session.run("""
                MATCH (d:Document) 
                RETURN d.source as source, count(d) as count 
                ORDER BY count DESC
            """)
            
            print(f"\n   Document nodes by source:")
            for record in result:
                source = record['source'] or 'null'
                count = record['count']
                print(f"     {source}: {count:,}")
            
            # Check for recent Document nodes (last 24 hours)
            result = session.run("""
                MATCH (d:Document) 
                WHERE d.created_at > datetime() - duration('P1D')
                RETURN count(d) as recent_count
            """)
            recent_count = result.single()['recent_count']
            print(f"\n   Recent Document nodes (last 24h): {recent_count}")
            
            # Sample of Document nodes with IDs starting with 'cl_'
            result = session.run("""
                MATCH (d:Document) 
                WHERE d.id STARTS WITH 'cl_'
                RETURN d.id, d.title, d.source
                LIMIT 5
            """)
            
            print(f"\n   Sample Court Listener Document nodes:")
            cl_count = 0
            for record in result:
                cl_count += 1
                doc_id = record['d.id']
                title = record['d.title']
                source = record['d.source']
                print(f"     {doc_id}: {title[:50] if title else 'No title'}...")
            
            if cl_count == 0:
                print(f"     ❌ No Document nodes with 'cl_' prefix found!")
            
        neo4j.close()
        
    except Exception as e:
        print(f"❌ Error checking Neo4j patterns: {e}")

async def main():
    """Main function."""
    found_count, missing_cases = await check_specific_cases()
    await check_neo4j_storage_errors()
    
    print(f"\n🎯 CONSISTENCY ISSUE ANALYSIS:")
    if len(missing_cases) > 0:
        print(f"   ❌ CONFIRMED: Recent cases are missing from Neo4j")
        print(f"   📊 Missing rate: {len(missing_cases)}/{len(missing_cases) + found_count} = {len(missing_cases)/(len(missing_cases) + found_count)*100:.1f}%")
        print(f"   🔧 ACTION NEEDED: Fix Neo4j storage pipeline")
    else:
        print(f"   ✅ Recent cases are properly stored in Neo4j")

if __name__ == "__main__":
    asyncio.run(main())
