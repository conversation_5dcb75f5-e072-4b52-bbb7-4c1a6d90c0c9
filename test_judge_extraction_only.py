#!/usr/bin/env python3
"""
Test Judge Extraction Only
Test just the judge extraction on real data without full pipeline
"""

import requests
import time
import json
import gzip
import os
from dotenv import load_dotenv
from judge_relationship_enhancer import JudgeRelationshipEnhancer

def test_courtlistener_judge_extraction():
    """Test judge extraction on real CourtListener data"""
    
    print("🌐 TESTING COURTLISTENER JUDGE EXTRACTION")
    print("=" * 60)
    
    load_dotenv()
    cl_api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not cl_api_key:
        print("❌ No CourtListener API key found")
        return []
    
    headers = {
        'Authorization': f'Token {cl_api_key}',
        'User-Agent': 'Texas Laws Personal Injury Research'
    }
    
    try:
        # Get a recent case
        response = requests.get(
            "https://www.courtlistener.com/api/rest/v4/opinions/",
            headers=headers,
            params={
                'court': 'ca5,ca2,scotus',
                'filed_after': '2020-01-01',
                'ordering': '-date_filed',
                'page_size': 5
            },
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return []
        
        results = response.json().get('results', [])
        
        enhancer = JudgeRelationshipEnhancer()
        all_judges = []
        
        for i, case in enumerate(results[:2], 1):  # Test first 2 cases
            case_name = case.get('case_name', 'Unknown')
            text = case.get('plain_text', '') or case.get('html', '')
            
            if not text or len(text) < 500:
                continue
            
            print(f"\n📄 CASE {i}: {case_name[:50]}...")
            print(f"   Text length: {len(text):,} characters")
            print(f"   Court: {case.get('court', 'Unknown')}")
            
            # Extract judges
            judges = enhancer._extract_judges_from_text(text)
            
            print(f"   📊 Judges extracted: {len(judges)}")
            
            for judge in judges:
                words = len(judge['name'].split())
                word_status = "✅ Full" if words > 1 else "❌ Partial"
                print(f"      - {judge['name']} ({word_status}, {words} words, role: {judge['role']})")
                all_judges.append(judge)
        
        enhancer.close()
        
        print(f"\n📊 COURTLISTENER SUMMARY:")
        print(f"   Total judges: {len(all_judges)}")
        full_names = [j for j in all_judges if len(j['name'].split()) > 1]
        print(f"   Full names: {len(full_names)}/{len(all_judges)} ({len(full_names)/len(all_judges)*100:.1f}%)")
        
        return all_judges
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []


def test_cap_judge_extraction():
    """Test judge extraction on real CAP data"""
    
    print(f"\n📂 TESTING CAP JUDGE EXTRACTION")
    print("=" * 60)
    
    cap_data_path = "/Users/<USER>/Documents/GitHub/texas-laws-personalinjury/data/caselaw_access_project"
    
    if not os.path.exists(cap_data_path):
        print(f"❌ CAP data path not found: {cap_data_path}")
        return []
    
    # Find CAP files
    cap_files = [f for f in os.listdir(cap_data_path) if f.startswith('cap_') and f.endswith('.jsonl.gz')]
    
    if not cap_files:
        print(f"❌ No CAP files found")
        return []
    
    print(f"📁 Found {len(cap_files)} CAP files")
    
    enhancer = JudgeRelationshipEnhancer()
    all_judges = []
    
    # Test first file
    file_path = os.path.join(cap_data_path, cap_files[0])
    print(f"📄 Testing file: {cap_files[0]}")
    
    try:
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                if len(all_judges) >= 10:  # Limit to avoid too much output
                    break
                
                try:
                    case_data = json.loads(line.strip())
                    text = case_data.get('text', '')
                    
                    if not text or len(text) < 500:
                        continue
                    
                    # Check for judge indicators
                    text_lower = text.lower()
                    if not any(word in text_lower for word in ['judge', 'justice', 'court']):
                        continue
                    
                    case_name = case_data.get('name', f'Case {line_num}')
                    print(f"\n📄 CASE: {case_name[:50]}...")
                    print(f"   Text length: {len(text):,} characters")
                    
                    # Extract judges
                    judges = enhancer._extract_judges_from_text(text)
                    
                    print(f"   📊 Judges extracted: {len(judges)}")
                    
                    for judge in judges:
                        words = len(judge['name'].split())
                        word_status = "✅ Full" if words > 1 else "❌ Partial"
                        print(f"      - {judge['name']} ({word_status}, {words} words, role: {judge['role']})")
                        all_judges.append(judge)
                    
                    if judges:  # Only process cases with judges
                        break
                
                except (json.JSONDecodeError, Exception):
                    continue
        
        enhancer.close()
        
        print(f"\n📊 CAP SUMMARY:")
        print(f"   Total judges: {len(all_judges)}")
        full_names = [j for j in all_judges if len(j['name'].split()) > 1]
        print(f"   Full names: {len(full_names)}/{len(all_judges)} ({len(full_names)/len(all_judges)*100:.1f}%)")
        
        return all_judges
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []


def analyze_judge_quality(all_judges):
    """Analyze the quality of extracted judges"""
    
    print(f"\n🔍 JUDGE EXTRACTION QUALITY ANALYSIS")
    print("=" * 60)
    
    if not all_judges:
        print("❌ No judges to analyze")
        return False
    
    # Categorize judges
    full_names = [j for j in all_judges if len(j['name'].split()) > 1]
    single_names = [j for j in all_judges if len(j['name'].split()) == 1]
    
    middle_initial_judges = [j for j in full_names 
                           if any(len(word) == 2 and word.endswith('.') 
                                 for word in j['name'].split())]
    
    three_word_judges = [j for j in full_names if len(j['name'].split()) >= 3]
    
    print(f"📊 JUDGE CATEGORIES:")
    print(f"   Total judges: {len(all_judges)}")
    print(f"   Full names (2+ words): {len(full_names)} ({len(full_names)/len(all_judges)*100:.1f}%)")
    print(f"   Single names (1 word): {len(single_names)} ({len(single_names)/len(all_judges)*100:.1f}%)")
    print(f"   With middle initials: {len(middle_initial_judges)} ({len(middle_initial_judges)/len(all_judges)*100:.1f}%)")
    print(f"   Three+ words: {len(three_word_judges)} ({len(three_word_judges)/len(all_judges)*100:.1f}%)")
    
    print(f"\n📋 SAMPLE FULL NAMES:")
    for judge in full_names[:10]:
        print(f"   ✅ {judge['name']} ({len(judge['name'].split())} words)")
    
    if single_names:
        print(f"\n📋 SAMPLE SINGLE NAMES:")
        for judge in single_names[:5]:
            print(f"   ❌ {judge['name']} (1 word)")
    
    # Success criteria
    full_name_ratio = len(full_names) / len(all_judges)
    quality_ratio = len(middle_initial_judges) / len(all_judges)
    
    success = (
        len(all_judges) > 0 and
        full_name_ratio >= 0.6 and  # At least 60% full names
        len(full_names) >= len(single_names)  # More full names than single names
    )
    
    print(f"\n🎯 QUALITY ASSESSMENT:")
    print(f"   Judges found: {'✅' if len(all_judges) > 0 else '❌'} ({len(all_judges)})")
    print(f"   Full name ratio: {'✅' if full_name_ratio >= 0.6 else '❌'} ({full_name_ratio:.1%})")
    print(f"   Quality ratio: {'✅' if quality_ratio > 0 else '⚠️'} ({quality_ratio:.1%})")
    print(f"   Full > Single: {'✅' if len(full_names) >= len(single_names) else '❌'} ({len(full_names)} vs {len(single_names)})")
    
    if success:
        print(f"\n🎉 JUDGE EXTRACTION QUALITY: SUCCESS!")
        print(f"✅ Full name extraction working on real data")
        print(f"✅ {full_name_ratio:.1%} full name ratio")
    else:
        print(f"\n⚠️ JUDGE EXTRACTION QUALITY: NEEDS IMPROVEMENT!")
        if full_name_ratio < 0.6:
            print(f"❌ Low full name ratio: {full_name_ratio:.1%}")
    
    return success


def main():
    """Run judge extraction tests on real data"""
    
    print("🧪 REAL DATA JUDGE EXTRACTION TEST")
    print("=" * 80)
    print("🎯 Testing judge extraction on real CourtListener and CAP data")
    
    # Test CourtListener
    cl_judges = test_courtlistener_judge_extraction()
    
    # Test CAP
    cap_judges = test_cap_judge_extraction()
    
    # Combine and analyze
    all_judges = cl_judges + cap_judges
    
    if all_judges:
        success = analyze_judge_quality(all_judges)
        
        if success:
            print(f"\n🎉 REAL DATA JUDGE EXTRACTION: SUCCESS!")
            print(f"✅ Judge extraction working on real data from both sources")
            return True
        else:
            print(f"\n⚠️ REAL DATA JUDGE EXTRACTION: PARTIAL SUCCESS!")
            print(f"⚠️ Judge extraction working but quality could be improved")
            return True
    else:
        print(f"\n❌ REAL DATA JUDGE EXTRACTION: FAILED!")
        print(f"❌ No judges extracted from real data")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
