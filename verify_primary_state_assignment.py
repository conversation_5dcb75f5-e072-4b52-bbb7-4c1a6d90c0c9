#!/usr/bin/env python3
"""
Verify Primary State Assignment
Each case gets assigned to exactly ONE primary state, and sum should equal total cases
"""

import os
import gzip
import json
import re
from pathlib import Path
from collections import defaultdict

class PrimaryStateDetector:
    """Assigns each case to exactly one primary state."""
    
    def __init__(self):
        """Initialize with state patterns and priority rules."""
        
        # State patterns (same as before)
        self.state_patterns = {
            'alabama': ['alabama', 'ala\.', r'\bala\b'],
            'alaska': ['alaska', r'\bak\b'],
            'arizona': ['arizona', 'ariz\.', r'\bariz\b'],
            'arkansas': ['arkansas', 'ark\.', r'\bark\b'],
            'california': ['california', 'cal\.', 'calif\.', r'\bcal\b', r'\bca\b'],
            'colorado': ['colorado', 'colo\.', r'\bcolo\b'],
            'connecticut': ['connecticut', 'conn\.', r'\bconn\b'],
            'delaware': ['delaware', 'del\.', r'\bdel\b'],
            'florida': ['florida', 'fla\.', r'\bfla\b'],
            'georgia': ['georgia', 'ga\.', r'\bga\b'],
            'hawaii': ['hawaii', r'\bhi\b'],
            'idaho': ['idaho', r'\bid\b'],
            'illinois': ['illinois', 'ill\.', r'\bill\b'],
            'indiana': ['indiana', 'ind\.', r'\bind\b'],
            'iowa': ['iowa', r'\bia\b'],
            'kansas': ['kansas', 'kan\.', 'kans\.', r'\bkan\b'],
            'kentucky': ['kentucky', 'ky\.', r'\bky\b'],
            'louisiana': ['louisiana', 'la\.', r'\bla\b'],
            'maine': ['maine', 'me\.', r'\bme\b'],
            'maryland': ['maryland', 'md\.', r'\bmd\b'],
            'massachusetts': ['massachusetts', 'mass\.', r'\bmass\b'],
            'michigan': ['michigan', 'mich\.', r'\bmich\b'],
            'minnesota': ['minnesota', 'minn\.', r'\bminn\b'],
            'mississippi': ['mississippi', 'miss\.', r'\bmiss\b'],
            'missouri': ['missouri', 'mo\.', r'\bmo\b'],
            'montana': ['montana', 'mont\.', r'\bmont\b'],
            'nebraska': ['nebraska', 'neb\.', 'nebr\.', r'\bneb\b'],
            'nevada': ['nevada', 'nev\.', r'\bnev\b'],
            'new_hampshire': ['new hampshire', 'n\.h\.', r'\bn\.?h\b'],
            'new_jersey': ['new jersey', 'n\.j\.', r'\bn\.?j\b'],
            'new_mexico': ['new mexico', 'n\.m\.', 'n\. mex\.', r'\bn\.?m\b'],
            'new_york': ['new york', 'n\.y\.', r'\bn\.?y\b'],
            'north_carolina': ['north carolina', 'n\.c\.', r'\bn\.?c\b'],
            'north_dakota': ['north dakota', 'n\.d\.', r'\bn\.?d\b'],
            'ohio': ['ohio', r'\bohio\b'],
            'oklahoma': ['oklahoma', 'okla\.', r'\bokla\b'],
            'oregon': ['oregon', 'ore\.', 'oreg\.', r'\bore\b'],
            'pennsylvania': ['pennsylvania', 'pa\.', 'penn\.', r'\bpa\b'],
            'rhode_island': ['rhode island', 'r\.i\.', r'\br\.?i\b'],
            'south_carolina': ['south carolina', 's\.c\.', r'\bs\.?c\b'],
            'south_dakota': ['south dakota', 's\.d\.', r'\bs\.?d\b'],
            'tennessee': ['tennessee', 'tenn\.', r'\btenn\b'],
            'texas': ['texas', 'tex\.', r'\btex\b', r'\btx\b'],
            'utah': ['utah', r'\butah\b'],
            'vermont': ['vermont', 'vt\.', r'\bvt\b'],
            'virginia': ['virginia', 'va\.', r'\bva\b'],
            'washington': ['washington', 'wash\.', r'\bwash\b'],
            'west_virginia': ['west virginia', 'w\.va\.', 'w\. va\.', r'\bw\.?va\b'],
            'wisconsin': ['wisconsin', 'wis\.', 'wisc\.', r'\bwis\b'],
            'wyoming': ['wyoming', 'wyo\.', r'\bwyo\b'],
            'federal': ['federal', 'united states', 'u\.s\.', 'supreme court of the united states', 'scotus'],
            'district_of_columbia': ['district of columbia', 'd\.c\.', r'\bd\.?c\b']
        }
        
        # Compile patterns
        self.compiled_patterns = {}
        for state, patterns in self.state_patterns.items():
            combined_pattern = '|'.join(f'({pattern})' for pattern in patterns)
            self.compiled_patterns[state] = re.compile(combined_pattern, re.IGNORECASE)
        
        # Priority order for when multiple states are detected
        # More specific/unique patterns get higher priority
        self.state_priority = [
            'district_of_columbia',  # Very specific
            'west_virginia',         # Specific compound name
            'new_hampshire', 'new_jersey', 'new_mexico', 'new_york',  # "New" states
            'north_carolina', 'north_dakota', 'south_carolina', 'south_dakota',  # Directional states
            'rhode_island',          # Specific compound name
            # Then alphabetical for regular states
            'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut',
            'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa',
            'kansas', 'kentucky', 'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan',
            'minnesota', 'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 'ohio',
            'oklahoma', 'oregon', 'pennsylvania', 'tennessee', 'texas', 'utah', 'vermont',
            'virginia', 'washington', 'wisconsin', 'wyoming',
            'federal'  # Federal is lowest priority (often mentioned alongside state courts)
        ]
    
    def detect_primary_state(self, case_data):
        """Detect the PRIMARY state for a case (exactly one state per case)."""
        
        text = case_data.get('text', '').lower()
        
        # Find all matching states
        detected_states = []
        for state, pattern in self.compiled_patterns.items():
            if pattern.search(text):
                detected_states.append(state)
        
        if not detected_states:
            return 'unclassified'
        elif len(detected_states) == 1:
            return detected_states[0]
        else:
            # Multiple states detected - use priority order
            for priority_state in self.state_priority:
                if priority_state in detected_states:
                    return priority_state
            
            # Fallback to first detected (shouldn't happen with good priority list)
            return detected_states[0]

def verify_primary_state_assignment():
    """Verify that sum of primary state assignments = total cases."""
    
    print("🔍 VERIFYING PRIMARY STATE ASSIGNMENT")
    print("Each case assigned to exactly ONE primary state")
    print("=" * 60)
    
    detector = PrimaryStateDetector()
    data_dir = Path("data/caselaw_access_project")
    cap_files = list(data_dir.glob("*.jsonl.gz"))
    
    print(f"📁 Found {len(cap_files)} CAP files")
    print(f"🎯 Will verify on first 3 files")
    print()
    
    verification_results = []
    
    # Test on first 3 files
    for file_idx, file_path in enumerate(cap_files[:3], 1):
        print(f"📁 [{file_idx}/3] Verifying: {file_path.name}")
        
        total_cases = 0
        primary_state_counts = defaultdict(int)
        
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            case_data = json.loads(line)
                            total_cases += 1
                            
                            # Assign to exactly ONE primary state
                            primary_state = detector.detect_primary_state(case_data)
                            primary_state_counts[primary_state] += 1
                            
                            # Progress indicator
                            if line_num % 10000 == 0:
                                print(f"    Processed {line_num:,} cases...")
                        
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            continue
        
        except Exception as e:
            print(f"  ❌ Error processing {file_path.name}: {e}")
            continue
        
        # Verification check
        total_assigned = sum(primary_state_counts.values())
        
        print(f"  📊 Results for {file_path.name}:")
        print(f"    Total cases: {total_cases:,}")
        print(f"    Total assigned: {total_assigned:,}")
        print(f"    Match: {'✅ YES' if total_cases == total_assigned else '❌ NO'}")
        
        if total_cases != total_assigned:
            print(f"    ⚠️  Difference: {abs(total_cases - total_assigned):,}")
        
        # Show top 10 states
        print(f"    Top 10 primary states:")
        sorted_states = sorted(primary_state_counts.items(), key=lambda x: x[1], reverse=True)
        for state, count in sorted_states[:10]:
            percentage = (count / total_cases) * 100 if total_cases > 0 else 0
            print(f"      {state}: {count:,} ({percentage:.1f}%)")
        
        verification_results.append({
            'file': file_path.name,
            'total_cases': total_cases,
            'total_assigned': total_assigned,
            'matches': total_cases == total_assigned,
            'unclassified': primary_state_counts.get('unclassified', 0),
            'top_states': sorted_states[:5]
        })
        
        print()
    
    # Overall verification summary
    print("📊 VERIFICATION SUMMARY")
    print("=" * 40)
    
    all_match = all(r['matches'] for r in verification_results)
    total_files = len(verification_results)
    matching_files = sum(1 for r in verification_results if r['matches'])
    
    print(f"Files verified: {total_files}")
    print(f"Perfect matches: {matching_files}/{total_files}")
    print(f"Overall result: {'✅ PERFECT' if all_match else '❌ ISSUES FOUND'}")
    
    # Show combined state distribution
    combined_states = defaultdict(int)
    total_all_cases = 0
    
    for result in verification_results:
        total_all_cases += result['total_cases']
        for state, count in result['top_states']:
            combined_states[state] += count
    
    print(f"\n🗺️  COMBINED STATE DISTRIBUTION:")
    sorted_combined = sorted(combined_states.items(), key=lambda x: x[1], reverse=True)
    for state, count in sorted_combined[:15]:
        percentage = (count / total_all_cases) * 100 if total_all_cases > 0 else 0
        print(f"  {state}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n🎯 ASSESSMENT:")
    if all_match:
        print("✅ PERFECT: All files have exact matches (sum of states = total cases)")
        print("🎉 Primary state assignment logic is working correctly")
        print("📊 Ready for production use with confidence")
    else:
        print("❌ ISSUES: Some files don't have exact matches")
        print("🔧 Need to investigate and fix assignment logic")
    
    return verification_results

if __name__ == "__main__":
    results = verify_primary_state_assignment()
