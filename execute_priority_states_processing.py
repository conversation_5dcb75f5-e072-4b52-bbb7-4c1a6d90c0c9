#!/usr/bin/env python3
"""
Priority States Processing Script

This script processes legal data for Texas, New York, and Florida using both:
1. Caselaw Access Project (Hugging Face dataset) - Bulk historical data
2. Court Listener API - Recent/live data

Features:
- Comprehensive deduplication using content hashes
- Source transparency tracking
- Quality assurance and validation
- Progress monitoring and checkpointing
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.caselaw_access_processor import CaselawAccessProcessor
from src.processing.courtlistener_bulk_client import CourtListenerBulkClient
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.checkpoint_manager import CheckpointManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('priority_states_processing.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class PriorityStatesProcessor:
    """
    Processes legal data for priority states (TX, NY, FL) using dual sources.
    """
    
    # Priority jurisdictions mapping
    PRIORITY_JURISDICTIONS = {
        'tx': {
            'name': 'Texas',
            'code': 'tx',
            'courts': ['tex', 'texapp', 'texcrimapp', 'texreview'],
            'target_cases': 50000  # Ambitious target for comprehensive coverage
        },
        'ny': {
            'name': 'New York', 
            'code': 'ny',
            'courts': ['ny', 'nyapp', 'nyappterm', 'nysupct'],
            'target_cases': 40000
        },
        'fl': {
            'name': 'Florida',
            'code': 'fl', 
            'courts': ['fla', 'flaapp', 'flasupct'],
            'target_cases': 35000
        }
    }
    
    def __init__(self, data_dir: str = "data/caselaw_access_project"):
        """Initialize the dual-source processor."""
        self.data_dir = Path(data_dir)
        
        # Initialize processors
        self.caselaw_processor = CaselawAccessProcessor(str(self.data_dir))
        self.courtlistener_client = CourtListenerBulkClient()
        self.supabase = SupabaseConnector()
        self.checkpoint_manager = CheckpointManager()
        
        # Processing statistics
        self.stats = {
            'caselaw_access': {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0},
            'court_listener': {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0},
            'total': {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0}
        }
        
        logger.info("Initialized Priority States Processor")
        logger.info(f"Target jurisdictions: {list(self.PRIORITY_JURISDICTIONS.keys())}")
        
    async def process_jurisdiction_dual_source(self, jurisdiction_code: str) -> Dict[str, int]:
        """
        Process a jurisdiction using both data sources.
        
        Args:
            jurisdiction_code: Jurisdiction code (tx, ny, fl)
            
        Returns:
            Processing statistics
        """
        if jurisdiction_code not in self.PRIORITY_JURISDICTIONS:
            raise ValueError(f"Jurisdiction {jurisdiction_code} not in priority list")
            
        jurisdiction_info = self.PRIORITY_JURISDICTIONS[jurisdiction_code]
        logger.info(f"🎯 Processing {jurisdiction_info['name']} ({jurisdiction_code})")
        logger.info(f"Target: {jurisdiction_info['target_cases']:,} cases")
        
        # Phase 1: Process Caselaw Access Project data
        logger.info("📚 Phase 1: Processing Caselaw Access Project data...")
        caselaw_stats = await self._process_caselaw_access_data(jurisdiction_code)
        
        # Phase 2: Process Court Listener data  
        logger.info("⚖️ Phase 2: Processing Court Listener data...")
        courtlistener_stats = await self._process_court_listener_data(jurisdiction_code)
        
        # Phase 3: Quality assurance
        logger.info("🔍 Phase 3: Quality assurance and validation...")
        qa_stats = await self._perform_quality_assurance(jurisdiction_code)
        
        # Combine statistics
        combined_stats = {
            'caselaw_access': caselaw_stats,
            'court_listener': courtlistener_stats,
            'quality_assurance': qa_stats,
            'total_unique_cases': caselaw_stats['success'] + courtlistener_stats['success'],
            'total_duplicates_prevented': caselaw_stats['duplicates'] + courtlistener_stats['duplicates']
        }
        
        logger.info(f"✅ Completed {jurisdiction_info['name']}")
        logger.info(f"📊 Total unique cases: {combined_stats['total_unique_cases']:,}")
        logger.info(f"🚫 Duplicates prevented: {combined_stats['total_duplicates_prevented']:,}")
        
        return combined_stats
        
    async def _process_caselaw_access_data(self, jurisdiction_code: str) -> Dict[str, int]:
        """Process Caselaw Access Project data for a jurisdiction."""
        try:
            # Filter files for the specific jurisdiction
            jurisdiction_files = self._get_jurisdiction_files(jurisdiction_code)
            
            if not jurisdiction_files:
                logger.warning(f"No Caselaw Access Project files found for {jurisdiction_code}")
                return {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0}
            
            logger.info(f"Found {len(jurisdiction_files)} files for {jurisdiction_code}")
            
            total_stats = {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0}
            
            for file_path in jurisdiction_files:
                logger.info(f"Processing file: {file_path.name}")
                
                # Process file with jurisdiction filtering
                file_stats = await self.caselaw_processor.process_file_with_filter(
                    file_path, 
                    jurisdiction_filter=jurisdiction_code
                )
                
                # Update totals
                for key in total_stats:
                    total_stats[key] += file_stats.get(key, 0)
                
                # Save checkpoint
                await self._save_checkpoint(jurisdiction_code, 'caselaw_access', total_stats)
                
            return total_stats
            
        except Exception as e:
            logger.error(f"Error processing Caselaw Access data for {jurisdiction_code}: {e}")
            return {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 1}
    
    async def _process_court_listener_data(self, jurisdiction_code: str) -> Dict[str, int]:
        """Process Court Listener data for a jurisdiction."""
        try:
            jurisdiction_info = self.PRIORITY_JURISDICTIONS[jurisdiction_code]
            
            # Fetch cases from Court Listener
            cases = self.courtlistener_client.fetch_jurisdiction_cases(
                jurisdiction_code, 
                limit=5000  # Reasonable limit for recent cases
            )
            
            if not cases:
                logger.warning(f"No Court Listener cases found for {jurisdiction_code}")
                return {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0}
                
            logger.info(f"Fetched {len(cases)} cases from Court Listener for {jurisdiction_code}")
            
            # Process cases through the caselaw processor
            stats = {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 0}
            
            for case in cases:
                try:
                    # Add source tracking
                    case['data_source'] = 'court_listener'
                    case['source_api'] = 'court_listener_api'
                    
                    # Process case
                    success = await self.caselaw_processor.process_court_listener_case(case)
                    
                    stats['processed'] += 1
                    if success:
                        stats['success'] += 1
                    else:
                        stats['duplicates'] += 1  # Likely duplicate
                        
                except Exception as e:
                    logger.error(f"Error processing Court Listener case: {e}")
                    stats['errors'] += 1
                    
                # Save checkpoint every 100 cases
                if stats['processed'] % 100 == 0:
                    await self._save_checkpoint(jurisdiction_code, 'court_listener', stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error processing Court Listener data for {jurisdiction_code}: {e}")
            return {'processed': 0, 'success': 0, 'duplicates': 0, 'errors': 1}
    
    async def _perform_quality_assurance(self, jurisdiction_code: str) -> Dict[str, int]:
        """Perform quality assurance checks for a jurisdiction."""
        try:
            # Get case count from database
            query = self.supabase.client.table('cases').select('id', count='exact').eq('jurisdiction', jurisdiction_code)
            result = query.execute()
            
            case_count = result.count if hasattr(result, 'count') else len(result.data)
            
            # Check for data quality issues
            quality_issues = await self._check_data_quality(jurisdiction_code)
            
            logger.info(f"Quality assurance for {jurisdiction_code}:")
            logger.info(f"  Total cases in database: {case_count:,}")
            logger.info(f"  Quality issues found: {len(quality_issues)}")
            
            return {
                'total_cases': case_count,
                'quality_issues': len(quality_issues),
                'data_completeness': self._calculate_completeness_score(jurisdiction_code)
            }
            
        except Exception as e:
            logger.error(f"Error in quality assurance for {jurisdiction_code}: {e}")
            return {'total_cases': 0, 'quality_issues': 1, 'data_completeness': 0.0}
    
    def _get_jurisdiction_files(self, jurisdiction_code: str) -> List[Path]:
        """Get JSONL files for a specific jurisdiction."""
        if not self.data_dir.exists():
            logger.warning(f"Data directory {self.data_dir} does not exist")
            return []
            
        # Look for files that might contain the jurisdiction
        # This is a simplified approach - in practice, you'd need to know the file structure
        all_files = list(self.data_dir.glob("*.jsonl"))
        
        # For now, return all files (filtering will happen during processing)
        return all_files
    
    async def _check_data_quality(self, jurisdiction_code: str) -> List[str]:
        """Check for data quality issues in a jurisdiction."""
        issues = []
        
        try:
            # Check for cases without essential fields
            query = self.supabase.client.table('cases').select('id', 'case_name', 'date_filed', 'court').eq('jurisdiction', jurisdiction_code)
            result = query.execute()
            
            for case in result.data:
                if not case.get('case_name'):
                    issues.append(f"Case {case['id']} missing case_name")
                if not case.get('date_filed'):
                    issues.append(f"Case {case['id']} missing date_filed")
                if not case.get('court'):
                    issues.append(f"Case {case['id']} missing court")
                    
        except Exception as e:
            logger.error(f"Error checking data quality: {e}")
            issues.append(f"Quality check failed: {e}")
            
        return issues
    
    def _calculate_completeness_score(self, jurisdiction_code: str) -> float:
        """Calculate data completeness score for a jurisdiction."""
        try:
            # This is a simplified completeness calculation
            # In practice, you'd check multiple fields and weight them
            return 0.85  # Placeholder
        except Exception:
            return 0.0
    
    async def _save_checkpoint(self, jurisdiction_code: str, source: str, stats: Dict[str, int]):
        """Save processing checkpoint."""
        checkpoint_data = {
            'jurisdiction': jurisdiction_code,
            'source': source,
            'timestamp': datetime.now().isoformat(),
            'stats': stats
        }
        
        checkpoint_id = f"{jurisdiction_code}_{source}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.checkpoint_manager.save_checkpoint(checkpoint_id, checkpoint_data)
    
    async def process_all_priority_jurisdictions(self) -> Dict[str, Dict[str, int]]:
        """Process all priority jurisdictions."""
        logger.info("🚀 Starting processing of all priority jurisdictions")
        
        all_results = {}
        
        for jurisdiction_code in self.PRIORITY_JURISDICTIONS.keys():
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"Processing {jurisdiction_code.upper()}")
                logger.info(f"{'='*60}")
                
                results = await self.process_jurisdiction_dual_source(jurisdiction_code)
                all_results[jurisdiction_code] = results
                
                # Log summary
                logger.info(f"✅ {jurisdiction_code.upper()} completed:")
                logger.info(f"   Unique cases: {results['total_unique_cases']:,}")
                logger.info(f"   Duplicates prevented: {results['total_duplicates_prevented']:,}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process {jurisdiction_code}: {e}")
                all_results[jurisdiction_code] = {'error': str(e)}
        
        # Generate final summary
        self._generate_final_summary(all_results)
        
        return all_results
    
    def _generate_final_summary(self, results: Dict[str, Dict[str, int]]):
        """Generate final processing summary."""
        logger.info(f"\n{'='*80}")
        logger.info("🎯 FINAL PROCESSING SUMMARY")
        logger.info(f"{'='*80}")
        
        total_cases = 0
        total_duplicates = 0
        
        for jurisdiction_code, stats in results.items():
            if 'error' not in stats:
                jurisdiction_name = self.PRIORITY_JURISDICTIONS[jurisdiction_code]['name']
                cases = stats.get('total_unique_cases', 0)
                duplicates = stats.get('total_duplicates_prevented', 0)
                
                logger.info(f"{jurisdiction_name:12}: {cases:8,} cases, {duplicates:6,} duplicates prevented")
                
                total_cases += cases
                total_duplicates += duplicates
        
        logger.info(f"{'='*80}")
        logger.info(f"{'TOTAL':12}: {total_cases:8,} cases, {total_duplicates:6,} duplicates prevented")
        logger.info(f"{'='*80}")
        
        # Calculate efficiency metrics
        if total_cases + total_duplicates > 0:
            efficiency = (total_cases / (total_cases + total_duplicates)) * 100
            logger.info(f"Processing efficiency: {efficiency:.1f}% (unique cases / total processed)")


async def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Priority States Legal Data Processing")
    parser.add_argument(
        "--jurisdiction", 
        choices=['tx', 'ny', 'fl', 'all'],
        default='all',
        help="Jurisdiction to process (default: all)"
    )
    parser.add_argument(
        "--data-dir",
        default="data/caselaw_access_project",
        help="Directory containing Caselaw Access Project data"
    )
    parser.add_argument(
        "--resume",
        help="Resume from checkpoint ID"
    )
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = PriorityStatesProcessor(args.data_dir)
    
    try:
        if args.jurisdiction == 'all':
            # Process all priority jurisdictions
            results = await processor.process_all_priority_jurisdictions()
        else:
            # Process single jurisdiction
            results = await processor.process_jurisdiction_dual_source(args.jurisdiction)
            
        logger.info("🎉 Processing completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
