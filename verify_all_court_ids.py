#!/usr/bin/env python3
"""
Verify all court IDs we're using in Texas, New York, and Florida are valid.
"""

import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

def verify_court_ids():
    """Verify all court IDs we're using are valid."""
    
    api_key = os.getenv("COURTLISTENER_API_KEY")
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    headers = {
        "Authorization": f"Token {api_key}",
        "User-Agent": "TexasLawsPersonalInjury/1.0"
    }
    
    print("🔍 VERIFYING ALL COURT IDs")
    print("=" * 50)
    
    # Court IDs we're using in our code
    court_lists = {
        'Texas': [
            'tex',        # Texas Supreme Court
            'texcrimapp', # Texas Court of Criminal Appeals
            'texapp',     # Texas Courts of Appeals
            'txnd',       # N.D. Texas
            'txed',       # E.D. Texas  
            'txsd',       # S.D. Texas
            'txwd',       # W.D. Texas
            'txeb',       # E.D. Texas Bankruptcy
            'txnb',       # N.D. Texas Bankruptcy
            'txsb',       # S.D. Texas Bankruptcy
            'txwb',       # W.D. Texas Bankruptcy
            'ca5'         # 5th Circuit Court of Appeals
        ],
        'New York': [
            'ny',         # NY Court of Appeals
            'nyappdiv',   # NY Appellate Division
            'nynd',       # N.D. New York
            'nyed',       # E.D. New York
            'nysd',       # S.D. New York
            'nywd',       # W.D. New York
            'ca2'         # 2nd Circuit Court of Appeals
        ],
        'Florida': [
            'fla',            # Supreme Court of Florida
            'fladistctapp',   # District Court of Appeal of Florida
            'fladistctapp1',  # Florida First District Court of Appeal
            'fladistctapp2',  # Florida Second District Court of Appeal
            'fladistctapp3',  # Florida Third District Court of Appeal
            'flnd',           # N.D. Florida
            'flmd',           # M.D. Florida
            'flsd',           # S.D. Florida
            'flnb',           # N.D. Florida Bankruptcy
            'flmb',           # M.D. Florida Bankruptcy
            'flsb'            # S.D. Florida Bankruptcy
        ]
    }
    
    for state, court_ids in court_lists.items():
        print(f"\n🏛️ VERIFYING {state.upper()} COURTS:")
        
        valid_courts = []
        invalid_courts = []
        
        for court_id in court_ids:
            try:
                # Check if court exists
                response = requests.get(f"{base_url}/courts/{court_id}/", headers=headers)
                
                if response.status_code == 200:
                    court_data = response.json()
                    court_name = court_data.get('full_name', 'N/A')
                    jurisdiction = court_data.get('jurisdiction', 'N/A')
                    
                    # Test if court has cases
                    params = {
                        'docket__court': court_id,
                        'format': 'json',
                        'page_size': 1,
                        'date_filed__gte': '2020-01-01'
                    }
                    
                    cases_response = requests.get(f"{base_url}/clusters/", headers=headers, params=params)
                    
                    if cases_response.status_code == 200:
                        cases = cases_response.json()
                        case_count = len(cases.get('results', []))
                        
                        print(f"   ✅ {court_id}: {court_name} ({jurisdiction}) - {case_count} cases available")
                        valid_courts.append(court_id)
                    else:
                        print(f"   ⚠️  {court_id}: {court_name} ({jurisdiction}) - Error fetching cases: {cases_response.status_code}")
                        valid_courts.append(court_id)  # Court exists but might have no cases
                
                elif response.status_code == 404:
                    print(f"   ❌ {court_id}: Court not found (404)")
                    invalid_courts.append(court_id)
                else:
                    print(f"   ❌ {court_id}: Error {response.status_code}")
                    invalid_courts.append(court_id)
            
            except Exception as e:
                print(f"   ❌ {court_id}: Exception - {e}")
                invalid_courts.append(court_id)
        
        print(f"\n   📊 {state} Summary:")
        print(f"      Valid courts: {len(valid_courts)}/{len(court_ids)}")
        print(f"      Invalid courts: {invalid_courts}")
        
        if valid_courts:
            print(f"      ✅ Recommended list: {valid_courts}")

if __name__ == "__main__":
    verify_court_ids()
