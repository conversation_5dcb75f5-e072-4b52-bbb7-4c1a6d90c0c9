#!/usr/bin/env python3
"""
Enhanced Texas Legal Corpus Processor
Main script using the new chunked processing system with all enhanced features
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.chunked_court_listener_processor import ChunkedCourtListenerProcessor

# Import existing infrastructure
from supabase import create_client, Client
import pinecone
from neo4j import GraphDatabase

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'enhanced_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MockGCSClient:
    """Mock GCS client for testing - replace with real implementation"""
    def __init__(self):
        self.name = "mock_gcs"
        logger.info("Using mock GCS client - replace with real implementation")


class MockNeo4jClient:
    """Mock Neo4j client for testing - replace with real implementation"""
    def __init__(self):
        self.name = "mock_neo4j"
        logger.info("Using mock Neo4j client - replace with real implementation")


async def setup_clients():
    """Setup all storage clients"""
    logger.info("🔧 Setting up storage clients...")
    
    # Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")

    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")
    
    supabase: Client = create_client(supabase_url, supabase_key)
    logger.info("✅ Supabase client initialized")
    
    # Pinecone client
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    pinecone_index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")

    if not pinecone_api_key:
        raise ValueError("PINECONE_API_KEY must be set")

    try:
        from pinecone import Pinecone
        pc = Pinecone(api_key=pinecone_api_key)
        pinecone_client = pc.Index(pinecone_index_name)
    except ImportError:
        # Fallback to older pinecone client
        pinecone.init(api_key=pinecone_api_key)
        pinecone_client = pinecone.Index(pinecone_index_name)

    logger.info(f"✅ Pinecone client initialized (index: {pinecone_index_name})")
    
    # GCS client (mock for now)
    gcs_client = MockGCSClient()
    logger.info("✅ GCS client initialized (mock)")
    
    # Neo4j client (mock for now)
    neo4j_client = MockNeo4jClient()
    logger.info("✅ Neo4j client initialized (mock)")
    
    return supabase, gcs_client, pinecone_client, neo4j_client


async def run_enhanced_processing(
    start_year: int = 1994,
    end_year: int = 2025,
    resume: bool = True,
    chunk_size: int = 20000,
    batch_size: int = 1000
):
    """
    Run the enhanced Texas legal corpus processing
    
    Args:
        start_year: Starting year for processing
        end_year: Ending year for processing  
        resume: Whether to resume from checkpoint
        chunk_size: Cases per chunk (20K recommended)
        batch_size: Cases per batch (1K recommended)
    """
    logger.info("🚀 Starting Enhanced Texas Legal Corpus Processing")
    logger.info(f"📅 Processing years: {start_year}-{end_year}")
    logger.info(f"📦 Chunk size: {chunk_size:,} cases")
    logger.info(f"🔄 Batch size: {batch_size:,} cases")
    logger.info(f"💾 Resume from checkpoint: {resume}")
    
    # Get API key
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        raise ValueError("COURTLISTENER_API_KEY must be set in environment")
    
    # Setup storage clients
    supabase, gcs_client, pinecone_client, neo4j_client = await setup_clients()
    
    # Initialize enhanced processor
    async with ChunkedCourtListenerProcessor(
        api_key=api_key,
        supabase_client=supabase,
        gcs_client=gcs_client,
        pinecone_client=pinecone_client,
        neo4j_client=neo4j_client,
        chunk_size=chunk_size,
        batch_size=batch_size
    ) as processor:
        
        logger.info("🎯 Enhanced processor initialized")
        
        # Start processing
        start_time = datetime.now()
        
        try:
            results = await processor.process_jurisdiction_chunked(
                jurisdiction='tx',
                start_year=start_year,
                end_year=end_year,
                resume=resume
            )
            
            # Processing complete
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("🎉 Enhanced processing completed successfully!")
            logger.info(f"📊 Results: {results}")
            logger.info(f"⏱️  Total duration: {duration}")
            
            # Print summary
            print("\n" + "="*80)
            print("🎉 ENHANCED TEXAS PROCESSING COMPLETE")
            print("="*80)
            print(f"📦 Chunks processed: {results.get('chunks_processed', 0)}")
            print(f"❌ Chunks failed: {results.get('chunks_failed', 0)}")
            print(f"📄 Total cases processed: {results.get('total_cases_processed', 0):,}")
            print(f"⏱️  Processing time: {results.get('processing_time', duration)}")
            print(f"📈 Success rate: {(results.get('chunks_processed', 0) / max(results.get('chunks_processed', 0) + results.get('chunks_failed', 0), 1)):.1%}")
            print("="*80)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Enhanced processing failed: {e}")
            raise


async def run_mini_test():
    """Run a mini test with a small date range"""
    logger.info("🧪 Running mini test (January 2024 only)")
    
    try:
        results = await run_enhanced_processing(
            start_year=2024,
            end_year=2024,  # Just 2024
            resume=False,   # Fresh start for test
            chunk_size=1000,  # Small chunks for testing
            batch_size=100    # Small batches for testing
        )
        
        logger.info("✅ Mini test completed successfully!")
        return results
        
    except Exception as e:
        logger.error(f"❌ Mini test failed: {e}")
        raise


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced Texas Legal Corpus Processor")
    parser.add_argument("--mini-test", action="store_true", help="Run mini test (2024 only)")
    parser.add_argument("--start-year", type=int, default=1994, help="Starting year")
    parser.add_argument("--end-year", type=int, default=2025, help="Ending year")
    parser.add_argument("--no-resume", action="store_true", help="Don't resume from checkpoint")
    parser.add_argument("--chunk-size", type=int, default=20000, help="Cases per chunk")
    parser.add_argument("--batch-size", type=int, default=1000, help="Cases per batch")
    
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    try:
        if args.mini_test:
            results = await run_mini_test()
        else:
            results = await run_enhanced_processing(
                start_year=args.start_year,
                end_year=args.end_year,
                resume=not args.no_resume,
                chunk_size=args.chunk_size,
                batch_size=args.batch_size
            )
        
        logger.info("🎯 Processing completed successfully")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️  Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"💥 Processing failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
