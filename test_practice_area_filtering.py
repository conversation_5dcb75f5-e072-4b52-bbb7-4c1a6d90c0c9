#!/usr/bin/env python3
"""
Comprehensive test script for Court Listener practice area filtering.
Tests all 7 practice areas with detailed validation.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any
from src.processing.courtlistener_bulk_client import CourtListenerBulkClient

class PracticeAreaFilterTester:
    """Test Court Listener filtering for all practice areas."""
    
    def __init__(self):
        self.client = CourtListenerBulkClient()
        self.practice_areas = [
            'personal injury',
            'criminal defense', 
            'family law',
            'estate planning',
            'immigration law',
            'real estate',
            'bankruptcy'
        ]
        self.results = {}
        
    def test_single_practice_area(self, practice_area: str, limit: int = 10) -> Dict[str, Any]:
        """Test filtering for a single practice area."""
        print(f"\n🔍 Testing: {practice_area.upper()}")
        print("=" * 50)
        
        start_time = time.time()
        
        try:
            cases = self.client.fetch_jurisdiction_cases(
                jurisdiction='tx',
                limit=limit,
                practice_areas=[practice_area]
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_area': practice_area,
                'cases_found': len(cases),
                'duration_seconds': round(duration, 2),
                'success': True,
                'error': None,
                'sample_cases': []
            }
            
            # Extract sample case information
            for i, case in enumerate(cases[:3]):  # Show first 3 cases
                case_info = {
                    'case_name': case.get('case_name', 'N/A'),
                    'docket_number': case.get('docket_number', 'N/A'),
                    'date_created': case.get('date_created', 'N/A'),
                    'court': case.get('court', {}).get('full_name', 'N/A') if case.get('court') else 'N/A'
                }
                result['sample_cases'].append(case_info)
            
            # Print results
            print(f"✅ Found: {len(cases)} cases")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            
            if cases:
                print(f"\n📋 Sample cases:")
                for i, case_info in enumerate(result['sample_cases'], 1):
                    print(f"   {i}. {case_info['case_name']}")
                    print(f"      Court: {case_info['court']}")
                    print(f"      Docket: {case_info['docket_number']}")
                    print(f"      Date: {case_info['date_created']}")
                    print()
            else:
                print("⚠️  No cases found for this practice area")
                
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_area': practice_area,
                'cases_found': 0,
                'duration_seconds': round(duration, 2),
                'success': False,
                'error': str(e),
                'sample_cases': []
            }
            
            print(f"❌ Error: {str(e)}")
            return result
    
    def test_multiple_practice_areas(self, areas: List[str], limit: int = 15) -> Dict[str, Any]:
        """Test filtering for multiple practice areas combined."""
        print(f"\n🔍 Testing MULTIPLE AREAS: {', '.join(areas).upper()}")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            cases = self.client.fetch_jurisdiction_cases(
                jurisdiction='tx',
                limit=limit,
                practice_areas=areas
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_areas': areas,
                'cases_found': len(cases),
                'duration_seconds': round(duration, 2),
                'success': True,
                'error': None
            }
            
            print(f"✅ Found: {len(cases)} cases")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_areas': areas,
                'cases_found': 0,
                'duration_seconds': round(duration, 2),
                'success': False,
                'error': str(e)
            }
            
            print(f"❌ Error: {str(e)}")
            return result
    
    def test_no_filtering(self, limit: int = 10) -> Dict[str, Any]:
        """Test fetching cases without any practice area filtering."""
        print(f"\n🔍 Testing: NO FILTERING (ALL CASES)")
        print("=" * 50)
        
        start_time = time.time()
        
        try:
            cases = self.client.fetch_jurisdiction_cases(
                jurisdiction='tx',
                limit=limit,
                practice_areas=None
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_area': 'no_filter',
                'cases_found': len(cases),
                'duration_seconds': round(duration, 2),
                'success': True,
                'error': None
            }
            
            print(f"✅ Found: {len(cases)} cases")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'practice_area': 'no_filter',
                'cases_found': 0,
                'duration_seconds': round(duration, 2),
                'success': False,
                'error': str(e)
            }
            
            print(f"❌ Error: {str(e)}")
            return result
    
    def run_comprehensive_test(self):
        """Run comprehensive test of all practice areas."""
        print("🚀 COMPREHENSIVE COURT LISTENER PRACTICE AREA FILTERING TEST")
        print("=" * 70)
        print(f"Timestamp: {datetime.now().isoformat()}")
        print(f"Jurisdiction: Texas (tx)")
        print(f"Practice Areas: {len(self.practice_areas)}")
        print("=" * 70)
        
        # Test 1: Individual practice areas
        print("\n📋 PHASE 1: Testing Individual Practice Areas")
        for area in self.practice_areas:
            result = self.test_single_practice_area(area, limit=8)
            self.results[area] = result
            time.sleep(1)  # Rate limiting
        
        # Test 2: Multiple practice areas
        print("\n📋 PHASE 2: Testing Multiple Practice Areas")
        multi_test_1 = self.test_multiple_practice_areas(['personal injury', 'criminal defense'], limit=12)
        self.results['multi_pi_cd'] = multi_test_1
        time.sleep(1)
        
        multi_test_2 = self.test_multiple_practice_areas(['family law', 'real estate', 'bankruptcy'], limit=15)
        self.results['multi_family_re_bank'] = multi_test_2
        time.sleep(1)
        
        # Test 3: No filtering
        print("\n📋 PHASE 3: Testing No Filtering")
        no_filter_result = self.test_no_filtering(limit=10)
        self.results['no_filter'] = no_filter_result
        
        # Generate summary
        self.print_summary()
        
        return self.results
    
    def print_summary(self):
        """Print comprehensive test summary."""
        print("\n" + "=" * 70)
        print("📊 TEST SUMMARY")
        print("=" * 70)
        
        successful_tests = 0
        total_cases_found = 0
        total_duration = 0
        
        print(f"{'Practice Area':<20} {'Cases':<8} {'Duration':<10} {'Status':<10}")
        print("-" * 60)
        
        for key, result in self.results.items():
            if isinstance(result.get('practice_areas'), list):
                area_name = f"{len(result['practice_areas'])} areas"
            else:
                area_name = result.get('practice_area', key)
            
            cases = result.get('cases_found', 0)
            duration = result.get('duration_seconds', 0)
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            
            print(f"{area_name:<20} {cases:<8} {duration:<10.2f} {status:<10}")
            
            if result.get('success', False):
                successful_tests += 1
            total_cases_found += cases
            total_duration += duration
        
        print("-" * 60)
        print(f"{'TOTALS':<20} {total_cases_found:<8} {total_duration:<10.2f}")
        print()
        print(f"✅ Successful tests: {successful_tests}/{len(self.results)}")
        print(f"📊 Total cases found: {total_cases_found}")
        print(f"⏱️  Total duration: {total_duration:.2f} seconds")
        
        # Success rate
        success_rate = (successful_tests / len(self.results)) * 100
        print(f"🎯 Success rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT: Filtering system is working perfectly!")
        elif success_rate >= 70:
            print("✅ GOOD: Filtering system is working well with minor issues")
        else:
            print("⚠️  NEEDS ATTENTION: Filtering system has significant issues")

def main():
    """Main test execution."""
    tester = PracticeAreaFilterTester()
    results = tester.run_comprehensive_test()
    
    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"practice_area_test_results_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {filename}")

if __name__ == "__main__":
    main()
