#!/usr/bin/env python3
"""
Check if the comprehensive Texas processing is complete
"""
import subprocess
import sys
import os
from datetime import datetime

def check_process_status(terminal_id=42):
    """Check if the processing terminal is still running"""
    try:
        # This would need to be adapted based on your system
        # For now, we'll check if we can see recent activity
        return "running"  # Placeholder
    except:
        return "unknown"

def check_file_completion():
    """Check if all 32 files have been processed by looking at logs"""
    try:
        # Look for completion indicators in recent output
        # This is a simplified check
        return False  # Will be True when complete
    except:
        return False

def estimate_completion():
    """Estimate completion based on current progress"""
    from monitor_processing import get_current_stats
    
    try:
        stats = get_current_stats()
        current_cases = stats['total_cases']
        
        # Rough estimates based on typical file sizes
        # Each file might contain 200-500 cases on average
        estimated_total = 32 * 350  # 32 files * ~350 cases per file
        
        progress_percent = (current_cases / estimated_total) * 100
        
        print(f"📊 COMPLETION ESTIMATE:")
        print(f"   Current cases: {current_cases:,}")
        print(f"   Estimated total: {estimated_total:,}")
        print(f"   Progress: {progress_percent:.1f}%")
        
        if progress_percent >= 95:
            print("🎯 Processing appears to be near completion!")
        elif progress_percent >= 50:
            print("⏳ Processing is in progress...")
        else:
            print("🚀 Processing is in early stages...")
            
        return progress_percent
        
    except Exception as e:
        print(f"❌ Error estimating completion: {e}")
        return 0

def main():
    print(f"🔍 TEXAS PROCESSING COMPLETION CHECK")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50)
    
    # Check process status
    status = check_process_status()
    print(f"Process Status: {status}")
    
    # Check file completion
    file_complete = check_file_completion()
    print(f"File Processing Complete: {file_complete}")
    
    # Estimate completion
    progress = estimate_completion()
    
    print("="*50)
    
    if file_complete:
        print("✅ PROCESSING COMPLETE!")
        print("All 32 files have been processed successfully.")
    elif progress >= 95:
        print("🎯 PROCESSING NEARLY COMPLETE!")
        print("Check again in 30-60 minutes.")
    else:
        print("⏳ PROCESSING IN PROGRESS...")
        print("Use monitor_processing.py for continuous monitoring.")

if __name__ == "__main__":
    main()
