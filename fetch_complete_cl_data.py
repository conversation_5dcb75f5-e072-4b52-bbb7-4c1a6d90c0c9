#!/usr/bin/env python3
import os
import requests
import json
from collections import defaultdict
from dotenv import load_dotenv

load_dotenv()

def fetch_complete_cl_data():
    api_key = os.getenv('COURTLISTENER_API_KEY')
    headers = {'Authorization': f'Token {api_key}', 'User-Agent': 'Texas Laws Personal Injury Research'}
    
    # Court mapping to precise IDs
    court_mapping = {
        'tex': 'TXSC',
        'texcrimapp': 'TCCA', 
        'txed': 'TXED',
        'txnd': 'TXND',
        'txsd': 'TXSD', 
        'txwd': 'TXWD',
        'ca5': 'CA5'
    }
    
    # Appellate district mapping
    appellate_mapping = {
        'houston': 'TXCOA01', 'fort worth': 'TXCOA02', 'austin': 'TXCOA03',
        'san antonio': 'TXCOA04', 'dallas': 'TXCOA05', 'texarkana': 'TXCOA06',
        'amarillo': 'TXCOA07', 'el paso': 'TXCOA08', 'beaumont': 'TXCOA09',
        'waco': 'TXCOA10', 'eastland': 'TXCOA11', 'tyler': 'TXCOA12',
        'corpus christi': 'TXCOA13', 'mcallen': 'TXCOA14'
    }
    
    all_cases = []
    court_year_counts = defaultdict(lambda: defaultdict(int))
    
    # Fetch from each court
    for court_id in ['tex', 'texapp', 'texcrimapp', 'txed', 'txnd', 'txsd', 'txwd', 'ca5']:
        print(f'Fetching {court_id}...')
        
        url = 'https://www.courtlistener.com/api/rest/v4/search/'
        params = {
            'q': f'court_id:{court_id}',
            'type': 'o',
            'page_size': 50,  # Smaller pages for reliability
            'format': 'json'
        }
        
        page = 1
        while page <= 20:  # Limit pages to prevent timeout
            params['page'] = page
            
            try:
                response = requests.get(url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    cases = data.get('results', [])
                    
                    if not cases:
                        break
                    
                    for case in cases:
                        # Map court
                        mapped_court = court_mapping.get(court_id, 'UNMAPPED')
                        
                        # Special appellate court handling
                        if court_id == 'texapp':
                            court_name = case.get('court', {}).get('full_name', '').lower()
                            mapped_court = 'TXCOA'  # Default
                            for city, code in appellate_mapping.items():
                                if city in court_name:
                                    mapped_court = code
                                    break
                        
                        # Extract year
                        date_filed = case.get('dateFiled', '')
                        year_filed = None
                        if date_filed and len(date_filed) >= 4:
                            try:
                                year_filed = int(date_filed[:4])
                            except:
                                pass
                        
                        all_cases.append({
                            'court_id': mapped_court,
                            'year_filed': year_filed,
                            'case_name': case.get('caseName', ''),
                            'date_filed': date_filed
                        })
                        
                        # Count for coverage
                        court_year_counts[mapped_court][year_filed] += 1
                    
                    print(f'  Page {page}: {len(cases)} cases')
                    page += 1
                    
                else:
                    print(f'  Error {response.status_code}')
                    break
                    
            except Exception as e:
                print(f'  Error: {e}')
                break
    
    return all_cases, dict(court_year_counts)

def generate_coverage_report(cases, court_year_counts):
    # Generate court×year matrix
    coverage_data = []
    
    for court_id, years in court_year_counts.items():
        for year, count in years.items():
            coverage_data.append({
                'court_id': court_id,
                'year_filed': year,
                'case_count': count
            })
    
    # Find missing data
    missing_year = sum(1 for case in cases if case['year_filed'] is None)
    unmapped_court = sum(1 for case in cases if case['court_id'] == 'UNMAPPED')
    
    # Zero opinion court-years (would need full matrix analysis)
    zero_coverage = []
    
    return {
        'court_year_counts': coverage_data,
        'zero_coverage': zero_coverage,
        'missing_year_filed': missing_year,
        'unmapped_court_id': unmapped_court,
        'total_cases': len(cases)
    }

if __name__ == '__main__':
    cases, court_year_counts = fetch_complete_cl_data()
    report = generate_coverage_report(cases, court_year_counts)
    
    # Save results
    with open('cl_complete_coverage.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f'\\nComplete! {len(cases)} cases fetched')
    print(f'Missing year_filed: {report["missing_year_filed"]}')
    print(f'Unmapped court_id: {report["unmapped_court_id"]}')
