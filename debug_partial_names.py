#!/usr/bin/env python3
"""
Debug Partial Names
Find why we're getting "<PERSON>" instead of "<PERSON>"
"""

from judge_relationship_enhancer import JudgeRelationshipEnhancer

def debug_partial_name_extraction():
    """Debug why we're getting partial names instead of full names"""
    
    print("🔍 DEBUGGING PARTIAL NAME EXTRACTION")
    print("=" * 60)
    
    # Test text from our full name test cases
    test_text = '''
    District Judge <PERSON> delivered this opinion.
    
    Judge <PERSON> wrote the preliminary ruling.
    
    <PERSON>, District Judge:
    After careful consideration, the motion is GRANTED.
    
    <PERSON>, J., concurring in part.
    
    Mr. Chief Justice <PERSON> delivered the opinion of the Court.
    
    <PERSON>, C.J., delivered the opinion.
    '''
    
    print(f"📄 TEST TEXT:")
    print(f"   {test_text}")
    
    enhancer = JudgeRelationshipEnhancer()
    
    print(f"\n🔍 PATTERN-BY-PATTERN ANALYSIS:")
    
    import re
    
    # Test each pattern to see what extracts partial vs full names
    for i, pattern in enumerate(enhancer.judge_patterns, 1):
        print(f"\n   Pattern {i}: {pattern}")
        
        matches = re.finditer(pattern, test_text, re.IGNORECASE)
        found_matches = []
        
        for match in matches:
            if len(match.groups()) >= 1:
                extracted = match.group(1).strip()
                context_start = max(0, match.start() - 40)
                context_end = min(len(test_text), match.end() + 40)
                context = test_text[context_start:context_end].replace('\n', ' ').strip()
                
                found_matches.append({
                    'extracted': extracted,
                    'context': context,
                    'words': len(extracted.split())
                })
        
        if found_matches:
            for j, match in enumerate(found_matches, 1):
                words_status = "✅ Full" if match['words'] > 1 else "❌ Partial"
                print(f"      Match {j}: '{match['extracted']}' ({words_status}, {match['words']} words)")
                print(f"         Context: ...{match['context']}...")
                
                # Identify the problematic patterns
                if match['words'] == 1 and match['extracted'] in ['Johnson', 'Warren']:
                    print(f"         🚨 PROBLEM: This pattern extracts partial name!")
        else:
            print(f"      No matches")
    
    print(f"\n🔄 FULL EXTRACTION TEST:")
    
    # Test the full extraction
    judges = enhancer._extract_judges_from_text(test_text)
    
    print(f"   📊 Total judges extracted: {len(judges)}")
    for judge in judges:
        words = len(judge['name'].split())
        words_status = "✅ Full" if words > 1 else "❌ Partial"
        print(f"      - '{judge['name']}' ({words_status}, {words} words)")
    
    # Expected vs actual
    expected_full_names = [
        "Rick A. Warren",
        "Sarah Michelle Johnson", 
        "Earl Warren"
    ]
    
    print(f"\n🎯 EXPECTED VS ACTUAL:")
    extracted_names = [j['name'] for j in judges]
    
    for expected in expected_full_names:
        found_full = expected in extracted_names
        found_partial = any(word in extracted_names for word in expected.split())
        
        if found_full:
            print(f"      ✅ {expected} (full name found)")
        elif found_partial:
            partial_found = [name for name in extracted_names if any(word in name for word in expected.split())]
            print(f"      ⚠️ {expected} → found partial: {partial_found}")
        else:
            print(f"      ❌ {expected} (not found)")
    
    enhancer.close()


def identify_problematic_patterns():
    """Identify which patterns are extracting partial names"""
    
    print(f"\n🔬 IDENTIFYING PROBLEMATIC PATTERNS")
    print("=" * 60)
    
    # Patterns that likely extract partial names
    problematic_cases = [
        ("Sarah Michelle Johnson, J., concurring", "Johnson", "Should extract 'Sarah Michelle Johnson'"),
        ("Earl Warren, C.J., delivered", "Warren", "Should extract 'Earl Warren'"),
        ("Johnson, District Judge:", "Johnson", "Should extract full name from context"),
    ]
    
    enhancer = JudgeRelationshipEnhancer()
    
    import re
    
    for case_text, partial_name, issue in problematic_cases:
        print(f"\n📄 CASE: {case_text}")
        print(f"   Issue: {issue}")
        
        # Find which pattern extracts the partial name
        for i, pattern in enumerate(enhancer.judge_patterns, 1):
            matches = re.finditer(pattern, case_text, re.IGNORECASE)
            
            for match in matches:
                if len(match.groups()) >= 1:
                    extracted = match.group(1).strip()
                    if extracted == partial_name:
                        print(f"   🚨 Pattern {i} extracts partial name: '{extracted}'")
                        print(f"      Pattern: {pattern}")
                        break
    
    print(f"\n💡 SOLUTION STRATEGIES:")
    print(f"   1. Move abbreviated patterns (J., C.J.) to lowest priority")
    print(f"   2. Add patterns that capture full names from context")
    print(f"   3. Improve deduplication to prefer longer names more strongly")
    
    enhancer.close()


def suggest_pattern_fixes():
    """Suggest specific pattern fixes"""
    
    print(f"\n🔧 PATTERN FIX SUGGESTIONS")
    print("=" * 60)
    
    print(f"📊 CURRENT ISSUE:")
    print(f"   - Abbreviated patterns like 'Johnson, J.' extract 'Johnson'")
    print(f"   - Full name patterns like 'Judge Sarah Michelle Johnson wrote' should win")
    
    print(f"\n💡 FIXES NEEDED:")
    print(f"   1. Add pattern: 'Judge ([Full Name]) wrote'")
    print(f"   2. Move abbreviated patterns to end (lowest priority)")
    print(f"   3. Strengthen deduplication scoring for full names")
    
    print(f"\n🎯 NEW PATTERN SUGGESTIONS:")
    new_patterns = [
        r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\s+wrote',  # "Judge Sarah Michelle Johnson wrote"
        r'(?:District|Circuit)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\s+delivered',  # "District Judge Rick A. Warren delivered"
        r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\s+delivered',  # "Chief Justice Earl Warren delivered"
    ]
    
    for i, pattern in enumerate(new_patterns, 1):
        print(f"   {i}. {pattern}")
    
    print(f"\n🔄 PRIORITY REORDERING:")
    print(f"   HIGH: Full name + action patterns")
    print(f"   MEDIUM: Full name + title patterns") 
    print(f"   LOW: Abbreviated patterns (J., C.J.)")


if __name__ == "__main__":
    print("🧪 PARTIAL NAME DEBUGGING")
    print("=" * 80)
    
    # Debug partial name extraction
    debug_partial_name_extraction()
    
    # Identify problematic patterns
    identify_problematic_patterns()
    
    # Suggest fixes
    suggest_pattern_fixes()
    
    print(f"\n🎯 GOAL: Get to 7/7 full names!")
    print(f"   Target: 'Sarah Michelle Johnson' not 'Johnson'")
    print(f"   Target: 'Earl Warren' not 'Warren'")
