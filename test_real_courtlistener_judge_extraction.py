#!/usr/bin/env python3
"""
Real CourtListener Judge Extraction Test
Tests judge extraction with actual CourtListener API responses
NO SYNTHETIC/MOCK DATA - REAL API DATA ONLY
"""

import asyncio
import logging
import os
import sys
import httpx
from datetime import datetime
from typing import Dict, List, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.judge_extraction_service import JudgeExtractionService
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealCourtListenerJudgeExtractionTest:
    """Test judge extraction with real CourtListener API data"""
    
    def __init__(self):
        self.judge_extractor = JudgeExtractionService()
        self.supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_KEY')
        )
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        self.base_url = "https://www.courtlistener.com/api/rest/v4"
        self.test_batch_id = f"real_cl_judge_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    async def fetch_real_courtlistener_cases(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Fetch real cases from CourtListener API"""
        print(f"\n📡 FETCHING REAL COURTLISTENER DATA")
        print("=" * 60)
        print(f"API Key: {self.api_key[:10]}..." if self.api_key else "❌ No API key")
        print(f"Cases to fetch: {limit}")
        
        if not self.api_key:
            print("❌ No CourtListener API key found")
            return []
        
        cases = []
        
        async with httpx.AsyncClient(
            headers={"Authorization": f"Token {self.api_key}"},
            timeout=30.0
        ) as client:
            
            # Fetch recent Texas cases with judge information
            url = f"{self.base_url}/opinions/"
            params = {
                'court': 'txnd,txsd,txed,txwd',  # Texas federal districts
                'ordering': '-date_created',
                'page_size': limit,
                'format': 'json'
            }
            
            try:
                print(f"📡 Fetching from: {url}")
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                api_cases = data.get('results', [])
                
                print(f"📊 API returned {len(api_cases)} cases")
                
                # Process each case to get full details
                for i, case in enumerate(api_cases, 1):
                    case_id = case.get('id')
                    case_name = case.get('case_name', 'Unknown Case')
                    author_str = case.get('author_str', '')
                    
                    print(f"   [{i}/{len(api_cases)}] {case_name[:50]}... (ID: {case_id})")
                    print(f"      Author: {author_str or 'None'}")
                    
                    # Only include cases with some judge information
                    if author_str or case.get('plain_text') or case.get('html'):
                        cases.append(case)
                        print(f"      ✅ Added (has judge info)")
                    else:
                        print(f"      ⚠️ Skipped (no judge info)")
                
            except Exception as e:
                print(f"❌ Error fetching from API: {e}")
                return []
        
        print(f"📊 Total real CourtListener cases: {len(cases)}")
        return cases
    
    async def test_real_courtlistener_judge_extraction(self, cases: List[Dict[str, Any]]):
        """Test judge extraction on real CourtListener cases"""
        print(f"\n🔍 TESTING REAL COURTLISTENER JUDGE EXTRACTION")
        print("=" * 60)
        print(f"Testing {len(cases)} real CourtListener cases")
        
        results = {
            'total_cases': len(cases),
            'cases_with_judges': 0,
            'total_judges_found': 0,
            'judges_found': [],
            'avg_confidence': 0,
            'full_names_found': 0,
            'api_extractions': 0,
            'pattern_extractions': 0
        }
        
        for i, case in enumerate(cases, 1):
            case_id = case.get('id')
            case_name = case.get('case_name', 'Unknown Case')
            author_str = case.get('author_str', '')
            
            print(f"\n   [{i}/{len(cases)}] {case_name[:60]}...")
            print(f"      ID: {case_id}")
            print(f"      Author (API): {author_str or 'None'}")
            
            try:
                # Extract judges using real CourtListener data
                judges = self.judge_extractor.extract_judges_from_courtlistener(case)
                
                if judges:
                    results['cases_with_judges'] += 1
                    results['total_judges_found'] += len(judges)
                    results['judges_found'].extend(judges)
                    
                    # Calculate average confidence
                    confidences = [j.confidence for j in judges]
                    avg_conf = sum(confidences) / len(confidences)
                    results['avg_confidence'] = (results['avg_confidence'] + avg_conf) / 2
                    
                    print(f"      ✅ Extracted {len(judges)} judges (avg confidence: {avg_conf:.2f})")
                    
                    for j, judge in enumerate(judges[:3], 1):  # Show top 3
                        name_type = "FULL NAME" if judge.full_name else "PARTIAL"
                        method_icon = "🔗" if judge.extraction_method == "api" else "🔍"
                        
                        print(f"         {j}. {judge.name} ({name_type}, conf: {judge.confidence:.2f}) {method_icon}")
                        
                        # Track extraction methods and name types
                        if judge.full_name:
                            results['full_names_found'] += 1
                        if judge.extraction_method == "api":
                            results['api_extractions'] += 1
                        else:
                            results['pattern_extractions'] += 1
                        
                        # Validate expectations for modern CourtListener data
                        if judge.confidence < 0.5:
                            print(f"            ⚠️ Low confidence for modern case")
                        if not judge.full_name and judge.extraction_method == "api":
                            print(f"            ⚠️ Expected full name from API extraction")
                else:
                    print(f"      ❌ No judges extracted")
                    
            except Exception as e:
                print(f"      ❌ Error extracting judges: {e}")
        
        # Summary
        print(f"\n📊 REAL COURTLISTENER JUDGE EXTRACTION RESULTS")
        print("=" * 60)
        
        success_rate = (results['cases_with_judges'] / results['total_cases'] * 100) if results['total_cases'] > 0 else 0
        full_name_rate = (results['full_names_found'] / results['total_judges_found'] * 100) if results['total_judges_found'] > 0 else 0
        
        print(f"📊 Cases tested: {results['total_cases']}")
        print(f"📊 Cases with judges: {results['cases_with_judges']}")
        print(f"📊 Success rate: {success_rate:.1f}%")
        print(f"📊 Total judges found: {results['total_judges_found']}")
        print(f"📊 Average confidence: {results['avg_confidence']:.2f}")
        print(f"📊 Full names found: {results['full_names_found']}/{results['total_judges_found']} ({full_name_rate:.1f}%)")
        print(f"📊 API extractions: {results['api_extractions']}")
        print(f"📊 Pattern extractions: {results['pattern_extractions']}")
        
        # Validation for modern CourtListener data
        print(f"\n🎯 VALIDATION FOR MODERN COURTLISTENER DATA:")
        print(f"   Success Rate: {'✅ GOOD' if success_rate >= 70 else '⚠️ LOW'} ({success_rate:.1f}% - expect ≥70%)")
        print(f"   Full Name Rate: {'✅ GOOD' if full_name_rate >= 60 else '⚠️ LOW'} ({full_name_rate:.1f}% - expect ≥60%)")
        print(f"   Average Confidence: {'✅ GOOD' if results['avg_confidence'] >= 0.7 else '⚠️ LOW'} ({results['avg_confidence']:.2f} - expect ≥0.7)")
        
        return results
    
    async def test_judge_metadata_storage(self, sample_case: Dict[str, Any]):
        """Test storing judge metadata in database"""
        print(f"\n💾 TESTING JUDGE METADATA STORAGE")
        print("=" * 60)
        
        try:
            # Extract judges
            judges = self.judge_extractor.extract_judges_from_courtlistener(sample_case)
            
            if not judges:
                print("❌ No judges to test storage with")
                return False
            
            # Format for storage
            judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, sample_case)
            
            # Create test case record
            test_case = {
                'id': f'{self.test_batch_id}_storage_test',
                'case_name': sample_case.get('case_name', 'Storage Test Case'),
                'judge_name': judge_name,
                'judge_metadata': judge_metadata,
                'jurisdiction': 'TX',
                'source': 'courtlistener',
                'created_at': datetime.now().isoformat()
            }
            
            # Insert into database
            response = self.supabase.table('cases').insert(test_case).execute()
            
            # Verify insertion
            verify_response = self.supabase.table('cases').select('*').eq('id', test_case['id']).execute()
            
            if verify_response.data:
                stored_case = verify_response.data[0]
                print(f"✅ Successfully stored judge metadata:")
                print(f"   Judge Name: {stored_case['judge_name']}")
                print(f"   Judge Metadata Keys: {list(stored_case['judge_metadata'].keys()) if stored_case['judge_metadata'] else 'None'}")
                
                if stored_case['judge_metadata']:
                    metadata = stored_case['judge_metadata']
                    print(f"   Judges Count: {len(metadata.get('judges', []))}")
                    print(f"   Primary Judge: {metadata.get('primary_judge')}")
                    print(f"   Avg Confidence: {metadata.get('extraction_stats', {}).get('avg_confidence', 0):.2f}")
                
                return True
            else:
                print("❌ Failed to verify storage")
                return False
                
        except Exception as e:
            print(f"❌ Storage test failed: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        try:
            response = self.supabase.table('cases').delete().like('id', f'{self.test_batch_id}%').execute()
            print("✅ Cleaned up test data")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
    
    async def run_real_courtlistener_test(self):
        """Run comprehensive real CourtListener judge extraction test"""
        print("🎯 REAL COURTLISTENER JUDGE EXTRACTION TEST")
        print("=" * 60)
        print("❌ NO SYNTHETIC/MOCK DATA - REAL API DATA ONLY")
        print(f"Test ID: {self.test_batch_id}")
        
        try:
            # Fetch real cases
            real_cases = await self.fetch_real_courtlistener_cases(limit=10)
            
            if not real_cases:
                print("❌ No real CourtListener cases found - cannot proceed with test")
                return False
            
            # Test judge extraction
            results = await self.test_real_courtlistener_judge_extraction(real_cases)
            
            # Test storage with first case that has judges
            sample_case = None
            for case in real_cases:
                judges = self.judge_extractor.extract_judges_from_courtlistener(case)
                if judges:
                    sample_case = case
                    break
            
            storage_success = False
            if sample_case:
                storage_success = await self.test_judge_metadata_storage(sample_case)
            
            # Evaluate overall success
            success_rate = (results['cases_with_judges'] / results['total_cases'] * 100) if results['total_cases'] > 0 else 0
            success = (
                success_rate >= 50 and  # At least 50% success rate
                results['avg_confidence'] >= 0.6 and  # Reasonable confidence
                storage_success  # Storage works
            )
            
            # Clean up
            await self.cleanup_test_data()
            
            print(f"\n🎉 REAL COURTLISTENER TEST RESULT: {'✅ SUCCESS' if success else '❌ NEEDS IMPROVEMENT'}")
            return success
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            await self.cleanup_test_data()
            return False


async def main():
    """Main test function"""
    test = RealCourtListenerJudgeExtractionTest()
    success = await test.run_real_courtlistener_test()
    
    if success:
        print("\n🎉 Real CourtListener judge extraction is working correctly!")
        return 0
    else:
        print("\n⚠️ Real CourtListener judge extraction needs improvement.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
