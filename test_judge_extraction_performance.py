#!/usr/bin/env python3
"""
Judge Extraction Performance Test
Tests judge extraction performance with small batch of real data
"""

import asyncio
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.judge_extraction_service import JudgeExtractionService
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JudgeExtractionPerformanceTest:
    """Test judge extraction performance with real data"""
    
    def __init__(self):
        self.judge_extractor = JudgeExtractionService()
        self.supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_KEY')
        )
    
    async def test_performance_with_real_data(self, limit: int = 10):
        """Test judge extraction performance with real cases from database"""
        print(f"\n🚀 JUDGE EXTRACTION PERFORMANCE TEST")
        print("=" * 60)
        print(f"Testing with {limit} real cases from database")
        
        # Fetch real cases from database
        try:
            response = self.supabase.table('cases').select('*').limit(limit).execute()
            cases = response.data
            
            if not cases:
                print("❌ No cases found in database")
                return False
            
            print(f"📊 Found {len(cases)} cases to test")
            
        except Exception as e:
            print(f"❌ Failed to fetch cases: {e}")
            return False
        
        # Performance metrics
        total_cases = len(cases)
        total_judges_extracted = 0
        total_processing_time = 0
        successful_extractions = 0
        failed_extractions = 0
        
        print(f"\n🔍 Processing {total_cases} cases...")
        
        for i, case in enumerate(cases, 1):
            case_id = case.get('id', f'case_{i}')
            source = case.get('source', 'unknown')
            
            print(f"   [{i}/{total_cases}] Processing {case_id} ({source})")
            
            start_time = time.time()
            
            try:
                # Extract judges based on source
                if source == 'caselaw_access_project':
                    judges = self.judge_extractor.extract_judges_from_cap(case)
                else:
                    judges = self.judge_extractor.extract_judges_from_courtlistener(case)
                
                processing_time = time.time() - start_time
                total_processing_time += processing_time
                
                if judges:
                    total_judges_extracted += len(judges)
                    successful_extractions += 1
                    
                    # Get primary judge
                    judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, case)
                    
                    print(f"      ✅ Extracted {len(judges)} judges in {processing_time:.3f}s")
                    if judge_name:
                        print(f"         Primary: {judge_name}")
                    
                    # Show top judges
                    for j, judge in enumerate(judges[:2], 1):
                        print(f"         {j}. {judge.name} (conf: {judge.confidence:.2f})")
                else:
                    print(f"      ⚠️ No judges extracted in {processing_time:.3f}s")
                    failed_extractions += 1
                
            except Exception as e:
                processing_time = time.time() - start_time
                total_processing_time += processing_time
                failed_extractions += 1
                print(f"      ❌ Error in {processing_time:.3f}s: {e}")
        
        # Calculate performance metrics
        avg_processing_time = total_processing_time / total_cases
        success_rate = (successful_extractions / total_cases) * 100
        avg_judges_per_case = total_judges_extracted / max(successful_extractions, 1)
        
        print(f"\n📊 PERFORMANCE RESULTS")
        print("=" * 60)
        print(f"   Total cases processed: {total_cases}")
        print(f"   Successful extractions: {successful_extractions}")
        print(f"   Failed extractions: {failed_extractions}")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Total judges extracted: {total_judges_extracted}")
        print(f"   Average judges per successful case: {avg_judges_per_case:.1f}")
        print(f"   Total processing time: {total_processing_time:.3f}s")
        print(f"   Average time per case: {avg_processing_time:.3f}s")
        print(f"   Throughput: {total_cases / total_processing_time:.1f} cases/second")
        
        # Performance thresholds
        performance_good = (
            avg_processing_time < 0.1 and  # Less than 100ms per case
            success_rate > 50 and          # At least 50% success rate
            avg_judges_per_case > 0.5      # At least 0.5 judges per successful case
        )
        
        print(f"\n🎯 PERFORMANCE ASSESSMENT")
        print("=" * 60)
        print(f"   Speed: {'✅ GOOD' if avg_processing_time < 0.1 else '⚠️ SLOW'} ({avg_processing_time:.3f}s per case)")
        print(f"   Success Rate: {'✅ GOOD' if success_rate > 50 else '⚠️ LOW'} ({success_rate:.1f}%)")
        print(f"   Judge Yield: {'✅ GOOD' if avg_judges_per_case > 0.5 else '⚠️ LOW'} ({avg_judges_per_case:.1f} per case)")
        print(f"   Overall: {'✅ PERFORMANCE GOOD' if performance_good else '⚠️ NEEDS IMPROVEMENT'}")
        
        return performance_good
    
    async def test_batch_processing_performance(self, batch_size: int = 5):
        """Test batch processing performance"""
        print(f"\n🚀 BATCH PROCESSING PERFORMANCE TEST")
        print("=" * 60)
        print(f"Testing batch processing with {batch_size} cases")
        
        # Fetch cases for batch test
        try:
            response = self.supabase.table('cases').select('*').limit(batch_size).execute()
            cases = response.data
            
            if not cases:
                print("❌ No cases found for batch test")
                return False
            
        except Exception as e:
            print(f"❌ Failed to fetch cases for batch test: {e}")
            return False
        
        # Test batch processing
        start_time = time.time()
        
        batch_results = []
        for case in cases:
            source = case.get('source', 'unknown')
            
            # Extract judges
            if source == 'caselaw_access_project':
                judges = self.judge_extractor.extract_judges_from_cap(case)
            else:
                judges = self.judge_extractor.extract_judges_from_courtlistener(case)
            
            # Format for storage
            judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, case)
            
            batch_results.append({
                'case_id': case.get('id'),
                'judges_count': len(judges),
                'primary_judge': judge_name,
                'has_metadata': judge_metadata is not None
            })
        
        batch_time = time.time() - start_time
        
        # Calculate batch metrics
        total_judges = sum(r['judges_count'] for r in batch_results)
        cases_with_judges = sum(1 for r in batch_results if r['judges_count'] > 0)
        cases_with_primary = sum(1 for r in batch_results if r['primary_judge'])
        
        print(f"📊 BATCH RESULTS")
        print("=" * 60)
        print(f"   Batch size: {len(cases)}")
        print(f"   Processing time: {batch_time:.3f}s")
        print(f"   Time per case: {batch_time / len(cases):.3f}s")
        print(f"   Total judges extracted: {total_judges}")
        print(f"   Cases with judges: {cases_with_judges}/{len(cases)}")
        print(f"   Cases with primary judge: {cases_with_primary}/{len(cases)}")
        print(f"   Batch throughput: {len(cases) / batch_time:.1f} cases/second")
        
        # Batch performance assessment
        batch_performance_good = (
            batch_time / len(cases) < 0.1 and  # Less than 100ms per case
            cases_with_judges / len(cases) > 0.5  # At least 50% success rate
        )
        
        print(f"   Batch Performance: {'✅ GOOD' if batch_performance_good else '⚠️ NEEDS IMPROVEMENT'}")
        
        return batch_performance_good
    
    async def run_performance_tests(self):
        """Run all performance tests"""
        print("🎯 JUDGE EXTRACTION PERFORMANCE VALIDATION")
        print("=" * 60)
        
        results = []
        
        # Test individual case performance
        individual_result = await self.test_performance_with_real_data(10)
        results.append(("Individual Case Performance", individual_result))
        
        # Test batch processing performance
        batch_result = await self.test_batch_processing_performance(5)
        results.append(("Batch Processing Performance", batch_result))
        
        # Summary
        print(f"\n🎉 PERFORMANCE TEST SUMMARY")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASS" if result else "⚠️ NEEDS IMPROVEMENT"
            print(f"   {test_name}: {status}")
            if not result:
                all_passed = False
        
        print(f"\n🎯 OVERALL PERFORMANCE: {'✅ EXCELLENT' if all_passed else '⚠️ ACCEPTABLE'}")
        
        return all_passed


async def main():
    """Main performance test function"""
    test = JudgeExtractionPerformanceTest()
    success = await test.run_performance_tests()
    
    if success:
        print("\n🎉 Judge extraction performance is excellent!")
        return 0
    else:
        print("\n⚠️ Judge extraction performance is acceptable but could be improved.")
        return 0  # Still return 0 as performance issues are not critical failures


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
