#!/usr/bin/env python3
"""
Connect to real Pinecone to count actual vectors stored for CAP cases
This won't disturb the current scale test process - just reads data
"""

import os
import asyncio
from dotenv import load_dotenv
from pinecone import Pinecone
from supabase import create_client

async def check_real_pinecone_vectors():
    """Check actual vectors in Pinecone for CAP cases"""
    
    print("🔍 CHECKING REAL PINECONE VECTORS FOR CAP CASES")
    print("=" * 60)
    
    load_dotenv()
    
    # Get environment variables
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not pinecone_api_key:
        print("❌ PINECONE_API_KEY not found in .env")
        return
    
    if not supabase_url or not supabase_key:
        print("❌ Supabase credentials not found in .env")
        return
    
    try:
        # Initialize Pinecone
        print("🔌 Connecting to Pinecone...")
        pc = Pinecone(api_key=pinecone_api_key)
        
        # List indexes
        indexes = pc.list_indexes()
        print(f"📋 Available indexes: {[idx.name for idx in indexes]}")
        
        # Try different possible index names
        possible_indexes = ["new-texas-laws", "texas-laws-voyage3large", "texas-laws"]
        index_name = None

        for possible_name in possible_indexes:
            if any(idx.name == possible_name for idx in indexes):
                index_name = possible_name
                break

        if not index_name:
            print(f"❌ No suitable index found")
            print(f"Available indexes: {[idx.name for idx in indexes]}")
            print("🤔 Let's try the first available index...")
            index_name = indexes[0].name if indexes else None

        if not index_name:
            print("❌ No indexes available")
            return
        
        index = pc.Index(index_name)
        print(f"✅ Connected to index: {index_name}")
        
        # Get index stats
        stats = index.describe_index_stats()
        print(f"📊 Total vectors in index: {stats.total_vector_count}")
        print(f"📊 Index dimension: {stats.dimension}")
        
        # Initialize Supabase to get CAP case IDs
        print("\n🔌 Connecting to Supabase...")
        supabase = create_client(supabase_url, supabase_key)
        
        # Get sample of CAP cases from database
        print("📋 Getting CAP cases from database...")
        cap_cases = supabase.table('cases').select('id, word_count, pinecone_id').like('batch_id', 'cap_tx_%').limit(20).execute()
        
        if not cap_cases.data:
            print("❌ No CAP cases found in database")
            return
        
        print(f"📊 Found {len(cap_cases.data)} CAP cases in database")
        
        # Check vectors for each case
        print("\n🔍 CHECKING VECTORS FOR SAMPLE CAP CASES:")
        print("-" * 50)
        
        total_db_word_count = 0
        total_actual_vectors = 0
        cases_checked = 0
        
        for case in cap_cases.data:
            case_id = case['id']
            db_word_count = case.get('word_count', 0)
            pinecone_id = case.get('pinecone_id', '')
            
            total_db_word_count += db_word_count
            
            # Query Pinecone for vectors with this case_id
            try:
                # Search for vectors with this case_id in metadata
                query_response = index.query(
                    vector=[0.0] * stats.dimension,  # Dummy vector for metadata search
                    filter={"case_id": case_id},
                    top_k=50,  # Get up to 50 chunks per case
                    include_metadata=True
                )
                
                actual_vector_count = len(query_response.matches)
                total_actual_vectors += actual_vector_count
                cases_checked += 1
                
                print(f"📄 Case: {case_id}")
                print(f"   DB word_count: {db_word_count}")
                print(f"   Actual vectors in Pinecone: {actual_vector_count}")
                print(f"   Primary Pinecone ID: {pinecone_id}")
                
                if actual_vector_count > 0:
                    # Show chunk details
                    for i, match in enumerate(query_response.matches):
                        chunk_idx = match.metadata.get('chunk_index', 'unknown')
                        print(f"      Vector {i+1}: {match.id} (chunk {chunk_idx})")
                
                print()
                
            except Exception as e:
                print(f"❌ Error querying case {case_id}: {e}")
        
        # Summary
        print("=" * 60)
        print("📊 SUMMARY:")
        print(f"   Cases checked: {cases_checked}")
        print(f"   Total DB word_count: {total_db_word_count}")
        print(f"   Total actual vectors in Pinecone: {total_actual_vectors}")
        
        if cases_checked > 0:
            avg_db = total_db_word_count / cases_checked
            avg_actual = total_actual_vectors / cases_checked
            print(f"   Average DB word_count per case: {avg_db:.2f}")
            print(f"   Average actual vectors per case: {avg_actual:.2f}")
            
            if avg_actual > avg_db:
                print(f"✅ GOOD: Actual vectors ({avg_actual:.2f}) > DB count ({avg_db:.2f})")
                print("   This suggests chunking is working correctly!")
            elif avg_actual == avg_db:
                print(f"✅ PERFECT: Actual vectors match DB count")
            else:
                print(f"⚠️ ISSUE: Actual vectors ({avg_actual:.2f}) < DB count ({avg_db:.2f})")
        
        # Check for specific vector patterns
        print("\n🔍 CHECKING VECTOR ID PATTERNS:")
        try:
            # First, try to get any vectors with CAP source
            cap_query = index.query(
                vector=[0.0] * stats.dimension,
                filter={"source": "caselaw_access_project"},
                top_k=10,
                include_metadata=True
            )

            print(f"📋 CAP vectors found: {len(cap_query.matches)}")
            for match in cap_query.matches:
                case_id = match.metadata.get('case_id', 'unknown')
                chunk_idx = match.metadata.get('chunk_index', 'unknown')
                print(f"   {match.id} (case: {case_id}, chunk: {chunk_idx})")

            # If no CAP vectors, check what sources exist
            if len(cap_query.matches) == 0:
                print("\n🔍 No CAP vectors found. Checking what sources exist...")

                # Get any vectors to see what sources are available
                any_query = index.query(
                    vector=[0.0] * stats.dimension,
                    top_k=10,
                    include_metadata=True
                )

                sources_found = set()
                print("📋 Sample vectors in index:")
                for match in any_query.matches:
                    source = match.metadata.get('source', 'unknown')
                    case_id = match.metadata.get('case_id', 'unknown')
                    sources_found.add(source)
                    print(f"   {match.id} (source: {source}, case: {case_id})")

                print(f"\n📊 Sources found in index: {list(sources_found)}")

        except Exception as e:
            print(f"❌ Error getting sample vectors: {e}")
        
    except Exception as e:
        print(f"💥 Error connecting to Pinecone: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_real_pinecone_vectors())
