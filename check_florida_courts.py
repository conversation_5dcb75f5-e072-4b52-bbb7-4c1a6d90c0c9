#!/usr/bin/env python3
"""
Check what Florida court IDs actually exist in Court Listener.
"""

import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

def check_florida_courts():
    """Check what Florida court IDs are available in Court Listener."""
    
    api_key = os.getenv("COURTLISTENER_API_KEY")
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    headers = {
        "Authorization": f"Token {api_key}",
        "User-Agent": "TexasLawsPersonalInjury/1.0"
    }
    
    print("🔍 CHECKING FLORIDA COURT IDs IN COURT LISTENER")
    print("=" * 60)
    
    # Search for Florida courts
    try:
        params = {
            'full_name__icontains': 'florida',
            'format': 'json',
            'page_size': 50  # Get more results
        }
        response = requests.get(f"{base_url}/courts/", headers=headers, params=params)
        response.raise_for_status()
        
        courts = response.json()
        florida_courts = courts.get('results', [])
        
        print(f"✅ Found {len(florida_courts)} Florida courts:")
        print()
        
        # Group by jurisdiction type
        state_courts = []
        federal_courts = []
        bankruptcy_courts = []
        other_courts = []
        
        for court in florida_courts:
            court_id = court.get('id', 'N/A')
            full_name = court.get('full_name', 'N/A')
            jurisdiction = court.get('jurisdiction', 'N/A')
            
            if 'bankruptcy' in full_name.lower():
                bankruptcy_courts.append((court_id, full_name, jurisdiction))
            elif jurisdiction in ['FD', 'FDC']:  # Federal District
                federal_courts.append((court_id, full_name, jurisdiction))
            elif jurisdiction in ['S', 'SC', 'SA']:  # State courts
                state_courts.append((court_id, full_name, jurisdiction))
            else:
                other_courts.append((court_id, full_name, jurisdiction))
        
        print("🏛️ STATE COURTS:")
        for court_id, name, jurisdiction in state_courts:
            print(f"   {court_id}: {name} ({jurisdiction})")
        
        print("\n🏛️ FEDERAL DISTRICT COURTS:")
        for court_id, name, jurisdiction in federal_courts:
            print(f"   {court_id}: {name} ({jurisdiction})")
        
        print("\n🏛️ BANKRUPTCY COURTS:")
        for court_id, name, jurisdiction in bankruptcy_courts:
            print(f"   {court_id}: {name} ({jurisdiction})")
        
        if other_courts:
            print("\n🏛️ OTHER COURTS:")
            for court_id, name, jurisdiction in other_courts:
                print(f"   {court_id}: {name} ({jurisdiction})")
        
        # Test a few court IDs to see which ones have cases
        print("\n🧪 TESTING COURT IDs FOR CASE AVAILABILITY:")
        
        test_courts = []
        if state_courts:
            test_courts.append(state_courts[0][0])  # First state court
        if federal_courts:
            test_courts.append(federal_courts[0][0])  # First federal court
        if len(state_courts) > 1:
            test_courts.append(state_courts[1][0])  # Second state court
        
        for court_id in test_courts[:3]:  # Test up to 3 courts
            try:
                params = {
                    'docket__court': court_id,
                    'format': 'json',
                    'page_size': 5,
                    'date_filed__gte': '2020-01-01'
                }
                
                response = requests.get(f"{base_url}/clusters/", headers=headers, params=params)
                response.raise_for_status()
                
                cases = response.json()
                case_count = len(cases.get('results', []))
                total_count = cases.get('count', 0)
                
                print(f"   ✅ {court_id}: {case_count} cases returned (total available: {total_count})")
                
                # Show sample case
                if cases.get('results'):
                    sample_case = cases['results'][0]
                    case_name = sample_case.get('case_name', 'N/A')
                    date_filed = sample_case.get('date_filed', 'N/A')
                    print(f"      Sample: {case_name} ({date_filed})")
            
            except Exception as e:
                print(f"   ❌ {court_id}: Error - {e}")
        
        # Suggest correct Florida court list
        print("\n💡 SUGGESTED FLORIDA COURT LIST:")
        suggested_courts = []
        
        # Add best state courts
        for court_id, name, jurisdiction in state_courts[:3]:
            suggested_courts.append(court_id)
            print(f"   '{court_id}',  # {name}")
        
        # Add best federal courts
        for court_id, name, jurisdiction in federal_courts[:3]:
            suggested_courts.append(court_id)
            print(f"   '{court_id}',  # {name}")
        
        print(f"\n🎯 RECOMMENDED FLORIDA COURTS LIST:")
        print(f"   fl_courts = {suggested_courts}")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_florida_courts()
