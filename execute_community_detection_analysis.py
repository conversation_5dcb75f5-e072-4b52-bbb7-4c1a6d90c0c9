#!/usr/bin/env python3
"""
Community Detection Analysis Script

This script runs the comprehensive community detection algorithms on the legal document
citation network to identify clusters of related cases and legal concepts.
"""

import os
import sys
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.processing.graph.networkx_community_detector import NetworkXCommunityDetector, detect_communities_networkx
from src.processing.graph.simple_community_detector import SimpleCommunityDetector
from src.processing.graph.batch_community_detector import BatchCommunityDetector, BatchProcessingConfig
from src.relationships.citation_network_analyzer import CitationNetworkAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('community_detection_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class CommunityDetectionOrchestrator:
    """Orchestrates all community detection algorithms and analysis"""
    
    def __init__(self):
        """Initialize the community detection orchestrator"""
        self.networkx_detector = NetworkXCommunityDetector()
        self.simple_detector = SimpleCommunityDetector()
        self.citation_analyzer = CitationNetworkAnalyzer()

        logger.info("Initialized community detection orchestrator (NetworkX-based, AuraDB compatible)")
    
    def run_networkx_community_detection(self, save_to_neo4j: bool = True) -> Dict[str, any]:
        """
        Run NetworkX-based community detection algorithms.
        
        Args:
            save_to_neo4j: Whether to save results to Neo4j
            
        Returns:
            NetworkX community detection results
        """
        logger.info("="*60)
        logger.info("RUNNING NETWORKX COMMUNITY DETECTION")
        logger.info("="*60)
        
        start_time = datetime.now()
        
        try:
            # Run all NetworkX algorithms
            results = self.networkx_detector.run_all_community_detection(
                save_to_neo4j=save_to_neo4j
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Summarize results
            summary = {
                "method": "networkx",
                "start_time": start_time,
                "end_time": end_time,
                "duration_seconds": duration,
                "algorithms_run": list(results.keys()),
                "total_algorithms": len(results),
                "results": {}
            }
            
            for algorithm, result in results.items():
                summary["results"][algorithm] = {
                    "community_count": result.community_count,
                    "modularity": result.modularity,
                    "execution_time": result.execution_time,
                    "nodes_processed": len(result.node_communities)
                }
                
                logger.info(f"✅ {algorithm.upper()} completed:")
                logger.info(f"   Communities: {result.community_count}")
                logger.info(f"   Modularity: {result.modularity:.4f}")
                logger.info(f"   Execution time: {result.execution_time:.2f}s")
                logger.info(f"   Nodes processed: {len(result.node_communities)}")
            
            return summary
            
        except Exception as e:
            logger.error(f"NetworkX community detection failed: {e}")
            return {
                "method": "networkx",
                "error": str(e),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    

    
    def run_simple_community_detection(self, min_community_size: int = 3) -> Dict[str, any]:
        """
        Run simple citation clustering community detection.
        
        Args:
            min_community_size: Minimum size for communities
            
        Returns:
            Simple community detection results
        """
        logger.info("="*60)
        logger.info("RUNNING SIMPLE COMMUNITY DETECTION")
        logger.info("="*60)
        
        start_time = datetime.now()
        
        try:
            # Run simple community detection
            result = self.simple_detector.detect_citation_communities(
                min_community_size=min_community_size
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ Simple community detection completed:")
            logger.info(f"   Communities: {result.community_count}")
            logger.info(f"   Execution time: {result.execution_time:.2f}s")
            logger.info(f"   Algorithm: {result.algorithm}")
            
            return {
                "method": "simple",
                "start_time": start_time,
                "end_time": end_time,
                "duration_seconds": duration,
                "community_count": result.community_count,
                "execution_time": result.execution_time,
                "algorithm": result.algorithm,
                "communities": result.communities
            }
            
        except Exception as e:
            logger.error(f"Simple community detection failed: {e}")
            return {
                "method": "simple",
                "error": str(e),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def run_batch_community_detection(self, batch_size: int = 1000) -> Dict[str, any]:
        """
        Run batch community detection for large datasets.
        
        Args:
            batch_size: Size of each processing batch
            
        Returns:
            Batch community detection results
        """
        logger.info("="*60)
        logger.info("RUNNING BATCH COMMUNITY DETECTION")
        logger.info("="*60)
        
        start_time = datetime.now()
        
        try:
            # Configure batch processing
            config = BatchProcessingConfig(
                batch_size=batch_size,
                algorithms=["louvain", "leiden"],
                max_memory_mb=2048,
                checkpoint_interval=5
            )
            
            # Run batch community detection
            batch_detector = BatchCommunityDetector(config)
            results = batch_detector.process_all_batches()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Summarize batch results
            total_cases = sum(r.cases_processed for r in results)
            total_communities = sum(r.communities_found for r in results)
            avg_execution_time = sum(r.execution_time for r in results) / len(results) if results else 0
            
            logger.info(f"✅ Batch community detection completed:")
            logger.info(f"   Total batches: {len(results)}")
            logger.info(f"   Total cases: {total_cases}")
            logger.info(f"   Total communities: {total_communities}")
            logger.info(f"   Average batch time: {avg_execution_time:.2f}s")
            
            return {
                "method": "batch",
                "start_time": start_time,
                "end_time": end_time,
                "duration_seconds": duration,
                "total_batches": len(results),
                "total_cases_processed": total_cases,
                "total_communities_found": total_communities,
                "average_batch_execution_time": avg_execution_time,
                "batch_results": results
            }
            
        except Exception as e:
            logger.error(f"Batch community detection failed: {e}")
            return {
                "method": "batch",
                "error": str(e),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def analyze_practice_area_networks(self, practice_areas: List[str] = None) -> Dict[str, any]:
        """
        Analyze citation networks for specific practice areas.
        
        Args:
            practice_areas: List of practice areas to analyze
            
        Returns:
            Practice area network analysis results
        """
        if practice_areas is None:
            practice_areas = [
                "personal_injury", "criminal_law", "business_law", 
                "family_law", "employment_law", "constitutional_law"
            ]
        
        logger.info("="*60)
        logger.info("ANALYZING PRACTICE AREA NETWORKS")
        logger.info("="*60)
        
        start_time = datetime.now()
        results = {}
        
        for practice_area in practice_areas:
            logger.info(f"\nAnalyzing {practice_area}...")
            
            try:
                analysis = self.citation_analyzer.analyze_practice_area_network(
                    practice_area=practice_area,
                    include_cross_practice=True
                )
                
                results[practice_area] = {
                    "network_metrics": analysis["network_metrics"],
                    "authority_scores": analysis["authority_scores"],
                    "influential_documents": analysis["influential_documents"],
                    "citation_patterns": analysis["citation_patterns"]
                }
                
                metrics = analysis["network_metrics"]
                logger.info(f"✅ {practice_area} analysis completed:")
                logger.info(f"   Nodes: {metrics['node_count']}")
                logger.info(f"   Edges: {metrics['edge_count']}")
                logger.info(f"   Density: {metrics['density']:.4f}")
                logger.info(f"   Connected components: {metrics['connected_components']}")
                
            except Exception as e:
                logger.error(f"Failed to analyze {practice_area}: {e}")
                results[practice_area] = {"error": str(e)}
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "method": "practice_area_analysis",
            "start_time": start_time,
            "end_time": end_time,
            "duration_seconds": duration,
            "practice_areas_analyzed": len(results),
            "results": results
        }
    
    def run_comprehensive_analysis(self,
                                 include_networkx: bool = True,
                                 include_simple: bool = True,
                                 include_batch: bool = False,
                                 include_practice_areas: bool = True) -> Dict[str, any]:
        """
        Run comprehensive community detection analysis.
        
        Args:
            include_networkx: Whether to run NetworkX algorithms
            include_simple: Whether to run simple clustering
            include_batch: Whether to run batch processing
            include_practice_areas: Whether to analyze practice areas

        Returns:
            Comprehensive analysis results
        """
        logger.info("="*80)
        logger.info("STARTING COMPREHENSIVE COMMUNITY DETECTION ANALYSIS")
        logger.info("="*80)
        
        overall_start = datetime.now()
        all_results = {}
        
        # Run NetworkX community detection
        if include_networkx:
            logger.info("\n" + "="*60)
            logger.info("PHASE 1: NETWORKX COMMUNITY DETECTION")
            logger.info("="*60)
            all_results["networkx"] = self.run_networkx_community_detection()

        # Run simple community detection
        if include_simple:
            logger.info("\n" + "="*60)
            logger.info("PHASE 2: SIMPLE COMMUNITY DETECTION")
            logger.info("="*60)
            all_results["simple"] = self.run_simple_community_detection()

        # Run batch community detection
        if include_batch:
            logger.info("\n" + "="*60)
            logger.info("PHASE 3: BATCH COMMUNITY DETECTION")
            logger.info("="*60)
            all_results["batch"] = self.run_batch_community_detection()

        # Analyze practice area networks
        if include_practice_areas:
            logger.info("\n" + "="*60)
            logger.info("PHASE 4: PRACTICE AREA NETWORK ANALYSIS")
            logger.info("="*60)
            all_results["practice_areas"] = self.analyze_practice_area_networks()
        
        # Calculate overall summary
        overall_end = datetime.now()
        overall_duration = (overall_end - overall_start).total_seconds()
        
        all_results["overall_summary"] = {
            "start_time": overall_start,
            "end_time": overall_end,
            "duration_seconds": overall_duration,
            "duration_hours": overall_duration / 3600,
            "phases_completed": len(all_results) - 1,  # Exclude this summary
            "total_methods_run": len([r for r in all_results.values() if "error" not in r])
        }
        
        return all_results
    
    def close(self):
        """Close all detector connections"""
        self.networkx_detector.close()
        self.simple_detector.close()


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Community Detection Analysis")
    parser.add_argument(
        "--method",
        choices=["networkx", "simple", "batch", "practice-areas", "all"],
        default="all",
        help="Community detection method to run (AuraDB compatible, no GDS)"
    )
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=1000,
        help="Batch size for batch processing"
    )
    parser.add_argument(
        "--min-community-size", 
        type=int, 
        default=3,
        help="Minimum community size for simple detection"
    )
    parser.add_argument(
        "--save-to-neo4j", 
        action="store_true",
        help="Save NetworkX results to Neo4j"
    )
    
    args = parser.parse_args()
    
    orchestrator = CommunityDetectionOrchestrator()
    
    try:
        if args.method == "networkx":
            results = orchestrator.run_networkx_community_detection(args.save_to_neo4j)
        elif args.method == "simple":
            results = orchestrator.run_simple_community_detection(args.min_community_size)
        elif args.method == "batch":
            results = orchestrator.run_batch_community_detection(args.batch_size)
        elif args.method == "practice-areas":
            results = orchestrator.analyze_practice_area_networks()
        else:  # all
            results = orchestrator.run_comprehensive_analysis()
        
        # Print summary
        if "overall_summary" in results:
            summary = results["overall_summary"]
            print(f"\n" + "="*80)
            print("COMPREHENSIVE COMMUNITY DETECTION ANALYSIS COMPLETE")
            print("="*80)
            print(f"Duration: {summary['duration_hours']:.2f} hours")
            print(f"Phases completed: {summary['phases_completed']}")
            print(f"Methods run: {summary['total_methods_run']}")
        else:
            print(f"\n" + "="*60)
            print(f"{args.method.upper()} COMMUNITY DETECTION COMPLETE")
            print("="*60)
            if "error" not in results:
                print(f"Duration: {results['duration_seconds']:.1f} seconds")
            else:
                print(f"Error: {results['error']}")
        
    except Exception as e:
        logger.error(f"Community detection analysis failed: {e}")
        sys.exit(1)
    finally:
        orchestrator.close()


if __name__ == "__main__":
    main()
