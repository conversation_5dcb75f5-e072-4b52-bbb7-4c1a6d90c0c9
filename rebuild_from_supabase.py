#!/usr/bin/env python3
"""
Rebuild all databases from Supabase data for perfect consistency.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from processing.storage.supabase_connector import SupabaseConnector
from processing.caselaw_access_processor import CaselawAccessProcessor, CaselawDocument

async def rebuild_from_supabase():
    """Rebuild all databases from Supabase cases."""
    
    try:
        print("🔄 REBUILDING DATABASES FROM SUPABASE")
        print("=" * 60)
        
        # Initialize connections
        supabase = SupabaseConnector()
        processor = CaselawAccessProcessor()
        
        # Get total count
        response = supabase.client.table('cases').select('id', count='exact').execute()
        total_cases = response.count
        print(f"📊 Total cases to process: {total_cases:,}")
        
        # Process in batches
        batch_size = 100
        processed = 0
        errors = 0
        
        for offset in range(0, total_cases, batch_size):
            print(f"\n📦 Processing batch {offset//batch_size + 1} (cases {offset+1}-{min(offset+batch_size, total_cases)})")
            
            # Fetch batch
            response = supabase.client.table('cases').select('*').range(offset, offset + batch_size - 1).execute()
            cases = response.data
            
            for case in cases:
                try:
                    # Convert Supabase case to CaselawDocument
                    doc = create_caselaw_document_from_supabase(case)
                    
                    # Process through the pipeline (Neo4j, Pinecone, GCS)
                    # Skip Supabase since it's already there
                    success = await process_document_to_secondary_stores(processor, doc, case['id'])
                    
                    if success:
                        processed += 1
                    else:
                        errors += 1
                        print(f"   ❌ Failed to process: {case['id']}")
                    
                    if processed % 50 == 0:
                        print(f"   📊 Progress: {processed:,}/{total_cases:,} ({processed/total_cases*100:.1f}%)")
                
                except Exception as e:
                    errors += 1
                    print(f"   ❌ Error processing {case.get('id', 'unknown')}: {e}")
        
        print(f"\n🎯 REBUILD SUMMARY:")
        print(f"   Total cases: {total_cases:,}")
        print(f"   Successfully processed: {processed:,}")
        print(f"   Errors: {errors:,}")
        print(f"   Success rate: {processed/total_cases*100:.1f}%")
        
        return processed, errors
        
    except Exception as e:
        print(f"❌ Error rebuilding from Supabase: {e}")
        import traceback
        traceback.print_exc()
        return 0, 1

def create_caselaw_document_from_supabase(case_data):
    """Convert Supabase case data to CaselawDocument."""
    
    # Parse date_filed if it exists
    date_filed = None
    if case_data.get('date_filed'):
        try:
            if isinstance(case_data['date_filed'], str):
                date_filed = datetime.fromisoformat(case_data['date_filed'].replace('Z', '+00:00'))
            else:
                date_filed = case_data['date_filed']
        except:
            date_filed = None
    
    # Create CaselawDocument
    doc = CaselawDocument(
        # Required positional arguments
        id=case_data['id'],
        source=case_data.get('source', 'supabase_rebuild'),
        added=datetime.now(),
        created=datetime.now(),
        author=case_data.get('court', 'Unknown Court'),
        license='public',
        url=case_data.get('url', ''),
        text=case_data.get('full_text', ''),
        
        # Optional fields
        case_name=case_data.get('case_name', ''),
        docket_number=case_data.get('docket_number', ''),
        date_filed=date_filed,
        court=case_data.get('court', ''),
        jurisdiction=case_data.get('jurisdiction', ''),
        practice_area=case_data.get('practice_area', ''),
        precedential_status=case_data.get('precedential_status', ''),
        citation_count=case_data.get('citation_count', 0),
        judges=case_data.get('judges', []) if case_data.get('judges') else [],
        parties=case_data.get('parties', []) if case_data.get('parties') else []
    )
    
    return doc

async def process_document_to_secondary_stores(processor, doc, case_id):
    """Process document to Neo4j, Pinecone, and GCS (skip Supabase)."""
    
    try:
        # Generate embeddings if text exists
        embeddings = []
        if doc.text:
            embeddings = await processor.embedding_service.generate_embeddings([doc.text])
        
        success_count = 0
        
        # Store in Neo4j
        try:
            await processor._store_in_neo4j(doc, case_id)
            success_count += 1
        except Exception as e:
            print(f"     Neo4j error for {case_id}: {e}")
        
        # Store in Pinecone (if we have embeddings)
        if embeddings:
            try:
                await processor._store_in_pinecone(doc, embeddings, case_id)
                success_count += 1
            except Exception as e:
                print(f"     Pinecone error for {case_id}: {e}")
        
        # Store in GCS (if we have text)
        if doc.text:
            try:
                await processor._store_in_gcs(doc, case_id)
                success_count += 1
            except Exception as e:
                print(f"     GCS error for {case_id}: {e}")
        
        # Consider success if at least 2 out of 3 stores worked
        return success_count >= 2
        
    except Exception as e:
        print(f"     Processing error for {case_id}: {e}")
        return False

async def verify_rebuild_consistency():
    """Verify that all databases are now consistent."""
    
    print(f"\n🔍 VERIFYING REBUILD CONSISTENCY")
    print("=" * 50)
    
    try:
        # Check Supabase count
        supabase = SupabaseConnector()
        response = supabase.client.table('cases').select('id', count='exact').execute()
        supabase_count = response.count
        
        # Check Neo4j count
        from processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        with neo4j.driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            neo4j_count = result.single()['count']
        neo4j.close()
        
        # Check Pinecone count
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        stats = index.describe_index_stats()
        pinecone_count = stats.total_vector_count
        
        print(f"📊 CONSISTENCY CHECK:")
        print(f"   Supabase cases: {supabase_count:,}")
        print(f"   Neo4j Document nodes: {neo4j_count:,}")
        print(f"   Pinecone vectors: {pinecone_count:,}")
        
        # Calculate consistency
        neo4j_consistency = (neo4j_count / supabase_count * 100) if supabase_count > 0 else 0
        expected_vectors = supabase_count * 3  # Assuming ~3 chunks per case
        pinecone_consistency = (pinecone_count / expected_vectors * 100) if expected_vectors > 0 else 0
        
        print(f"\n📈 CONSISTENCY RATES:")
        print(f"   Neo4j: {neo4j_consistency:.1f}%")
        print(f"   Pinecone: {pinecone_consistency:.1f}%")
        
        if neo4j_consistency >= 95 and pinecone_consistency >= 80:
            print(f"   ✅ EXCELLENT CONSISTENCY ACHIEVED!")
        elif neo4j_consistency >= 90 and pinecone_consistency >= 70:
            print(f"   ✅ GOOD CONSISTENCY ACHIEVED!")
        else:
            print(f"   ⚠️  CONSISTENCY NEEDS IMPROVEMENT")
        
        return neo4j_consistency, pinecone_consistency
        
    except Exception as e:
        print(f"❌ Error verifying consistency: {e}")
        return 0, 0

async def main():
    """Main rebuild function."""
    
    print("🔄 REBUILD ALL DATABASES FROM SUPABASE")
    print("=" * 70)
    print("This will rebuild Neo4j, Pinecone, and GCS from Supabase data")
    print("=" * 70)
    
    # Perform rebuild
    processed, errors = await rebuild_from_supabase()
    
    if processed > 0:
        # Verify consistency
        neo4j_consistency, pinecone_consistency = await verify_rebuild_consistency()
        
        print(f"\n🎉 REBUILD COMPLETE!")
        print(f"   ✅ Processed: {processed:,} cases")
        print(f"   ❌ Errors: {errors:,}")
        print(f"   📊 Neo4j consistency: {neo4j_consistency:.1f}%")
        print(f"   📊 Pinecone consistency: {pinecone_consistency:.1f}%")
        
        if neo4j_consistency >= 95:
            print(f"   🎯 TARGET ACHIEVED: 100% consistency goal reached!")
    else:
        print(f"\n❌ REBUILD FAILED")
        print(f"   No cases were successfully processed")

if __name__ == "__main__":
    asyncio.run(main())
