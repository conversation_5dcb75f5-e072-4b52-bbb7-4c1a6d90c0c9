#!/usr/bin/env python3
"""
Source-Agnostic Legal Corpus Processor

Ensures perfect coherence between CourtListener and CAP data:
- Same field names and meanings
- Same metadata structure  
- Same node relationships in Neo4j
- Same vector metadata in Pinecone
- Same cross-system tracking approach

Result: Seamless temporal continuity from 1950-2025
"""

import asyncio
import logging
import os
import sys
import json
import gzip
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from src.processing.atomic_storage_pipeline import AtomicStoragePipeline
from src.processing.cross_system_validator import CrossSystemValidator
from src.processing.judge_extraction_service import JudgeExtractionService

# Import existing infrastructure
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CoherentCase:
    """
    Source-agnostic case representation
    Same structure regardless of CourtListener or CAP origin
    """
    
    def __init__(self, raw_data: Dict[str, Any], source_type: str):
        """Initialize from raw data with source-agnostic normalization"""
        
        # Core identification (consistent across sources)
        self.id = self._normalize_id(raw_data, source_type)
        self.source = source_type  # 'courtlistener' or 'caselaw_access_project'
        
        # Legal metadata (unified philosophy)
        self.case_name = self._normalize_case_name(raw_data, source_type)
        self.court = self._normalize_court(raw_data, source_type)
        self.jurisdiction = self._normalize_jurisdiction(raw_data, source_type)
        self.date_filed = self._normalize_date(raw_data, source_type)
        self.docket_number = self._normalize_docket(raw_data, source_type)
        
        # Content (unified processing)
        self.text_content = self._normalize_text(raw_data, source_type)
        self.word_count = len(self.text_content.split()) if self.text_content else 0
        
        # Classification (same philosophy)
        self.practice_area = 'general'  # Will be classified uniformly
        self.practice_areas = ['general']

        # Judge information (will be populated during processing)
        self.judge_name = None
        self.judge_metadata = None

        # Temporal context (unified approach)
        self.year_filed = self.date_filed.year if self.date_filed else None
        self.historical_era = self._determine_historical_era()
        self.temporal_authority = self._calculate_temporal_authority()
        
        # Quality metrics (consistent calculation)
        self.completeness_score = self._calculate_completeness()
        self.authority_score = self._calculate_authority()
        
        # Cross-system tracking (identical philosophy)
        self.gcs_path = f"cases/{self.jurisdiction.lower()}/{self.id}.json"
        self.pinecone_id = f"{self.id}_chunk_0"  # Primary vector ID
        self.neo4j_node_id = f"case_{self.id}"
        self.pinecone_namespace = self.jurisdiction.lower()
        
        # Processing metadata
        self.content_hash = self._calculate_content_hash()
        self.batch_id = ''  # Will be set during processing
        self.created_at = datetime.now()
    
    def _normalize_id(self, raw_data: Dict, source_type: str) -> str:
        """Create consistent ID format regardless of source"""
        if source_type == 'courtlistener':
            return str(raw_data.get('id') or raw_data.get('opinion_id', ''))
        else:  # CAP
            # Remove prefixes and normalize
            cap_id = str(raw_data.get('id', ''))
            return cap_id.replace('cap_', '').replace('/', '_').replace('.html', '')
    
    def _normalize_case_name(self, raw_data: Dict, source_type: str) -> str:
        """Normalize case name to consistent format"""
        if source_type == 'courtlistener':
            return raw_data.get('case_name', '') or raw_data.get('name', '')
        else:  # CAP
            return raw_data.get('name', '') or raw_data.get('case_name', '')
    
    def _normalize_court(self, raw_data: Dict, source_type: str) -> str:
        """Normalize court information to consistent format"""
        if source_type == 'courtlistener':
            return raw_data.get('court_name', '') or raw_data.get('court', '')
        else:  # CAP
            court_info = raw_data.get('court', {})
            if isinstance(court_info, dict):
                return court_info.get('name', '')
            return str(court_info)
    
    def _normalize_jurisdiction(self, raw_data: Dict, source_type: str) -> str:
        """Normalize jurisdiction to consistent format"""
        if source_type == 'courtlistener':
            return raw_data.get('jurisdiction', 'TX').upper()
        else:  # CAP
            # Infer from court or use default
            return raw_data.get('jurisdiction', 'TX').upper()
    
    def _normalize_date(self, raw_data: Dict, source_type: str) -> Optional[datetime]:
        """Normalize date to consistent format"""
        date_field = None
        
        if source_type == 'courtlistener':
            date_field = raw_data.get('date_filed')
        else:  # CAP
            date_field = raw_data.get('decision_date') or raw_data.get('date_filed')
        
        if not date_field:
            return None
        
        try:
            if isinstance(date_field, str):
                return datetime.fromisoformat(date_field.replace('Z', '+00:00'))
            return date_field
        except:
            return None
    
    def _normalize_docket(self, raw_data: Dict, source_type: str) -> str:
        """Normalize docket number to consistent format"""
        return raw_data.get('docket_number', '') or ''
    
    def _normalize_text(self, raw_data: Dict, source_type: str) -> str:
        """Normalize text content to consistent format"""
        if source_type == 'courtlistener':
            return raw_data.get('plain_text', '') or raw_data.get('text', '') or raw_data.get('html', '') or ''
        else:  # CAP and other sources (including test)
            return raw_data.get('text', '') or raw_data.get('plain_text', '') or ''
    
    def _determine_historical_era(self) -> str:
        """Determine historical era using consistent philosophy"""
        if not self.year_filed:
            return 'unknown'
        
        year = self.year_filed
        
        # Unified historical classification
        if year >= 2010:
            return 'contemporary'
        elif year >= 1994:
            return 'modern'
        elif year >= 1970:
            return 'late_20th_century'
        elif year >= 1950:
            return 'mid_20th_century'
        else:
            return 'early_20th_century'
    
    def _calculate_temporal_authority(self) -> float:
        """Calculate temporal authority using consistent formula"""
        if not self.year_filed:
            return 0.0
        
        # Consistent authority calculation across sources
        current_year = datetime.now().year
        age = current_year - self.year_filed
        
        # Authority decreases with age but stabilizes for very old cases
        if age <= 5:
            return 1.0
        elif age <= 15:
            return 0.9 - (age - 5) * 0.05  # Gradual decrease
        else:
            return max(0.4, 0.9 - age * 0.01)  # Minimum authority for historical cases
    
    def _calculate_completeness(self) -> float:
        """Calculate completeness score using consistent metrics"""
        score = 0.0
        
        # Text content (40%)
        if self.text_content and len(self.text_content) > 100:
            score += 0.4 * min(1.0, len(self.text_content) / 5000)
        
        # Metadata completeness (60%)
        metadata_fields = [
            self.case_name, self.court, self.date_filed, 
            self.docket_number, self.jurisdiction
        ]
        filled_fields = sum(1 for field in metadata_fields if field)
        score += 0.6 * (filled_fields / len(metadata_fields))
        
        return min(1.0, score)
    
    def _calculate_authority(self) -> float:
        """Calculate authority score using consistent methodology"""
        # Base authority from temporal authority
        authority = self.temporal_authority
        
        # Boost for completeness
        authority *= (0.5 + 0.5 * self.completeness_score)
        
        # Boost for content length (indicates substantial case)
        if self.word_count > 1000:
            authority *= 1.1
        elif self.word_count > 5000:
            authority *= 1.2
        
        return min(1.0, authority)
    
    def _calculate_content_hash(self) -> str:
        """Calculate consistent content hash"""
        content = f"{self.case_name}|{self.text_content}|{self.date_filed}"
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    def to_supabase_dict(self) -> Dict[str, Any]:
        """Convert to Supabase format with consistent schema"""
        return {
            # Core identification
            'id': self.id,
            'source': self.source,
            'source_id': self.id,
            'cluster_id': self.id,
            
            # Legal metadata
            'case_name': self.case_name,
            'court': self.court,
            'court_id': self.court,  # Consistent field
            'jurisdiction': self.jurisdiction,
            'date_filed': self.date_filed.date().isoformat() if self.date_filed else None,
            'year_filed': self.year_filed,
            'docket_number': self.docket_number,
            
            # Classification
            'primary_practice_area': self.practice_area,
            'practice_areas': self.practice_areas,

            # Judge information
            'judge_name': self.judge_name,
            'judge_metadata': self.judge_metadata,

            # Temporal context
            'historical_era': self.historical_era,
            'source_window': 'historical' if self.year_filed and self.year_filed < 1994 else 'modern',
            
            # Quality metrics
            'word_count': self.word_count,
            'completeness_score': self.completeness_score,
            'authority_score': self.authority_score,
            
            # Cross-system tracking (IDENTICAL PHILOSOPHY)
            'gcs_path': self.gcs_path,
            'pinecone_id': self.pinecone_id,
            'pinecone_namespace': self.pinecone_namespace,
            'neo4j_node_id': self.neo4j_node_id,
            
            # Processing metadata
            'content_hash': self.content_hash,
            'batch_id': self.batch_id,
            'created_at': self.created_at.isoformat(),
            'document_type': 'opinion',
            'source_type': self.source
        }
    
    def to_gcs_dict(self) -> Dict[str, Any]:
        """Convert to GCS storage format (full document)"""
        return {
            'id': self.id,
            'case_name': self.case_name,
            'court': self.court,
            'jurisdiction': self.jurisdiction,
            'date_filed': self.date_filed.isoformat() if self.date_filed else None,
            'text_content': self.text_content,
            'source': self.source,
            'historical_era': self.historical_era,
            'practice_area': self.practice_area,
            'authority_score': self.authority_score,
            'processed_at': datetime.now().isoformat()
        }
    
    def to_pinecone_vectors(self) -> List[Dict[str, Any]]:
        """Convert to Pinecone vectors with consistent metadata philosophy"""
        if not self.text_content:
            return []
        
        # Chunk text consistently
        chunks = self._chunk_text(self.text_content)
        vectors = []
        
        for i, chunk in enumerate(chunks):
            vector_id = f"{self.id}_chunk_{i}"
            
            # CONSISTENT metadata across all sources
            metadata = {
                'case_id': self.id,
                'chunk_index': i,
                'case_name': self.case_name,
                'court': self.court,
                'jurisdiction': self.jurisdiction,
                'date_filed': self.date_filed.isoformat() if self.date_filed else 'unknown',
                'year_filed': self.year_filed or 0,
                'historical_era': self.historical_era,
                'practice_area': self.practice_area,
                'source': self.source,
                'authority_score': self.authority_score,
                'text_length': len(chunk),
                'batch_id': self.batch_id
            }
            
            # Generate real embedding for the chunk
            logger.info(f"Generating embedding for chunk {i} of case {self.id}")
            embedding = self._generate_embedding(chunk)
            logger.info(f"Generated embedding with {len(embedding)} dimensions, first few values: {embedding[:3]}")

            vectors.append({
                'id': vector_id,
                'values': embedding,
                'metadata': metadata
            })
        
        return vectors
    
    def to_neo4j_node(self) -> Dict[str, Any]:
        """Convert to Neo4j node with consistent relationship philosophy"""
        return {
            'id': self.neo4j_node_id,
            'properties': {
                'case_id': self.id,
                'case_name': self.case_name,
                'court': self.court,
                'jurisdiction': self.jurisdiction,
                'date_filed': self.date_filed.isoformat() if self.date_filed else None,
                'year_filed': self.year_filed,
                'historical_era': self.historical_era,
                'practice_area': self.practice_area,
                'source': self.source,
                'authority_score': self.authority_score,
                'completeness_score': self.completeness_score,
                'created_at': datetime.now().isoformat()
            },
            'labels': ['Case', f'Era_{self.historical_era}', f'Source_{self.source}']
        }
    
    def _chunk_text(self, text: str, max_chunk_size: int = 1000) -> List[str]:
        """
        Chunk text consistently across sources with size enforcement
        Uses hybrid paragraph + sentence-based chunking to prevent oversized chunks
        """
        if not text:
            return [f'Case {self.id} - No content available']

        # Safety limits (characters)
        max_chunk_chars = max_chunk_size
        max_safe_chars = int(max_chunk_size * 1.2)  # 20% buffer before hard split

        # First pass: paragraph-based chunking
        paragraphs = text.split('\n\n')
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # Check if adding this paragraph would exceed limit
            potential_chunk = current_chunk + ("\n\n" + paragraph if current_chunk else paragraph)

            if len(potential_chunk) <= max_chunk_chars:
                # Safe to add paragraph
                current_chunk = potential_chunk
            else:
                # Adding paragraph would exceed limit
                if current_chunk:
                    # Save current chunk and start new one
                    chunks.append(current_chunk.strip())
                    current_chunk = paragraph
                else:
                    # Single paragraph is too large - need to split it
                    current_chunk = paragraph

                # Check if current chunk (single paragraph) is still too large
                if len(current_chunk) > max_safe_chars:
                    # Split oversized paragraph by sentences
                    sentence_chunks = self._split_oversized_paragraph(current_chunk, max_chunk_chars)
                    chunks.extend(sentence_chunks)
                    current_chunk = ""

        # Add final chunk
        if current_chunk:
            if len(current_chunk) > max_safe_chars:
                # Split final oversized chunk
                sentence_chunks = self._split_oversized_paragraph(current_chunk, max_chunk_chars)
                chunks.extend(sentence_chunks)
            else:
                chunks.append(current_chunk.strip())

        return chunks if chunks else [text[:max_chunk_size]]

    def _split_oversized_paragraph(self, paragraph: str, max_chunk_size: int) -> List[str]:
        """Split oversized paragraph by sentences, then by words if needed"""

        # Try sentence-based splitting first
        sentences = self._split_into_sentences(paragraph)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            potential_chunk = current_chunk + (" " + sentence if current_chunk else sentence)

            if len(potential_chunk) <= max_chunk_size:
                current_chunk = potential_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    current_chunk = sentence

                # If single sentence is still too large, split by words
                if len(current_chunk) > max_chunk_size:
                    word_chunks = self._split_by_words(current_chunk, max_chunk_size)
                    chunks.extend(word_chunks)
                    current_chunk = ""

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using simple heuristics"""
        import re

        # Split on sentence endings, but preserve the punctuation
        sentences = re.split(r'([.!?]+\s+)', text)

        # Recombine sentences with their punctuation
        result = []
        for i in range(0, len(sentences) - 1, 2):
            sentence = sentences[i]
            if i + 1 < len(sentences):
                sentence += sentences[i + 1]
            if sentence.strip():
                result.append(sentence.strip())

        return result if result else [text]

    def _split_by_words(self, text: str, max_chunk_size: int) -> List[str]:
        """Final fallback: split by words to enforce hard limit"""
        words = text.split()
        chunks = []
        current_chunk = ""

        for word in words:
            potential_chunk = current_chunk + (" " + word if current_chunk else word)

            if len(potential_chunk) <= max_chunk_size:
                current_chunk = potential_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = word

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _generate_embedding(self, text: str) -> List[float]:
        """Generate real embedding for text using Voyage-3-Large"""
        logger.info(f"_generate_embedding called with text length: {len(text)}")
        try:
            import voyageai
            import os

            # Get Voyage API key
            api_key = os.getenv("VOYAGE_API_KEY")
            if not api_key:
                logger.warning("No Voyage API key found, using mock embedding")
                return self._generate_mock_embedding()

            # Initialize Voyage client
            vo = voyageai.Client(api_key=api_key)

            # Generate embedding using voyage-3-large
            result = vo.embed(
                texts=[text],
                model="voyage-3-large",  # 1024 dimensions
                input_type="document"
            )

            embedding = result.embeddings[0]
            logger.debug(f"Generated Voyage embedding with {len(embedding)} dimensions")
            return embedding

        except Exception as e:
            logger.warning(f"Error generating Voyage embedding: {e}, using mock")
            return self._generate_mock_embedding()

    def _generate_mock_embedding(self) -> List[float]:
        """Generate a mock embedding that's not all zeros (Voyage-3-Large dimensions)"""
        logger.info("Generating mock embedding (non-zero)")
        import random
        # Generate random values between -0.1 and 0.1 (typical embedding range)
        # Voyage-3-Large uses 1024 dimensions
        embedding = [random.uniform(-0.1, 0.1) for _ in range(1024)]
        logger.info(f"Mock embedding generated: {len(embedding)} dimensions, first few: {embedding[:3]}")
        return embedding


class SourceAgnosticProcessor:
    """
    Processes both CourtListener and CAP data with identical output philosophy
    Ensures seamless temporal continuity in the legal knowledge graph
    """
    
    def __init__(self,
                 supabase_client,
                 gcs_client,
                 pinecone_client,
                 neo4j_client,
                 enable_legal_relationships: bool = True,
                 legal_relationship_timeout: int = 300):
        self.supabase = supabase_client
        self.storage_pipeline = AtomicStoragePipeline(
            supabase_client,
            gcs_client,
            pinecone_client,
            neo4j_client,
            batch_size=1000,
            enable_legal_relationships=enable_legal_relationships,
            legal_relationship_timeout=legal_relationship_timeout
        )
        self.validator = CrossSystemValidator(
            supabase_client, gcs_client, pinecone_client, neo4j_client
        )
        self.judge_extractor = JudgeExtractionService()

        logger.info("✅ Source-Agnostic Processor initialized")
        logger.info("🎯 Philosophy: Seamless temporal continuity 1950-2025")
        logger.info(f"⚖️ Legal relationships: {'enabled' if enable_legal_relationships else 'disabled'}")
        if enable_legal_relationships:
            logger.info(f"⏱️ Legal relationship timeout: {legal_relationship_timeout}s")
    
    async def process_coherent_batch(
        self, 
        raw_cases: List[Dict[str, Any]], 
        source_type: str,
        batch_id: str
    ) -> Dict[str, Any]:
        """Process batch with source-agnostic coherent output"""
        
        logger.info(f"🔄 Processing {len(raw_cases)} {source_type} cases with coherent output")
        
        # Transform to coherent format
        coherent_cases = []
        for raw_case in raw_cases:
            try:
                coherent_case = CoherentCase(raw_case, source_type)
                coherent_case.batch_id = batch_id

                # Extract judge information based on source type
                if source_type == 'caselaw_access_project':
                    judges = self.judge_extractor.extract_judges_from_cap(raw_case)
                else:
                    judges = self.judge_extractor.extract_judges_from_courtlistener(raw_case)

                # Format judge data for storage
                judge_name, judge_metadata = self.judge_extractor.format_for_storage(judges, raw_case)

                # Add judge data to coherent case
                if judge_name or judge_metadata:
                    coherent_case.judge_name = judge_name
                    coherent_case.judge_metadata = judge_metadata

                # Apply practice area classification (consistent across sources)
                coherent_case.practice_area = await self._classify_practice_area(coherent_case)
                coherent_case.practice_areas = [coherent_case.practice_area]

                coherent_cases.append(coherent_case)
                
            except Exception as e:
                logger.error(f"Error creating coherent case: {e}")
        
        if not coherent_cases:
            return {'success': False, 'processed': 0}
        
        # Convert to storage format with vectors
        storage_cases = []
        case_vectors_map = {}

        for case in coherent_cases:
            storage_dict = case.to_supabase_dict()
            # Add text field for Neo4j judge extraction
            storage_dict['text'] = case.text_content
            storage_dict['plain_text'] = case.text_content
            storage_cases.append(storage_dict)

            # Get vectors for this case
            case_vectors = case.to_pinecone_vectors()
            case_vectors_map[case.id] = case_vectors

        # Process through enhanced atomic storage pipeline with pre-generated vectors
        result = await self.storage_pipeline.store_batch_with_vectors(
            storage_cases, batch_id, case_vectors_map
        )
        
        logger.info(f"✅ Coherent batch processed: {len(coherent_cases)} cases")
        
        return {
            'success': result.get('success', False),
            'processed': len(coherent_cases),
            'coherent_format': True,
            'temporal_range': self._get_temporal_range(coherent_cases)
        }
    
    async def _classify_practice_area(self, case: CoherentCase) -> str:
        """Classify practice area using consistent methodology"""
        # Simplified classification - replace with actual classifier
        text = case.text_content.lower()
        
        if any(term in text for term in ['injury', 'accident', 'negligence', 'damages']):
            return 'personal_injury'
        elif any(term in text for term in ['criminal', 'prosecution', 'defendant', 'guilty']):
            return 'criminal_defense'
        elif any(term in text for term in ['divorce', 'custody', 'marriage', 'family']):
            return 'family_law'
        elif any(term in text for term in ['estate', 'will', 'probate', 'inheritance']):
            return 'estate_planning'
        elif any(term in text for term in ['immigration', 'visa', 'deportation', 'asylum']):
            return 'immigration_law'
        elif any(term in text for term in ['property', 'real estate', 'deed', 'mortgage']):
            return 'real_estate'
        elif any(term in text for term in ['bankruptcy', 'debt', 'creditor', 'discharge']):
            return 'bankruptcy'
        else:
            return 'general'
    
    def _get_temporal_range(self, cases: List[CoherentCase]) -> Dict[str, Any]:
        """Get temporal range of processed cases"""
        years = [case.year_filed for case in cases if case.year_filed]
        
        if not years:
            return {'min_year': None, 'max_year': None, 'span': 0}
        
        return {
            'min_year': min(years),
            'max_year': max(years),
            'span': max(years) - min(years),
            'eras': list(set(case.historical_era for case in cases))
        }


# Test function
async def test_source_agnostic_processing():
    """Test source-agnostic processing with sample data"""
    
    # Mock CourtListener case
    cl_case = {
        'id': '12345',
        'case_name': 'Smith v. Jones',
        'court': 'Texas Supreme Court',
        'date_filed': '2023-05-15T00:00:00Z',
        'plain_text': 'This is a personal injury case involving negligence...',
        'jurisdiction': 'TX'
    }
    
    # Mock CAP case  
    cap_case = {
        'id': 'cap_67890',
        'name': 'Brown v. Wilson',
        'court': {'name': 'Texas Court of Appeals'},
        'decision_date': '1975-03-20T00:00:00Z',
        'text': 'This historical case involves property rights and real estate...',
        'jurisdiction': 'TX'
    }
    
    # Test coherent transformation
    coherent_cl = CoherentCase(cl_case, 'courtlistener')
    coherent_cap = CoherentCase(cap_case, 'caselaw_access_project')
    
    print("🔍 COHERENT OUTPUT COMPARISON:")
    print(f"CourtListener ID: {coherent_cl.id}")
    print(f"CAP ID: {coherent_cap.id}")
    print(f"CL GCS Path: {coherent_cl.gcs_path}")
    print(f"CAP GCS Path: {coherent_cap.gcs_path}")
    print(f"CL Neo4j Node: {coherent_cl.neo4j_node_id}")
    print(f"CAP Neo4j Node: {coherent_cap.neo4j_node_id}")
    print(f"CL Historical Era: {coherent_cl.historical_era}")
    print(f"CAP Historical Era: {coherent_cap.historical_era}")
    
    print("\n✅ Both cases follow identical philosophy!")
    print("🎯 Users see seamless 1975-2023 continuity")


if __name__ == "__main__":
    asyncio.run(test_source_agnostic_processing())
