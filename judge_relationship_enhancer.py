#!/usr/bin/env python3
"""
Judge Relationship Enhancer - Extract judges and create judge-case relationships
"""

import logging
import re
from typing import List, Dict, Any, Optional
from real_neo4j_client import RealNeo4jClient

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JudgeRelationshipEnhancer:
    """Extract judge information and create judge-case relationships in Neo4j"""
    
    def __init__(self):
        self.neo4j_client = RealNeo4jClient()

        # Initialize cross-reference validator
        try:
            from src.processing.cross_reference_validator import CrossReferenceValidator
            self.cross_ref_validator = CrossReferenceValidator()
            self.cross_ref_enabled = True
        except ImportError:
            self.cross_ref_validator = None
            self.cross_ref_enabled = False

        # Enhanced disambiguation features
        self.court_hierarchy = {
            'scotus': {'type': 'supreme', 'level': 4, 'region': 'federal'},
            'ca1': {'type': 'circuit', 'level': 3, 'region': '1st_circuit'},
            'ca2': {'type': 'circuit', 'level': 3, 'region': '2nd_circuit'},
            'ca5': {'type': 'circuit', 'level': 3, 'region': '5th_circuit'},
            'ca9': {'type': 'circuit', 'level': 3, 'region': '9th_circuit'},
            'txnd': {'type': 'district', 'level': 2, 'region': 'texas_north'},
            'txsd': {'type': 'district', 'level': 2, 'region': 'texas_south'},
            'nysd': {'type': 'district', 'level': 2, 'region': 'new_york_south'},
        }

        # Full name prioritized patterns - ordered by preference for full names
        self.judge_patterns = [
            # PRIORITY 0: Historical CAP patterns (for Case Law Access Project data)
            # These patterns handle historical cases from 1950-1993

            # PRIORITY 0A: Later CAP patterns (1980s-1993) - Full names available
            # More precise patterns to avoid capturing court names
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+District\s+Judge',  # "JOHN T. NIXON, District Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+Circuit\s+Judge',  # "WILLIAM H. REHNQUIST, Circuit Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+Chief\s+Judge',  # "PATRICIA M. WALD, Chief Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+(?:U\.S\.\s+)?District\s+Judge',  # "ROBERT E. JONES, U.S. District Judge"
            r'([A-Z][A-Z]+(?:\s+[A-Z]\.?\s*)*[A-Z][A-Z]+),\s+(?:Associate\s+)?Justice',  # "SANDRA DAY O'CONNOR, Associate Justice"

            # Multiple judge patterns for "Before X, Y, and Z, Circuit Judges" - simplified
            r'Before\s+([A-Z][A-Z\s\.\']+),\s+([A-Z][A-Z\s\.\']+),\s+and\s+([A-Z][A-Z\s\.\']+),\s+Circuit\s+Judges',  # Three judges
            r'Before\s+([A-Z][A-Z\s\.\']+)\s+and\s+([A-Z][A-Z\s\.\']+),\s+Circuit\s+Judges',  # Two judges

            # PRIORITY 0B: Early CAP patterns (1950s-1970s) - Surnames only
            r'([A-Z][A-Z]+),\s+C\.?\s*J\.?',  # "HICKMAN, C. J." or "SMITH, C.J."
            r'([A-Z][A-Z]+),\s+Chief\s+Justice',  # "WARREN, Chief Justice"

            # Historical Justice patterns (surnames only)
            r'([A-Z][A-Z]+),\s+J\.?',  # "BRENNAN, J." or "MARSHALL, J"
            r'([A-Z][A-Z]+),\s+Justice',  # "BLACKMUN, Justice"
            r'([A-Z][A-Z]+),\s+Associate\s+Justice',  # "POWELL, Associate Justice"

            # Historical Judge patterns (surnames only)
            r'([A-Z][A-Z]+),\s+Judge',  # "JOHNSON, Judge"
            r'([A-Z][A-Z]+),\s+Circuit\s+Judge',  # "ADAMS, Circuit Judge"
            r'([A-Z][A-Z]+),\s+District\s+Judge',  # "BROWN, District Judge"

            # Historical opinion delivery patterns
            r'([A-Z][A-Z]+),\s+(?:C\.?\s*J\.?|J\.?|Justice|Judge)\.?\s*(?:delivered|wrote|authored)',  # "REHNQUIST, C.J., delivered"
            r'([A-Z][A-Z]+),\s+(?:C\.?\s*J\.?|J\.?|Justice|Judge)\.?\s*(?:concurring|dissenting)',  # "SCALIA, J., concurring"

            # PRIORITY 1: Real federal court document patterns (most common in actual data)

            # Circuit Court patterns (appellate)
            r'Before\s+(?:[A-Z][A-Z]+,\s+)*([A-Z][A-Z]+),\s+(?:and\s+)?([A-Z][A-Z]+),\s+Circuit\s+Judges',  # "Before SMITH, JONES, and WILSON, Circuit Judges"
            r'Before\s+(?:[A-Z][A-Z]+,\s+)*([A-Z][A-Z]+)\s+and\s+([A-Z][A-Z]+),\s+Circuit\s+Judges',  # "Before SMITH and JONES, Circuit Judges"
            r'Before\s+([A-Z][A-Z]+),\s+Circuit\s+Judge',  # "Before SMITH, Circuit Judge" (single judge)

            # District Court patterns (trial court)
            r'The\s+Honorable\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+)',  # "The Honorable Miranda M. Du" (REAL DATA FORMAT)
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?),?\s+(?:U\.S\.\s+)?(?:District|Magistrate)\s+Judge',  # "John Smith, U.S. District Judge"
            r'(?:U\.S\.\s+)?(?:District|Magistrate)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',  # "U.S. District Judge John Smith"
            r'(?:Chief\s+)?(?:District|Magistrate)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)?)',  # "Chief District Judge John Smith"

            # PRIORITY 2: Full name patterns with action words (ideal but less common in real data) - REQUIRE multiple words
            r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored)',  # Simple: First + (Middle/Last)+
            r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored|concurred|dissented)',  # Simple: First + (Middle/Last)+
            r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored|presiding)',  # Simple: First + (Middle/Last)+

            # PRIORITY 2: Full name with formal titles - REQUIRE multiple words (more flexible matching)
            r'Mr\.\s+(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+delivered',  # Simple: First + (Middle/Last)+
            r'Ms\.\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote)',  # Simple: First + (Middle/Last)+

            # PRIORITY 3: Full name with title and colon (common format) - REQUIRE multiple words
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+(?:Circuit|District)\s+Judge\s*:',  # Requires 2+ words
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)+),\s+(?:Chief\s+)?Justice\s*:',  # Requires 2+ words

            # PRIORITY 4: Justice joining patterns (common in Supreme Court) - Allow single or multiple words
            r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*),\s+with\s+whom\s+Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s+joins',  # Captures both justices
            r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s+(?:wrote\s+separately|concurred|dissented)',
            r'Justice\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s+[A-Z][a-z]+)*)\s+wrote\s+separately',

            # PRIORITY 5: Panel patterns (only if they capture full names) - REQUIRE multiple words
            r'Before[^.]*?([A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+)[^,]*,\s+(?:Circuit\s+)?Judge',  # Requires middle name/initial

            # PRIORITY 6: Single word patterns with action (allow single words but lower priority)
            r'(?:Circuit|District)\s+Judge\s+([A-Z][a-z]+)\s+(?:delivered|wrote|authored)',  # Single word with action
            r'(?:Chief\s+)?Justice\s+([A-Z][a-z]+)\s+(?:delivered|wrote|authored|concurred|dissented)',  # Single word with action
            r'Judge\s+([A-Z][a-z]+)\s+(?:delivered|wrote|authored|presiding)',  # Single word with action

            # LOWEST PRIORITY: Abbreviated patterns (J., C.J.) - moved to end
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',  # REQUIRE 2+ words for J.
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+),\s+C\.?J\.,?\s+(?:delivered|wrote)',  # REQUIRE 2+ words for C.J.

            # FALLBACK: Single names only if no full names found (lowest priority)
            r'([A-Z][a-z]+),\s+(?:Circuit|District)\s+Judge\s*:',  # Single name fallback
            r'([A-Z][a-z]+),\s+(?:Chief\s+)?Justice\s*:',  # Single name fallback
        ]
        
    def close(self):
        """Close connections"""
        if self.neo4j_client:
            self.neo4j_client.close()
    
    def enhance_judge_relationships(self, case_ids: List[str] = None) -> bool:
        """Enhance judge relationships for specified cases or all cases"""
        
        print("👨‍⚖️ JUDGE RELATIONSHIP ENHANCEMENT")
        print("=" * 60)
        
        try:
            # Get cases that need judge enhancement
            cases_to_enhance = self._get_cases_needing_judges(case_ids)
            
            if not cases_to_enhance:
                print("   ✅ All cases already have judge relationships")
                return True
            
            print(f"📊 Enhancing judge relationships for {len(cases_to_enhance)} cases")
            
            enhanced_count = 0
            total_judges = 0
            
            for case_id in cases_to_enhance:
                print(f"\n📄 Processing case: {case_id}")
                
                # Get case data
                case_data = self._get_case_data(case_id)
                
                if not case_data:
                    print(f"   ⚠️ No case data found")
                    continue
                
                # Debug: Show what text was retrieved
                text = case_data.get('text', '') if case_data else ''
                print(f"   📝 Text analysis:")
                print(f"      Text length: {len(text)} characters")
                if text:
                    print(f"      Text sample: {text[:200]}...")
                    print(f"      Has judge indicators: {'judge' in text.lower() or 'justice' in text.lower()}")
                else:
                    print(f"      ❌ No text found for context analysis")

                # Extract judges from case data
                judges = self._extract_judges_from_case(case_data)

                if judges:
                    # Create judge nodes and relationships
                    success = self._create_judge_relationships(case_id, judges, case_data)
                    if success:
                        enhanced_count += 1
                        total_judges += len(judges)
                        print(f"   ✅ Created {len(judges)} judge relationships")
                        
                        # Show judge names
                        for judge in judges[:3]:  # Show first 3
                            print(f"      - {judge['name']} ({judge['role']})")
                        if len(judges) > 3:
                            print(f"      ... and {len(judges) - 3} more")
                    else:
                        print(f"   ❌ Failed to create judge relationships")
                else:
                    print(f"   ⚠️ No judges found")
            
            print(f"\n📊 JUDGE ENHANCEMENT SUMMARY:")
            print(f"   Cases enhanced: {enhanced_count}")
            print(f"   Total judges processed: {total_judges}")
            
            return enhanced_count > 0
            
        except Exception as e:
            print(f"❌ Judge relationship enhancement failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _get_cases_needing_judges(self, case_ids: List[str] = None) -> List[str]:
        """Get cases that need judge enhancement (don't have judge relationships)"""
        
        try:
            with self.neo4j_client.driver.session() as session:
                if case_ids:
                    # Check specific cases
                    result = session.run('''
                        MATCH (c:Case)
                        WHERE c.id IN $case_ids
                        AND NOT EXISTS {
                            MATCH (c)-[r]-(:Judge)
                            WHERE type(r) IN ['DECIDED_BY', 'AUTHORED', 'PARTICIPATED']
                        }
                        RETURN c.id as case_id
                    ''', case_ids=case_ids)
                else:
                    # Check all cases
                    result = session.run('''
                        MATCH (c:Case)
                        WHERE NOT EXISTS {
                            MATCH (c)-[r]-(:Judge)
                            WHERE type(r) IN ['DECIDED_BY', 'AUTHORED', 'PARTICIPATED']
                        }
                        RETURN c.id as case_id
                        LIMIT 100
                    ''')
                
                return [record['case_id'] for record in result]
                
        except Exception as e:
            logger.error(f"Error checking cases needing judges: {e}")
            return case_ids or []
    
    def _get_case_data(self, case_id: str) -> Optional[Dict[str, Any]]:
        """Get case data from Neo4j and retrieve text from GCS if needed"""

        try:
            # First, get basic case data from Neo4j
            with self.neo4j_client.driver.session() as session:
                result = session.run('''
                    MATCH (c:Case {id: $case_id})
                    RETURN c.id as id, c.case_name as case_name, c.court as court,
                           c.court_name as court_name, c.text as text,
                           c.jurisdiction as jurisdiction, c.date_filed as date_filed
                ''', case_id=case_id)

                record = result.single()
                if not record:
                    return None

                case_data = dict(record)

                # Check if we have text in Neo4j
                neo4j_text = case_data.get('text', '')
                if neo4j_text and len(neo4j_text.strip()) > 100:
                    # We have good text in Neo4j
                    return case_data

                # If no text in Neo4j, try to get it from Supabase/GCS
                print(f"   ⚠️ No text in Neo4j for {case_id}, checking Supabase/GCS...")

                # Try to get text from Supabase and GCS
                enhanced_text = self._get_text_from_storage(case_id)
                if enhanced_text:
                    case_data['text'] = enhanced_text
                    print(f"   ✅ Retrieved text from storage: {len(enhanced_text)} characters")
                    return case_data
                else:
                    print(f"   ❌ No text found in any storage system")
                    return case_data  # Return without text

        except Exception as e:
            logger.error(f"Error getting case data for {case_id}: {e}")

        return None

    def _get_text_from_storage(self, case_id: str) -> Optional[str]:
        """Get case text from Supabase/GCS storage with multiple fallback strategies"""

        try:
            # Import Supabase and GCS clients
            from supabase import create_client
            from src.processing.storage.gcs_connector import GCSConnector
            import os

            # Initialize clients
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")

            if not supabase_url or not supabase_key:
                print(f"   ⚠️ No Supabase credentials available")
                return None

            supabase = create_client(supabase_url, supabase_key)

            # Strategy 1: Try to get text directly from Supabase (if stored there)
            try:
                response = supabase.table('cases').select('text, plain_text, gcs_path').eq('id', case_id).execute()

                if response.data and len(response.data) > 0:
                    case_record = response.data[0]

                    # Try direct text fields first
                    for text_field in ['text', 'plain_text']:
                        text = case_record.get(text_field, '')
                        if text and len(text.strip()) > 100:
                            print(f"   ✅ Retrieved text from Supabase.{text_field}: {len(text)} characters")
                            return text

                    # Strategy 2: Try GCS path if direct text not available
                    gcs_path = case_record.get('gcs_path')
                    if gcs_path:
                        try:
                            gcs_client = GCSConnector()
                            text = gcs_client.get_text(gcs_path)
                            if text and len(text.strip()) > 100:
                                print(f"   ✅ Retrieved text from GCS: {len(text)} characters")
                                return text
                        except Exception as gcs_error:
                            print(f"   ⚠️ GCS retrieval failed: {gcs_error}")

                    print(f"   ⚠️ No usable text found in Supabase record")
                else:
                    print(f"   ⚠️ No Supabase record found for case {case_id}")

            except Exception as supabase_error:
                print(f"   ⚠️ Supabase query failed: {supabase_error}")

            # Strategy 3: Try to get text from Neo4j again with different query
            try:
                with self.neo4j_client.driver.session() as session:
                    result = session.run('''
                        MATCH (c:Case {id: $case_id})
                        RETURN c.text as text, c.plain_text as plain_text
                    ''', case_id=case_id)

                    record = result.single()
                    if record:
                        for text_field in ['text', 'plain_text']:
                            text = record.get(text_field, '')
                            if text and len(text.strip()) > 100:
                                print(f"   ✅ Retrieved text from Neo4j.{text_field}: {len(text)} characters")
                                return text

            except Exception as neo4j_error:
                print(f"   ⚠️ Neo4j fallback query failed: {neo4j_error}")

            return None

        except Exception as e:
            print(f"   ⚠️ Storage text retrieval failed: {e}")
            return None
    
    def _extract_judges_from_case(self, case_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract judge information from case data"""
        
        judges = []
        seen_judges = set()
        
        # Method 1: Extract from case text using patterns
        text = case_data.get('text', '')
        if text:
            text_judges = self._extract_judges_from_text(text)
            for judge in text_judges:
                if judge['name'] not in seen_judges:
                    judges.append(judge)
                    seen_judges.add(judge['name'])
        
        # Method 2: Extract from case name (sometimes includes judge names)
        case_name = case_data.get('case_name', '')
        if case_name and 'judge' in case_name.lower():
            name_judges = self._extract_judges_from_case_name(case_name)
            for judge in name_judges:
                if judge['name'] not in seen_judges:
                    judges.append(judge)
                    seen_judges.add(judge['name'])
        
        # Add court context to judges
        court = case_data.get('court', '')
        court_name = case_data.get('court_name', '')
        
        for judge in judges:
            judge['court'] = court
            judge['court_name'] = court_name
            judge['jurisdiction'] = case_data.get('jurisdiction', 'US')
        
        return judges
    
    def _extract_judges_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract judge names from case text using patterns"""

        judges = []
        seen_names = set()

        # Check first 3000 characters where judge names typically appear
        search_text = text[:3000]

        print(f"         Debug: Testing {len(self.judge_patterns)} patterns on text")

        for i, pattern in enumerate(self.judge_patterns):
            matches = re.finditer(pattern, search_text, re.IGNORECASE)
            pattern_matches = []

            for match in matches:
                # Handle different pattern types
                if len(match.groups()) == 3:
                    # Three-judge pattern: "Before X, Y, and Z, Circuit Judges"
                    for group_idx in [1, 2, 3]:
                        judge_name = match.group(group_idx).strip()
                        if judge_name:
                            cleaned_name = self._clean_judge_name(judge_name)
                            if self._is_valid_judge_name(cleaned_name) and cleaned_name not in seen_names:
                                role = self._determine_judge_role(match.group(0), search_text, match.start())
                                judge_info = {
                                    'name': cleaned_name,
                                    'role': role,
                                    'source': 'text_extraction'
                                }
                                judges.append(judge_info)
                                pattern_matches.append(judge_info)
                                seen_names.add(cleaned_name)

                elif len(match.groups()) == 2:
                    # Check if this is a joining pattern, before pattern, or reverse pattern
                    if 'joins' in match.group(0):  # Joining pattern: Justice A, with whom Justice B joins
                        # Extract both judges from joining pattern
                        judge1_name = match.group(1).strip()
                        judge2_name = match.group(2).strip()

                        # Add both judges
                        for judge_name in [judge1_name, judge2_name]:
                            if judge_name:
                                cleaned_name = self._clean_judge_name(judge_name)
                                if self._is_valid_judge_name(cleaned_name) and cleaned_name not in seen_names:
                                    role = self._determine_judge_role(match.group(0), search_text, match.start())
                                    judge_info = {
                                        'name': cleaned_name,
                                        'role': role,
                                        'source': 'text_extraction'
                                    }
                                    judges.append(judge_info)  # Add to main judges list
                                    pattern_matches.append(judge_info)  # Also add to pattern matches for logging
                                    seen_names.add(cleaned_name)
                        continue  # Skip the normal processing for this match
                    elif 'Before' in match.group(0) and 'Circuit Judges' in match.group(0):  # Before pattern: Before A, B, and C, Circuit Judges
                        # Extract both judges from before pattern
                        judge1_name = match.group(1).strip()
                        judge2_name = match.group(2).strip()

                        # Add both judges
                        for judge_name in [judge1_name, judge2_name]:
                            if judge_name:
                                cleaned_name = self._clean_judge_name(judge_name)
                                if self._is_valid_judge_name(cleaned_name) and cleaned_name not in seen_names:
                                    role = self._determine_judge_role(match.group(0), search_text, match.start())
                                    judge_info = {
                                        'name': cleaned_name,
                                        'role': role,
                                        'source': 'text_extraction'
                                    }
                                    judges.append(judge_info)  # Add to main judges list
                                    pattern_matches.append(judge_info)  # Also add to pattern matches for logging
                                    seen_names.add(cleaned_name)
                        continue  # Skip the normal processing for this match
                    else:  # Reverse pattern (Last, First)
                        last_name = match.group(1).strip()
                        first_name = match.group(2).strip()
                        judge_name = f"{first_name} {last_name}"
                else:  # Standard pattern
                    judge_name = match.group(1).strip()

                # Clean up the name
                judge_name = self._clean_judge_name(judge_name)

                # Validate name (reasonable length, not common words)
                if self._is_valid_judge_name(judge_name) and judge_name not in seen_names:
                    # Determine role based on context
                    role = self._determine_judge_role(match.group(0), search_text, match.start())

                    judges.append({
                        'name': judge_name,
                        'role': role,
                        'source': 'text_extraction'
                    })
                    seen_names.add(judge_name)
                    pattern_matches.append(judge_name)
                else:
                    print(f"         Pattern {i+1}: Rejected '{judge_name}' (valid: {self._is_valid_judge_name(judge_name)}, seen: {judge_name in seen_names})")

            if pattern_matches:
                print(f"         Pattern {i+1}: Found {pattern_matches}")

        print(f"         Debug: Total judges found before deduplication: {len(judges)}")

        # Deduplicate judges (prefer full names over partial names)
        deduplicated_judges = self._deduplicate_judge_names(judges)

        print(f"         Debug: Total judges found after deduplication: {len(deduplicated_judges)}")
        return deduplicated_judges[:5]  # Limit to 5 judges per case
    
    def _extract_judges_from_case_name(self, case_name: str) -> List[Dict[str, Any]]:
        """Extract judge names from case name if present"""
        
        judges = []
        
        # Pattern for "In re Judge [Name]" or similar
        patterns = [
            r'(?:In re|Matter of)\s+Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+v\.',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, case_name, re.IGNORECASE)
            if match:
                judge_name = self._clean_judge_name(match.group(1))
                if self._is_valid_judge_name(judge_name):
                    judges.append({
                        'name': judge_name,
                        'role': 'subject',
                        'source': 'case_name'
                    })
        
        return judges
    
    def _clean_judge_name(self, name: str) -> str:
        """Clean and normalize judge name"""

        # Remove common prefixes/suffixes
        name = re.sub(r'^(Mr\.|Mrs\.|Ms\.|Dr\.)\s+', '', name, flags=re.IGNORECASE)
        name = re.sub(r',?\s+(Jr\.?|Sr\.?|III?|IV)$', '', name, flags=re.IGNORECASE)

        # Remove common false positive words that get captured in patterns
        # These often come from "Before X, Y and Z, Circuit Judges" patterns
        false_positive_words = [
            'and', 'or', 'the', 'before', 'after', 'with', 'by', 'from', 'to',
            'circuit', 'district', 'judge', 'judges', 'justice', 'justices',
            'court', 'courts', 'chief', 'associate', 'senior'
        ]

        # Split name into words and filter out false positives
        words = name.split()
        cleaned_words = []

        for word in words:
            # Check the word without punctuation for false positives
            word_clean = word.strip('.,;:').lower()

            # Keep the word if:
            # 1. It's not a false positive word
            # 2. It's at least 2 characters OR it's a single letter with a period (middle initial)
            if (word_clean not in false_positive_words and
                (len(word_clean) >= 2 or (len(word) == 2 and word.endswith('.')))):
                cleaned_words.append(word)

        # Rejoin the cleaned words
        name = ' '.join(cleaned_words)

        # Remove trailing punctuation from the final name
        name = re.sub(r'[,;:]+$', '', name)

        # Handle historical CAP names
        if name.isupper():
            if ' ' in name or '.' in name:
                # Later CAP full names: "JOHN T. NIXON" -> "John T. Nixon"
                name = name.title()
            elif len(name) >= 4:
                # Early CAP surnames: "HICKMAN" -> "Hickman"
                name = name.title()
        elif not name.isupper():
            # For mixed case names, normalize to title case
            name = name.title()

        # Clean up extra whitespace
        name = re.sub(r'\s+', ' ', name)

        return name.strip()
    
    def _is_valid_judge_name(self, name: str) -> bool:
        """Validate if extracted text is likely a judge name"""

        if not name or len(name) < 2 or len(name) > 60:  # Reduced minimum for historical names
            return False

        # Must contain at least one letter
        if not re.search(r'[a-zA-Z]', name):
            return False

        # Historical CAP names can be:
        # 1. All caps single surnames (e.g., "HICKMAN", "WARREN") - early CAP
        # 2. All caps full names (e.g., "JOHN T. NIXON", "SANDRA DAY O'CONNOR") - later CAP
        if name.isupper():
            # Single surname pattern (early CAP)
            if len(name) >= 4 and len(name) <= 15 and re.match(r'^[A-Z]+$', name):
                if name not in {'COURT', 'STATE', 'TEXAS', 'UNITED', 'STATES'}:
                    return True

            # Full name pattern (later CAP) - multiple words with spaces/dots
            if ' ' in name or '.' in name:
                # Check if it looks like a full name: "JOHN T. NIXON", "SANDRA DAY O'CONNOR"
                words = name.replace('.', '').split()
                if 2 <= len(words) <= 4:  # Reasonable name length
                    # All words should be reasonable name parts
                    if all(len(word) >= 2 and word.isalpha() for word in words):
                        return True

        # Exclude common false positives (expanded list)
        false_positives = {
            'court', 'case', 'state', 'united', 'states', 'america', 'texas',
            'district', 'circuit', 'supreme', 'appeals', 'opinion', 'decision',
            'plaintiff', 'defendant', 'appellant', 'appellee', 'petitioner',
            'respondent', 'majority', 'dissent', 'concur', 'per curiam',
            'delivered', 'writing', 'dissenting', 'concurring', 'joining',
            # Geographic false positives
            'city', 'county', 'heights', 'valley', 'river', 'mountain', 'hill',
            'cottonwood', 'springville', 'utah', 'california', 'new york',
            # Action/verb false positives
            'announced', 'presiding', 'over', 'under', 'above', 'below', 'between',
            'had announced', 'presiding over', 'has ruled', 'will decide',
            # Business/organization false positives
            'company', 'corporation', 'inc', 'llc', 'ltd', 'department', 'agency'
        }

        # Check if any word in the name is a false positive
        name_words = name.lower().split()
        if any(word in false_positives for word in name_words):
            return False

        # Check for phrase-like false positives
        name_lower = name.lower()
        phrase_false_positives = [
            'had announced', 'presiding over', 'cottonwood heights',
            'has ruled', 'will decide', 'was appointed', 'is presiding'
        ]
        if any(phrase in name_lower for phrase in phrase_false_positives):
            return False

        # Should have reasonable word count (1-4 words for full names)
        word_count = len(name_words)
        if word_count > 4:
            return False

        # For multi-word names, ensure they look like real names
        if word_count > 1:
            # Each word should start with a capital letter (after cleaning)
            words = name.split()
            for word in words:
                if not word[0].isupper() and not word.endswith('.'):  # Allow initials like "K."
                    return False

            # Multi-word names should not be all common words
            common_words = {'the', 'of', 'and', 'in', 'on', 'at', 'to', 'for', 'with', 'by'}
            if all(word.lower() in common_words for word in words):
                return False

        # Additional validation for single words
        if word_count == 1:
            # Single word should be a reasonable name length
            if len(name) < 3 or len(name) > 15:
                return False

            # Should not be a common English word
            common_english_words = {
                'over', 'under', 'above', 'below', 'between', 'through', 'during',
                'before', 'after', 'since', 'until', 'while', 'where', 'when'
            }
            if name_lower in common_english_words:
                return False

        return True

    def _deduplicate_judge_names(self, judges: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate judge names, preferring full names over partial names
        E.g., prefer 'Ryan M. Harris' over 'Harris'
        """
        if not judges:
            return judges

        # Sort by name quality (heavily prefer full names with multiple words and middle initials)
        def name_quality_score(judge):
            name = judge['name']
            words = name.split()
            score = 0

            # HEAVILY prefer multi-word names
            if len(words) >= 3:  # First Middle Last
                score += 1000
            elif len(words) == 2:  # First Last
                score += 500
            else:  # Single word
                score += 10

            # HUGE bonus for middle initials (e.g., "Ryan M. Harris")
            if any(len(word) == 2 and word.endswith('.') for word in words):
                score += 200

            # Bonus for longer total length
            score += len(name)

            # Bonus for common full name patterns
            if len(words) >= 2 and any(word.endswith('.') for word in words[1:-1]):  # Middle initial
                score += 100

            return score

        sorted_judges = sorted(judges, key=name_quality_score, reverse=True)

        deduplicated_judges = []
        seen_names = set()

        for judge in sorted_judges:
            name = judge['name']
            name_lower = name.lower()

            # Check if this name is a subset of any already seen name, or vice versa
            is_duplicate = False
            for seen_name in list(seen_names):  # Convert to list to allow modification
                seen_name_lower = seen_name.lower()

                # Check if names are related (one contains the other)
                if (name_lower in seen_name_lower or seen_name_lower in name_lower):
                    print(f"         Dedup: '{name}' conflicts with '{seen_name}'")

                    # Prefer the longer, more complete name
                    if len(name) > len(seen_name):
                        # Remove the shorter name and add the longer one
                        deduplicated_judges = [j for j in deduplicated_judges if j['name'] != seen_name]
                        seen_names.remove(seen_name)
                        deduplicated_judges.append(judge)
                        seen_names.add(name)
                        print(f"         Dedup: Keeping longer name: '{name}'")
                    else:
                        print(f"         Dedup: Keeping existing longer name: '{seen_name}'")

                    is_duplicate = True
                    break

            if not is_duplicate:
                deduplicated_judges.append(judge)
                seen_names.add(name)

        return deduplicated_judges

    def _determine_judge_role(self, match_text: str, full_text: str, match_pos: int) -> str:
        """Determine judge role based on context"""
        
        match_lower = match_text.lower()
        
        # Check for specific role indicators
        if 'chief' in match_lower:
            return 'chief'
        elif 'delivered the opinion' in full_text[match_pos:match_pos+100].lower():
            return 'author'
        elif 'dissent' in full_text[match_pos-50:match_pos+100].lower():
            return 'dissent'
        elif 'concur' in full_text[match_pos-50:match_pos+100].lower():
            return 'concur'
        else:
            return 'participated'
    
    def _create_judge_relationships(self, case_id: str, judges: List[Dict[str, Any]], 
                                  case_data: Dict[str, Any]) -> bool:
        """Create judge nodes and relationships in Neo4j"""
        
        try:
            with self.neo4j_client.driver.session() as session:
                success_count = 0
                
                for judge in judges:
                    judge_name = judge['name']
                    judge_role = judge['role']
                    
                    # Create unique judge ID with disambiguation
                    judge_id = self._create_unique_judge_id(judge_name, judge.get('court', ''), case_data.get('date_filed', ''))

                    # Calculate enhanced confidence score
                    confidence_score = self._calculate_judge_confidence(
                        judge_name,
                        judge.get('court', ''),
                        case_data.get('date_filed', ''),
                        case_data.get('text', '')
                    )

                    # Check for career progression (if enabled)
                    career_info = self._check_career_progression(
                        judge_name,
                        judge.get('court', ''),
                        case_data.get('date_filed', '')
                    )

                    # Cross-reference validation (if enabled)
                    external_validation = self._validate_judge_externally(
                        judge_name,
                        judge.get('court', '')
                    )

                    # Create or update judge node with enhanced features
                    session.run('''
                        MERGE (j:Judge {id: $judge_id})
                        ON CREATE SET
                            j.name = $name,
                            j.court = $court,
                            j.court_name = $court_name,
                            j.jurisdiction = $jurisdiction,
                            j.confidence_score = $confidence_score,
                            j.career_progression_detected = $career_progression,
                            j.external_validated = $external_validated,
                            j.external_confidence = $external_confidence,
                            j.created_at = datetime(),
                            j.case_count = 1
                        ON MATCH SET
                            j.case_count = COALESCE(j.case_count, 0) + 1,
                            j.confidence_score = CASE
                                WHEN $confidence_score > COALESCE(j.confidence_score, 0)
                                THEN $confidence_score
                                ELSE COALESCE(j.confidence_score, 0)
                            END,
                            j.updated_at = datetime()
                    ''',
                        judge_id=judge_id,
                        name=judge_name,
                        court=judge.get('court', ''),
                        court_name=judge.get('court_name', ''),
                        jurisdiction=judge.get('jurisdiction', 'US'),
                        confidence_score=confidence_score,
                        career_progression=career_info.get('is_career_progression', False),
                        external_validated=external_validation.get('validated', False),
                        external_confidence=external_validation.get('confidence', 0.0)
                    )
                    
                    # Determine relationship type
                    if judge_role in ['author', 'chief']:
                        relationship_type = 'AUTHORED'
                    elif judge_role in ['dissent', 'concur']:
                        relationship_type = 'PARTICIPATED'
                    else:
                        relationship_type = 'DECIDED_BY'
                    
                    # Create judge-case relationship
                    session.run(f'''
                        MATCH (j:Judge {{id: $judge_id}})
                        MATCH (c:Case {{id: $case_id}})
                        MERGE (c)-[r:{relationship_type}]->(j)
                        ON CREATE SET 
                            r.role = $role,
                            r.source = $source,
                            r.created_at = datetime()
                    ''', 
                        judge_id=judge_id,
                        case_id=case_id,
                        role=judge_role,
                        source=judge.get('source', 'unknown')
                    )
                    
                    success_count += 1
                
                return success_count > 0
                
        except Exception as e:
            logger.error(f"Error creating judge relationships for {case_id}: {e}")
            return False

    def _create_unique_judge_id(self, judge_name: str, court: str, date_filed: str) -> str:
        """Create a unique judge ID that handles disambiguation"""

        # Clean name for ID
        clean_name = judge_name.lower().replace(' ', '_').replace('.', '').replace("'", '')

        # Add court context for disambiguation
        court_suffix = ""
        if court:
            if 'supreme' in court.lower():
                court_suffix = "_scotus"
            elif 'circuit' in court.lower():
                # Extract circuit number/name
                if 'fifth' in court.lower() or 'ca5' in court.lower():
                    court_suffix = "_ca5"
                elif 'second' in court.lower() or 'ca2' in court.lower():
                    court_suffix = "_ca2"
                else:
                    court_suffix = "_circuit"
            elif 'district' in court.lower():
                if 'texas' in court.lower():
                    if 'northern' in court.lower():
                        court_suffix = "_txnd"
                    elif 'southern' in court.lower():
                        court_suffix = "_txsd"
                    elif 'eastern' in court.lower():
                        court_suffix = "_txed"
                    elif 'western' in court.lower():
                        court_suffix = "_txwd"
                    else:
                        court_suffix = "_txd"
                elif 'new york' in court.lower():
                    if 'southern' in court.lower():
                        court_suffix = "_nysd"
                    elif 'northern' in court.lower():
                        court_suffix = "_nynd"
                    elif 'eastern' in court.lower():
                        court_suffix = "_nyed"
                    elif 'western' in court.lower():
                        court_suffix = "_nywd"
                    else:
                        court_suffix = "_nyd"
                else:
                    court_suffix = "_district"

        # Add time period for historical disambiguation (optional)
        time_suffix = ""
        if date_filed:
            try:
                year = int(date_filed[:4])
                if year < 1900:
                    time_suffix = "_pre1900"
                elif year < 1950:
                    time_suffix = "_early1900s"
                elif year < 2000:
                    time_suffix = "_late1900s"
                else:
                    time_suffix = "_2000s"
            except (ValueError, IndexError):
                pass

        return f"judge_{clean_name}{court_suffix}{time_suffix}"

    def _calculate_judge_confidence(self,
                                  name: str,
                                  court: str,
                                  case_date: str,
                                  case_text: str = "") -> float:
        """Calculate confidence score for judge identification"""

        score = 0.0

        # Name quality (40% of score)
        words = name.split()
        if len(words) >= 3:
            score += 40  # Full name with middle initial
        elif len(words) == 2:
            score += 30  # First and last name
        else:
            score += 15  # Single name only

        # Court context (25% of score)
        if court in self.court_hierarchy:
            score += 25  # Known federal court
        else:
            score += 10  # Unknown court

        # Temporal context (15% of score)
        if case_date and len(case_date) >= 4:
            score += 15  # Good date info
        else:
            score += 5   # Limited date info

        # Text context (10% of score)
        if case_text and len(case_text) > 1000:
            score += 10  # Substantial text
        elif case_text:
            score += 5   # Some text

        # Pattern quality (10% of score)
        if any(word in case_text.lower() for word in ['delivered', 'authored', 'wrote']):
            score += 10  # Strong judge action pattern
        elif any(word in case_text.lower() for word in ['judge', 'justice']):
            score += 5   # Basic judge pattern

        return min(100.0, score)

    def _check_career_progression(self, judge_name: str, court: str, case_date: str) -> Dict[str, Any]:
        """Check for career progression patterns"""

        try:
            # Basic career progression detection
            # This is a framework - can be enhanced with more sophisticated logic

            career_info = {
                'is_career_progression': False,
                'progression_type': None,
                'confidence': 0.0,
                'notes': 'Career progression detection framework active'
            }

            # Simple heuristics for career progression
            if court in ['scotus']:
                # Supreme Court appointments often come from lower courts
                career_info['is_career_progression'] = True
                career_info['progression_type'] = 'supreme_appointment'
                career_info['confidence'] = 0.8
            elif court in ['ca1', 'ca2', 'ca5', 'ca9']:
                # Circuit court appointments often come from district courts
                career_info['is_career_progression'] = True
                career_info['progression_type'] = 'circuit_appointment'
                career_info['confidence'] = 0.6

            return career_info

        except Exception as e:
            print(f"   ⚠️ Career progression check failed for {judge_name}: {e}")
            return {'is_career_progression': False, 'confidence': 0.0, 'notes': f'Error: {e}'}

    def _validate_judge_externally(self, judge_name: str, court: str) -> Dict[str, Any]:
        """Validate judge against external sources"""

        if not self.cross_ref_enabled or not self.cross_ref_validator:
            return {'validated': False, 'confidence': 0.0, 'notes': 'Cross-reference validation disabled'}

        try:
            validation_summary = self.cross_ref_validator.get_validation_summary(judge_name, court)
            return validation_summary
        except Exception as e:
            print(f"   ⚠️ External validation failed for {judge_name}: {e}")
            return {'validated': False, 'confidence': 0.0, 'notes': f'Validation error: {e}'}


def main():
    """Run judge relationship enhancement"""
    
    enhancer = JudgeRelationshipEnhancer()
    
    try:
        # Enhance judge relationships for all cases
        success = enhancer.enhance_judge_relationships()
        
        if success:
            print("\n✅ Judge relationship enhancement successful!")
            return 0
        else:
            print("\n❌ Judge relationship enhancement failed!")
            return 1
            
    finally:
        enhancer.close()


if __name__ == "__main__":
    exit(main())
