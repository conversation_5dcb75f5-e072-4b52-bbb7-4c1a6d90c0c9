#!/usr/bin/env python3
"""
Test CAP processing with REAL Pinecone to verify actual vector chunking
This will show the true vector count per case
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client
from pinecone import Pinecone

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components with REAL clients
from src.processing.atomic_storage_pipeline import AtomicStoragePipeline
from src.processing.cross_system_validator import CrossSystemValidator
from src.processing.atomic_checkpoint_manager import AtomicCheckpointManager
from src.processing.retry_manager import RetryManager
from src.ui.enhanced_progress_ui import EnhancedProgressUI
from source_agnostic_processor import SourceAgnosticProcessor

# Import mock GCS and Neo4j (we'll keep these as mocks)
from test_cap_enhanced_tracking import MockGCSClient, MockNeo4jClient

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'test_cap_real_pinecone_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RealPineconeClient:
    """Real Pinecone client for testing actual vector storage"""
    
    def __init__(self):
        load_dotenv()
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        
        if not pinecone_api_key:
            raise ValueError("PINECONE_API_KEY not found in environment")
        
        self.pc = Pinecone(api_key=pinecone_api_key)

        # Use the index specified in .env file
        self.index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
        self.index = self.pc.Index(self.index_name)
        
        logger.info(f"✅ Real Pinecone client initialized with index: {self.index_name}")
    
    def upsert(self, vectors, namespace="tx"):
        """Upsert vectors to real Pinecone"""
        try:
            # Convert vectors to Pinecone format
            pinecone_vectors = []
            for vector in vectors:
                pinecone_vectors.append({
                    'id': vector['id'],
                    'values': vector['values'],
                    'metadata': vector['metadata']
                })
            
            # Upsert to Pinecone
            response = self.index.upsert(vectors=pinecone_vectors, namespace=namespace)
            logger.info(f"✅ Upserted {len(pinecone_vectors)} vectors to Pinecone")
            return response
            
        except Exception as e:
            logger.error(f"❌ Error upserting to Pinecone: {e}")
            raise


async def test_cap_with_real_pinecone():
    """Test CAP processing with real Pinecone to see actual vector counts"""
    
    logger.info("🚀 TESTING CAP WITH REAL PINECONE")
    logger.info("=" * 80)
    
    load_dotenv()
    
    # Setup clients - REAL Pinecone, mock others
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    supabase = create_client(supabase_url, supabase_key)
    
    gcs_client = MockGCSClient()
    pinecone_client = RealPineconeClient()  # REAL PINECONE!
    neo4j_client = MockNeo4jClient()
    
    # Clean existing test data
    logger.info("🧹 Cleaning existing test data...")
    try:
        supabase.table('cases').delete().like('batch_id', 'real_pinecone_test_%').execute()
        logger.info("✅ Existing test data cleaned")
    except Exception as e:
        logger.warning(f"Warning cleaning test data: {e}")
    
    # Initialize source-agnostic processor with REAL Pinecone
    processor = SourceAgnosticProcessor(
        supabase, gcs_client, pinecone_client, neo4j_client
    )
    
    logger.info("🎯 Testing with REAL Pinecone:")
    logger.info(f"   📦 Batch Size: 5 cases (small test)")
    logger.info(f"   🔄 Real vector storage and counting")
    
    # Process a small batch of CAP cases with real Pinecone
    import json
    import gzip
    from pathlib import Path
    
    cap_file = Path("data/caselaw_access_project/cap_00000.jsonl.gz")
    
    # Get a few Texas cases
    raw_cases = []
    with gzip.open(cap_file, 'rt', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if len(raw_cases) >= 5:  # Just 5 cases for testing
                break
                
            try:
                raw_case = json.loads(line.strip())
                text = raw_case.get('text', '')
                
                # Check if it's a Texas case
                if 'texas' in text.lower() or 'tex' in text.lower():
                    raw_cases.append(raw_case)
                    
            except Exception as e:
                logger.warning(f"Error parsing line {i}: {e}")
    
    if not raw_cases:
        logger.error("❌ No Texas cases found")
        return False
    
    logger.info(f"📚 Found {len(raw_cases)} Texas cases to test")
    
    # Process with real Pinecone
    batch_id = f"real_pinecone_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        result = await processor.process_coherent_batch(
            raw_cases=raw_cases,
            source_type='caselaw_access_project',
            batch_id=batch_id
        )
        
        logger.info(f"📊 Processing Result: {result}")
        
        if not result['success']:
            logger.error("❌ Processing failed")
            return False
        
        # Now check the actual results
        logger.info("\n🔍 CHECKING RESULTS WITH REAL PINECONE:")
        logger.info("=" * 60)
        
        # Get cases from database
        cases = supabase.table('cases').select('id, word_count, pinecone_id').eq('batch_id', batch_id).execute()
        
        total_db_count = 0
        total_actual_vectors = 0
        
        for case in cases.data:
            case_id = case['id']
            db_word_count = case.get('word_count') or 0
            pinecone_id = case.get('pinecone_id', '')

            total_db_count += db_word_count
            
            # Query real Pinecone for actual vectors
            try:
                # Get index stats first
                stats = pinecone_client.index.describe_index_stats()
                
                # Query for vectors with this case_id
                query_response = pinecone_client.index.query(
                    vector=[0.0] * stats.dimension,
                    filter={"case_id": case_id},
                    top_k=50,
                    include_metadata=True,
                    namespace="tx"
                )
                
                actual_vector_count = len(query_response.matches)
                total_actual_vectors += actual_vector_count
                
                logger.info(f"📄 Case: {case_id}")
                logger.info(f"   DB word_count: {db_word_count}")
                logger.info(f"   Actual vectors in Pinecone: {actual_vector_count}")
                logger.info(f"   Primary Pinecone ID: {pinecone_id}")
                
                # Show vector details
                if actual_vector_count > 0:
                    for i, match in enumerate(query_response.matches):
                        chunk_idx = match.metadata.get('chunk_index', 'unknown')
                        logger.info(f"      Vector {i+1}: {match.id} (chunk {chunk_idx})")
                else:
                    logger.warning(f"⚠️ No vectors found for case {case_id}")
                
                logger.info("")
                
            except Exception as e:
                logger.error(f"❌ Error querying Pinecone for case {case_id}: {e}")
        
        # Summary
        logger.info("=" * 60)
        logger.info("📊 REAL PINECONE TEST SUMMARY:")
        logger.info(f"   Cases processed: {len(cases.data)}")
        logger.info(f"   Total DB word_count: {total_db_count}")
        logger.info(f"   Total actual vectors in Pinecone: {total_actual_vectors}")
        
        if len(cases.data) > 0:
            avg_db = total_db_count / len(cases.data)
            avg_actual = total_actual_vectors / len(cases.data)
            logger.info(f"   Average DB word_count per case: {avg_db:.2f}")
            logger.info(f"   Average actual vectors per case: {avg_actual:.2f}")
            
            if avg_actual > 1:
                logger.info("✅ SUCCESS: Multiple vectors per case confirmed!")
                logger.info("✅ Chunking is working correctly with real Pinecone!")
                return True
            else:
                logger.warning("⚠️ Still seeing 1 vector per case - need to investigate further")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_cap_with_real_pinecone())
    if success:
        print("\n🎉 REAL PINECONE TEST SUCCESSFUL!")
        print("✅ CAP chunking verified with actual vector storage")
    else:
        print("\n❌ REAL PINECONE TEST FAILED")
        print("❌ Need to investigate chunking issue")
