#!/usr/bin/env python3
"""
Test Corrected Patterns
Test the corrected patterns with simpler regex
"""

import re

def test_corrected_patterns():
    """Test the corrected patterns with simpler regex"""
    
    print("🔍 TESTING CORRECTED PATTERNS")
    print("=" * 60)
    
    # Corrected patterns with simpler regex
    corrected_patterns = [
        {
            'pattern': r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+(?:delivered|wrote|authored|presiding)',
            'text': 'Judge <PERSON> wrote the preliminary ruling.',
            'expected': '<PERSON>',
            'description': 'Judge + Full <PERSON> + wrote'
        },
        {
            'pattern': r'Mr\.\s+(?:Chief\s+)?Justice\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+)\s+delivered',
            'text': 'Mr. Chief Justice <PERSON> delivered the opinion of the Court.',
            'expected': '<PERSON>',
            'description': 'Mr. Chief Justice + Full Name + delivered'
        },
        {
            'pattern': r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+),\s+J\.,?\s+(?:delivered|wrote|concurring|dissenting)',
            'text': '<PERSON>, J., concurring in part.',
            'expected': '<PERSON> <PERSON> <PERSON>',
            'description': 'Full Name + J. + concurring'
        },
        {
            'pattern': r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+),\s+C\.?<PERSON>\.,?\s+(?:delivered|wrote)',
            'text': '<PERSON> Warren, C.J., delivered the opinion.',
            'expected': 'Earl Warren',
            'description': 'Full Name + C.J. + delivered'
        }
    ]
    
    print(f"📊 Testing {len(corrected_patterns)} corrected patterns:")
    
    success_count = 0
    
    for i, case in enumerate(corrected_patterns, 1):
        print(f"\n   {i}. {case['description']}")
        print(f"      Text: {case['text']}")
        print(f"      Pattern: {case['pattern']}")
        print(f"      Expected: '{case['expected']}'")
        
        # Test the pattern
        match = re.search(case['pattern'], case['text'], re.IGNORECASE)
        
        if match:
            extracted = match.group(1).strip()
            success = extracted == case['expected']
            status = "✅" if success else "❌"
            print(f"      Result: {status} Extracted '{extracted}'")
            
            if success:
                success_count += 1
            else:
                print(f"         Expected: '{case['expected']}'")
                print(f"         Got: '{extracted}'")
        else:
            print(f"      Result: ❌ No match found")
    
    print(f"\n🎯 CORRECTED PATTERNS SUMMARY:")
    print(f"   Success rate: {success_count}/{len(corrected_patterns)} ({success_count/len(corrected_patterns)*100:.1f}%)")
    
    return success_count == len(corrected_patterns)


def test_name_regex_component():
    """Test the corrected name regex component"""
    
    print(f"\n🔬 TESTING CORRECTED NAME REGEX")
    print("=" * 60)
    
    # Corrected name pattern
    name_pattern = r'[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)+'
    
    test_names = [
        ('Sarah Michelle Johnson', True),  # Should match
        ('Earl Warren', True),             # Should match
        ('Rick A. Warren', True),          # Should match
        ('Hugo L. Black', True),           # Should match
        ('Johnson', False),                # Should not match (single word)
        ('Warren', False),                 # Should not match (single word)
        ('J. Smith', True),                # Should match (initial + last)
        ('Mary K. Johnson', True),         # Should match (first + initial + last)
    ]
    
    print(f"📊 Testing name pattern: {name_pattern}")
    
    success_count = 0
    
    for name, expected_match in test_names:
        match = re.fullmatch(name_pattern, name)
        actual_match = bool(match)
        success = actual_match == expected_match
        status = "✅" if success else "❌"
        
        print(f"   {status} '{name}' → {'Match' if actual_match else 'No match'} (expected: {'Match' if expected_match else 'No match'})")
        
        if success:
            success_count += 1
    
    print(f"\n🎯 NAME REGEX SUMMARY:")
    print(f"   Success rate: {success_count}/{len(test_names)} ({success_count/len(test_names)*100:.1f}%)")
    
    return success_count == len(test_names)


def test_full_extraction():
    """Test full extraction with corrected patterns"""
    
    print(f"\n🔄 TESTING FULL EXTRACTION")
    print("=" * 60)
    
    # Test text with all the cases
    test_text = '''
    District Judge Rick A. Warren delivered this opinion.
    Judge Sarah Michelle Johnson wrote the preliminary ruling.
    Rick A. Warren, District Judge:
    After careful consideration, the motion is GRANTED.
    Sarah Michelle Johnson, J., concurring in part.
    Mr. Chief Justice Earl Warren delivered the opinion of the Court.
    Earl Warren, C.J., delivered the opinion.
    '''
    
    print(f"📄 Test text:")
    print(f"   {test_text.strip()}")
    
    # Use the actual patterns from the enhancer
    from judge_relationship_enhancer import JudgeRelationshipEnhancer
    
    enhancer = JudgeRelationshipEnhancer()
    judges = enhancer._extract_judges_from_text(test_text)
    
    print(f"\n📊 EXTRACTION RESULTS:")
    print(f"   Total judges extracted: {len(judges)}")
    
    full_name_count = 0
    for judge in judges:
        words = len(judge['name'].split())
        is_full_name = words > 1
        status = "✅ Full" if is_full_name else "❌ Partial"
        print(f"      - '{judge['name']}' ({status}, {words} words)")
        
        if is_full_name:
            full_name_count += 1
    
    # Expected full names
    expected_full_names = [
        'Rick A. Warren',
        'Sarah Michelle Johnson',
        'Earl Warren'
    ]
    
    print(f"\n🎯 EXPECTED VS ACTUAL:")
    extracted_names = [j['name'] for j in judges]
    
    matches = 0
    for expected in expected_full_names:
        found = expected in extracted_names
        status = "✅" if found else "❌"
        print(f"      {status} {expected}")
        if found:
            matches += 1
    
    print(f"\n🎯 FULL EXTRACTION SUMMARY:")
    print(f"   Full names: {full_name_count}/{len(judges)} ({full_name_count/len(judges)*100:.1f}%)")
    print(f"   Expected matches: {matches}/{len(expected_full_names)} ({matches/len(expected_full_names)*100:.1f}%)")
    
    enhancer.close()
    
    # Success if we get all expected full names
    return matches == len(expected_full_names) and full_name_count >= matches


if __name__ == "__main__":
    print("🧪 CORRECTED PATTERN TESTING")
    print("=" * 80)
    
    # Test corrected patterns
    patterns_success = test_corrected_patterns()
    
    # Test name regex component
    regex_success = test_name_regex_component()
    
    # Test full extraction
    extraction_success = test_full_extraction()
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Corrected patterns: {'✅' if patterns_success else '❌'}")
    print(f"   Name regex: {'✅' if regex_success else '❌'}")
    print(f"   Full extraction: {'✅' if extraction_success else '❌'}")
    
    if patterns_success and regex_success and extraction_success:
        print(f"\n🎉 SUCCESS: Ready to achieve 7/7 full names!")
    else:
        print(f"\n⚠️ NEEDS MORE WORK: Some patterns still failing")
