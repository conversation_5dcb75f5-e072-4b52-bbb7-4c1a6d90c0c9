#!/usr/bin/env python3
"""
Detailed script to check Neo4j database population and node types.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append('src')

from processing.storage.neo4j_connector import Neo4jConnector

async def check_neo4j_detailed():
    """Check Neo4j database with detailed node analysis."""
    try:
        print("🔍 DETAILED NEO4J DATABASE ANALYSIS")
        print("=" * 60)
        
        # Initialize Neo4j connector
        neo4j = Neo4jConnector()
        
        with neo4j.driver.session() as session:
            # Count total nodes
            result = session.run("MATCH (n) RETURN count(n) as total_nodes")
            total_nodes = result.single()['total_nodes']
            
            print(f"📊 Neo4j Database:")
            print(f"   Total nodes: {total_nodes:,}")
            
            # Get all node labels and their counts
            result = session.run("""
                MATCH (n)
                WITH labels(n) as node_labels
                UNWIND node_labels as label
                RETURN label, count(*) as count
                ORDER BY count DESC
            """)
            
            print(f"\n   All Node Types:")
            node_counts = {}
            for record in result:
                label = record['label']
                count = record['count']
                node_counts[label] = count
                print(f"     {label}: {count:,}")
            
            # Check for Document nodes specifically
            result = session.run("MATCH (d:Document) RETURN count(d) as document_count")
            document_count = result.single()['document_count']
            print(f"\n   📄 Document nodes: {document_count:,}")
            
            # Check for Case nodes specifically  
            result = session.run("MATCH (c:Case) RETURN count(c) as case_count")
            case_count = result.single()['case_count']
            print(f"   📋 Case nodes: {case_count:,}")
            
            # Sample Document nodes
            print(f"\n   Sample Document nodes:")
            result = session.run("MATCH (d:Document) RETURN d.id, d.title, d.source LIMIT 5")
            for record in result:
                doc_id = record['d.id']
                title = record['d.title']
                source = record['d.source']
                print(f"     {doc_id}: {title[:50] if title else 'No title'}... (source: {source})")
            
            # Sample Case nodes
            print(f"\n   Sample Case nodes:")
            result = session.run("MATCH (c:Case) RETURN c.id, c.case_name, c.source LIMIT 5")
            for record in result:
                case_id = record['c.id']
                case_name = record['c.case_name']
                source = record['c.source']
                print(f"     {case_id}: {case_name[:50] if case_name else 'No name'}... (source: {source})")
            
            # Count relationships
            result = session.run("MATCH ()-[r]->() RETURN count(r) as total_relationships")
            total_relationships = result.single()['total_relationships']
            print(f"\n   Total relationships: {total_relationships:,}")
            
            # Check relationship types
            result = session.run("""
                MATCH ()-[r]->()
                RETURN type(r) as rel_type, count(*) as count
                ORDER BY count DESC
                LIMIT 10
            """)
            
            print(f"\n   Top Relationship Types:")
            for record in result:
                rel_type = record['rel_type']
                count = record['count']
                print(f"     {rel_type}: {count:,}")
        
        neo4j.close()
        return total_nodes, document_count, case_count
        
    except Exception as e:
        print(f"❌ Error checking Neo4j: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0

async def main():
    """Main function."""
    total_nodes, document_count, case_count = await check_neo4j_detailed()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total Nodes: {total_nodes:,}")
    print(f"   Document Nodes: {document_count:,}")
    print(f"   Case Nodes: {case_count:,}")
    
    # Expected vs Actual
    expected_cases = 15284  # From Supabase
    print(f"\n📊 CONSISTENCY CHECK:")
    print(f"   Expected case-related nodes: {expected_cases:,}")
    print(f"   Actual Document nodes: {document_count:,}")
    print(f"   Actual Case nodes: {case_count:,}")
    print(f"   Total case-related nodes: {document_count + case_count:,}")
    
    if document_count + case_count < expected_cases * 0.9:
        print(f"   ❌ MAJOR INCONSISTENCY: Missing {expected_cases - (document_count + case_count):,} nodes")
    else:
        print(f"   ✅ CONSISTENT: Node count matches expectations")

if __name__ == "__main__":
    asyncio.run(main())
