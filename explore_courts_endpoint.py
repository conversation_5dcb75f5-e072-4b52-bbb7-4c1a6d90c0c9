#!/usr/bin/env python3
"""
Explore CourtListener Courts Endpoint
Analyze the dedicated Courts API for structured court data
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def explore_courts_endpoint():
    """Explore the Courts endpoint for structured court data"""
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    base_url_v3 = "https://www.courtlistener.com/api/rest/v3"
    base_url_v4 = "https://www.courtlistener.com/api/rest/v4"
    
    if not api_key:
        print("❌ No CourtListener API key found")
        return
    
    print("🏛️ EXPLORING COURTLISTENER COURTS ENDPOINT")
    print("=" * 60)
    
    async with httpx.AsyncClient(
        headers={"Authorization": f"Token {api_key}"},
        timeout=30.0
    ) as client:
        
        # 1. Explore Courts endpoint structure
        print("\n🏛️ 1. COURTS ENDPOINT STRUCTURE")
        print("-" * 40)
        
        try:
            # Try both v3 and v4
            for version, base_url in [("v3", base_url_v3), ("v4", base_url_v4)]:
                print(f"\n📡 Testing {version} Courts endpoint:")
                courts_url = f"{base_url}/courts/"
                
                try:
                    # Get OPTIONS to see available fields
                    options_response = await client.options(courts_url)
                    print(f"   OPTIONS status: {options_response.status_code}")
                    
                    # Get basic courts data
                    params = {'page_size': 5, 'format': 'json'}
                    response = await client.get(courts_url, params=params)
                    response.raise_for_status()
                    
                    data = response.json()
                    courts = data.get('results', [])
                    
                    print(f"   ✅ Found {len(courts)} courts")
                    
                    if courts:
                        court = courts[0]
                        print(f"   📊 Available fields: {list(court.keys())}")
                        
                        # Show key court information
                        print(f"   📄 Sample court:")
                        key_fields = ['id', 'short_name', 'full_name', 'jurisdiction', 'citation_string']
                        for field in key_fields:
                            value = court.get(field, 'N/A')
                            print(f"      {field}: {value}")
                        
                        # Check if this version works
                        if len(courts) > 0:
                            working_version = version
                            working_base_url = base_url
                            print(f"   ✅ {version} is working!")
                            break
                
                except Exception as e:
                    print(f"   ❌ {version} failed: {e}")
        
        except Exception as e:
            print(f"❌ Error exploring courts endpoint: {e}")
            return
        
        # 2. Get comprehensive court data
        print(f"\n🏛️ 2. COMPREHENSIVE COURT DATA ANALYSIS")
        print("-" * 40)
        
        try:
            courts_url = f"{working_base_url}/courts/"
            params = {'page_size': 20, 'format': 'json'}
            
            response = await client.get(courts_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            courts = data.get('results', [])
            
            print(f"📊 Analyzing {len(courts)} courts")
            
            # Categorize courts by jurisdiction
            jurisdictions = {}
            court_types = {}
            
            for court in courts:
                jurisdiction = court.get('jurisdiction', 'Unknown')
                short_name = court.get('short_name', 'Unknown')
                
                if jurisdiction not in jurisdictions:
                    jurisdictions[jurisdiction] = []
                jurisdictions[jurisdiction].append(short_name)
                
                # Categorize by type (rough heuristic)
                if 'Supreme' in short_name:
                    court_type = 'Supreme'
                elif 'Circuit' in short_name or 'App' in short_name:
                    court_type = 'Appellate'
                elif 'District' in short_name or 'D.' in short_name:
                    court_type = 'District'
                else:
                    court_type = 'Other'
                
                if court_type not in court_types:
                    court_types[court_type] = []
                court_types[court_type].append(short_name)
            
            print(f"\n📊 COURTS BY JURISDICTION:")
            for jurisdiction, court_list in jurisdictions.items():
                print(f"   {jurisdiction}: {len(court_list)} courts")
                for court in court_list[:3]:  # Show first 3
                    print(f"      - {court}")
                if len(court_list) > 3:
                    print(f"      ... and {len(court_list) - 3} more")
            
            print(f"\n📊 COURTS BY TYPE:")
            for court_type, court_list in court_types.items():
                print(f"   {court_type}: {len(court_list)} courts")
                for court in court_list[:3]:  # Show first 3
                    print(f"      - {court}")
                if len(court_list) > 3:
                    print(f"      ... and {len(court_list) - 3} more")
        
        except Exception as e:
            print(f"❌ Error analyzing court data: {e}")
        
        # 3. Test court-case linking
        print(f"\n🔗 3. COURT-CASE LINKING ANALYSIS")
        print("-" * 40)
        
        try:
            # Test filtering opinions by court
            test_courts = ['scotus', 'ca1', 'txnd']  # Supreme Court, 1st Circuit, N.D. Texas
            
            for court_slug in test_courts:
                print(f"\n🔍 Testing court: {court_slug}")
                
                # Test the filtering approach mentioned in your message
                opinions_url = f"{working_base_url}/opinions/"
                params = {
                    'cluster__docket__court': court_slug,
                    'page_size': 3,
                    'format': 'json'
                }
                
                try:
                    response = await client.get(opinions_url, params=params)
                    response.raise_for_status()
                    
                    data = response.json()
                    opinions = data.get('results', [])
                    
                    print(f"   ✅ Found {len(opinions)} opinions for {court_slug}")
                    
                    for opinion in opinions:
                        case_name = opinion.get('case_name', 'Unknown')
                        opinion_id = opinion.get('id')
                        print(f"      - Opinion {opinion_id}: {case_name[:50]}...")
                
                except Exception as e:
                    print(f"   ❌ Failed to get opinions for {court_slug}: {e}")
        
        except Exception as e:
            print(f"❌ Error testing court-case linking: {e}")
        
        # 4. Analyze how to integrate with our current system
        print(f"\n🔧 4. INTEGRATION ANALYSIS")
        print("-" * 40)
        
        print("📊 CURRENT SYSTEM vs COURTS ENDPOINT:")
        print("   Current: Extract court from position data (indirect)")
        print("   Better: Use Courts endpoint for structured court data")
        print("   Best: Link cases to courts via court IDs/slugs")
        
        print(f"\n🎯 INTEGRATION OPPORTUNITIES:")
        opportunities = [
            "✅ Replace position-based court extraction with Courts API",
            "✅ Use court slugs for precise court identification",
            "✅ Filter cases by court for targeted processing",
            "✅ Get comprehensive court metadata (jurisdiction, citation format)",
            "✅ Link judges to specific courts through position data",
            "✅ Enable court-based case categorization and search"
        ]
        
        for opp in opportunities:
            print(f"   {opp}")
        
        print(f"\n🚀 RECOMMENDED IMPLEMENTATION:")
        recommendations = [
            "1. Fetch court data from Courts endpoint during initialization",
            "2. Create court ID → court metadata mapping",
            "3. Extract court information from case docket data",
            "4. Store court as structured data (not just text)",
            "5. Use court slugs for filtering and categorization",
            "6. Link judge positions to specific courts"
        ]
        
        for rec in recommendations:
            print(f"   {rec}")

if __name__ == "__main__":
    asyncio.run(explore_courts_endpoint())
