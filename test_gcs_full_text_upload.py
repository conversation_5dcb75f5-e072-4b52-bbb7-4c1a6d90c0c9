#!/usr/bin/env python3
"""
Test GCS Full Text Upload Functionality

This script tests the complete GCS full text upload pipeline:
1. Store case text in GCS with proper organization
2. Retrieve and verify stored text
3. Test jurisdiction-based path organization
4. Validate metadata storage
"""

import os
import logging
import asyncio
from datetime import datetime
from dotenv import load_dotenv

from src.processing.storage.gcs_connector import GCSConnector

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GCSFullTextTester:
    """Test GCS full text upload functionality."""
    
    def __init__(self):
        """Initialize GCS tester."""
        self.bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        self.gcs = GCSConnector(bucket_name=self.bucket_name)
        
        # Test data
        self.test_cases = [
            {
                'case_id': 'cl_test_12345',
                'jurisdiction': 'tx',
                'year': '2023',
                'text': '''SUPREME COURT OF TEXAS
                
Case No. 23-0123
SMITH v. JONES
Decided: March 15, 2023

OPINION

This case involves a personal injury claim arising from a motor vehicle accident. The plaintiff, <PERSON>, filed suit against defendant <PERSON> <PERSON> for negligence.

FACTS

On January 10, 2022, plaintiff was driving southbound on Highway 35 when defendant ran a red light and collided with plaintiff's vehicle. Plaintiff sustained significant injuries including a broken leg and concussion.

HOLDING

We hold that defendant's conduct constituted negligence per se due to the traffic violation. The trial court's judgment for plaintiff is AFFIRMED.

DISPOSITION

The judgment of the court of appeals is affirmed. Costs are assessed against defendant.

Justice BROWN delivered the opinion of the Court.
Justice GARCIA concurred.
Justice WILSON dissented.''',
                'source': 'court_listener'
            },
            {
                'case_id': 'cap_ny_67890',
                'jurisdiction': 'ny',
                'year': '2022',
                'text': '''NEW YORK COURT OF APPEALS
                
Case No. 2022-456
JOHNSON v. CITY OF NEW YORK
Decided: September 8, 2022

MEMORANDUM OPINION

This appeal concerns municipal liability for sidewalk defects under New York City Administrative Code § 7-210.

BACKGROUND

Plaintiff tripped and fell on a cracked sidewalk in Manhattan. The crack was approximately 2 inches wide and 1 inch deep. Plaintiff sued the City for negligence and violation of the sidewalk maintenance statute.

ANALYSIS

Under § 7-210, property owners are liable for sidewalk defects abutting their property. However, the City retains liability for defects it creates or has actual notice of.

CONCLUSION

The evidence shows the City had constructive notice of the defect through prior complaints. Summary judgment for defendant is REVERSED.

Chief Judge RIVERA authored the opinion.
Judge GARCIA concurred.
Judge WILSON dissented.''',
                'source': 'caselaw_access_project'
            },
            {
                'case_id': 'cl_fl_11111',
                'jurisdiction': 'fl',
                'year': '2024',
                'text': '''FLORIDA SUPREME COURT
                
Case No. SC24-111
MARTINEZ v. SUNSHINE INSURANCE CO.
Decided: February 14, 2024

OPINION

This case presents the question of whether an insurance company's delay in processing a claim constitutes bad faith under Florida Statutes § 624.155.

PROCEDURAL HISTORY

Plaintiff filed a hurricane damage claim in August 2023. Defendant insurance company did not respond for 90 days, then denied the claim without adequate investigation.

LEGAL STANDARD

Bad faith requires: (1) no reasonable basis for denial, and (2) knowledge or reckless disregard of the lack of reasonable basis.

APPLICATION

The 90-day delay without communication, followed by denial without proper investigation, satisfies both prongs of the bad faith test.

DISPOSITION

The district court's judgment for plaintiff is AFFIRMED. Punitive damages award of $500,000 is also affirmed.

Justice CANADY delivered the opinion of the Court.
Justice POLSTON concurred.
Justice MUÑIZ dissented.''',
                'source': 'court_listener'
            }
        ]
        
        logger.info(f"✅ GCS Full Text Tester initialized for bucket: {self.bucket_name}")
    
    def test_basic_text_storage(self) -> bool:
        """Test basic text storage and retrieval."""
        
        logger.info("🔍 Testing basic GCS text storage...")
        
        try:
            # Test simple text storage
            test_path = f"test/basic_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            test_content = f"GCS test content at {datetime.now().isoformat()}"
            
            # Store text
            result_url = self.gcs.store_text(test_content, test_path)
            logger.info(f"   Stored test content at: {result_url}")
            
            # Retrieve text
            retrieved_content = self.gcs.get_text(test_path)
            
            # Verify content matches
            if retrieved_content == test_content:
                logger.info("   ✅ Basic text storage and retrieval successful")
                return True
            else:
                logger.error("   ❌ Retrieved content doesn't match stored content")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ Basic text storage test failed: {e}")
            return False
    
    def test_case_text_storage(self) -> bool:
        """Test case-specific text storage with proper organization."""
        
        logger.info("🔍 Testing case text storage with jurisdiction organization...")
        
        success_count = 0
        
        for i, test_case in enumerate(self.test_cases, 1):
            logger.info(f"   Testing case {i}/{len(self.test_cases)}: {test_case['case_id']}")
            
            try:
                # Store case text using jurisdiction-based organization
                gcs_path = self.gcs.store_case_text(
                    case_id=test_case['case_id'],
                    text=test_case['text'],
                    jurisdiction=test_case['jurisdiction'],
                    year=test_case['year']
                )
                
                if gcs_path:
                    logger.info(f"     ✅ Stored at: {gcs_path}")
                    
                    # Verify storage by retrieving
                    retrieved_text = self.gcs.get_text(gcs_path)
                    
                    if retrieved_text == test_case['text']:
                        logger.info(f"     ✅ Verification successful ({len(retrieved_text)} chars)")
                        success_count += 1
                    else:
                        logger.error(f"     ❌ Verification failed - content mismatch")
                else:
                    logger.error(f"     ❌ Storage failed - no path returned")
                    
            except Exception as e:
                logger.error(f"     ❌ Error storing case {test_case['case_id']}: {e}")
        
        success_rate = success_count / len(self.test_cases)
        logger.info(f"   Case text storage success rate: {success_count}/{len(self.test_cases)} ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% success rate required
    
    def test_metadata_storage(self) -> bool:
        """Test storing case metadata alongside text."""
        
        logger.info("🔍 Testing case metadata storage...")
        
        try:
            test_case = self.test_cases[0]  # Use first test case
            
            # Create metadata
            metadata = {
                'case_id': test_case['case_id'],
                'case_name': 'SMITH v. JONES',
                'jurisdiction': test_case['jurisdiction'],
                'court': 'Supreme Court of Texas',
                'date_decided': '2023-03-15',
                'judges': ['Justice BROWN', 'Justice GARCIA', 'Justice WILSON'],
                'outcome': 'AFFIRMED',
                'practice_area': 'personal_injury',
                'word_count': len(test_case['text'].split()),
                'source': test_case['source'],
                'processed_at': datetime.now().isoformat()
            }
            
            # Store metadata as JSON
            metadata_path = f"cases/{test_case['jurisdiction']}/{test_case['year']}/{test_case['case_id']}/metadata.json"
            result_url = self.gcs.store_json(metadata, metadata_path)
            
            logger.info(f"   Stored metadata at: {result_url}")
            
            # Retrieve and verify metadata
            retrieved_metadata = self.gcs.get_json(metadata_path)
            
            if retrieved_metadata == metadata:
                logger.info("   ✅ Metadata storage and retrieval successful")
                return True
            else:
                logger.error("   ❌ Retrieved metadata doesn't match stored metadata")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ Metadata storage test failed: {e}")
            return False
    
    def test_file_organization(self) -> bool:
        """Test that files are organized correctly by jurisdiction and year."""
        
        logger.info("🔍 Testing file organization structure...")
        
        try:
            # Check that files exist in expected locations
            expected_paths = []
            
            for test_case in self.test_cases:
                expected_path = f"cases/{test_case['jurisdiction']}/{test_case['year']}/{test_case['case_id']}/full_text.txt"
                expected_paths.append(expected_path)
            
            existing_count = 0
            for path in expected_paths:
                if self.gcs.file_exists(path):
                    logger.info(f"   ✅ Found file at expected path: {path}")
                    existing_count += 1
                else:
                    logger.warning(f"   ⚠️ File not found at expected path: {path}")
            
            organization_rate = existing_count / len(expected_paths)
            logger.info(f"   File organization success rate: {existing_count}/{len(expected_paths)} ({organization_rate:.1%})")
            
            return organization_rate >= 0.8  # 80% success rate required
            
        except Exception as e:
            logger.error(f"   ❌ File organization test failed: {e}")
            return False
    
    def test_large_text_handling(self) -> bool:
        """Test handling of large case texts."""
        
        logger.info("🔍 Testing large text handling...")
        
        try:
            # Create a large text (simulate a complex case with multiple opinions)
            large_text = self.test_cases[0]['text'] * 50  # Repeat text 50 times
            large_case_id = 'cl_large_test_99999'
            
            logger.info(f"   Testing with large text: {len(large_text):,} characters")
            
            # Store large text
            gcs_path = self.gcs.store_case_text(
                case_id=large_case_id,
                text=large_text,
                jurisdiction='tx',
                year='2024'
            )
            
            if gcs_path:
                logger.info(f"   ✅ Stored large text at: {gcs_path}")
                
                # Verify retrieval
                retrieved_text = self.gcs.get_text(gcs_path)
                
                if len(retrieved_text) == len(large_text):
                    logger.info(f"   ✅ Large text retrieval successful ({len(retrieved_text):,} chars)")
                    return True
                else:
                    logger.error(f"   ❌ Large text size mismatch: {len(retrieved_text)} vs {len(large_text)}")
                    return False
            else:
                logger.error("   ❌ Large text storage failed")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ Large text handling test failed: {e}")
            return False
    
    def run_comprehensive_test(self) -> dict:
        """Run comprehensive GCS full text upload tests."""
        
        logger.info("🚀 STARTING GCS FULL TEXT UPLOAD TESTS")
        logger.info("=" * 50)
        
        results = {
            'basic_storage': False,
            'case_text_storage': False,
            'metadata_storage': False,
            'file_organization': False,
            'large_text_handling': False,
            'overall_success': False
        }
        
        # Run all tests
        results['basic_storage'] = self.test_basic_text_storage()
        results['case_text_storage'] = self.test_case_text_storage()
        results['metadata_storage'] = self.test_metadata_storage()
        results['file_organization'] = self.test_file_organization()
        results['large_text_handling'] = self.test_large_text_handling()
        
        # Calculate overall success
        passed_tests = sum(1 for result in results.values() if result is True)
        total_tests = len([k for k in results.keys() if k != 'overall_success'])
        
        results['overall_success'] = passed_tests >= 4  # At least 4/5 tests must pass
        
        # Print results
        logger.info("\n🎉 GCS FULL TEXT UPLOAD TEST RESULTS")
        logger.info("=" * 40)
        
        for test_name, passed in results.items():
            if test_name != 'overall_success':
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        logger.info(f"\n📊 OVERALL RESULT: {'✅ SUCCESS' if results['overall_success'] else '❌ FAILURE'}")
        logger.info(f"   Tests passed: {passed_tests}/{total_tests}")
        
        return results


def main():
    """Main function to run GCS full text upload tests."""
    
    tester = GCSFullTextTester()
    results = tester.run_comprehensive_test()
    
    return results['overall_success']


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
