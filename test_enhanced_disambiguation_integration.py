#!/usr/bin/env python3
"""
Test Enhanced Disambiguation Integration
Test the enhanced disambiguation features with real data
"""

import asyncio
import logging
import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Import components
from src.processing.storage.gcs_connector import GCSConnector
from test_cap_real_pinecone import RealPineconeClient
from real_neo4j_client import RealNeo4jClient
from source_agnostic_processor import SourceAgnosticProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedDisambiguationTest:
    """Test enhanced disambiguation features"""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize storage clients
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        self.gcs_client = GCSConnector()
        self.pinecone_client = RealPineconeClient()
        self.neo4j_client = RealNeo4jClient()
        
        # CourtListener API
        self.cl_api_key = os.getenv("COURTLISTENER_API_KEY")
        self.cl_base_url = "https://www.courtlistener.com/api/rest/v4"
        
        # Test batch ID
        self.test_batch_id = f"enhanced_disambig_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def close(self):
        """Close connections"""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
    
    def create_career_progression_test_cases(self) -> list:
        """Create test cases that simulate judge career progression"""
        
        print(f"👨‍⚖️ CREATING CAREER PROGRESSION TEST CASES")
        print("=" * 60)
        
        # Simulate a judge's career progression: District → Circuit → Supreme Court
        test_cases = [
            # Judge John Smith - Early Career (District Court)
            {
                'id': 'career_test_1',
                'source': 'courtlistener',
                'case_name': 'United States v. Corporation A',
                'court': 'txnd',
                'court_name': 'U.S. District Court, Northern District of Texas',
                'date_filed': '1995-03-15',
                'jurisdiction': 'US',
                'text': '''
                UNITED STATES v. CORPORATION A
                
                MEMORANDUM OPINION AND ORDER
                
                District Judge John Smith delivered this opinion.
                
                This matter comes before the Court on Defendant's Motion to Dismiss.
                After careful consideration, the motion is DENIED.
                
                John Smith, District Judge
                ''',
                'expected_judges': ['John Smith']
            },
            
            # Judge John Smith - Mid Career (Circuit Court)
            {
                'id': 'career_test_2',
                'source': 'courtlistener',
                'case_name': 'Smith v. Jones Industries',
                'court': 'ca5',
                'court_name': 'U.S. Court of Appeals, Fifth Circuit',
                'date_filed': '2005-08-22',
                'jurisdiction': 'US',
                'text': '''
                SMITH v. JONES INDUSTRIES
                
                Before WILLIAMS, SMITH, and RODRIGUEZ, Circuit Judges.
                
                Circuit Judge John Smith delivered the opinion of the court.
                
                We reverse the district court's judgment.
                
                John Smith, Circuit Judge:
                The district court erred in its analysis...
                ''',
                'expected_judges': ['John Smith']
            },
            
            # Judge John Smith - Late Career (Supreme Court)
            {
                'id': 'career_test_3',
                'source': 'courtlistener',
                'case_name': 'Brown v. State Education Board',
                'court': 'scotus',
                'court_name': 'Supreme Court of the United States',
                'date_filed': '2015-05-17',
                'jurisdiction': 'US',
                'text': '''
                BROWN v. STATE EDUCATION BOARD
                
                Justice John Smith delivered the opinion of the Court.
                
                We hold that the state's education policy violates the Equal Protection Clause.
                
                Justice John Smith, with whom Justice Williams joins, delivered the opinion.
                ''',
                'expected_judges': ['John Smith', 'Williams']
            },
            
            # Different Judge with Same Name (Different Geographic Region)
            {
                'id': 'career_test_4',
                'source': 'courtlistener',
                'case_name': 'Johnson v. Technology Corp',
                'court': 'ca2',
                'court_name': 'U.S. Court of Appeals, Second Circuit',
                'date_filed': '2005-08-22',  # Same era as mid-career John Smith
                'jurisdiction': 'US',
                'text': '''
                JOHNSON v. TECHNOLOGY CORP
                
                Before MARTINEZ, SMITH, and DAVIS, Circuit Judges.
                
                Circuit Judge John Smith authored the dissenting opinion.
                
                John Smith, Circuit Judge, dissenting:
                I respectfully dissent from the majority...
                ''',
                'expected_judges': ['John Smith']  # Different John Smith (CA2 vs CA5)
            }
        ]
        
        print(f"   ✅ Created {len(test_cases)} career progression test cases")
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n   {i}. {case['case_name']} ({case['court']}, {case['date_filed'][:4]})")
            print(f"      Expected judges: {case['expected_judges']}")
        
        return test_cases
    
    async def test_enhanced_disambiguation(self) -> bool:
        """Test enhanced disambiguation with career progression"""
        
        print(f"\n🔄 ENHANCED DISAMBIGUATION TEST")
        print("=" * 60)
        
        try:
            # Create career progression test cases
            test_cases = self.create_career_progression_test_cases()
            
            print(f"\n📊 Processing {len(test_cases)} career progression cases...")
            
            # Process through pipeline
            processor = SourceAgnosticProcessor(
                self.supabase, self.gcs_client, self.pinecone_client, self.neo4j_client
            )
            
            result = await processor.process_coherent_batch(
                raw_cases=test_cases,
                source_type='courtlistener',
                batch_id=self.test_batch_id
            )
            
            print(f"\n📊 PROCESSING RESULT:")
            print(f"   Success: {result['success']}")
            print(f"   Processed: {result['processed']}")
            
            if not result['success']:
                print(f"   ❌ Processing failed")
                return False
            
            # Verify enhanced disambiguation
            return await self.verify_enhanced_disambiguation()
            
        except Exception as e:
            print(f"❌ Enhanced disambiguation test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_enhanced_disambiguation(self) -> bool:
        """Verify enhanced disambiguation worked correctly"""
        
        print(f"\n🔍 VERIFYING ENHANCED DISAMBIGUATION")
        print("=" * 60)
        
        try:
            with self.neo4j_client.driver.session() as session:
                # Get all judges from the test
                result = session.run('''
                    MATCH (j:Judge)
                    WHERE EXISTS {
                        MATCH (j)<-[r]-(c:Case {batch_id: $batch_id})
                        WHERE type(r) IN ['DECIDED_BY', 'PARTICIPATED', 'AUTHORED']
                    }
                    RETURN j.id as judge_id, j.name as judge_name, j.court as judge_court,
                           j.era as judge_era, j.confidence_score as confidence,
                           count(*) as case_count
                    ORDER BY j.name, j.era
                ''', batch_id=self.test_batch_id)
                
                all_judges = list(result)
                
                print(f"📊 1. JUDGES FROM ENHANCED DISAMBIGUATION TEST:")
                print(f"   Total judges found: {len(all_judges)}")
                
                for judge in all_judges:
                    confidence = judge.get('confidence', 0)
                    print(f"      - {judge['judge_name']} (ID: {judge['judge_id']})")
                    print(f"        Court: {judge['judge_court']}, Era: {judge['judge_era']}")
                    print(f"        Confidence: {confidence:.1f}, Cases: {judge['case_count']}")
                
                # Analyze career progression detection
                john_smith_judges = [j for j in all_judges if 'john' in j['judge_name'].lower() and 'smith' in j['judge_name'].lower()]
                
                print(f"\n📊 2. JOHN SMITH CAREER PROGRESSION ANALYSIS:")
                print(f"   John Smith judges found: {len(john_smith_judges)}")
                
                if len(john_smith_judges) == 1:
                    # Perfect: Career progression detected, single judge identity
                    judge = john_smith_judges[0]
                    print(f"   ✅ CAREER PROGRESSION DETECTED:")
                    print(f"      Single identity: {judge['judge_name']}")
                    print(f"      ID: {judge['judge_id']}")
                    print(f"      Cases across career: {judge['case_count']}")
                    career_success = True
                    
                elif len(john_smith_judges) == 2:
                    # Good: Two different John Smiths (CA5 vs CA2) properly distinguished
                    print(f"   ✅ GEOGRAPHIC DISAMBIGUATION:")
                    for judge in john_smith_judges:
                        region = 'CA5' if 'ca5' in judge['judge_id'] or '5th' in judge['judge_court'] else 'CA2'
                        print(f"      {region} John Smith: {judge['judge_id']}")
                    career_success = True
                    
                else:
                    # Too many or too few John Smiths
                    print(f"   ❌ DISAMBIGUATION ISSUE:")
                    print(f"      Expected 1-2 John Smith judges, found {len(john_smith_judges)}")
                    career_success = False
                
                # Check for enhanced features
                enhanced_features = {
                    'geographic_regions': len(set(j['judge_id'].split('_')[2] for j in all_judges if len(j['judge_id'].split('_')) > 2)),
                    'temporal_eras': len(set(j['judge_era'] for j in all_judges if j['judge_era'])),
                    'confidence_scores': len([j for j in all_judges if j.get('confidence', 0) > 0]),
                }
                
                print(f"\n📊 3. ENHANCED FEATURES VERIFICATION:")
                print(f"   Geographic regions: {enhanced_features['geographic_regions']}")
                print(f"   Temporal eras: {enhanced_features['temporal_eras']}")
                print(f"   Confidence scores: {enhanced_features['confidence_scores']}")
                
                # Success criteria
                success_criteria = [
                    len(all_judges) > 0,  # Judges found
                    career_success,  # Career progression or geographic disambiguation working
                    enhanced_features['geographic_regions'] > 1,  # Multiple regions detected
                    enhanced_features['temporal_eras'] > 1,  # Multiple eras detected
                ]
                
                success = all(success_criteria)
                
                print(f"\n🎯 ENHANCED DISAMBIGUATION VERIFICATION:")
                print(f"   Judges found: {'✅' if len(all_judges) > 0 else '❌'} ({len(all_judges)})")
                print(f"   Career/Geographic logic: {'✅' if career_success else '❌'}")
                print(f"   Geographic regions: {'✅' if enhanced_features['geographic_regions'] > 1 else '❌'} ({enhanced_features['geographic_regions']})")
                print(f"   Temporal eras: {'✅' if enhanced_features['temporal_eras'] > 1 else '❌'} ({enhanced_features['temporal_eras']})")
                print(f"   Overall success: {'✅' if success else '❌'}")
                
                if success:
                    print(f"\n🎉 ENHANCED DISAMBIGUATION: SUCCESS!")
                    print(f"✅ Career progression and geographic disambiguation working")
                    print(f"✅ Enhanced features (regions, eras, confidence) implemented")
                    print(f"✅ Smart judge identity management operational")
                else:
                    print(f"\n⚠️ ENHANCED DISAMBIGUATION: NEEDS REFINEMENT!")
                    if not career_success:
                        print(f"❌ Career progression or geographic disambiguation issues")
                
                return success
                
        except Exception as e:
            print(f"❌ Enhanced disambiguation verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data"""
        
        print(f"\n🧹 CLEANING UP ENHANCED DISAMBIGUATION TEST")
        print("=" * 60)
        
        try:
            # Clean Supabase
            self.supabase.table('cases').delete().eq('batch_id', self.test_batch_id).execute()
            print("   ✅ Cleaned Supabase test data")
            
            # Clean Neo4j
            with self.neo4j_client.driver.session() as session:
                session.run('MATCH (c:Case {batch_id: $batch_id}) DETACH DELETE c', 
                          batch_id=self.test_batch_id)
                session.run('''
                    MATCH (j:Judge)
                    WHERE NOT EXISTS { MATCH (j)-[]-() }
                    DELETE j
                ''')
                
            print("   ✅ Cleaned Neo4j test data")
            
        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def main():
    """Run enhanced disambiguation test"""
    
    print("🧪 ENHANCED JUDGE DISAMBIGUATION TEST")
    print("=" * 80)
    print("🎯 Testing smart career progression and geographic disambiguation")
    
    test = EnhancedDisambiguationTest()
    
    try:
        # Run the test
        success = await test.test_enhanced_disambiguation()
        
        if success:
            print(f"\n🎉 ENHANCED DISAMBIGUATION: SUCCESS!")
            print(f"✅ Smart judge disambiguation working with career progression")
            print(f"✅ Geographic and temporal disambiguation operational")
            print(f"✅ Enhanced features ready for production")
            return True
        else:
            print(f"\n❌ ENHANCED DISAMBIGUATION: NEEDS MORE WORK!")
            print(f"❌ Smart disambiguation features need refinement")
            return False
            
    finally:
        # Cleanup
        await test.cleanup_test_data()
        test.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
